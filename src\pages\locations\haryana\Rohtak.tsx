import React from 'react';
import { Link } from 'react-router-dom';
import { ArrowRight, MapPin, TrendingUp, Users, CheckCircle, Building, Star, GraduationCap } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';

const Rohtak = () => {
  const services = [
    'SEO Services Rohtak',
    'Google Ads Management Rohtak',
    'Social Media Marketing Rohtak',
    'Local SEO Rohtak',
    'E-commerce SEO Rohtak',
    'Content Marketing Rohtak',
    'Website Development Rohtak',
    'Digital Marketing Consulting Rohtak'
  ];

  const industries = [
    'Education Rohtak',
    'Agriculture Rohtak',
    'Automobile Parts Rohtak',
    'Textile Industry Rohtak',
    'Real Estate Rohtak',
    'Healthcare Rohtak',
    'Sports Equipment Rohtak',
    'Retail & E-commerce Rohtak'
  ];

  const areas = [
    'Model Town Digital Marketing',
    'Civil Lines SEO Services',
    'Subhash Nagar Digital Marketing',
    'Sector 14 SEO Services',
    'Delhi Road Digital Marketing',
    'Jind Road SEO Services',
    'Meham Digital Marketing',
    'Kalanaur SEO Services'
  ];

  const stats = [
    {
      metric: '85+',
      description: 'Rohtak Businesses Served',
      detail: 'Across all sectors'
    },
    {
      metric: '370%',
      description: 'Average Traffic Increase',
      detail: 'For Rohtak clients'
    },
    {
      metric: '₹26L+',
      description: 'Revenue Generated',
      detail: 'For Rohtak businesses'
    }
  ];

  return (
    <div className="min-h-screen bg-slate-900 relative">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        {/* Hero Section */}
        <section className="pt-32 pb-20">
          <div className="container mx-auto px-6">
            <div className="max-w-4xl mx-auto text-center">
              <div className="inline-flex items-center space-x-2 bg-violet-500/20 border border-violet-500/30 rounded-full px-6 py-2 mb-8">
                <GraduationCap className="w-4 h-4 text-violet-400" />
                <span className="text-violet-400 font-medium">Rohtak Digital Marketing • Education & Sports Hub</span>
              </div>
              
              <h1 className="text-5xl md:text-6xl font-bold mb-6 leading-tight">
                #1 Digital Marketing Company in
                <span className="bg-gradient-to-r from-violet-400 to-violet-600 bg-clip-text text-transparent"> Rohtak</span>
              </h1>
              
              <p className="text-xl text-slate-300 mb-12 max-w-3xl mx-auto">
                Leading digital marketing agency in Rohtak helping businesses dominate online. From educational institutions to sports equipment manufacturers, we've helped 85+ businesses grow with proven SEO, Google Ads, and digital marketing strategies across all Rohtak areas.
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button asChild size="lg" className="bg-gradient-to-r from-violet-500 to-violet-600 hover:from-violet-600 hover:to-violet-700 text-white text-lg px-8 py-4">
                  <Link to="/quote">Get Free Rohtak SEO Audit</Link>
                </Button>
                <Button asChild size="lg" variant="outline" className="border-violet-500 text-violet-400 hover:bg-violet-500 hover:text-white text-lg px-8 py-4">
                  <Link to="/contact">Call Rohtak Office</Link>
                </Button>
              </div>
            </div>
          </div>
        </section>

        {/* Stats Section */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Rohtak Digital Marketing
                <span className="bg-gradient-to-r from-violet-400 to-violet-600 bg-clip-text text-transparent"> Success Stories</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto mb-12">
                Real results from Rohtak businesses across industries - from educational institutions to sports equipment manufacturers in this academic hub.
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
              {stats.map((stat, index) => (
                <div key={index} className="text-center">
                  <div className="text-5xl font-bold text-violet-400 mb-2">{stat.metric}</div>
                  <div className="text-white font-semibold mb-1">{stat.description}</div>
                  <div className="text-slate-400">{stat.detail}</div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Services Section */}
        <section className="py-20">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Digital Marketing Services in
                <span className="bg-gradient-to-r from-violet-400 to-violet-600 bg-clip-text text-transparent"> Rohtak</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto">
                Comprehensive digital marketing solutions for Rohtak businesses looking to dominate online in the Education & Sports Hub.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {services.map((service, index) => (
                <Card key={index} className="bg-slate-900/80 border-violet-500/20 hover:border-violet-500/40 transition-all duration-300">
                  <CardContent className="p-6 text-center">
                    <Building className="w-8 h-8 text-violet-400 mx-auto mb-4" />
                    <h3 className="text-white font-semibold mb-2">{service}</h3>
                    <Button asChild size="sm" variant="outline" className="border-violet-500 text-violet-400 hover:bg-violet-500 hover:text-white">
                      <Link to="/quote">Learn More</Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Industries Section */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Rohtak Industries We
                <span className="bg-gradient-to-r from-violet-400 to-violet-600 bg-clip-text text-transparent"> Serve</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto">
                Specialized digital marketing strategies for Rohtak's education-focused and sports equipment manufacturing industries.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {industries.map((industry, index) => (
                <Card key={index} className="bg-slate-900/80 border-violet-500/20 hover:border-violet-500/40 transition-all duration-300">
                  <CardContent className="p-6 text-center">
                    <Star className="w-8 h-8 text-violet-400 mx-auto mb-4" />
                    <h3 className="text-white font-semibold mb-2">{industry}</h3>
                    <Button asChild size="sm" variant="outline" className="border-violet-500 text-violet-400 hover:bg-violet-500 hover:text-white">
                      <Link to="/quote">Get Strategy</Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Areas Section */}
        <section className="py-20">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Rohtak Areas We
                <span className="bg-gradient-to-r from-violet-400 to-violet-600 bg-clip-text text-transparent"> Cover</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto">
                Local digital marketing expertise across all major Rohtak areas including educational districts and commercial zones.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {areas.map((area, index) => (
                <Card key={index} className="bg-slate-900/80 border-violet-500/20 hover:border-violet-500/40 transition-all duration-300">
                  <CardContent className="p-6 text-center">
                    <MapPin className="w-8 h-8 text-violet-400 mx-auto mb-4" />
                    <h3 className="text-white font-semibold mb-2">{area}</h3>
                    <Button asChild size="sm" variant="outline" className="border-violet-500 text-violet-400 hover:bg-violet-500 hover:text-white">
                      <Link to="/contact">Contact Us</Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Why Choose Us Section */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Why Choose GOD Digital Marketing in
                <span className="bg-gradient-to-r from-violet-400 to-violet-600 bg-clip-text text-transparent"> Rohtak?</span>
              </h2>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              <Card className="bg-slate-900/80 border-violet-500/20">
                <CardContent className="p-8">
                  <TrendingUp className="w-12 h-12 text-violet-400 mb-6" />
                  <h3 className="text-xl font-bold text-white mb-4">Education Sector Expertise</h3>
                  <p className="text-slate-300">Deep understanding of Rohtak's educational ecosystem, sports industry, and academic institution marketing needs.</p>
                </CardContent>
              </Card>

              <Card className="bg-slate-900/80 border-violet-500/20">
                <CardContent className="p-8">
                  <Users className="w-12 h-12 text-violet-400 mb-6" />
                  <h3 className="text-xl font-bold text-white mb-4">Proven Rohtak Results</h3>
                  <p className="text-slate-300">85+ successful Rohtak campaigns with measurable ROI improvements and business growth across educational and commercial sectors.</p>
                </CardContent>
              </Card>

              <Card className="bg-slate-900/80 border-violet-500/20">
                <CardContent className="p-8">
                  <CheckCircle className="w-12 h-12 text-violet-400 mb-6" />
                  <h3 className="text-xl font-bold text-white mb-4">Sports & Education Focus</h3>
                  <p className="text-slate-300">Specialized expertise in marketing for educational institutions and sports-related businesses with proven student acquisition strategies.</p>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 bg-gradient-to-r from-violet-600 to-violet-500">
          <div className="container mx-auto px-6 text-center">
            <h2 className="text-4xl md:text-5xl font-bold mb-6">Ready to Dominate Rohtak's Digital Market?</h2>
            <p className="text-xl mb-8 max-w-3xl mx-auto">
              Join 85+ Rohtak businesses that trust GOD Digital Marketing for their online growth. From educational institutions to sports equipment, we deliver results.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button asChild size="lg" className="bg-white text-violet-600 hover:bg-slate-100 text-lg px-8 py-4">
                <Link to="/quote">Get Free Rohtak SEO Audit</Link>
              </Button>
              <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-violet-600 text-lg px-8 py-4">
                <Link to="/contact">Call Rohtak Office Now</Link>
              </Button>
            </div>
          </div>
        </section>
      </main>
      
      <Footer />
    </div>
  );
};

export default Rohtak;
