import React from 'react';
import { Link } from 'react-router-dom';
import { ArrowRight, Mail, TrendingUp, Users, CheckCircle, Building, Star, Crown, Send, Zap } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';

const EmailMarketingDelhi = () => {
  const emailServices = [
    'Email Marketing Strategy Delhi',
    'Email Campaign Management Delhi',
    'Email Automation Delhi',
    'Newsletter Marketing Delhi',
    'Drip Campaign Setup Delhi',
    'Email Template Design Delhi',
    'Email List Building Delhi',
    'Email Segmentation Delhi',
    'Personalized Email Marketing Delhi',
    'Transactional Email Setup Delhi',
    'Email A/B Testing Delhi',
    'Email Analytics & Reporting Delhi',
    'Email Deliverability Optimization Delhi',
    'CRM Email Integration Delhi',
    'E-commerce Email Marketing Delhi',
    'Lead Nurturing Emails Delhi',
    'Welcome Email Series Delhi',
    'Abandoned Cart Emails Delhi',
    'Re-engagement Campaigns Delhi',
    'Email Compliance Management Delhi'
  ];

  const delhiAreas = [
    'Central Delhi Email Marketing',
    'South Delhi Email Services',
    'North Delhi Email Campaigns',
    'East Delhi Email Marketing',
    'West Delhi Email Services',
    'New Delhi Email Campaigns',
    'Connaught Place Email Marketing',
    'Karol Bagh Email Services',
    'Lajpat Nagar Email Campaigns',
    'Khan Market Email Marketing',
    'Nehru Place Email Services',
    'Saket Email Campaigns'
  ];

  const emailTypes = [
    {
      type: 'Newsletter Campaigns',
      description: 'Regular newsletters to keep your Delhi audience engaged and informed',
      icon: Mail,
      features: ['Weekly/Monthly Newsletters', 'Industry Updates', 'Company News', 'Subscriber Engagement']
    },
    {
      type: 'Automated Drip Campaigns',
      description: 'Automated email sequences that nurture leads and drive conversions',
      icon: Zap,
      features: ['Welcome Series', 'Lead Nurturing', 'Onboarding Sequences', 'Re-engagement Campaigns']
    },
    {
      type: 'Promotional Campaigns',
      description: 'Targeted promotional emails that drive sales and revenue',
      icon: Star,
      features: ['Product Launches', 'Special Offers', 'Seasonal Campaigns', 'Flash Sales']
    },
    {
      type: 'Transactional Emails',
      description: 'Essential transactional emails that enhance customer experience',
      icon: Send,
      features: ['Order Confirmations', 'Shipping Updates', 'Password Resets', 'Account Notifications']
    }
  ];

  const emailPackages = [
    {
      name: 'Email Marketing Delhi Starter',
      price: '₹15,000',
      period: '/month',
      description: 'Perfect for small Delhi businesses starting with email marketing',
      features: [
        'Up to 5,000 Subscribers',
        '4 Email Campaigns per Month',
        'Basic Email Templates',
        'List Management',
        'Basic Analytics',
        'Email Support'
      ]
    },
    {
      name: 'Delhi Email Marketing Professional',
      price: '₹28,000',
      period: '/month',
      description: 'Comprehensive email marketing for growing Delhi businesses',
      features: [
        'Up to 25,000 Subscribers',
        'Unlimited Email Campaigns',
        'Custom Email Templates',
        'Advanced Automation',
        'A/B Testing',
        'Advanced Analytics',
        'Phone Support'
      ],
      popular: true
    },
    {
      name: 'Enterprise Email Marketing Delhi',
      price: '₹50,000',
      period: '/month',
      description: 'Advanced email marketing for large Delhi enterprises',
      features: [
        'Unlimited Subscribers',
        'Advanced Automation Workflows',
        'Custom Integrations',
        'Dedicated IP',
        'Advanced Segmentation',
        'Dedicated Account Manager',
        'Priority Support'
      ]
    }
  ];

  const stats = [
    {
      metric: '1M+',
      description: 'Emails Sent Monthly',
      detail: 'For Delhi businesses'
    },
    {
      metric: '28%',
      description: 'Average Open Rate',
      detail: 'Above industry standard'
    },
    {
      metric: '₹15Cr+',
      description: 'Revenue Generated',
      detail: 'Through email campaigns'
    },
    {
      metric: '95%',
      description: 'Deliverability Rate',
      detail: 'Consistent inbox placement'
    }
  ];

  const achievements = [
    'Top Email Marketing Agency in Delhi',
    'Mailchimp Certified Partners',
    'HubSpot Email Marketing Experts',
    'High Deliverability Specialists',
    'Automation Workflow Masters',
    'CRM Integration Champions'
  ];

  return (
    <div className="min-h-screen bg-slate-900 relative">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        {/* Hero Section */}
        <section className="pt-32 pb-20">
          <div className="container mx-auto px-6">
            <div className="max-w-5xl mx-auto text-center">
              <div className="inline-flex items-center space-x-2 bg-cyan-500/20 border border-cyan-500/30 rounded-full px-6 py-2 mb-8">
                <Mail className="w-4 h-4 text-cyan-400" />
                <span className="text-cyan-400 font-medium">Email Marketing Delhi • Direct Communication Excellence</span>
              </div>
              
              <h1 className="text-5xl md:text-7xl font-bold mb-6 leading-tight">
                #1 Email Marketing Company in
                <span className="bg-gradient-to-r from-cyan-400 to-cyan-600 bg-clip-text text-transparent"> Delhi NCR</span>
              </h1>
              
              <p className="text-xl text-slate-300 mb-12 max-w-4xl mx-auto">
                Premier email marketing in Delhi offering comprehensive email campaign management and automation services. Serving 400+ Delhi businesses across all districts - from government sector enterprises in Central Delhi to tech startups in Gurgaon. Expert email marketing solutions with proven ₹15Cr+ revenue generation and 1M+ emails sent monthly for Delhi clients.
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
                <Button asChild size="lg" className="bg-gradient-to-r from-cyan-500 to-cyan-600 hover:from-cyan-600 hover:to-cyan-700 text-white text-lg px-8 py-4">
                  <Link to="/quote">Get Free Delhi Email Audit</Link>
                </Button>
                <Button asChild size="lg" variant="outline" className="border-cyan-500 text-cyan-400 hover:bg-cyan-500 hover:text-white text-lg px-8 py-4">
                  <Link to="/contact">Call Email Experts: +91-8708577598</Link>
                </Button>
              </div>

              {/* Achievements */}
              <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                {achievements.map((achievement, index) => (
                  <div key={index} className="flex items-center justify-center space-x-2 bg-slate-800/50 rounded-lg p-3">
                    <CheckCircle className="w-4 h-4 text-cyan-400 flex-shrink-0" />
                    <span className="text-slate-300">{achievement}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* Stats Section */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-6xl font-bold mb-6">
                Delhi Email Marketing
                <span className="bg-gradient-to-r from-cyan-400 to-cyan-600 bg-clip-text text-transparent"> Success Metrics</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-4xl mx-auto mb-12">
                Measurable email marketing results from Delhi businesses across all industries - delivering exceptional engagement and revenue growth.
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-4 gap-8 mb-16">
              {stats.map((stat, index) => (
                <div key={index} className="text-center">
                  <div className="text-5xl font-bold text-cyan-400 mb-2">{stat.metric}</div>
                  <div className="text-white font-semibold mb-1">{stat.description}</div>
                  <div className="text-slate-400">{stat.detail}</div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Email Types Section */}
        <section className="py-20">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-6xl font-bold mb-6">
                Email Campaign Types We
                <span className="bg-gradient-to-r from-cyan-400 to-cyan-600 bg-clip-text text-transparent"> Master</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-4xl mx-auto">
                Comprehensive email marketing campaigns designed for Delhi businesses across all industries.
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-4 gap-8 mb-20">
              {emailTypes.map((email, index) => (
                <Card key={index} className="bg-slate-900/80 border-cyan-500/20 hover:border-cyan-500/40 transition-all duration-300">
                  <CardContent className="p-8">
                    <div className="w-12 h-12 bg-gradient-to-r from-cyan-500 to-cyan-600 rounded-lg flex items-center justify-center mb-6">
                      <email.icon className="w-6 h-6 text-white" />
                    </div>
                    <h3 className="text-xl font-bold text-white mb-4">{email.type}</h3>
                    <p className="text-slate-300 mb-6">{email.description}</p>
                    <ul className="space-y-2">
                      {email.features.map((feature, idx) => (
                        <li key={idx} className="flex items-center text-slate-400 text-sm">
                          <CheckCircle className="w-3 h-3 text-cyan-400 mr-2 flex-shrink-0" />
                          {feature}
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Services Grid */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-6xl font-bold mb-6">
                Complete Email Marketing Services
                <span className="bg-gradient-to-r from-cyan-400 to-cyan-600 bg-clip-text text-transparent"> Available in Delhi</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-4xl mx-auto">
                Comprehensive range of email marketing services covering every aspect of email campaigns for Delhi businesses.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {emailServices.map((service, index) => (
                <Card key={index} className="bg-slate-900/80 border-cyan-500/20 hover:border-cyan-500/40 transition-all duration-300">
                  <CardContent className="p-6 text-center">
                    <Mail className="w-8 h-8 text-cyan-400 mx-auto mb-4" />
                    <h3 className="text-white font-semibold mb-2 text-sm">{service}</h3>
                    <Button asChild size="sm" variant="outline" className="border-cyan-500 text-cyan-400 hover:bg-cyan-500 hover:text-white text-xs">
                      <Link to="/quote">Get Quote</Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Pricing Section */}
        <section className="py-20">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-6xl font-bold mb-6">
                Delhi Email Marketing
                <span className="bg-gradient-to-r from-cyan-400 to-cyan-600 bg-clip-text text-transparent"> Pricing</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-4xl mx-auto">
                Transparent email marketing pricing designed for Delhi businesses of all sizes.
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
              {emailPackages.map((pkg, index) => (
                <Card key={index} className={`bg-slate-900/80 border-cyan-500/20 hover:border-cyan-500/40 transition-all duration-300 relative ${pkg.popular ? 'ring-2 ring-cyan-500' : ''}`}>
                  {pkg.popular && (
                    <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                      <span className="bg-cyan-500 text-white px-4 py-2 rounded-full text-sm font-semibold">Most Popular</span>
                    </div>
                  )}
                  <CardContent className="p-8">
                    <h3 className="text-2xl font-bold text-white mb-2">{pkg.name}</h3>
                    <p className="text-slate-300 mb-6">{pkg.description}</p>
                    <div className="mb-6">
                      <span className="text-4xl font-bold text-cyan-400">{pkg.price}</span>
                      <span className="text-slate-400">{pkg.period}</span>
                    </div>
                    <ul className="space-y-3 mb-8">
                      {pkg.features.map((feature, idx) => (
                        <li key={idx} className="flex items-center text-slate-300">
                          <CheckCircle className="w-4 h-4 text-cyan-400 mr-3 flex-shrink-0" />
                          {feature}
                        </li>
                      ))}
                    </ul>
                    <Button asChild className={`w-full ${pkg.popular ? 'bg-gradient-to-r from-cyan-500 to-cyan-600 hover:from-cyan-600 hover:to-cyan-700' : 'bg-slate-700 hover:bg-slate-600'} text-white`}>
                      <Link to="/quote">Get Started</Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 bg-gradient-to-r from-cyan-600 to-cyan-500">
          <div className="container mx-auto px-6 text-center">
            <h2 className="text-4xl md:text-6xl font-bold mb-6">Ready to Dominate Delhi Inboxes?</h2>
            <p className="text-xl mb-8 max-w-4xl mx-auto">
              Join 400+ Delhi businesses that trust GOD Digital Marketing for email marketing success. Proven strategies that deliver 28% open rates and ₹15Cr+ revenue generation.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button asChild size="lg" className="bg-white text-cyan-600 hover:bg-slate-100 text-lg px-8 py-4">
                <Link to="/quote">Get Free Email Audit</Link>
              </Button>
              <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-cyan-600 text-lg px-8 py-4">
                <Link to="/contact">Call Email Experts: +91-8708577598</Link>
              </Button>
            </div>
          </div>
        </section>
      </main>
      
      <Footer />
    </div>
  );
};

export default EmailMarketingDelhi;
