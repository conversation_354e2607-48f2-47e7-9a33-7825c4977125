import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { ArrowRight, Search, TrendingUp, Users, CheckCircle, Building, Star, Crown, Target, Zap } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';
import Sidebar from '@/components/Sidebar';

const SeoServicesLucknow = () => {
  const seoServices = [
    'SEO Services Lucknow',
    'Local SEO Lucknow',
    'Technical SEO Lucknow',
    'On-Page SEO Lucknow',
    'Off-Page SEO Lucknow',
    'E-commerce SEO Lucknow',
    'Enterprise SEO Lucknow',
    'SEO Audit Lucknow',
    'Keyword Research Lucknow',
    'Content Optimization Lucknow',
    'Link Building Lucknow',
    'Google Ranking Lucknow',
    'Government SEO Lucknow',
    'Education SEO Lucknow',
    'Healthcare SEO Lucknow',
    'Manufacturing SEO Lucknow',
    'IT Services SEO Lucknow',
    'Real Estate SEO Lucknow',
    'Restaurant SEO Lucknow',
    'Legal Services SEO Lucknow'
  ];

  const seoSpecializations = [
    {
      type: 'Government & Administrative SEO',
      description: 'Specialized SEO for Lucknow\'s government capital and administrative sector businesses',
      icon: Building,
      features: ['Government Contractor SEO', 'Administrative Services SEO', 'Public Sector SEO', 'Civic Services SEO']
    },
    {
      type: 'Education & Research SEO',
      description: 'Expert SEO for Lucknow\'s renowned education and research institutions',
      icon: Star,
      features: ['Educational Institution SEO', 'Research Center SEO', 'Coaching Institute SEO', 'University SEO']
    },
    {
      type: 'Healthcare & Medical SEO',
      description: 'Professional SEO for Lucknow\'s growing healthcare and medical sector',
      icon: Crown,
      features: ['Hospital SEO', 'Medical Practice SEO', 'Healthcare Services SEO', 'Pharmaceutical SEO']
    },
    {
      type: 'Manufacturing & Industrial SEO',
      description: 'Comprehensive SEO for Lucknow\'s manufacturing and industrial sector',
      icon: Target,
      features: ['Manufacturing Company SEO', 'Industrial Services SEO', 'B2B Manufacturing SEO', 'Export Business SEO']
    }
  ];

  const seoPackages = [
    {
      name: 'SEO Lucknow Starter',
      price: '₹22,000',
      period: '/month',
      description: 'Perfect for small Lucknow businesses and government contractors',
      features: [
        'Local Lucknow SEO Optimization',
        'Google My Business Setup',
        '20 Target Keywords',
        'On-Page SEO (10 pages)',
        'Monthly Reporting',
        'Government Sector Focus'
      ]
    },
    {
      name: 'Lucknow SEO Professional',
      price: '₹42,000',
      period: '/month',
      description: 'Comprehensive SEO for growing Lucknow businesses',
      features: [
        'Advanced Local + National SEO',
        '50 Target Keywords',
        'Technical SEO Audit',
        'Content Marketing Strategy',
        'Link Building Campaign',
        'Education/Healthcare Specialization',
        'Bi-weekly Reporting'
      ],
      popular: true
    },
    {
      name: 'Enterprise SEO Lucknow',
      price: '₹78,000',
      period: '/month',
      description: 'Advanced SEO for large Lucknow enterprises and institutions',
      features: [
        'Enterprise-Level SEO Strategy',
        'Unlimited Keywords',
        'Advanced Technical SEO',
        'Multi-location Optimization',
        'Government Tender SEO',
        'Institutional SEO',
        'Dedicated SEO Manager'
      ]
    }
  ];

  const stats = [
    {
      metric: '1100+',
      description: 'Lucknow Businesses Ranked',
      detail: 'Across all sectors'
    },
    {
      metric: '540%',
      description: 'Average Traffic Increase',
      detail: 'For Lucknow clients'
    },
    {
      metric: '₹28Cr+',
      description: 'Revenue Generated',
      detail: 'Through SEO campaigns'
    },
    {
      metric: '91%',
      description: 'First Page Rankings',
      detail: 'Target keyword success'
    }
  ];

  const achievements = [
    'Top SEO Company in Lucknow',
    'Government Sector SEO Leaders',
    'Education Industry SEO Experts',
    'Healthcare SEO Specialists',
    'Manufacturing SEO Champions',
    'City of Nawabs Digital Marketing Pioneers'
  ];

  return (
    <div className="min-h-screen bg-slate-900 relative">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        <div className="container mx-auto px-6 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            {/* Main Content */}
            <div className="lg:col-span-3">
              {/* Hero Section */}
              <section className="pt-24 pb-16">
                <div className="max-w-4xl">
                  <div className="inline-flex items-center space-x-2 bg-amber-500/20 border border-amber-500/30 rounded-full px-6 py-2 mb-8">
                    <Search className="w-4 h-4 text-amber-400" />
                    <span className="text-amber-400 font-medium">SEO Services Lucknow • City of Nawabs Digital Excellence</span>
                  </div>
                  
                  <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
                    #1 SEO Company in
                    <span className="bg-gradient-to-r from-amber-400 to-amber-600 bg-clip-text text-transparent"> Lucknow</span>
                  </h1>
                  
                  <p className="text-xl text-slate-300 mb-8">
                    Premier SEO services in Lucknow offering comprehensive search engine optimization solutions including local SEO, technical SEO, on-page optimization, off-page SEO, and enterprise SEO strategies. Our Lucknow SEO company provides professional SEO services with keyword research, content optimization, link building, SEO audits, and Google ranking optimization. Serving 1100+ Lucknow businesses across all sectors - from government contractors in Hazratganj to educational institutions in Gomti Nagar. Expert SEO solutions with proven ₹28Cr+ revenue generation and 540% average organic traffic increase for Lucknow clients in Uttar Pradesh's capital through strategic search engine optimization and digital marketing excellence.
                  </p>
                  
                  <div className="flex flex-col sm:flex-row gap-4 mb-8">
                    <Button asChild size="lg" className="bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700 text-white text-lg px-8 py-4">
                      <Link to="/quote">Get Free Lucknow SEO Audit</Link>
                    </Button>
                    <Button asChild size="lg" variant="outline" className="border-amber-500 text-amber-400 hover:bg-amber-500 hover:text-white text-lg px-8 py-4">
                      <Link to="/contact">Call Lucknow SEO Experts: +91-8708577598</Link>
                    </Button>
                  </div>

                  {/* Achievements */}
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                    {achievements.map((achievement, index) => (
                      <div key={index} className="flex items-center justify-center space-x-2 bg-slate-800/50 rounded-lg p-3">
                        <CheckCircle className="w-4 h-4 text-amber-400 flex-shrink-0" />
                        <span className="text-slate-300">{achievement}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </section>

              {/* Stats Section */}
              <section className="mb-16 bg-slate-800/50 rounded-2xl p-8">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Lucknow SEO
                    <span className="bg-gradient-to-r from-amber-400 to-amber-600 bg-clip-text text-transparent"> Success Metrics</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Measurable SEO results from Lucknow businesses across government, education, healthcare, and manufacturing sectors.
                  </p>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
                  {stats.map((stat, index) => (
                    <div key={index} className="text-center">
                      <div className="text-4xl font-bold text-amber-400 mb-2">{stat.metric}</div>
                      <div className="text-white font-semibold mb-1">{stat.description}</div>
                      <div className="text-slate-400">{stat.detail}</div>
                    </div>
                  ))}
                </div>
              </section>

              {/* SEO Specializations Section */}
              <section className="mb-16">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    SEO Strategies We
                    <span className="bg-gradient-to-r from-amber-400 to-amber-600 bg-clip-text text-transparent"> Master</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Specialized SEO strategies designed for Lucknow's unique government, education, healthcare, and manufacturing business landscape.
                  </p>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  {seoSpecializations.map((seo, index) => (
                    <Card key={index} className="bg-slate-900/80 border-amber-500/20 hover:border-amber-500/40 transition-all duration-300">
                      <CardContent className="p-8">
                        <div className="w-12 h-12 bg-gradient-to-r from-amber-500 to-amber-600 rounded-lg flex items-center justify-center mb-6">
                          <seo.icon className="w-6 h-6 text-white" />
                        </div>
                        <h3 className="text-xl font-bold text-white mb-4">{seo.type}</h3>
                        <p className="text-slate-300 mb-6">{seo.description}</p>
                        <ul className="space-y-2">
                          {seo.features.map((feature, idx) => (
                            <li key={idx} className="flex items-center text-slate-400 text-sm">
                              <CheckCircle className="w-3 h-3 text-amber-400 mr-2 flex-shrink-0" />
                              {feature}
                            </li>
                          ))}
                        </ul>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>

              {/* Pricing Section */}
              <section className="mb-16">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Lucknow SEO
                    <span className="bg-gradient-to-r from-amber-400 to-amber-600 bg-clip-text text-transparent"> Pricing</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Competitive SEO pricing designed for Lucknow's government, education, and business environment.
                  </p>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                  {seoPackages.map((pkg, index) => (
                    <Card key={index} className={`bg-slate-900/80 border-amber-500/20 hover:border-amber-500/40 transition-all duration-300 relative ${pkg.popular ? 'ring-2 ring-amber-500' : ''}`}>
                      {pkg.popular && (
                        <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                          <span className="bg-amber-500 text-white px-4 py-2 rounded-full text-sm font-semibold">Most Popular</span>
                        </div>
                      )}
                      <CardContent className="p-8">
                        <h3 className="text-2xl font-bold text-white mb-2">{pkg.name}</h3>
                        <p className="text-slate-300 mb-6">{pkg.description}</p>
                        <div className="mb-6">
                          <span className="text-4xl font-bold text-amber-400">{pkg.price}</span>
                          <span className="text-slate-400">{pkg.period}</span>
                        </div>
                        <ul className="space-y-3 mb-8">
                          {pkg.features.map((feature, idx) => (
                            <li key={idx} className="flex items-center text-slate-300">
                              <CheckCircle className="w-4 h-4 text-amber-400 mr-3 flex-shrink-0" />
                              {feature}
                            </li>
                          ))}
                        </ul>
                        <Button asChild className={`w-full ${pkg.popular ? 'bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700' : 'bg-slate-700 hover:bg-slate-600'} text-white`}>
                          <Link to="/quote">Get Started</Link>
                        </Button>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>

              {/* CTA Section */}
              <section className="bg-gradient-to-r from-amber-600 to-amber-500 rounded-2xl p-8 text-center">
                <h2 className="text-3xl md:text-5xl font-bold mb-6">Ready to Dominate Lucknow Search Results?</h2>
                <p className="text-xl mb-8">
                  Join 1100+ Lucknow businesses that trust GOD Digital Marketing for SEO success. Proven strategies that deliver 540% traffic increase and ₹28Cr+ revenue generation.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button asChild size="lg" className="bg-white text-amber-600 hover:bg-slate-100 text-lg px-8 py-4">
                    <Link to="/quote">Get Free SEO Audit</Link>
                  </Button>
                  <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-amber-600 text-lg px-8 py-4">
                    <Link to="/contact">Call SEO Experts: +91-8708577598</Link>
                  </Button>
                </div>
              </section>
            </div>

            {/* Sidebar */}
            <div className="lg:col-span-1">
              <Sidebar currentService="seo" currentLocation="lucknow" />
            </div>
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
};

export default SeoServicesLucknow;
