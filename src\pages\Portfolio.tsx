import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { ArrowRight, TrendingUp, Users, Award, Target, CheckCircle, ExternalLink, Play, Star, Crown, Zap, Building, Globe, ShoppingCart, Heart, Briefcase, Car, Dumbbell, Flower, Gift, Sparkles, MapPin, Calendar, Phone, Mail } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';

const Portfolio = () => {
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedCase, setSelectedCase] = useState(null);

  const portfolioStats = [
    {
      metric: '18,500+',
      description: 'Businesses Transformed',
      detail: 'Across 6 countries'
    },
    {
      metric: '₹450Cr+',
      description: 'Revenue Generated',
      detail: 'For our clients'
    },
    {
      metric: '2,800%',
      description: 'Average Growth',
      detail: 'Traffic & rankings'
    },
    {
      metric: '97.8%',
      description: 'Client Retention',
      detail: 'Long-term partnerships'
    }
  ];

  const caseStudies = [
    {
      id: 'ezoutdoors',
      category: 'e-commerce',
      client: 'EzOutdoors',
      industry: 'Outdoor Equipment & Camping Gear',
      location: 'UAE, Saudi Arabia, Kuwait',
      logo: '/api/placeholder/120/60',
      heroImage: '/api/placeholder/600/400',
      challenge: 'New e-commerce store with zero organic visibility in competitive outdoor equipment market across Middle East',
      solution: 'Comprehensive SEO strategy with multilingual optimization, technical SEO, and strategic link building',
      results: {
        trafficIncrease: '2,400%',
        keywordRankings: '850+ keywords in top 10',
        revenue: '₹2.8Cr+ generated',
        timeframe: '6 months',
        conversionRate: '340% increase'
      },
      testimonial: {
        text: "GOD Digital Marketing transformed our online presence completely. From zero visibility to dominating search results across UAE, Saudi Arabia, and Kuwait.",
        author: "Ahmed Al-Rashid",
        position: "Founder, EzOutdoors"
      },
      screenshots: [
        { type: 'traffic', title: 'Organic Traffic Growth', description: 'From 0 to 45,000+ monthly visitors' },
        { type: 'rankings', title: 'Keyword Rankings', description: '850+ keywords ranking in top 10 positions' },
        { type: 'revenue', title: 'Revenue Impact', description: '₹2.8Cr+ revenue generated through SEO' }
      ],
      tags: ['E-commerce SEO', 'Multilingual SEO', 'Technical SEO', 'Link Building', 'Conversion Optimization']
    },
    {
      id: 'smartwhip',
      category: 'b2b',
      client: 'Smart Whip UAE',
      industry: 'Culinary Equipment & Gas Trading',
      location: 'Dubai, UAE',
      logo: '/api/placeholder/120/60',
      heroImage: '/api/placeholder/600/400',
      challenge: 'B2B company struggling to reach professional chefs and culinary businesses in competitive Dubai market',
      solution: 'Industry-specific SEO strategy targeting professional culinary keywords with local optimization',
      results: {
        trafficIncrease: '1,800%',
        keywordRankings: '420+ keywords in top 5',
        revenue: '₹1.2Cr+ generated',
        timeframe: '4 months',
        conversionRate: '280% increase'
      },
      testimonial: {
        text: "Our B2B leads increased dramatically. We're now the go-to supplier for professional kitchens across Dubai.",
        author: "Raaj Patel",
        position: "Business Development, Smart Whip UAE"
      },
      screenshots: [
        { type: 'leads', title: 'B2B Lead Generation', description: '280% increase in qualified leads' },
        { type: 'local', title: 'Local Dominance', description: 'Top 3 rankings for all target keywords in Dubai' }
      ],
      tags: ['B2B SEO', 'Local SEO', 'Industry Targeting', 'Lead Generation']
    },
    {
      id: 'vrdanceworld',
      category: 'local-business',
      client: 'VR Dance World',
      industry: 'Dance Academy & Entertainment',
      location: 'Dubai, UAE',
      logo: '/api/placeholder/120/60',
      heroImage: '/api/placeholder/600/400',
      challenge: 'Local dance academy competing with established studios, needed to attract both adults and kids',
      solution: 'Local SEO optimization with Google My Business enhancement and community-focused content strategy',
      results: {
        trafficIncrease: '1,200%',
        keywordRankings: '180+ local keywords in top 3',
        revenue: '₹85L+ generated',
        timeframe: '3 months',
        conversionRate: '450% increase'
      },
      testimonial: {
        text: "We went from struggling to find students to having a waiting list. Our classes are now fully booked!",
        author: "Shan & Raaj",
        position: "Founders, VR Dance World"
      },
      screenshots: [
        { type: 'local', title: 'Local Search Dominance', description: 'Top 3 for all dance-related searches in Dubai' },
        { type: 'gmb', title: 'Google My Business', description: '450% increase in profile views and calls' }
      ],
      tags: ['Local SEO', 'Google My Business', 'Community Marketing', 'Entertainment Industry']
    },
    {
      id: 'fhwellness',
      category: 'healthcare',
      client: 'FH Wellness',
      industry: 'Corporate Wellness & Healthcare',
      location: 'Dubai, UAE',
      logo: '/api/placeholder/120/60',
      heroImage: '/api/placeholder/600/400',
      challenge: 'Healthcare company needed to establish authority and reach corporate clients for wellness programs',
      solution: 'Authority-building SEO with medical content optimization and corporate-focused keyword strategy',
      results: {
        trafficIncrease: '2,100%',
        keywordRankings: '320+ healthcare keywords in top 5',
        revenue: '₹1.8Cr+ generated',
        timeframe: '5 months',
        conversionRate: '380% increase'
      },
      testimonial: {
        text: "Our corporate wellness programs are now in high demand. We've become the trusted wellness partner for major companies.",
        author: "Dr. Sarah Ahmed",
        position: "Director, FH Wellness"
      },
      screenshots: [
        { type: 'authority', title: 'Medical Authority', description: 'Established as leading wellness provider in Dubai' },
        { type: 'corporate', title: 'Corporate Clients', description: '380% increase in corporate inquiries' }
      ],
      tags: ['Healthcare SEO', 'Authority Building', 'Corporate Marketing', 'Medical Content']
    },
    {
      id: 'goldenrose',
      category: 'healthcare',
      client: 'Golden Rose Med Spa',
      industry: 'Medical Spa & Aesthetic Treatments',
      location: 'Dubai, UAE',
      logo: '/api/placeholder/120/60',
      heroImage: '/api/placeholder/600/400',
      challenge: 'Luxury med spa needed to attract high-end clients in competitive aesthetic treatment market',
      solution: 'Luxury-focused SEO with high-intent keyword targeting and premium content strategy',
      results: {
        trafficIncrease: '1,600%',
        keywordRankings: '280+ aesthetic keywords in top 3',
        revenue: '₹2.2Cr+ generated',
        timeframe: '4 months',
        conversionRate: '520% increase'
      },
      testimonial: {
        text: "We're now the premier destination for aesthetic treatments in Dubai. Our booking calendar is consistently full.",
        author: "Dr. Maria Rodriguez",
        position: "Medical Director, Golden Rose Med Spa"
      },
      screenshots: [
        { type: 'luxury', title: 'Luxury Market Penetration', description: 'Captured high-end aesthetic treatment market' },
        { type: 'bookings', title: 'Booking Increase', description: '520% increase in treatment bookings' }
      ],
      tags: ['Medical SEO', 'Luxury Marketing', 'Aesthetic Treatments', 'High-End Clients']
    },
    {
      id: 'balkland',
      category: 'travel',
      client: 'Balkland Tours',
      industry: 'Travel & Tourism',
      location: 'Balkan Region',
      logo: '/api/placeholder/120/60',
      heroImage: '/api/placeholder/600/400',
      challenge: 'Travel company needed to promote Balkan tours to international audience with seasonal booking patterns',
      solution: 'International SEO with seasonal content strategy and multilingual optimization for global reach',
      results: {
        trafficIncrease: '3,200%',
        keywordRankings: '650+ travel keywords in top 5',
        revenue: '₹3.5Cr+ generated',
        timeframe: '6 months',
        conversionRate: '420% increase'
      },
      testimonial: {
        text: "Our Balkan tours are now booked months in advance. We've become the leading tour operator for the region.",
        author: "Marko Petrovic",
        position: "CEO, Balkland Tours"
      },
      screenshots: [
        { type: 'international', title: 'Global Reach', description: 'Attracting tourists from 25+ countries' },
        { type: 'seasonal', title: 'Seasonal Optimization', description: '420% increase in advance bookings' }
      ],
      tags: ['Travel SEO', 'International SEO', 'Seasonal Marketing', 'Multilingual Optimization']
    }
  ];

  const categories = [
    { id: 'all', name: 'All Projects', icon: Globe },
    { id: 'e-commerce', name: 'E-commerce', icon: ShoppingCart },
    { id: 'healthcare', name: 'Healthcare', icon: Heart },
    { id: 'b2b', name: 'B2B Services', icon: Briefcase },
    { id: 'local-business', name: 'Local Business', icon: MapPin },
    { id: 'travel', name: 'Travel & Tourism', icon: Car }
  ];

  const filteredCases = selectedCategory === 'all' 
    ? caseStudies 
    : caseStudies.filter(study => study.category === selectedCategory);

  return (
    <div className="min-h-screen bg-slate-900 relative">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        <div className="container mx-auto px-6 py-8">
          {/* Hero Section */}
          <section className="pt-24 pb-16 text-center">
            <div className="inline-flex items-center space-x-2 bg-amber-500/20 border border-amber-500/30 rounded-full px-6 py-2 mb-8">
              <Award className="w-4 h-4 text-amber-400" />
              <span className="text-amber-400 font-medium">Proven Results • Real Success Stories</span>
            </div>
            
            <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
              Portfolio of
              <span className="bg-gradient-to-r from-amber-400 to-amber-600 bg-clip-text text-transparent"> Digital Success</span>
            </h1>
            
            <p className="text-xl text-slate-300 mb-12 max-w-3xl mx-auto">
              Discover how we've transformed businesses across industries with our proven digital marketing strategies. 
              From startups to enterprises, see the measurable results that speak for themselves.
            </p>

            {/* Portfolio Stats */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-8 mb-16">
              {portfolioStats.map((stat, index) => (
                <div key={index} className="bg-slate-800/50 rounded-2xl p-6 text-center">
                  <div className="text-3xl font-bold text-amber-400 mb-2">{stat.metric}</div>
                  <div className="text-white font-semibold mb-1">{stat.description}</div>
                  <div className="text-slate-400 text-sm">{stat.detail}</div>
                </div>
              ))}
            </div>
          </section>

          {/* Category Filter */}
          <section className="mb-12">
            <div className="flex flex-wrap justify-center gap-4">
              {categories.map((category) => (
                <button
                  key={category.id}
                  onClick={() => setSelectedCategory(category.id)}
                  className={`flex items-center space-x-2 px-6 py-3 rounded-full transition-all duration-300 ${
                    selectedCategory === category.id
                      ? 'bg-amber-500 text-white'
                      : 'bg-slate-800/50 text-slate-300 hover:bg-slate-700/50'
                  }`}
                >
                  <category.icon className="w-4 h-4" />
                  <span>{category.name}</span>
                </button>
              ))}
            </div>
          </section>

          {/* Case Studies Grid */}
          <section className="mb-16">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {filteredCases.map((study) => (
                <Card key={study.id} className="bg-slate-900/80 border-amber-500/20 hover:border-amber-500/40 transition-all duration-300 overflow-hidden group">
                  <div className="relative">
                    <img 
                      src={study.heroImage} 
                      alt={study.client}
                      className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-slate-900/80 to-transparent" />
                    <div className="absolute bottom-4 left-4 right-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <h3 className="text-xl font-bold text-white mb-1">{study.client}</h3>
                          <p className="text-slate-300 text-sm">{study.industry}</p>
                        </div>
                        <div className="text-right">
                          <div className="text-2xl font-bold text-amber-400">{study.results.trafficIncrease}</div>
                          <div className="text-slate-300 text-xs">Traffic Growth</div>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <CardContent className="p-6">
                    <div className="flex items-center space-x-2 mb-4">
                      <MapPin className="w-4 h-4 text-amber-400" />
                      <span className="text-slate-300 text-sm">{study.location}</span>
                    </div>
                    
                    <p className="text-slate-300 mb-4 text-sm leading-relaxed">{study.challenge}</p>
                    
                    <div className="grid grid-cols-2 gap-4 mb-4">
                      <div className="text-center bg-slate-800/50 rounded-lg p-3">
                        <div className="text-lg font-bold text-amber-400">{study.results.keywordRankings}</div>
                        <div className="text-slate-400 text-xs">Top Rankings</div>
                      </div>
                      <div className="text-center bg-slate-800/50 rounded-lg p-3">
                        <div className="text-lg font-bold text-green-400">{study.results.revenue}</div>
                        <div className="text-slate-400 text-xs">Revenue Generated</div>
                      </div>
                    </div>
                    
                    <div className="flex flex-wrap gap-2 mb-4">
                      {study.tags.slice(0, 3).map((tag, index) => (
                        <span key={index} className="bg-amber-500/20 text-amber-400 px-2 py-1 rounded text-xs">
                          {tag}
                        </span>
                      ))}
                    </div>
                    
                    <Button 
                      onClick={() => setSelectedCase(study)}
                      className="w-full bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700 text-white"
                    >
                      View Full Case Study
                      <ArrowRight className="w-4 h-4 ml-2" />
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </section>

          {/* CTA Section */}
          <section className="bg-gradient-to-r from-amber-600 to-amber-500 rounded-2xl p-8 text-center">
            <h2 className="text-3xl md:text-5xl font-bold mb-6">Ready to Be Our Next Success Story?</h2>
            <p className="text-xl mb-8">
              Join 18,500+ businesses that have transformed their digital presence with GOD Digital Marketing.
              Your success story starts with a conversation.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button asChild size="lg" className="bg-white text-amber-600 hover:bg-slate-100 text-lg px-8 py-4">
                <Link to="/quote">Get Your Free Strategy Session</Link>
              </Button>
              <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-amber-600 text-lg px-8 py-4">
                <Link to="/contact">Call Success Experts: +91-8708577598</Link>
              </Button>
            </div>
          </section>
        </div>

        {/* Case Study Modal */}
        {selectedCase && (
          <div className="fixed inset-0 bg-black/80 z-50 flex items-center justify-center p-4">
            <div className="bg-slate-900 rounded-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
              <div className="relative">
                <img
                  src={selectedCase.heroImage}
                  alt={selectedCase.client}
                  className="w-full h-64 object-cover"
                />
                <button
                  onClick={() => setSelectedCase(null)}
                  className="absolute top-4 right-4 bg-black/50 text-white rounded-full p-2 hover:bg-black/70"
                >
                  ✕
                </button>
                <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-slate-900 to-transparent p-6">
                  <h2 className="text-3xl font-bold text-white mb-2">{selectedCase.client}</h2>
                  <p className="text-amber-400 text-lg">{selectedCase.industry}</p>
                </div>
              </div>

              <div className="p-8">
                {/* Results Overview */}
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
                  <div className="bg-slate-800/50 rounded-lg p-4 text-center">
                    <div className="text-2xl font-bold text-amber-400">{selectedCase.results.trafficIncrease}</div>
                    <div className="text-slate-300 text-sm">Traffic Growth</div>
                  </div>
                  <div className="bg-slate-800/50 rounded-lg p-4 text-center">
                    <div className="text-2xl font-bold text-green-400">{selectedCase.results.revenue}</div>
                    <div className="text-slate-300 text-sm">Revenue Generated</div>
                  </div>
                  <div className="bg-slate-800/50 rounded-lg p-4 text-center">
                    <div className="text-2xl font-bold text-blue-400">{selectedCase.results.keywordRankings}</div>
                    <div className="text-slate-300 text-sm">Top Rankings</div>
                  </div>
                  <div className="bg-slate-800/50 rounded-lg p-4 text-center">
                    <div className="text-2xl font-bold text-purple-400">{selectedCase.results.timeframe}</div>
                    <div className="text-slate-300 text-sm">Timeline</div>
                  </div>
                </div>

                {/* Challenge & Solution */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
                  <div>
                    <h3 className="text-xl font-bold text-white mb-4 flex items-center">
                      <Target className="w-5 h-5 text-red-400 mr-2" />
                      The Challenge
                    </h3>
                    <p className="text-slate-300 leading-relaxed">{selectedCase.challenge}</p>
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-white mb-4 flex items-center">
                      <Zap className="w-5 h-5 text-amber-400 mr-2" />
                      Our Solution
                    </h3>
                    <p className="text-slate-300 leading-relaxed">{selectedCase.solution}</p>
                  </div>
                </div>

                {/* Testimonial */}
                <div className="bg-slate-800/50 rounded-lg p-6 mb-8">
                  <div className="flex items-start space-x-4">
                    <div className="text-4xl text-amber-400">"</div>
                    <div>
                      <p className="text-slate-300 text-lg italic mb-4">{selectedCase.testimonial.text}</p>
                      <div className="flex items-center space-x-2">
                        <div className="w-10 h-10 bg-amber-500 rounded-full flex items-center justify-center">
                          <span className="text-white font-bold">{selectedCase.testimonial.author.charAt(0)}</span>
                        </div>
                        <div>
                          <div className="text-white font-semibold">{selectedCase.testimonial.author}</div>
                          <div className="text-slate-400 text-sm">{selectedCase.testimonial.position}</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Tags */}
                <div className="flex flex-wrap gap-2 mb-8">
                  {selectedCase.tags.map((tag, index) => (
                    <span key={index} className="bg-amber-500/20 text-amber-400 px-3 py-1 rounded-full text-sm">
                      {tag}
                    </span>
                  ))}
                </div>

                {/* CTA */}
                <div className="text-center">
                  <Button asChild size="lg" className="bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700 text-white text-lg px-8 py-4">
                    <Link to="/quote">Get Similar Results for Your Business</Link>
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}
      </main>
      
      <Footer />
    </div>
  );
};

export default Portfolio;
