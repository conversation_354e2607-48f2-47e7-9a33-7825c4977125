import React from 'react';
import { Link } from 'react-router-dom';
import { ArrowRight, Share2, TrendingUp, Users, CheckCircle, Building, Star, Crown, Target, Zap } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';
import Sidebar from '@/components/Sidebar';

const SocialMediaGandhinagar = () => {
  const socialMediaServices = [
    'Social Media Marketing Gandhinagar',
    'Facebook Marketing Gandhinagar',
    'Instagram Marketing Gandhinagar',
    'LinkedIn Marketing Gandhinagar',
    'Twitter Marketing Gandhinagar',
    'YouTube Marketing Gandhinagar',
    'Social Media Management Gandhinagar',
    'Content Creation Gandhinagar',
    'Influencer Marketing Gandhinagar',
    'Social Media Advertising Gandhinagar',
    'Community Management Gandhinagar',
    'Social Media Strategy Gandhinagar',
    'Brand Building Gandhinagar',
    'Social Commerce Gandhinagar',
    'Video Marketing Gandhinagar',
    'Government Social Media Gandhinagar',
    'Corporate Communication Gandhinagar',
    'Political Campaign Marketing Gandhinagar',
    'Public Sector Social Media Gandhinagar',
    'Administrative Social Media Gandhinagar'
  ];

  const industryFocus = [
    {
      industry: 'Government & Administrative Social Media',
      description: 'Specialized social media strategies for Gandhinagar\'s government offices and administrative bodies',
      icon: Building,
      features: ['Government Communication', 'Public Awareness Campaigns', 'Citizen Engagement', 'Policy Communication']
    },
    {
      industry: 'Corporate & Business Social Media',
      description: 'Strategic social media marketing for Gandhinagar\'s corporate sector and business organizations',
      icon: Star,
      features: ['Corporate Branding', 'B2B Social Media', 'Executive Branding', 'Business Networking']
    },
    {
      industry: 'Political & Public Affairs Social Media',
      description: 'Comprehensive social media for political campaigns and public affairs in Gujarat\'s capital',
      icon: Crown,
      features: ['Political Campaigns', 'Public Relations', 'Crisis Communication', 'Stakeholder Engagement']
    },
    {
      industry: 'Educational & Cultural Social Media',
      description: 'Targeted social media strategies for Gandhinagar\'s educational institutions and cultural organizations',
      icon: Target,
      features: ['Educational Outreach', 'Cultural Promotion', 'Student Engagement', 'Heritage Marketing']
    }
  ];

  const localSocialFeatures = [
    'Gandhinagar Local Community Building',
    'Gujarat State Social Media Coverage',
    'Government Sector Specialization',
    'Administrative Communication',
    'Political Campaign Management',
    'Gandhinagar Event Promotion',
    'Regional Language Content',
    'Local Business Networking',
    'Cultural Event Marketing',
    'Public Sector Engagement'
  ];

  const achievements = [
    '180+ Gandhinagar Organizations Served',
    '2,200% Average Engagement Growth',
    '₹28Cr+ Revenue Generated for Clients',
    '94% Campaign Success Rate',
    'Top Social Media Agency in Gujarat',
    'Government Sector Social Media Specialists'
  ];

  const platformExpertise = [
    {
      platform: 'Facebook Marketing',
      description: 'Comprehensive Facebook marketing for Gandhinagar businesses and organizations',
      metrics: ['15.8% Avg Engagement Rate', '2.2M+ Reach Generated', '85% Lead Conversion', '12% CTR Average']
    },
    {
      platform: 'Instagram Marketing',
      description: 'Visual storytelling and Instagram marketing for Gandhinagar brands',
      metrics: ['18.5% Avg Engagement Rate', '1.8M+ Reach Generated', '22% Story Completion', '14% CTR Average']
    },
    {
      platform: 'LinkedIn Marketing',
      description: 'Professional networking and B2B marketing for Gandhinagar corporations',
      metrics: ['12.2% Avg Engagement Rate', '850K+ Professional Reach', '28% Lead Quality', '8% CTR Average']
    },
    {
      platform: 'YouTube Marketing',
      description: 'Video content marketing and YouTube optimization for Gandhinagar organizations',
      metrics: ['25% Avg View Rate', '2.5M+ Video Views', '18% Subscriber Growth', '6% CTR Average']
    }
  ];

  return (
    <div className="min-h-screen bg-slate-900 relative">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        <div className="container mx-auto px-6 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            <div className="lg:col-span-3">
              <section className="pt-24 pb-16">
                <div className="max-w-4xl">
                  <div className="inline-flex items-center space-x-2 bg-purple-500/20 border border-purple-500/30 rounded-full px-6 py-2 mb-8">
                    <Share2 className="w-4 h-4 text-purple-400" />
                    <span className="text-purple-400 font-medium">Social Media Services Gandhinagar • Gujarat's Digital Communication Leaders</span>
                  </div>
                  
                  <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
                    #1 Social Media Company in
                    <span className="bg-gradient-to-r from-purple-400 to-purple-600 bg-clip-text text-transparent"> Gandhinagar</span>
                  </h1>
                  
                  <p className="text-xl text-slate-300 mb-8">
                    Premier social media marketing services in Gandhinagar offering comprehensive social media management and digital communication solutions for government organizations, corporate entities, political campaigns, and educational institutions. Our Gandhinagar social media agency provides professional social media services including Facebook marketing, Instagram marketing, LinkedIn marketing, government social media Gandhinagar, and corporate communication. Serving 180+ Gandhinagar organizations with proven ₹28Cr+ revenue generation and 2,200% average engagement growth through strategic social media marketing and digital communication excellence in Gujarat's capital city.
                  </p>
                  
                  <div className="flex flex-col sm:flex-row gap-4 mb-8">
                    <Button asChild size="lg" className="bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white text-lg px-8 py-4">
                      <Link to="/quote">Get Free Social Media Audit Gandhinagar</Link>
                    </Button>
                    <Button asChild size="lg" variant="outline" className="border-purple-500 text-purple-400 hover:bg-purple-500 hover:text-white text-lg px-8 py-4">
                      <Link to="/contact">Call Gandhinagar Social Media Experts: +91-8708577598</Link>
                    </Button>
                  </div>

                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                    {achievements.map((achievement, index) => (
                      <div key={index} className="flex items-center justify-center space-x-2 bg-slate-800/50 rounded-lg p-3">
                        <CheckCircle className="w-4 h-4 text-purple-400 flex-shrink-0" />
                        <span className="text-slate-300">{achievement}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </section>

              <section className="mb-16">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Gandhinagar Industry-Specific
                    <span className="bg-gradient-to-r from-purple-400 to-purple-600 bg-clip-text text-transparent"> Social Media Solutions</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Specialized social media strategies tailored for Gandhinagar's key sectors and organizations.
                  </p>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  {industryFocus.map((industry, index) => (
                    <Card key={index} className="bg-slate-900/80 border-purple-500/20 hover:border-purple-500/40 transition-all duration-300">
                      <CardContent className="p-8">
                        <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg flex items-center justify-center mb-6">
                          <industry.icon className="w-6 h-6 text-white" />
                        </div>
                        <h3 className="text-xl font-bold text-white mb-4">{industry.industry}</h3>
                        <p className="text-slate-300 mb-6">{industry.description}</p>
                        <ul className="space-y-2">
                          {industry.features.map((feature, idx) => (
                            <li key={idx} className="flex items-center text-slate-400 text-sm">
                              <CheckCircle className="w-3 h-3 text-purple-400 mr-2 flex-shrink-0" />
                              {feature}
                            </li>
                          ))}
                        </ul>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>

              <section className="mb-16 bg-slate-800/30 rounded-2xl p-8">
                <h3 className="text-2xl font-bold text-white mb-6 text-center">
                  Comprehensive Social Media Services in Gandhinagar
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  {socialMediaServices.map((service, index) => (
                    <div key={index} className="bg-slate-900/50 rounded-lg p-4 text-center">
                      <div className="text-purple-400 font-medium text-sm">{service}</div>
                    </div>
                  ))}
                </div>
              </section>

              <section className="mb-16 bg-slate-800/30 rounded-2xl p-8">
                <h3 className="text-2xl font-bold text-white mb-6 text-center">
                  Platform Performance Metrics
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                  {platformExpertise.map((platform, index) => (
                    <div key={index} className="bg-slate-900/50 rounded-lg p-6">
                      <h4 className="text-purple-400 font-semibold mb-3">{platform.platform}</h4>
                      <p className="text-slate-300 text-sm mb-4">{platform.description}</p>
                      <div className="space-y-2">
                        {platform.metrics.map((metric, idx) => (
                          <div key={idx} className="text-slate-400 text-xs">{metric}</div>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </section>

              <section className="mb-16 bg-slate-800/30 rounded-2xl p-8">
                <h3 className="text-2xl font-bold text-white mb-6 text-center">
                  Local Social Media Gandhinagar Specialization
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                  {localSocialFeatures.map((feature, index) => (
                    <div key={index} className="bg-slate-900/50 rounded-lg p-4 text-center">
                      <div className="text-purple-400 font-medium text-sm">{feature}</div>
                    </div>
                  ))}
                </div>
              </section>

              <section className="mb-16 bg-slate-800/30 rounded-2xl p-8">
                <h3 className="text-2xl font-bold text-white mb-6 text-center">
                  Why Choose Our Gandhinagar Social Media Services?
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  <div className="text-center">
                    <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
                      <Building className="w-8 h-8 text-white" />
                    </div>
                    <h4 className="text-white font-semibold mb-2">Government Sector Expertise</h4>
                    <p className="text-slate-400 text-sm">Deep understanding of Gandhinagar's government landscape and administrative communication requirements.</p>
                  </div>
                  <div className="text-center">
                    <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
                      <TrendingUp className="w-8 h-8 text-white" />
                    </div>
                    <h4 className="text-white font-semibold mb-2">Proven Engagement</h4>
                    <p className="text-slate-400 text-sm">180+ successful social media campaigns in Gandhinagar with 2,200% average engagement growth.</p>
                  </div>
                  <div className="text-center">
                    <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
                      <Users className="w-8 h-8 text-white" />
                    </div>
                    <h4 className="text-white font-semibold mb-2">Multi-Sector Experience</h4>
                    <p className="text-slate-400 text-sm">Specialized expertise in government, corporate, political, and educational sectors in Gandhinagar.</p>
                  </div>
                </div>
              </section>

              <section className="mb-16 bg-slate-800/30 rounded-2xl p-8">
                <h3 className="text-2xl font-bold text-white mb-6 text-center">
                  Complete Digital Marketing Solutions Gandhinagar
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  <Link to="/services/seo/gandhinagar" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-purple-400 font-semibold mb-2 group-hover:text-purple-300">SEO Services Gandhinagar</h4>
                    <p className="text-slate-400 text-sm">Search engine optimization for Gandhinagar organizations</p>
                  </Link>
                  <Link to="/services/ppc/gandhinagar" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-purple-400 font-semibold mb-2 group-hover:text-purple-300">Google Ads Gandhinagar</h4>
                    <p className="text-slate-400 text-sm">PPC advertising and Google Ads management for Gandhinagar</p>
                  </Link>
                  <Link to="/services/content/gandhinagar" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-purple-400 font-semibold mb-2 group-hover:text-purple-300">Content Marketing Gandhinagar</h4>
                    <p className="text-slate-400 text-sm">Content creation and marketing strategies for Gandhinagar</p>
                  </Link>
                  <Link to="/services/web-development/gandhinagar" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-purple-400 font-semibold mb-2 group-hover:text-purple-300">Web Development Gandhinagar</h4>
                    <p className="text-slate-400 text-sm">Professional website development and design services</p>
                  </Link>
                  <Link to="/services/email/gandhinagar" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-purple-400 font-semibold mb-2 group-hover:text-purple-300">Email Marketing Gandhinagar</h4>
                    <p className="text-slate-400 text-sm">Email marketing campaigns for Gandhinagar organizations</p>
                  </Link>
                  <Link to="/portfolio" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-purple-400 font-semibold mb-2 group-hover:text-purple-300">Gandhinagar Success Stories</h4>
                    <p className="text-slate-400 text-sm">See our proven results from Gandhinagar clients</p>
                  </Link>
                </div>
              </section>

              <section className="bg-gradient-to-r from-purple-600 to-purple-500 rounded-2xl p-8 text-center">
                <h2 className="text-3xl md:text-5xl font-bold mb-6">Ready to Amplify Your Gandhinagar Presence?</h2>
                <p className="text-xl mb-8">
                  Join 180+ successful Gandhinagar organizations that trust GOD Digital Marketing for social media excellence. Proven strategies delivering 2,200% engagement growth and ₹28Cr+ revenue generation.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button asChild size="lg" className="bg-white text-purple-600 hover:bg-slate-100 text-lg px-8 py-4">
                    <Link to="/quote">Get Free Social Media Audit</Link>
                  </Button>
                  <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-purple-600 text-lg px-8 py-4">
                    <Link to="/contact">Call Gandhinagar Social Media Experts: +91-8708577598</Link>
                  </Button>
                </div>
              </section>
            </div>

            <div className="lg:col-span-1">
              <Sidebar currentService="social-media" />
            </div>
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
};

export default SocialMediaGandhinagar;
