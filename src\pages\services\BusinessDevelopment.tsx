import React from 'react';
import { Link } from 'react-router-dom';
import { TrendingUp, Zap, Users, CheckCircle, Building, Star, Crown, Target, ArrowRight, Briefcase, BarChart, Globe } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';
import Sidebar from '@/components/Sidebar';

const BusinessDevelopment = () => {
  const stats = [
    {
      metric: '950+',
      description: 'Businesses Scaled',
      detail: 'Across all industries'
    },
    {
      metric: '320%',
      description: 'Average Revenue Growth',
      detail: 'For our clients'
    },
    {
      metric: '₹350Cr+',
      description: 'Revenue Generated',
      detail: 'Through our strategies'
    },
    {
      metric: '95%',
      description: 'Client Success Rate',
      detail: 'Achieving growth targets'
    }
  ];

  const achievements = [
    'Top Business Development Company in India',
    'Strategic Growth Consultants',
    'Market Expansion Specialists',
    'Revenue Optimization Experts',
    'Business Scaling Champions',
    'Digital Transformation Leaders'
  ];

  const developmentServices = [
    {
      type: 'Strategic Business Planning',
      description: 'Comprehensive business strategy development and market positioning for sustainable growth',
      icon: Target,
      features: ['Business Strategy Development', 'Market Analysis & Research', 'Competitive Intelligence', 'Growth Planning & Roadmaps', 'Revenue Model Optimization', 'Performance Metrics & KPIs']
    },
    {
      type: 'Market Expansion & Growth',
      description: 'Strategic market entry and expansion services for new territories and customer segments',
      icon: Globe,
      features: ['Market Entry Strategies', 'Geographic Expansion Planning', 'Customer Segmentation', 'Channel Development', 'Partnership Strategies', 'International Market Entry']
    },
    {
      type: 'Sales & Revenue Optimization',
      description: 'Sales process optimization and revenue generation strategies for maximum profitability',
      icon: BarChart,
      features: ['Sales Process Optimization', 'Lead Generation Strategies', 'Conversion Rate Optimization', 'Pricing Strategy Development', 'Sales Team Training', 'Revenue Stream Diversification']
    },
    {
      type: 'Digital Transformation Consulting',
      description: 'Complete digital transformation strategies to modernize business operations and growth',
      icon: Zap,
      features: ['Digital Strategy Development', 'Technology Integration Planning', 'Process Digitization', 'Digital Marketing Strategy', 'E-commerce Development', 'Digital Customer Experience']
    }
  ];

  const industryExpertise = [
    {
      industry: 'Technology & Software',
      services: ['Product Strategy', 'Market Positioning', 'Scaling Operations', 'Investor Relations'],
      clients: '180+',
      growth: '420%'
    },
    {
      industry: 'E-commerce & Retail',
      services: ['Market Expansion', 'Channel Optimization', 'Customer Acquisition', 'Revenue Growth'],
      clients: '220+',
      growth: '380%'
    },
    {
      industry: 'Healthcare & Medical',
      services: ['Practice Growth', 'Service Expansion', 'Patient Acquisition', 'Operational Efficiency'],
      clients: '150+',
      growth: '290%'
    },
    {
      industry: 'Manufacturing & Industrial',
      services: ['Market Development', 'Supply Chain Optimization', 'Export Strategy', 'Capacity Planning'],
      clients: '120+',
      growth: '250%'
    },
    {
      industry: 'Financial Services',
      services: ['Client Acquisition', 'Service Diversification', 'Digital Transformation', 'Compliance Strategy'],
      clients: '100+',
      growth: '310%'
    },
    {
      industry: 'Professional Services',
      services: ['Practice Development', 'Client Retention', 'Service Innovation', 'Market Leadership'],
      clients: '180+',
      growth: '270%'
    }
  ];

  const caseStudies = [
    {
      client: 'Tech Startup',
      industry: 'Software Technology',
      challenge: 'Early-stage startup needed strategic direction and market positioning for rapid growth',
      solution: 'Comprehensive business development strategy including market analysis, product positioning, and growth roadmap',
      results: ['500% revenue growth in 18 months', 'Expanded to 3 new markets', '₹15Cr funding raised', 'Team scaled from 10 to 85 employees']
    },
    {
      client: 'Manufacturing Company',
      industry: 'Industrial Manufacturing',
      challenge: 'Traditional manufacturer needed digital transformation and new market expansion',
      solution: 'Digital transformation strategy with e-commerce platform and international market entry plan',
      results: ['300% online sales growth', 'Entered 5 international markets', '₹25Cr revenue increase', '40% operational efficiency improvement']
    },
    {
      client: 'Healthcare Network',
      industry: 'Healthcare Services',
      challenge: 'Regional healthcare provider wanted to expand services and improve patient acquisition',
      solution: 'Strategic expansion plan with digital marketing integration and service diversification',
      results: ['250% patient acquisition growth', 'Launched 3 new service lines', '₹18Cr revenue boost', '85% patient satisfaction improvement']
    }
  ];

  const growthStrategies = [
    {
      strategy: 'Market Penetration',
      description: 'Increase market share in existing markets through competitive strategies',
      benefits: ['Higher market share', 'Increased revenue', 'Brand dominance', 'Customer loyalty']
    },
    {
      strategy: 'Market Development',
      description: 'Expand into new markets and customer segments with existing products',
      benefits: ['New revenue streams', 'Risk diversification', 'Geographic expansion', 'Customer base growth']
    },
    {
      strategy: 'Product Development',
      description: 'Develop new products and services for existing customer base',
      benefits: ['Innovation leadership', 'Customer retention', 'Premium pricing', 'Competitive advantage']
    },
    {
      strategy: 'Diversification',
      description: 'Enter new markets with new products for maximum growth potential',
      benefits: ['Risk mitigation', 'Growth acceleration', 'Market leadership', 'Strategic positioning']
    }
  ];

  return (
    <div className="min-h-screen bg-slate-900 relative">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        <div className="container mx-auto px-6 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            <div className="lg:col-span-3">
              <section className="pt-24 pb-16">
                <div className="max-w-4xl">
                  <div className="inline-flex items-center space-x-2 bg-amber-500/20 border border-amber-500/30 rounded-full px-6 py-2 mb-8">
                    <TrendingUp className="w-4 h-4 text-amber-400" />
                    <span className="text-amber-400 font-medium">Business Development • Strategic Growth Excellence</span>
                  </div>
                  
                  <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
                    #1 Business Development Company in
                    <span className="bg-gradient-to-r from-amber-400 to-amber-600 bg-clip-text text-transparent"> India</span>
                  </h1>
                  
                  <p className="text-xl text-slate-300 mb-8">
                    Premier business development services offering comprehensive strategic growth solutions including business strategy consulting, market expansion, revenue optimization, and digital transformation. Our business development company provides professional growth services with strategic planning, market analysis, sales optimization, and business scaling strategies. Serving 950+ businesses across all industries with proven ₹350Cr+ revenue generation and 320% average growth for clients through strategic business development and growth acceleration excellence.
                  </p>
                  
                  <div className="flex flex-col sm:flex-row gap-4 mb-8">
                    <Button asChild size="lg" className="bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700 text-white text-lg px-8 py-4">
                      <Link to="/quote">Get Free Business Growth Consultation</Link>
                    </Button>
                    <Button asChild size="lg" variant="outline" className="border-amber-500 text-amber-400 hover:bg-amber-500 hover:text-white text-lg px-8 py-4">
                      <Link to="/contact">Call Growth Experts: +91-8708577598</Link>
                    </Button>
                  </div>

                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                    {achievements.map((achievement, index) => (
                      <div key={index} className="flex items-center justify-center space-x-2 bg-slate-800/50 rounded-lg p-3">
                        <CheckCircle className="w-4 h-4 text-amber-400 flex-shrink-0" />
                        <span className="text-slate-300">{achievement}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </section>

              <section className="mb-16 bg-slate-800/50 rounded-2xl p-8">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Business Development
                    <span className="bg-gradient-to-r from-amber-400 to-amber-600 bg-clip-text text-transparent"> Success Metrics</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Measurable business development results from companies across all industries and business stages.
                  </p>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
                  {stats.map((stat, index) => (
                    <div key={index} className="text-center">
                      <div className="text-4xl font-bold text-amber-400 mb-2">{stat.metric}</div>
                      <div className="text-white font-semibold mb-1">{stat.description}</div>
                      <div className="text-slate-400">{stat.detail}</div>
                    </div>
                  ))}
                </div>
              </section>

              <section className="mb-16">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Business Development Services We
                    <span className="bg-gradient-to-r from-amber-400 to-amber-600 bg-clip-text text-transparent"> Provide</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Comprehensive business development solutions designed to accelerate your growth and maximize market potential.
                  </p>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  {developmentServices.map((service, index) => (
                    <Card key={index} className="bg-slate-900/80 border-amber-500/20 hover:border-amber-500/40 transition-all duration-300">
                      <CardContent className="p-8">
                        <div className="w-12 h-12 bg-gradient-to-r from-amber-500 to-amber-600 rounded-lg flex items-center justify-center mb-6">
                          <service.icon className="w-6 h-6 text-white" />
                        </div>
                        <h3 className="text-xl font-bold text-white mb-4">{service.type}</h3>
                        <p className="text-slate-300 mb-6">{service.description}</p>
                        <ul className="space-y-2">
                          {service.features.map((feature, idx) => (
                            <li key={idx} className="flex items-center text-slate-400 text-sm">
                              <CheckCircle className="w-3 h-3 text-amber-400 mr-2 flex-shrink-0" />
                              {feature}
                            </li>
                          ))}
                        </ul>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>

              {/* Growth Strategies */}
              <section className="mb-16 bg-slate-800/30 rounded-2xl p-8">
                <h3 className="text-2xl font-bold text-white mb-6 text-center">
                  Strategic Growth Frameworks
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                  {growthStrategies.map((strategy, index) => (
                    <div key={index} className="bg-slate-900/50 rounded-lg p-6">
                      <h4 className="text-amber-400 font-semibold mb-3">{strategy.strategy}</h4>
                      <p className="text-slate-300 text-sm mb-4">{strategy.description}</p>
                      <ul className="space-y-1">
                        {strategy.benefits.map((benefit, idx) => (
                          <li key={idx} className="text-slate-400 text-xs flex items-center">
                            <CheckCircle className="w-3 h-3 text-amber-400 mr-2 flex-shrink-0" />
                            {benefit}
                          </li>
                        ))}
                      </ul>
                    </div>
                  ))}
                </div>
              </section>

              {/* Industry Expertise */}
              <section className="mb-16 bg-slate-800/30 rounded-2xl p-8">
                <h3 className="text-2xl font-bold text-white mb-6 text-center">
                  Industry-Specific Business Development
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {industryExpertise.map((industry, index) => (
                    <div key={index} className="bg-slate-900/50 rounded-lg p-6">
                      <h4 className="text-amber-400 font-semibold mb-3">{industry.industry}</h4>
                      <ul className="space-y-2 mb-4">
                        {industry.services.map((service, idx) => (
                          <li key={idx} className="text-slate-300 text-sm flex items-center">
                            <CheckCircle className="w-3 h-3 text-amber-400 mr-2 flex-shrink-0" />
                            {service}
                          </li>
                        ))}
                      </ul>
                      <div className="border-t border-slate-700 pt-3">
                        <div className="text-white font-semibold">{industry.clients}</div>
                        <div className="text-slate-400 text-sm mb-1">Clients Served</div>
                        <div className="text-green-400 font-semibold">{industry.growth} avg growth</div>
                      </div>
                    </div>
                  ))}
                </div>
              </section>

              {/* Case Studies */}
              <section className="mb-16">
                <h3 className="text-2xl font-bold text-white mb-8 text-center">
                  Business Development Success Stories
                </h3>
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                  {caseStudies.map((study, index) => (
                    <Card key={index} className="bg-slate-900/60 border-amber-500/20 hover:border-amber-500/40 transition-all duration-300">
                      <CardContent className="p-6">
                        <h4 className="text-amber-400 font-semibold mb-2">{study.client}</h4>
                        <p className="text-slate-400 text-sm mb-3">{study.industry}</p>
                        <div className="mb-4">
                          <h5 className="text-white font-medium text-sm mb-1">Challenge:</h5>
                          <p className="text-slate-300 text-sm mb-3">{study.challenge}</p>
                          <h5 className="text-white font-medium text-sm mb-1">Solution:</h5>
                          <p className="text-slate-300 text-sm mb-3">{study.solution}</p>
                        </div>
                        <div className="bg-slate-800/50 rounded p-3">
                          <h5 className="text-green-400 font-semibold mb-2 text-sm">Results:</h5>
                          <ul className="space-y-1">
                            {study.results.map((result, idx) => (
                              <li key={idx} className="text-slate-400 text-xs flex items-center">
                                <CheckCircle className="w-3 h-3 text-amber-400 mr-2 flex-shrink-0" />
                                {result}
                              </li>
                            ))}
                          </ul>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>

              {/* Internal Linking Section */}
              <section className="mb-16 bg-slate-800/30 rounded-2xl p-8">
                <h3 className="text-2xl font-bold text-white mb-6 text-center">
                  Complete Growth & Development Solutions
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  <Link to="/services/ai-automation" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-amber-400 font-semibold mb-2 group-hover:text-amber-300">AI-Powered Growth</h4>
                    <p className="text-slate-400 text-sm">Artificial intelligence solutions for business acceleration</p>
                  </Link>
                  <Link to="/services/business-automation" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-amber-400 font-semibold mb-2 group-hover:text-amber-300">Process Automation</h4>
                    <p className="text-slate-400 text-sm">Streamline operations for scalable growth</p>
                  </Link>
                  <Link to="/services/seo" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-amber-400 font-semibold mb-2 group-hover:text-amber-300">Digital Marketing Growth</h4>
                    <p className="text-slate-400 text-sm">SEO and digital marketing for business expansion</p>
                  </Link>
                  <Link to="/services/web-development" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-amber-400 font-semibold mb-2 group-hover:text-amber-300">Digital Platform Development</h4>
                    <p className="text-slate-400 text-sm">Custom platforms for business growth and scaling</p>
                  </Link>
                  <Link to="/services/ppc" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-amber-400 font-semibold mb-2 group-hover:text-amber-300">Paid Growth Strategies</h4>
                    <p className="text-slate-400 text-sm">Strategic advertising for rapid business growth</p>
                  </Link>
                  <Link to="/portfolio" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-amber-400 font-semibold mb-2 group-hover:text-amber-300">Growth Success Stories</h4>
                    <p className="text-slate-400 text-sm">See proven results from our business development projects</p>
                  </Link>
                </div>
              </section>

              <section className="bg-gradient-to-r from-amber-600 to-amber-500 rounded-2xl p-8 text-center">
                <h2 className="text-3xl md:text-5xl font-bold mb-6">Ready to Scale Your Business?</h2>
                <p className="text-xl mb-8">
                  Join 950+ businesses that trust GOD Digital Marketing for growth success. Proven strategies that deliver 320% revenue growth and ₹350Cr+ value creation.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button asChild size="lg" className="bg-white text-amber-600 hover:bg-slate-100 text-lg px-8 py-4">
                    <Link to="/quote">Get Free Growth Consultation</Link>
                  </Button>
                  <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-amber-600 text-lg px-8 py-4">
                    <Link to="/contact">Call Growth Experts: +91-8708577598</Link>
                  </Button>
                </div>
              </section>
            </div>

            <div className="lg:col-span-1">
              <Sidebar currentService="business-development" />
            </div>
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
};

export default BusinessDevelopment;
