
import React, { useRef, useMemo, Suspense } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import { Text } from '@react-three/drei';
import { Mesh, Group } from 'three';

// Simplified 3D Dollar Symbol Component
const DollarSymbol = ({ position }: { position: [number, number, number] }) => {
  const groupRef = useRef<Group>(null);

  useFrame((state) => {
    if (groupRef.current) {
      groupRef.current.rotation.y = Math.sin(state.clock.elapsedTime) * 0.1;
      groupRef.current.position.y = position[1] + Math.sin(state.clock.elapsedTime * 2) * 0.02;
    }
  });

  return (
    <group ref={groupRef} position={position}>
      <mesh>
        <sphereGeometry args={[0.15, 32, 32]} />
        <meshStandardMaterial 
          color="#fbbf24" 
          metalness={0.8}
          roughness={0.2}
          emissive="#fbbf24"
          emissiveIntensity={0.1}
        />
      </mesh>
      <Text
        fontSize={0.12}
        color="#1e293b"
        anchorX="center"
        anchorY="middle"
        position={[0, 0, 0.16]}
      >
        $
      </Text>
    </group>
  );
};

// Simplified Professional Ring Component
const ProfessionalRing = ({ position, radius = 0.4 }: { position: [number, number, number], radius?: number }) => {
  const ringRef = useRef<Mesh>(null);

  useFrame((state) => {
    if (ringRef.current) {
      ringRef.current.rotation.z += 0.005;
      ringRef.current.rotation.x = Math.sin(state.clock.elapsedTime * 0.5) * 0.1;
    }
  });

  return (
    <mesh ref={ringRef} position={position}>
      <torusGeometry args={[radius, 0.05, 16, 100]} />
      <meshStandardMaterial 
        color="#0ea5e9" 
        metalness={0.9}
        roughness={0.1}
        emissive="#0ea5e9"
        emissiveIntensity={0.05}
      />
    </mesh>
  );
};

// Simplified Floating Cubes Component
const FloatingCubes = () => {
  const cubesRef = useRef<Group>(null);

  useFrame((state) => {
    if (cubesRef.current) {
      cubesRef.current.rotation.y += 0.002;
      cubesRef.current.children.forEach((child, index) => {
        child.position.y = Math.sin(state.clock.elapsedTime + index) * 0.1;
      });
    }
  });

  return (
    <group ref={cubesRef}>
      {[-1.2, 1.2].map((x, index) => (
        <mesh key={index} position={[x, 0.3, -0.5]}>
          <boxGeometry args={[0.08, 0.08, 0.08]} />
          <meshStandardMaterial 
            color="#10b981" 
            metalness={0.6}
            roughness={0.3}
          />
        </mesh>
      ))}
    </group>
  );
};

// Main Logo Scene Component
const LogoScene = () => {
  const mainGroupRef = useRef<Group>(null);

  useFrame((state) => {
    if (mainGroupRef.current) {
      mainGroupRef.current.rotation.y = Math.sin(state.clock.elapsedTime * 0.2) * 0.05;
    }
  });

  return (
    <group ref={mainGroupRef}>
      {/* Lighting setup */}
      <ambientLight intensity={0.3} />
      <pointLight position={[2, 2, 2]} intensity={0.8} color="#ffffff" />
      <pointLight position={[-2, -1, 1]} intensity={0.4} color="#fbbf24" />
      <spotLight 
        position={[0, 3, 2]} 
        intensity={0.6} 
        angle={0.3} 
        penumbra={0.5}
        color="#0ea5e9"
      />

      {/* Main GOD Text */}
      <Text
        fontSize={0.6}
        color="#ffffff"
        anchorX="center"
        anchorY="middle"
        position={[0, 0.15, 0]}
        letterSpacing={0.02}
      >
        GOD
      </Text>

      {/* Subtitle */}
      <Text
        fontSize={0.16}
        color="#fbbf24"
        anchorX="center"
        anchorY="middle"
        position={[0, -0.2, 0]}
        letterSpacing={0.05}
      >
        DIGITAL MARKETING
      </Text>

      {/* Professional rings */}
      <ProfessionalRing position={[0, 0, -0.3]} radius={0.8} />
      <ProfessionalRing position={[0, 0, -0.5]} radius={1.0} />

      {/* Dollar symbols */}
      <DollarSymbol position={[0.9, 0.4, 0.2]} />
      <DollarSymbol position={[-0.9, -0.2, 0.3]} />

      {/* Floating cubes */}
      <FloatingCubes />

      {/* Decorative spheres */}
      <mesh position={[1.1, -0.4, 0.4]}>
        <sphereGeometry args={[0.03, 16, 16]} />
        <meshStandardMaterial color="#06b6d4" metalness={0.8} roughness={0.2} />
      </mesh>
      <mesh position={[-1.1, 0.6, 0.3]}>
        <sphereGeometry args={[0.025, 16, 16]} />
        <meshStandardMaterial color="#8b5cf6" metalness={0.8} roughness={0.2} />
      </mesh>
    </group>
  );
};

// Fallback component
const LogoFallback = () => (
  <div className="w-full h-48 flex items-center justify-center bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 rounded-lg">
    <div className="text-center animate-pulse">
      <div className="text-4xl font-bold text-white mb-2 tracking-wider">GOD</div>
      <div className="text-base text-amber-400 tracking-widest">DIGITAL MARKETING</div>
      <div className="mt-4 flex justify-center space-x-1">
        <div className="w-2 h-2 bg-amber-400 rounded-full animate-bounce"></div>
        <div className="w-2 h-2 bg-blue-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
        <div className="w-2 h-2 bg-green-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
      </div>
    </div>
  </div>
);

// Main 3D Logo Component
const Logo3D = ({ className = "" }: { className?: string }) => {
  const canvasSettings = useMemo(() => ({
    camera: { 
      position: [0, 0, 3] as [number, number, number], 
      fov: 50
    },
    gl: { 
      antialias: false,
      alpha: true,
      powerPreference: "default" as const
    },
    dpr: 1
  }), []);

  return (
    <div className={`w-full h-48 ${className}`}>
      <Suspense fallback={<LogoFallback />}>
        <Canvas {...canvasSettings}>
          <LogoScene />
        </Canvas>
      </Suspense>
    </div>
  );
};

export default Logo3D;
