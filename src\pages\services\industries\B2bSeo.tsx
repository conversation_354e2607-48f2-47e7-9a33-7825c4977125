import React from 'react';
import { Link } from 'react-router-dom';
import { Briefcase, Search, TrendingUp, Users, CheckCircle, Building, Star, Crown, Target, Zap, ArrowRight } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';
import Sidebar from '@/components/Sidebar';

const B2bSeo = () => {
  const stats = [
    {
      metric: '3,500+',
      description: 'B2B Companies Optimized',
      detail: 'Across all industries'
    },
    {
      metric: '980%',
      description: 'Average Lead Growth',
      detail: 'For B2B clients'
    },
    {
      metric: '₹280Cr+',
      description: 'B2B Revenue Generated',
      detail: 'Through SEO campaigns'
    },
    {
      metric: '94%',
      description: 'B2B Keywords Ranking',
      detail: 'Top 10 positions achieved'
    }
  ];

  const achievements = [
    'Top B2B SEO Company in India',
    'Enterprise SEO Specialists',
    'B2B Lead Generation Experts',
    'SaaS SEO Leaders',
    'Manufacturing SEO Champions',
    'B2B Digital Marketing Pioneers'
  ];

  const b2bSpecializations = [
    {
      type: 'Enterprise B2B SEO',
      description: 'Large-scale SEO solutions for enterprise B2B companies and corporations',
      icon: Building,
      features: ['Enterprise SEO Strategy', 'Multi-location B2B SEO', 'Corporate Website Optimization', 'B2B Content Marketing', 'Executive Thought Leadership', 'Industry Authority Building']
    },
    {
      type: 'SaaS & Technology SEO',
      description: 'Specialized SEO for software companies, SaaS platforms, and technology providers',
      icon: Star,
      features: ['SaaS Product SEO', 'Technology Content Marketing', 'Developer-focused SEO', 'API Documentation SEO', 'Software Feature Optimization', 'Tech Industry Positioning']
    },
    {
      type: 'Manufacturing & Industrial SEO',
      description: 'SEO strategies for manufacturing companies, industrial suppliers, and B2B equipment providers',
      icon: Crown,
      features: ['Industrial Product SEO', 'Manufacturing Process Content', 'B2B Equipment Optimization', 'Supply Chain SEO', 'Technical Specification Pages', 'Industrial Compliance Content']
    },
    {
      type: 'Professional Services SEO',
      description: 'SEO for consulting firms, legal services, accounting, and professional service providers',
      icon: Target,
      features: ['Professional Service Pages', 'Expertise Demonstration', 'Client Case Study SEO', 'Professional Certification Pages', 'Industry Expertise Content', 'Thought Leadership SEO']
    }
  ];

  const b2bIndustries = [
    { name: 'SaaS & Software', clients: '850+', growth: '1,200%' },
    { name: 'Manufacturing', clients: '720+', growth: '980%' },
    { name: 'Professional Services', clients: '680+', growth: '850%' },
    { name: 'Financial Services', clients: '420+', growth: '1,100%' },
    { name: 'Technology Hardware', clients: '380+', growth: '920%' },
    { name: 'Industrial Equipment', clients: '450+', growth: '780%' }
  ];

  const caseStudies = [
    {
      client: 'Smart Whip UAE',
      industry: 'B2B Culinary Equipment',
      challenge: 'B2B company struggling to reach professional chefs and culinary businesses',
      result: '1,800% traffic growth in 4 months',
      metrics: ['420+ B2B keywords in top 5', '₹1.2Cr+ revenue generated', '280% increase in qualified B2B leads']
    },
    {
      client: 'SaaS Platform',
      industry: 'Software Technology',
      challenge: 'New SaaS platform needed to establish authority and attract enterprise clients',
      result: '2,200% organic lead growth',
      metrics: ['650+ SaaS keywords ranking', '₹3.5Cr+ ARR generated', '450% increase in enterprise inquiries']
    },
    {
      client: 'Manufacturing Company',
      industry: 'Industrial Equipment',
      challenge: 'Traditional manufacturer needed digital presence for B2B lead generation',
      result: '1,500% B2B inquiry increase',
      metrics: ['380+ industrial keywords ranking', '₹2.8Cr+ revenue boost', '320% increase in qualified leads']
    }
  ];

  const b2bStrategies = [
    {
      strategy: 'Account-Based SEO',
      description: 'Target specific high-value accounts with personalized SEO strategies',
      benefits: ['Higher conversion rates', 'Targeted lead quality', 'Account penetration', 'Sales alignment']
    },
    {
      strategy: 'Thought Leadership SEO',
      description: 'Establish industry authority through expert content and thought leadership',
      benefits: ['Industry recognition', 'Trust building', 'Expert positioning', 'Media coverage']
    },
    {
      strategy: 'Technical Content SEO',
      description: 'Create technical content that educates and converts B2B decision makers',
      benefits: ['Educational value', 'Trust establishment', 'Lead nurturing', 'Sales enablement']
    },
    {
      strategy: 'B2B Funnel Optimization',
      description: 'Optimize entire B2B sales funnel from awareness to conversion',
      benefits: ['Higher ROI', 'Shorter sales cycles', 'Better lead quality', 'Increased conversions']
    }
  ];

  return (
    <div className="min-h-screen bg-slate-900 relative">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        <div className="container mx-auto px-6 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            <div className="lg:col-span-3">
              <section className="pt-24 pb-16">
                <div className="max-w-4xl">
                  <div className="inline-flex items-center space-x-2 bg-indigo-500/20 border border-indigo-500/30 rounded-full px-6 py-2 mb-8">
                    <Briefcase className="w-4 h-4 text-indigo-400" />
                    <span className="text-indigo-400 font-medium">B2B SEO • Business-to-Business Optimization Excellence</span>
                  </div>
                  
                  <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
                    #1 B2B SEO Company in
                    <span className="bg-gradient-to-r from-indigo-400 to-indigo-600 bg-clip-text text-transparent"> India</span>
                  </h1>
                  
                  <p className="text-xl text-slate-300 mb-8">
                    Premier B2B SEO services offering comprehensive search engine optimization solutions for business-to-business companies including enterprise SEO, SaaS SEO, manufacturing SEO, and professional services optimization. Our B2B SEO company provides professional SEO services with B2B lead generation, account-based SEO, thought leadership content, and B2B conversion optimization. Serving 3,500+ B2B companies across all industries with proven ₹280Cr+ revenue generation and 980% average lead growth for B2B clients through strategic search engine optimization and B2B digital marketing excellence.
                  </p>
                  
                  <div className="flex flex-col sm:flex-row gap-4 mb-8">
                    <Button asChild size="lg" className="bg-gradient-to-r from-indigo-500 to-indigo-600 hover:from-indigo-600 hover:to-indigo-700 text-white text-lg px-8 py-4">
                      <Link to="/quote">Get Free B2B SEO Audit</Link>
                    </Button>
                    <Button asChild size="lg" variant="outline" className="border-indigo-500 text-indigo-400 hover:bg-indigo-500 hover:text-white text-lg px-8 py-4">
                      <Link to="/contact">Call B2B SEO Experts: +91-**********</Link>
                    </Button>
                  </div>

                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                    {achievements.map((achievement, index) => (
                      <div key={index} className="flex items-center justify-center space-x-2 bg-slate-800/50 rounded-lg p-3">
                        <CheckCircle className="w-4 h-4 text-indigo-400 flex-shrink-0" />
                        <span className="text-slate-300">{achievement}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </section>

              <section className="mb-16 bg-slate-800/50 rounded-2xl p-8">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    B2B SEO
                    <span className="bg-gradient-to-r from-indigo-400 to-indigo-600 bg-clip-text text-transparent"> Success Metrics</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Measurable B2B SEO results from companies across all business-to-business industries and sectors.
                  </p>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
                  {stats.map((stat, index) => (
                    <div key={index} className="text-center">
                      <div className="text-4xl font-bold text-indigo-400 mb-2">{stat.metric}</div>
                      <div className="text-white font-semibold mb-1">{stat.description}</div>
                      <div className="text-slate-400">{stat.detail}</div>
                    </div>
                  ))}
                </div>
              </section>

              <section className="mb-16">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    B2B SEO Strategies We
                    <span className="bg-gradient-to-r from-indigo-400 to-indigo-600 bg-clip-text text-transparent"> Master</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Specialized SEO strategies designed for B2B success across all business-to-business industries and markets.
                  </p>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  {b2bSpecializations.map((spec, index) => (
                    <Card key={index} className="bg-slate-900/80 border-indigo-500/20 hover:border-indigo-500/40 transition-all duration-300">
                      <CardContent className="p-8">
                        <div className="w-12 h-12 bg-gradient-to-r from-indigo-500 to-indigo-600 rounded-lg flex items-center justify-center mb-6">
                          <spec.icon className="w-6 h-6 text-white" />
                        </div>
                        <h3 className="text-xl font-bold text-white mb-4">{spec.type}</h3>
                        <p className="text-slate-300 mb-6">{spec.description}</p>
                        <ul className="space-y-2">
                          {spec.features.map((feature, idx) => (
                            <li key={idx} className="flex items-center text-slate-400 text-sm">
                              <CheckCircle className="w-3 h-3 text-indigo-400 mr-2 flex-shrink-0" />
                              {feature}
                            </li>
                          ))}
                        </ul>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>

              {/* B2B Industries */}
              <section className="mb-16 bg-slate-800/30 rounded-2xl p-8">
                <h3 className="text-2xl font-bold text-white mb-6 text-center">
                  B2B Industry Expertise
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-6">
                  {b2bIndustries.map((industry, index) => (
                    <div key={index} className="text-center bg-slate-900/50 rounded-lg p-4">
                      <h4 className="text-indigo-400 font-semibold mb-2">{industry.name}</h4>
                      <div className="text-2xl font-bold text-white mb-1">{industry.clients}</div>
                      <div className="text-slate-400 text-sm mb-2">Companies Served</div>
                      <div className="text-green-400 font-semibold text-sm">{industry.growth} avg growth</div>
                    </div>
                  ))}
                </div>
              </section>

              {/* B2B Strategies */}
              <section className="mb-16 bg-slate-800/30 rounded-2xl p-8">
                <h3 className="text-2xl font-bold text-white mb-6 text-center">
                  Advanced B2B SEO Strategies
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                  {b2bStrategies.map((strategy, index) => (
                    <div key={index} className="bg-slate-900/50 rounded-lg p-6">
                      <h4 className="text-indigo-400 font-semibold mb-3">{strategy.strategy}</h4>
                      <p className="text-slate-300 text-sm mb-4">{strategy.description}</p>
                      <ul className="space-y-1">
                        {strategy.benefits.map((benefit, idx) => (
                          <li key={idx} className="text-slate-400 text-xs flex items-center">
                            <CheckCircle className="w-3 h-3 text-indigo-400 mr-2 flex-shrink-0" />
                            {benefit}
                          </li>
                        ))}
                      </ul>
                    </div>
                  ))}
                </div>
              </section>

              {/* Case Studies */}
              <section className="mb-16">
                <h3 className="text-2xl font-bold text-white mb-8 text-center">
                  B2B SEO Success Stories
                </h3>
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                  {caseStudies.map((study, index) => (
                    <Card key={index} className="bg-slate-900/60 border-indigo-500/20 hover:border-indigo-500/40 transition-all duration-300">
                      <CardContent className="p-6">
                        <h4 className="text-indigo-400 font-semibold mb-2">{study.client}</h4>
                        <p className="text-slate-400 text-sm mb-3">{study.industry}</p>
                        <p className="text-slate-300 text-sm mb-4">{study.challenge}</p>
                        <div className="bg-slate-800/50 rounded p-3 mb-4">
                          <div className="text-green-400 font-semibold mb-2">{study.result}</div>
                          <ul className="space-y-1">
                            {study.metrics.map((metric, idx) => (
                              <li key={idx} className="text-slate-400 text-xs flex items-center">
                                <CheckCircle className="w-3 h-3 text-indigo-400 mr-2 flex-shrink-0" />
                                {metric}
                              </li>
                            ))}
                          </ul>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>

              {/* Internal Linking Section */}
              <section className="mb-16 bg-slate-800/30 rounded-2xl p-8">
                <h3 className="text-2xl font-bold text-white mb-6 text-center">
                  Complete B2B Digital Marketing Solutions
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  <Link to="/services/ppc" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-indigo-400 font-semibold mb-2 group-hover:text-indigo-300">B2B Google Ads</h4>
                    <p className="text-slate-400 text-sm">PPC advertising for B2B lead generation and account targeting</p>
                  </Link>
                  <Link to="/services/social-media" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-indigo-400 font-semibold mb-2 group-hover:text-indigo-300">B2B Social Media</h4>
                    <p className="text-slate-400 text-sm">LinkedIn and professional social media marketing</p>
                  </Link>
                  <Link to="/services/content" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-indigo-400 font-semibold mb-2 group-hover:text-indigo-300">B2B Content Marketing</h4>
                    <p className="text-slate-400 text-sm">Thought leadership and technical content creation</p>
                  </Link>
                  <Link to="/services/email" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-indigo-400 font-semibold mb-2 group-hover:text-indigo-300">B2B Email Marketing</h4>
                    <p className="text-slate-400 text-sm">Account-based email campaigns and lead nurturing</p>
                  </Link>
                  <Link to="/services/business-development" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-indigo-400 font-semibold mb-2 group-hover:text-indigo-300">B2B Business Development</h4>
                    <p className="text-slate-400 text-sm">Strategic B2B growth and market expansion</p>
                  </Link>
                  <Link to="/portfolio" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-indigo-400 font-semibold mb-2 group-hover:text-indigo-300">B2B Success Stories</h4>
                    <p className="text-slate-400 text-sm">See proven results from our B2B clients</p>
                  </Link>
                </div>
              </section>

              <section className="bg-gradient-to-r from-indigo-600 to-indigo-500 rounded-2xl p-8 text-center">
                <h2 className="text-3xl md:text-5xl font-bold mb-6">Ready to Generate More B2B Leads?</h2>
                <p className="text-xl mb-8">
                  Join 3,500+ B2B companies that trust GOD Digital Marketing for SEO success. Proven strategies that deliver 980% lead growth and ₹280Cr+ revenue generation.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button asChild size="lg" className="bg-white text-indigo-600 hover:bg-slate-100 text-lg px-8 py-4">
                    <Link to="/quote">Get Free B2B SEO Audit</Link>
                  </Button>
                  <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-indigo-600 text-lg px-8 py-4">
                    <Link to="/contact">Call B2B SEO Experts: +91-**********</Link>
                  </Button>
                </div>
              </section>
            </div>

            <div className="lg:col-span-1">
              <Sidebar currentService="seo" />
            </div>
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
};

export default B2bSeo;
