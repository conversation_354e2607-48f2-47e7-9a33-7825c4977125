
import React from 'react';

interface LogoStaticProps {
  width?: number;
  height?: number;
  className?: string;
}

const LogoStatic: React.FC<LogoStaticProps> = ({ 
  width = 200, 
  height = 200, 
  className = "" 
}) => {
  return (
    <svg
      id="logo-svg"
      width={width}
      height={height}
      viewBox="0 0 400 400"
      className={className}
      xmlns="http://www.w3.org/2000/svg"
    >
      <defs>
        {/* Gradients for professional look */}
        <linearGradient id="goldGradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" stopColor="#fbbf24" />
          <stop offset="50%" stopColor="#f59e0b" />
          <stop offset="100%" stopColor="#d97706" />
        </linearGradient>
        
        <linearGradient id="blueGradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" stopColor="#0ea5e9" />
          <stop offset="50%" stopColor="#0284c7" />
          <stop offset="100%" stopColor="#0369a1" />
        </linearGradient>

        <linearGradient id="textGradient" x1="0%" y1="0%" x2="100%" y2="0%">
          <stop offset="0%" stopColor="#ffffff" />
          <stop offset="50%" stopColor="#f8fafc" />
          <stop offset="100%" stopColor="#e2e8f0" />
        </linearGradient>

        {/* Shadow filters */}
        <filter id="textShadow" x="-50%" y="-50%" width="200%" height="200%">
          <feDropShadow dx="2" dy="4" stdDeviation="3" floodColor="#000000" floodOpacity="0.3"/>
        </filter>
        
        <filter id="elementShadow" x="-50%" y="-50%" width="200%" height="200%">
          <feDropShadow dx="1" dy="2" stdDeviation="2" floodColor="#000000" floodOpacity="0.2"/>
        </filter>
      </defs>

      {/* Background circles for depth */}
      <circle cx="200" cy="200" r="180" fill="none" stroke="url(#blueGradient)" strokeWidth="2" opacity="0.3" />
      <circle cx="200" cy="200" r="150" fill="none" stroke="url(#blueGradient)" strokeWidth="1.5" opacity="0.5" />

      {/* Decorative elements */}
      <circle cx="320" cy="120" r="8" fill="url(#goldGradient)" filter="url(#elementShadow)" />
      <circle cx="80" cy="280" r="6" fill="#06b6d4" filter="url(#elementShadow)" />
      <circle cx="340" cy="300" r="5" fill="#8b5cf6" filter="url(#elementShadow)" />
      <circle cx="60" cy="100" r="7" fill="#10b981" filter="url(#elementShadow)" />

      {/* Data visualization elements */}
      <rect x="70" y="250" width="12" height="30" fill="#10b981" rx="2" filter="url(#elementShadow)" />
      <rect x="85" y="240" width="12" height="40" fill="#10b981" rx="2" filter="url(#elementShadow)" />
      <rect x="100" y="235" width="12" height="45" fill="#10b981" rx="2" filter="url(#elementShadow)" />

      {/* Dollar symbols */}
      <g transform="translate(300, 140)">
        <circle r="20" fill="url(#goldGradient)" filter="url(#elementShadow)" />
        <text x="0" y="8" textAnchor="middle" fontSize="24" fontWeight="bold" fill="#1e293b">$</text>
      </g>
      
      <g transform="translate(100, 320)">
        <circle r="15" fill="url(#goldGradient)" filter="url(#elementShadow)" />
        <text x="0" y="6" textAnchor="middle" fontSize="18" fontWeight="bold" fill="#1e293b">$</text>
      </g>

      {/* Main GOD text */}
      <text 
        x="200" 
        y="180" 
        textAnchor="middle" 
        fontSize="72" 
        fontWeight="900" 
        fill="url(#textGradient)"
        filter="url(#textShadow)"
        letterSpacing="4px"
        fontFamily="Inter, Arial, sans-serif"
      >
        GOD
      </text>

      {/* Subtitle */}
      <text 
        x="200" 
        y="220" 
        textAnchor="middle" 
        fontSize="16" 
        fontWeight="600" 
        fill="url(#goldGradient)"
        letterSpacing="3px"
        fontFamily="Inter, Arial, sans-serif"
      >
        DIGITAL MARKETING
      </text>

      {/* Professional accent lines */}
      <line x1="120" y1="240" x2="280" y2="240" stroke="url(#goldGradient)" strokeWidth="2" opacity="0.6" />
      <line x1="140" y1="160" x2="260" y2="160" stroke="url(#blueGradient)" strokeWidth="1.5" opacity="0.4" />
    </svg>
  );
};

export default LogoStatic;
