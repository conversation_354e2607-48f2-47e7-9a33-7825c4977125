
import React, { useEffect } from 'react';
import { <PERSON> } from 'react-router-dom';
import { ArrowRight, Search, Globe, Bot, TrendingUp, Users, Building } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { motion, useAnimation } from 'framer-motion';
import { useInView } from 'react-intersection-observer';

const ServicesOverview = () => {
  const [ref, inView] = useInView({
    threshold: 0.1,
    triggerOnce: true,
  });

  const controls = useAnimation();

  useEffect(() => {
    if (inView) {
      controls.start('visible');
    }
  }, [controls, inView]);

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delayChildren: 0.2,
        staggerChildren: 0.1
      }
    }
  };

  const cardVariants = {
    hidden: {
      y: 50,
      opacity: 0,
      scale: 0.9
    },
    visible: {
      y: 0,
      opacity: 1,
      scale: 1,
      transition: {
        duration: 0.6,
        ease: "easeOut" as const
      }
    }
  };

  const iconVariants = {
    hidden: { scale: 0, rotate: -180 },
    visible: {
      scale: 1,
      rotate: 0,
      transition: {
        duration: 0.8,
        ease: "easeOut" as const
      }
    }
  };

  const services = [
    {
      icon: Globe,
      title: 'International SEO',
      description: 'Multi-country SEO campaigns that dominate global markets. Proven expertise across UK, US, Dubai, India, Kuwait & South Africa.',
      link: '/services/seo',
      features: ['Multi-Country Ranking', 'International Keyword Research', 'Global Technical SEO', 'Cross-Border Link Building']
    },
    {
      icon: Bot,
      title: 'AI-Powered Content Creation',
      description: 'Scale content production with advanced AI automation while maintaining quality and relevance for each market.',
      link: '/services/ai-content',
      features: ['AI Content at Scale', 'Multi-Language Optimization', 'Automated Publishing', 'Quality Control Systems']
    },
    {
      icon: TrendingUp,
      title: 'Business Growth Automation',
      description: 'Complete digital ecosystem automation that scales your business operations and lead generation systems.',
      link: '/services/automation',
      features: ['Lead Generation Systems', 'Sales Funnel Automation', 'CRM Integration', 'Performance Analytics']
    },
    {
      icon: Users,
      title: 'Social Media Automation',
      description: 'AI-powered social media management across all platforms with content scheduling and engagement automation.',
      link: '/services/social-automation',
      features: ['Multi-Platform Management', 'Content Automation', 'Engagement Bots', 'Analytics & Reporting']
    },
    {
      icon: Building,
      title: 'Real Estate Lead Generation',
      description: 'Specialized lead generation systems for real estate companies targeting high-value property leads.',
      link: '/services/real-estate',
      features: ['Property Lead Funnels', 'Local SEO Dominance', 'Buyer Intent Targeting', 'CRM Integration']
    },
    {
      icon: Search,
      title: 'Enterprise SEO Consulting',
      description: 'Premium SEO consulting for businesses ready to invest ₹60K-150K monthly for international market dominance.',
      link: '/services/enterprise-seo',
      features: ['Strategic SEO Planning', 'International Expansion', 'Technical SEO Audits', 'Competitive Analysis']
    }
  ];

  return (
    <section ref={ref} className="py-20 bg-slate-800/50 relative overflow-hidden">
      {/* Background decorative elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <motion.div
          className="absolute top-10 right-10 w-40 h-40 bg-gradient-to-r from-amber-400/5 to-amber-600/5 rounded-full blur-3xl"
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.3, 0.6, 0.3],
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
        <motion.div
          className="absolute bottom-20 left-10 w-32 h-32 bg-gradient-to-r from-blue-400/5 to-blue-600/5 rounded-full blur-3xl"
          animate={{
            scale: [1, 1.3, 1],
            opacity: [0.2, 0.5, 0.2],
          }}
          transition={{
            duration: 10,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 2
          }}
        />
      </div>

      <div className="container mx-auto px-6 relative z-10">
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
          transition={{ duration: 0.8, ease: "easeOut" }}
        >
          <motion.h2
            className="text-4xl md:text-5xl font-bold mb-6"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={inView ? { opacity: 1, scale: 1 } : { opacity: 0, scale: 0.9 }}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            Premium International SEO &
            <motion.span
              className="bg-gradient-to-r from-amber-400 to-amber-600 bg-clip-text text-transparent"
              initial={{ opacity: 0 }}
              animate={inView ? { opacity: 1 } : { opacity: 0 }}
              transition={{ duration: 0.8, delay: 0.5 }}
            >
              {" "}AI Automation
            </motion.span>
          </motion.h2>
          <motion.p
            className="text-xl text-slate-300 max-w-3xl mx-auto mb-8"
            initial={{ opacity: 0, y: 20 }}
            animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
            transition={{ duration: 0.8, delay: 0.4 }}
          >
            Specialized services for businesses ready to dominate international markets. From zero to millions of monthly views across 6 countries - proven strategies that deliver premium results.
          </motion.p>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
            transition={{ duration: 0.8, delay: 0.6 }}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Button asChild variant="outline" className="border-amber-500 text-amber-400 hover:bg-amber-500 hover:text-white">
              <Link to="/services">Explore Premium Services</Link>
            </Button>
          </motion.div>
        </motion.div>

        <motion.div
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
          variants={containerVariants}
          initial="hidden"
          animate={controls}
        >
          {services.map((service, index) => (
            <motion.div
              key={index}
              variants={cardVariants}
              whileHover={{
                y: -10,
                transition: { duration: 0.3, ease: "easeOut" }
              }}
              whileTap={{ scale: 0.98 }}
            >
              <Card className="bg-slate-900/80 border-amber-500/20 hover:border-amber-500/40 transition-all duration-300 group cursor-pointer h-full backdrop-blur-sm">
                <CardContent className="p-8 h-full flex flex-col">
                  <div className="mb-6">
                    <motion.div
                      className="w-12 h-12 bg-gradient-to-r from-amber-500 to-amber-600 rounded-lg flex items-center justify-center mb-4"
                      variants={iconVariants}
                      whileHover={{
                        scale: 1.2,
                        rotate: 360,
                        transition: { duration: 0.6, ease: "easeInOut" }
                      }}
                    >
                      <service.icon className="w-6 h-6 text-white" />
                    </motion.div>

                    <motion.h3
                      className="text-2xl font-bold text-white mb-3"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ duration: 0.6, delay: index * 0.1 + 0.3 }}
                    >
                      {service.title}
                    </motion.h3>

                    <motion.p
                      className="text-slate-300 mb-6"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ duration: 0.6, delay: index * 0.1 + 0.4 }}
                    >
                      {service.description}
                    </motion.p>
                  </div>

                  <motion.ul
                    className="space-y-2 mb-6 flex-grow"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ duration: 0.6, delay: index * 0.1 + 0.5 }}
                  >
                    {service.features.map((feature, idx) => (
                      <motion.li
                        key={idx}
                        className="flex items-center text-slate-400"
                        initial={{ opacity: 0, x: -10 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.4, delay: index * 0.1 + 0.6 + idx * 0.1 }}
                      >
                        <motion.div
                          className="w-2 h-2 bg-amber-400 rounded-full mr-3"
                          initial={{ scale: 0 }}
                          animate={{ scale: 1 }}
                          transition={{ duration: 0.3, delay: index * 0.1 + 0.7 + idx * 0.1 }}
                        />
                        {feature}
                      </motion.li>
                    ))}
                  </motion.ul>

                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: index * 0.1 + 0.8 }}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <Button asChild variant="ghost" className="text-amber-400 hover:text-white hover:bg-amber-500 p-0">
                      <Link to={service.link} className="flex items-center">
                        Learn More
                        <motion.div
                          animate={{ x: [0, 5, 0] }}
                          transition={{ duration: 1.5, repeat: Infinity, ease: "easeInOut" }}
                        >
                          <ArrowRight className="ml-2 w-4 h-4" />
                        </motion.div>
                      </Link>
                    </Button>
                  </motion.div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </section>
  );
};

export default ServicesOverview;
