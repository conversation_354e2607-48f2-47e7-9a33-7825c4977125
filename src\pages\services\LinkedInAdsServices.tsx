import React from 'react';
import { Link } from 'react-router-dom';
import { ArrowR<PERSON>, Linkedin, TrendingUp, Users, CheckCircle, Target, Star, Briefcase } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';

const LinkedInAdsServices = () => {
  const features = [
    'LinkedIn Campaign Strategy & B2B Targeting',
    'Sponsored Content & Video Ad Creation',
    'Advanced Professional Targeting Options',
    'LinkedIn Lead Gen Forms & Automation',
    'Account-Based Marketing (ABM) Campaigns',
    'LinkedIn Analytics & Conversion Tracking',
    'Message Ads & Conversation Campaigns',
    'Dynamic Ads & Personalization',
    'LinkedIn Audience Network Expansion',
    'B2B Lead Nurturing & Follow-up Sequences'
  ];

  const packages = [
    {
      name: 'LinkedIn B2B Starter',
      price: '₹35,000',
      period: '/month',
      description: 'Perfect for B2B companies starting LinkedIn advertising',
      features: [
        'LinkedIn Campaign Setup',
        'Professional Audience Targeting',
        'Sponsored Content Creation',
        'Lead Gen Form Setup',
        'Monthly B2B Performance Reports'
      ]
    },
    {
      name: 'LinkedIn B2B Pro',
      price: '₹60,000',
      period: '/month',
      description: 'Comprehensive LinkedIn advertising for B2B growth',
      features: [
        'Everything in B2B Starter',
        'Account-Based Marketing',
        'Video Ad Creation',
        'Message Ad Campaigns',
        'Advanced B2B Analytics',
        'Weekly Strategy Optimization'
      ],
      popular: true
    },
    {
      name: 'LinkedIn Enterprise',
      price: '₹100,000',
      period: '/month',
      description: 'Enterprise LinkedIn advertising for large B2B organizations',
      features: [
        'Everything in B2B Pro',
        'Multi-Account Management',
        'Custom B2B Audiences',
        'Advanced Attribution',
        'Dedicated LinkedIn Specialist',
        'Daily Campaign Optimization'
      ]
    }
  ];

  const results = [
    {
      metric: '300%',
      description: 'Increase in B2B leads',
      timeframe: 'through LinkedIn campaigns'
    },
    {
      metric: '₹50L+',
      description: 'B2B revenue generated',
      timeframe: 'for enterprise clients'
    },
    {
      metric: '40%',
      description: 'Lower cost per lead',
      timeframe: 'vs other B2B platforms'
    }
  ];

  const campaignTypes = [
    {
      type: 'B2B Lead Generation',
      description: 'Generate high-quality B2B leads with LinkedIn Lead Gen Forms',
      icon: Target,
      benefits: ['Pre-filled forms', 'Professional targeting', 'CRM integration', 'Higher conversion rates']
    },
    {
      type: 'Account-Based Marketing',
      description: 'Target specific companies and decision-makers with precision',
      icon: Briefcase,
      benefits: ['Company targeting', 'Job title precision', 'Industry focus', 'Decision-maker reach']
    },
    {
      type: 'Brand Awareness B2B',
      description: 'Build professional brand recognition among industry leaders',
      icon: Star,
      benefits: ['Professional audience', 'Industry influence', 'Thought leadership', 'Network expansion']
    }
  ];

  return (
    <div className="min-h-screen bg-slate-900 relative">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        {/* Hero Section */}
        <section className="pt-32 pb-20">
          <div className="container mx-auto px-6">
            <div className="max-w-4xl mx-auto text-center">
              <div className="inline-flex items-center space-x-2 bg-blue-600/20 border border-blue-600/30 rounded-full px-6 py-2 mb-8">
                <Linkedin className="w-4 h-4 text-blue-500" />
                <span className="text-blue-500 font-medium">LinkedIn Ads Services • B2B Marketing Experts</span>
              </div>
              
              <h1 className="text-5xl md:text-6xl font-bold mb-6 leading-tight">
                LinkedIn Ads That Generate
                <span className="bg-gradient-to-r from-blue-500 to-blue-700 bg-clip-text text-transparent"> Quality B2B Leads</span>
              </h1>
              
              <p className="text-xl text-slate-300 mb-12 max-w-3xl mx-auto">
                Expert LinkedIn advertising services that connect you with decision-makers and generate high-quality B2B leads. With proven experience across international markets, we've generated ₹50L+ B2B revenue through strategic LinkedIn campaigns.
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button asChild size="lg" className="bg-gradient-to-r from-blue-500 to-blue-700 hover:from-blue-600 hover:to-blue-800 text-white text-lg px-8 py-4">
                  <Link to="/quote">Get LinkedIn B2B Strategy</Link>
                </Button>
                <Button asChild size="lg" variant="outline" className="border-blue-500 text-blue-500 hover:bg-blue-500 hover:text-white text-lg px-8 py-4">
                  <Link to="/case-studies">View B2B Results</Link>
                </Button>
              </div>
            </div>
          </div>
        </section>

        {/* Results Section */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                LinkedIn B2B
                <span className="bg-gradient-to-r from-blue-500 to-blue-700 bg-clip-text text-transparent"> Campaign Results</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto mb-12">
                Real B2B results from LinkedIn advertising campaigns across industries and professional sectors.
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
              {results.map((result, index) => (
                <div key={index} className="text-center">
                  <div className="text-5xl font-bold text-blue-500 mb-2">{result.metric}</div>
                  <div className="text-white font-semibold mb-1">{result.description}</div>
                  <div className="text-slate-400">{result.timeframe}</div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Campaign Types Section */}
        <section className="py-20">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                LinkedIn Campaign Types We
                <span className="bg-gradient-to-r from-blue-500 to-blue-700 bg-clip-text text-transparent"> Master</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto">
                Strategic LinkedIn advertising campaigns designed for B2B success and professional networking.
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-20">
              {campaignTypes.map((campaign, index) => (
                <Card key={index} className="bg-slate-900/80 border-blue-500/20 hover:border-blue-500/40 transition-all duration-300">
                  <CardContent className="p-8">
                    <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-blue-700 rounded-lg flex items-center justify-center mb-6">
                      <campaign.icon className="w-6 h-6 text-white" />
                    </div>
                    <h3 className="text-xl font-bold text-white mb-4">{campaign.type}</h3>
                    <p className="text-slate-300 mb-6">{campaign.description}</p>
                    <ul className="space-y-2">
                      {campaign.benefits.map((benefit, idx) => (
                        <li key={idx} className="flex items-center text-slate-400 text-sm">
                          <CheckCircle className="w-3 h-3 text-blue-500 mr-2 flex-shrink-0" />
                          {benefit}
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Features Section */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                LinkedIn Ads Management
                <span className="bg-gradient-to-r from-blue-500 to-blue-700 bg-clip-text text-transparent"> Features</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto">
                Comprehensive LinkedIn advertising services that connect you with the right professionals.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-w-4xl mx-auto">
              {features.map((feature, index) => (
                <div key={index} className="flex items-center space-x-3">
                  <CheckCircle className="w-6 h-6 text-blue-500 flex-shrink-0" />
                  <span className="text-slate-300">{feature}</span>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Pricing Section */}
        <section className="py-20">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                LinkedIn B2B Advertising
                <span className="bg-gradient-to-r from-blue-500 to-blue-700 bg-clip-text text-transparent"> Packages</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto">
                Choose the perfect LinkedIn advertising package to connect with decision-makers and grow your B2B business.
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
              {packages.map((pkg, index) => (
                <Card key={index} className={`bg-slate-900/80 border-blue-500/20 hover:border-blue-500/40 transition-all duration-300 relative ${pkg.popular ? 'ring-2 ring-blue-500' : ''}`}>
                  {pkg.popular && (
                    <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                      <span className="bg-blue-500 text-white px-4 py-2 rounded-full text-sm font-semibold">Most Popular</span>
                    </div>
                  )}
                  <CardContent className="p-8">
                    <h3 className="text-2xl font-bold text-white mb-2">{pkg.name}</h3>
                    <p className="text-slate-300 mb-6">{pkg.description}</p>
                    <div className="mb-6">
                      <span className="text-4xl font-bold text-blue-500">{pkg.price}</span>
                      <span className="text-slate-400">{pkg.period}</span>
                    </div>
                    <ul className="space-y-3 mb-8">
                      {pkg.features.map((feature, idx) => (
                        <li key={idx} className="flex items-center text-slate-300">
                          <CheckCircle className="w-4 h-4 text-blue-500 mr-3 flex-shrink-0" />
                          {feature}
                        </li>
                      ))}
                    </ul>
                    <Button asChild className={`w-full ${pkg.popular ? 'bg-gradient-to-r from-blue-500 to-blue-700 hover:from-blue-600 hover:to-blue-800' : 'bg-slate-700 hover:bg-slate-600'} text-white`}>
                      <Link to="/quote">Get Started</Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 bg-gradient-to-r from-blue-600 to-blue-700">
          <div className="container mx-auto px-6 text-center">
            <h2 className="text-4xl md:text-5xl font-bold mb-6">Ready to Connect with Decision-Makers?</h2>
            <p className="text-xl mb-8 max-w-3xl mx-auto">
              Join B2B companies generating ₹50L+ revenue through strategic LinkedIn advertising. Expert B2B campaign management with proven results.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button asChild size="lg" className="bg-white text-blue-600 hover:bg-slate-100 text-lg px-8 py-4">
                <Link to="/quote">Get LinkedIn B2B Strategy</Link>
              </Button>
              <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-blue-600 text-lg px-8 py-4">
                <Link to="/contact">Book B2B Consultation</Link>
              </Button>
            </div>
          </div>
        </section>
      </main>
      
      <Footer />
    </div>
  );
};

export default LinkedInAdsServices;
