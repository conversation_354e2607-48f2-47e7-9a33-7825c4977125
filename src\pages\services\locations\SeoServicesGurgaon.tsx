import React from 'react';
import { Link } from 'react-router-dom';
import { ArrowRight, Search, TrendingUp, Users, CheckCircle, MapPin, Star } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';

const SeoServicesGurgaon = () => {
  const features = [
    'Local SEO Gurgaon Optimization',
    'Gurgaon Business Directory Listings',
    'Google My Business Gurgaon Setup',
    'Gurgaon Keyword Research & Targeting',
    'Cyber City SEO Strategies',
    'DLF Phase SEO Optimization',
    'Gurgaon Competition Analysis',
    'Local Gurgaon Link Building',
    'Gurgaon Maps Optimization',
    'Sector-wise SEO Targeting'
  ];

  const packages = [
    {
      name: 'Gurgaon Local SEO',
      price: '₹30,000',
      period: '/month',
      description: 'Perfect for local Gurgaon businesses targeting nearby customers',
      features: [
        'Gurgaon Local Keyword Research',
        'Google My Business Optimization',
        'Gurgaon Directory Submissions',
        'Local Citation Building',
        'Gurgaon Competition Analysis'
      ]
    },
    {
      name: 'Gurgaon Business SEO',
      price: '₹50,000',
      period: '/month',
      description: 'Comprehensive SEO for Gurgaon businesses targeting NCR region',
      features: [
        'Everything in Local SEO',
        'NCR Region Targeting',
        'Advanced Gurgaon SEO',
        'Content Marketing',
        'Link Building Campaigns'
      ],
      popular: true
    },
    {
      name: 'Gurgaon Enterprise SEO',
      price: '₹80,000',
      period: '/month',
      description: 'Enterprise SEO for large Gurgaon corporations and MNCs',
      features: [
        'Everything in Business SEO',
        'Multi-location SEO',
        'International SEO',
        'Advanced Analytics',
        'Dedicated SEO Manager'
      ]
    }
  ];

  const results = [
    {
      metric: '50+',
      description: 'Gurgaon businesses ranked',
      timeframe: 'on first page Google'
    },
    {
      metric: '400%',
      description: 'Average traffic increase',
      timeframe: 'for Gurgaon clients'
    },
    {
      metric: '#1',
      description: 'Local rankings achieved',
      timeframe: 'for target keywords'
    }
  ];

  return (
    <div className="min-h-screen bg-slate-900 relative">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        {/* Hero Section */}
        <section className="pt-32 pb-20">
          <div className="container mx-auto px-6">
            <div className="max-w-4xl mx-auto text-center">
              <div className="inline-flex items-center space-x-2 bg-amber-500/20 border border-amber-500/30 rounded-full px-6 py-2 mb-8">
                <Search className="w-4 h-4 text-amber-400" />
                <span className="text-amber-400 font-medium">SEO Services Gurgaon • Millennium City SEO</span>
              </div>
              
              <h1 className="text-5xl md:text-6xl font-bold mb-6 leading-tight">
                #1 SEO Services in
                <span className="bg-gradient-to-r from-amber-400 to-amber-600 bg-clip-text text-transparent"> Gurgaon</span>
              </h1>
              
              <p className="text-xl text-slate-300 mb-12 max-w-3xl mx-auto">
                Leading SEO company in Gurgaon helping businesses dominate Google search results. From Cyber City to DLF phases, we've helped 50+ Gurgaon businesses achieve first-page rankings and drive massive organic traffic growth.
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button asChild size="lg" className="bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700 text-white text-lg px-8 py-4">
                  <Link to="/quote">Get Free Gurgaon SEO Audit</Link>
                </Button>
                <Button asChild size="lg" variant="outline" className="border-amber-500 text-amber-400 hover:bg-amber-500 hover:text-white text-lg px-8 py-4">
                  <Link to="/case-studies">View Gurgaon SEO Results</Link>
                </Button>
              </div>
            </div>
          </div>
        </section>

        {/* Results Section */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Gurgaon SEO
                <span className="bg-gradient-to-r from-amber-400 to-amber-600 bg-clip-text text-transparent"> Success Stories</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto mb-12">
                Real SEO results from Gurgaon businesses across all sectors and industries.
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
              {results.map((result, index) => (
                <div key={index} className="text-center">
                  <div className="text-5xl font-bold text-amber-400 mb-2">{result.metric}</div>
                  <div className="text-white font-semibold mb-1">{result.description}</div>
                  <div className="text-slate-400">{result.timeframe}</div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Features Section */}
        <section className="py-20">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Gurgaon SEO
                <span className="bg-gradient-to-r from-amber-400 to-amber-600 bg-clip-text text-transparent"> Services</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto">
                Comprehensive SEO services specifically designed for Gurgaon businesses and local market dynamics.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-w-4xl mx-auto">
              {features.map((feature, index) => (
                <div key={index} className="flex items-center space-x-3">
                  <CheckCircle className="w-6 h-6 text-amber-400 flex-shrink-0" />
                  <span className="text-slate-300">{feature}</span>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Pricing Section */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Gurgaon SEO
                <span className="bg-gradient-to-r from-amber-400 to-amber-600 bg-clip-text text-transparent"> Packages</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto">
                Choose the perfect SEO package for your Gurgaon business growth and online visibility.
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
              {packages.map((pkg, index) => (
                <Card key={index} className={`bg-slate-900/80 border-amber-500/20 hover:border-amber-500/40 transition-all duration-300 relative ${pkg.popular ? 'ring-2 ring-amber-500' : ''}`}>
                  {pkg.popular && (
                    <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                      <span className="bg-amber-500 text-white px-4 py-2 rounded-full text-sm font-semibold">Most Popular</span>
                    </div>
                  )}
                  <CardContent className="p-8">
                    <h3 className="text-2xl font-bold text-white mb-2">{pkg.name}</h3>
                    <p className="text-slate-300 mb-6">{pkg.description}</p>
                    <div className="mb-6">
                      <span className="text-4xl font-bold text-amber-400">{pkg.price}</span>
                      <span className="text-slate-400">{pkg.period}</span>
                    </div>
                    <ul className="space-y-3 mb-8">
                      {pkg.features.map((feature, idx) => (
                        <li key={idx} className="flex items-center text-slate-300">
                          <CheckCircle className="w-4 h-4 text-amber-400 mr-3 flex-shrink-0" />
                          {feature}
                        </li>
                      ))}
                    </ul>
                    <Button asChild className={`w-full ${pkg.popular ? 'bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700' : 'bg-slate-700 hover:bg-slate-600'} text-white`}>
                      <Link to="/quote">Get Started</Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Local Areas Section */}
        <section className="py-20">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Gurgaon Areas We
                <span className="bg-gradient-to-r from-amber-400 to-amber-600 bg-clip-text text-transparent"> Cover</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto">
                Local SEO expertise across all major Gurgaon sectors and business districts.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {['Cyber City', 'DLF Phase 1', 'DLF Phase 2', 'DLF Phase 3', 'DLF Phase 4', 'DLF Phase 5', 'Sector 14', 'MG Road', 'Golf Course Road', 'Sohna Road', 'Old Gurgaon', 'New Gurgaon'].map((area, index) => (
                <Card key={index} className="bg-slate-900/80 border-amber-500/20 hover:border-amber-500/40 transition-all duration-300">
                  <CardContent className="p-6 text-center">
                    <MapPin className="w-8 h-8 text-amber-400 mx-auto mb-4" />
                    <h3 className="text-white font-semibold mb-2">{area} SEO</h3>
                    <Button asChild size="sm" variant="outline" className="border-amber-500 text-amber-400 hover:bg-amber-500 hover:text-white">
                      <Link to="/contact">Get Quote</Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 bg-gradient-to-r from-amber-600 to-amber-500">
          <div className="container mx-auto px-6 text-center">
            <h2 className="text-4xl md:text-5xl font-bold mb-6">Ready to Dominate Gurgaon Search Results?</h2>
            <p className="text-xl mb-8 max-w-3xl mx-auto">
              Join 50+ Gurgaon businesses that trust GOD Digital Marketing for their SEO success. From Cyber City to DLF phases, we deliver first-page rankings.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button asChild size="lg" className="bg-white text-amber-600 hover:bg-slate-100 text-lg px-8 py-4">
                <Link to="/quote">Get Free Gurgaon SEO Audit</Link>
              </Button>
              <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-amber-600 text-lg px-8 py-4">
                <Link to="/contact">Call Gurgaon SEO Expert</Link>
              </Button>
            </div>
          </div>
        </section>
      </main>
      
      <Footer />
    </div>
  );
};

export default SeoServicesGurgaon;
