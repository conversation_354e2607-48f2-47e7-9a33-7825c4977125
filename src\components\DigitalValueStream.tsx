
import React, { useRef, useMemo } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import { Points, PointMaterial, Float } from '@react-three/drei';
import { BufferAttribute, Vector3, Color } from 'three';

// Particle Stream Component
const ParticleStream = () => {
  const pointsRef = useRef<any>();
  const particleCount = 1500; // Reduced for better performance
  
  const positions = useMemo(() => {
    const pos = new Float32Array(particleCount * 3);
    for (let i = 0; i < particleCount; i++) {
      // Create a flowing stream pattern
      const angle = (i / particleCount) * Math.PI * 4;
      const radius = 0.5 + Math.sin(angle) * 0.3;
      pos[i * 3] = Math.cos(angle) * radius; // x
      pos[i * 3 + 1] = (i / particleCount) * 20 - 10; // y (flowing upward)
      pos[i * 3 + 2] = Math.sin(angle) * radius; // z
    }
    return pos;
  }, []);

  const colors = useMemo(() => {
    const cols = new Float32Array(particleCount * 3);
    const tealColor = new Color('#20C997');
    const whiteColor = new Color('#FFFFFF');
    
    for (let i = 0; i < particleCount; i++) {
      const mixFactor = Math.random();
      const color = tealColor.clone().lerp(whiteColor, mixFactor);
      cols[i * 3] = color.r;
      cols[i * 3 + 1] = color.g;
      cols[i * 3 + 2] = color.b;
    }
    return cols;
  }, []);

  useFrame((state) => {
    if (pointsRef.current) {
      const positions = pointsRef.current.geometry.attributes.position.array;
      
      for (let i = 0; i < particleCount; i++) {
        // Flow upward continuously
        positions[i * 3 + 1] += 0.02;
        
        // Reset particles that flow too high
        if (positions[i * 3 + 1] > 10) {
          positions[i * 3 + 1] = -10;
        }
        
        // Add subtle wave motion
        positions[i * 3] += Math.sin(state.clock.elapsedTime + i * 0.01) * 0.002;
        positions[i * 3 + 2] += Math.cos(state.clock.elapsedTime + i * 0.01) * 0.002;
      }
      
      pointsRef.current.geometry.attributes.position.needsUpdate = true;
    }
  });

  return (
    <Points ref={pointsRef} positions={positions}>
      <PointMaterial
        size={0.03}
        vertexColors
        transparent
        opacity={0.8}
        sizeAttenuation
      />
      <bufferAttribute
        attach="geometry-attributes-color"
        args={[colors, 3]}
      />
    </Points>
  );
};

// Currency Symbol Component (using 3D shapes instead of text)
const CurrencySymbol = ({ position, type, delay = 0 }: { position: [number, number, number], type: 'dollar' | 'rupee' | 'euro' | 'pound' | 'yen', delay?: number }) => {
  const meshRef = useRef<any>();

  useFrame((state) => {
    if (meshRef.current) {
      // Continuous upward movement
      meshRef.current.position.y += 0.015;
      
      // Gentle rotation
      meshRef.current.rotation.x += 0.01;
      meshRef.current.rotation.y += 0.008;
      
      // Reset position when too high
      if (meshRef.current.position.y > 10) {
        meshRef.current.position.y = -10;
      }
      
      // Subtle floating motion
      meshRef.current.position.x += Math.sin(state.clock.elapsedTime + delay) * 0.001;
      meshRef.current.position.z += Math.cos(state.clock.elapsedTime + delay) * 0.001;
    }
  });

  // Different geometric shapes for different currencies
  const getGeometry = () => {
    switch (type) {
      case 'dollar':
        return <cylinderGeometry args={[0.3, 0.3, 0.1, 8]} />;
      case 'rupee':
        return <boxGeometry args={[0.4, 0.6, 0.1]} />;
      case 'euro':
        return <torusGeometry args={[0.25, 0.1, 8, 16]} />;
      case 'pound':
        return <sphereGeometry args={[0.25, 8, 6]} />;
      case 'yen':
        return <coneGeometry args={[0.3, 0.6, 6]} />;
      default:
        return <cylinderGeometry args={[0.3, 0.3, 0.1, 8]} />;
    }
  };

  return (
    <Float speed={2} rotationIntensity={0.5} floatIntensity={0.3}>
      <mesh ref={meshRef} position={position}>
        {getGeometry()}
        <meshStandardMaterial 
          color="#0A2647" 
          metalness={0.8}
          roughness={0.2}
          emissive="#1a4b7a"
          emissiveIntensity={0.1}
        />
      </mesh>
    </Float>
  );
};

// Growth Crystal Component
const GrowthCrystal = ({ position }: { position: [number, number, number] }) => {
  const crystalRef = useRef<any>();

  useFrame((state) => {
    if (crystalRef.current) {
      // Slow growth animation
      const scale = 0.8 + Math.sin(state.clock.elapsedTime * 0.3) * 0.2;
      crystalRef.current.scale.set(scale, scale * 1.5, scale);
      
      // Subtle rotation
      crystalRef.current.rotation.y += 0.005;
    }
  });

  return (
    <mesh ref={crystalRef} position={position}>
      <octahedronGeometry args={[0.3, 0]} />
      <meshStandardMaterial
        color="#20C997"
        transparent
        opacity={0.7}
        roughness={0.1}
        metalness={0.3}
        emissive="#20C997"
        emissiveIntensity={0.1}
      />
    </mesh>
  );
};

// Digital Grid Background
const DigitalGrid = () => {
  return (
    <gridHelper 
      args={[20, 20, '#0A2647', '#0A2647']} 
      position={[0, -5, 0]} 
      rotation={[Math.PI / 2, 0, 0]}
    />
  );
};

// Main 3D Scene
const Scene = () => {
  return (
    <>
      {/* Lighting */}
      <ambientLight intensity={0.3} />
      <pointLight position={[10, 10, 10]} intensity={1} color="#20C997" />
      <pointLight position={[-10, -10, -10]} intensity={0.5} color="#FFFFFF" />
      
      {/* Digital Grid Background */}
      <DigitalGrid />
      
      {/* Particle Stream */}
      <ParticleStream />
      
      {/* Currency Symbols */}
      <CurrencySymbol position={[-2, -8, 1]} type="rupee" delay={0} />
      <CurrencySymbol position={[1, -6, -1]} type="dollar" delay={1} />
      <CurrencySymbol position={[-1, -4, 2]} type="euro" delay={2} />
      <CurrencySymbol position={[2, -2, -2]} type="pound" delay={3} />
      <CurrencySymbol position={[0, 0, 0]} type="yen" delay={4} />
      
      {/* Growth Crystals */}
      <GrowthCrystal position={[-4, -2, 3]} />
      <GrowthCrystal position={[4, 1, -3]} />
      <GrowthCrystal position={[-3, 3, 2]} />
      <GrowthCrystal position={[3, -1, 4]} />
    </>
  );
};

// Main Component
const DigitalValueStream = () => {
  return (
    <div className="fixed inset-0 pointer-events-none z-0">
      <Canvas
        camera={{ position: [0, 0, 8], fov: 60 }}
        gl={{ 
          antialias: false, // Disable for performance
          powerPreference: "high-performance",
          alpha: true,
          premultipliedAlpha: false
        }}
        dpr={[1, 1.5]} // Limit pixel ratio for performance
        performance={{
          min: 0.8,
          max: 1,
          debounce: 200
        }}
      >
        <Scene />
      </Canvas>
    </div>
  );
};

export default DigitalValueStream;
