
import React from 'react';
import { Link } from 'react-router-dom';
import { Building2, TrendingUp, Users, CheckCircle, MapPin, Star } from 'lucide-react';
import { Button } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';

const ConnaughtPlaceDigitalMarketing = () => {
  return (
    <div className="min-h-screen bg-slate-900 text-white relative overflow-hidden">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        {/* Hero Section */}
        <section className="pt-32 pb-20">
          <div className="container mx-auto px-6">
            <div className="max-w-4xl mx-auto text-center">
              <div className="inline-flex items-center space-x-2 bg-amber-500/20 border border-amber-500/30 rounded-full px-6 py-2 mb-8">
                <Building2 className="w-4 h-4 text-amber-400" />
                <span className="text-amber-400 font-medium">Connaught Place Digital Marketing</span>
              </div>
              
              <h1 className="text-5xl md:text-6xl font-bold mb-6 leading-tight">
                Digital Marketing for
                <span className="bg-gradient-to-r from-amber-400 to-amber-600 bg-clip-text text-transparent"> Connaught Place Businesses</span>
              </h1>
              
              <p className="text-xl text-slate-300 mb-12 max-w-3xl mx-auto">
                Dominate Delhi's prime commercial hub with targeted digital strategies. 
                Helping CP businesses outrank competitors and capture high-value customers in India's most prestigious business district.
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
                <Button asChild size="lg" className="bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700 text-white text-lg px-8 py-4">
                  <Link to="/quote">Get CP Business Strategy</Link>
                </Button>
                <Button asChild size="lg" variant="outline" className="border-amber-500 text-amber-400 hover:bg-amber-500 hover:text-white text-lg px-8 py-4">
                  <Link to="/case-studies">View CP Success Stories</Link>
                </Button>
              </div>

              {/* CP Specific Stats */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-16">
                <div className="text-center">
                  <div className="text-3xl font-bold text-amber-400 mb-2">150+</div>
                  <div className="text-slate-300">CP Businesses Served</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-amber-400 mb-2">₹2Cr+</div>
                  <div className="text-slate-300">Revenue Generated</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-amber-400 mb-2">400%</div>
                  <div className="text-slate-300">Average Growth</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-amber-400 mb-2">24/7</div>
                  <div className="text-slate-300">Support Available</div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* CP Market Understanding */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="max-w-6xl mx-auto">
              <h2 className="text-4xl font-bold text-center mb-6">Why Connaught Place Businesses Choose Us</h2>
              <p className="text-xl text-slate-300 text-center mb-16 max-w-3xl mx-auto">
                Deep understanding of CP's unique business ecosystem, from heritage brands to modern startups.
              </p>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div className="bg-slate-700/30 border border-amber-500/20 rounded-xl p-6">
                  <MapPin className="w-12 h-12 text-amber-400 mb-4" />
                  <h3 className="text-xl font-semibold mb-3">Local Expertise</h3>
                  <p className="text-slate-300">5+ years helping CP businesses from Outer Circle to Inner Circle, understanding foot traffic patterns, peak hours, and customer demographics.</p>
                </div>
                
                <div className="bg-slate-700/30 border border-amber-500/20 rounded-xl p-6">
                  <TrendingUp className="w-12 h-12 text-amber-400 mb-4" />
                  <h3 className="text-xl font-semibold mb-3">Competitive Edge</h3>
                  <p className="text-slate-300">Stand out in CP's crowded marketplace with data-driven strategies that target high-intent customers visiting Delhi's commercial heart.</p>
                </div>
                
                <div className="bg-slate-700/30 border border-amber-500/20 rounded-xl p-6">
                  <Users className="w-12 h-12 text-amber-400 mb-4" />
                  <h3 className="text-xl font-semibold mb-3">Premium Targeting</h3>
                  <p className="text-slate-300">Reach affluent customers, business executives, and tourists who frequent CP for shopping, dining, and business meetings.</p>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Services for CP Businesses */}
        <section className="py-20">
          <div className="container mx-auto px-6">
            <div className="max-w-6xl mx-auto">
              <h2 className="text-4xl font-bold text-center mb-16">Specialized Services for CP Businesses</h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div className="bg-slate-800/50 border border-amber-500/20 rounded-xl p-6">
                  <h3 className="text-xl font-semibold mb-4 text-amber-400">Local SEO for CP Visibility</h3>
                  <ul className="space-y-3 text-slate-300">
                    <li className="flex items-center"><CheckCircle className="w-4 h-4 text-green-400 mr-2" />Google My Business optimization for CP location</li>
                    <li className="flex items-center"><CheckCircle className="w-4 h-4 text-green-400 mr-2" />Local keyword targeting: "near Connaught Place"</li>
                    <li className="flex items-center"><CheckCircle className="w-4 h-4 text-green-400 mr-2" />Metro station and landmark-based optimization</li>
                    <li className="flex items-center"><CheckCircle className="w-4 h-4 text-green-400 mr-2" />"Best in CP" review and rating management</li>
                  </ul>
                </div>

                <div className="bg-slate-800/50 border border-amber-500/20 rounded-xl p-6">
                  <h3 className="text-xl font-semibold mb-4 text-amber-400">Social Media for CP Brands</h3>
                  <ul className="space-y-3 text-slate-300">
                    <li className="flex items-center"><CheckCircle className="w-4 h-4 text-green-400 mr-2" />Instagram location-based content strategy</li>
                    <li className="flex items-center"><CheckCircle className="w-4 h-4 text-green-400 mr-2" />Facebook events for CP store promotions</li>
                    <li className="flex items-center"><CheckCircle className="w-4 h-4 text-green-400 mr-2" />LinkedIn networking for B2B CP businesses</li>
                    <li className="flex items-center"><CheckCircle className="w-4 h-4 text-green-400 mr-2" />Influencer partnerships with Delhi bloggers</li>
                  </ul>
                </div>

                <div className="bg-slate-800/50 border border-amber-500/20 rounded-xl p-6">
                  <h3 className="text-xl font-semibold mb-4 text-amber-400">PPC Advertising for CP Market</h3>
                  <ul className="space-y-3 text-slate-300">
                    <li className="flex items-center"><CheckCircle className="w-4 h-4 text-green-400 mr-2" />Geo-targeted ads for CP vicinity</li>
                    <li className="flex items-center"><CheckCircle className="w-4 h-4 text-green-400 mr-2" />Shopping campaigns for retail stores</li>
                    <li className="flex items-center"><CheckCircle className="w-4 h-4 text-green-400 mr-2" />Time-based bidding for peak CP hours</li>
                    <li className="flex items-center"><CheckCircle className="w-4 h-4 text-green-400 mr-2" />Mobile-first campaigns for on-the-go customers</li>
                  </ul>
                </div>

                <div className="bg-slate-800/50 border border-amber-500/20 rounded-xl p-6">
                  <h3 className="text-xl font-semibold mb-4 text-amber-400">Website Optimization</h3>
                  <ul className="space-y-3 text-slate-300">
                    <li className="flex items-center"><CheckCircle className="w-4 h-4 text-green-400 mr-2" />Mobile-responsive design for smartphone users</li>
                    <li className="flex items-center"><CheckCircle className="w-4 h-4 text-green-400 mr-2" />Fast loading speeds for busy customers</li>
                    <li className="flex items-center"><CheckCircle className="w-4 h-4 text-green-400 mr-2" />Online booking and reservation systems</li>
                    <li className="flex items-center"><CheckCircle className="w-4 h-4 text-green-400 mr-2" />Multi-language support (Hindi/English)</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* CP Business Types */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="max-w-6xl mx-auto">
              <h2 className="text-4xl font-bold text-center mb-16">CP Business Categories We Serve</h2>
              
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
                <div className="bg-slate-700/30 border border-amber-500/20 rounded-lg p-4 text-center">
                  <div className="font-semibold text-amber-400 mb-2">Retail Stores</div>
                  <div className="text-sm text-slate-300">Fashion, Electronics, Books</div>
                </div>
                <div className="bg-slate-700/30 border border-amber-500/20 rounded-lg p-4 text-center">
                  <div className="font-semibold text-amber-400 mb-2">Restaurants</div>
                  <div className="text-sm text-slate-300">Fine Dining, Cafes, Street Food</div>
                </div>
                <div className="bg-slate-700/30 border border-amber-500/20 rounded-lg p-4 text-center">
                  <div className="font-semibold text-amber-400 mb-2">Corporate Offices</div>
                  <div className="text-sm text-slate-300">Consulting, Finance, Legal</div>
                </div>
                <div className="bg-slate-700/30 border border-amber-500/20 rounded-lg p-4 text-center">
                  <div className="font-semibold text-amber-400 mb-2">Entertainment</div>
                  <div className="text-sm text-slate-300">Theaters, Gaming, Events</div>
                </div>
                <div className="bg-slate-700/30 border border-amber-500/20 rounded-lg p-4 text-center">
                  <div className="font-semibold text-amber-400 mb-2">Healthcare</div>
                  <div className="text-sm text-slate-300">Clinics, Dental, Wellness</div>
                </div>
                <div className="bg-slate-700/30 border border-amber-500/20 rounded-lg p-4 text-center">
                  <div className="font-semibold text-amber-400 mb-2">Education</div>
                  <div className="text-sm text-slate-300">Coaching, Languages, Skills</div>
                </div>
                <div className="bg-slate-700/30 border border-amber-500/20 rounded-lg p-4 text-center">
                  <div className="font-semibold text-amber-400 mb-2">Beauty & Wellness</div>
                  <div className="text-sm text-slate-300">Salons, Spas, Fitness</div>
                </div>
                <div className="bg-slate-700/30 border border-amber-500/20 rounded-lg p-4 text-center">
                  <div className="font-semibold text-amber-400 mb-2">Professional Services</div>
                  <div className="text-sm text-slate-300">CA, Architects, Designers</div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 bg-gradient-to-r from-amber-600 to-amber-500">
          <div className="container mx-auto px-6 text-center">
            <h2 className="text-4xl md:text-5xl font-bold mb-6">Ready to Dominate Connaught Place?</h2>
            <p className="text-xl mb-8 max-w-3xl mx-auto">
              Join 150+ successful CP businesses who trust GOD Digital Marketing to drive growth in Delhi's premier commercial hub.
            </p>
            <Button asChild size="lg" className="bg-white text-amber-600 hover:bg-slate-100 text-lg px-8 py-4">
              <Link to="/quote">Get Your CP Digital Strategy</Link>
            </Button>
          </div>
        </section>
      </main>
      
      <Footer />
    </div>
  );
};

export default ConnaughtPlaceDigitalMarketing;
