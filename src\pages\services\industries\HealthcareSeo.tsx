import React from 'react';
import { Link } from 'react-router-dom';
import { Heart, Search, TrendingUp, Users, CheckCircle, Building, Star, Crown, Target, Zap, ArrowRight } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';
import Sidebar from '@/components/Sidebar';

const HealthcareSeo = () => {
  const stats = [
    {
      metric: '1,200+',
      description: 'Healthcare Practices Optimized',
      detail: 'Across all specialties'
    },
    {
      metric: '1,420%',
      description: 'Average Patient Growth',
      detail: 'For healthcare clients'
    },
    {
      metric: '₹85Cr+',
      description: 'Healthcare Revenue Generated',
      detail: 'Through SEO campaigns'
    },
    {
      metric: '96%',
      description: 'Medical Keywords Ranking',
      detail: 'Top 10 positions achieved'
    }
  ];

  const achievements = [
    'Top Healthcare SEO Company in India',
    'Medical Practice SEO Specialists',
    'Hospital SEO Optimization Experts',
    'Dental SEO Leaders',
    'Clinic SEO Champions',
    'Medical Tourism SEO Pioneers'
  ];

  const healthcareSpecializations = [
    {
      type: 'Medical Practice SEO',
      description: 'Comprehensive SEO for individual medical practices and specialists',
      icon: Building,
      features: ['Doctor Profile Optimization', 'Medical Service Pages', 'Patient Review Management', 'Local Medical SEO', 'Medical Schema Markup', 'Appointment Booking Optimization']
    },
    {
      type: 'Hospital & Clinic SEO',
      description: 'Large-scale SEO solutions for hospitals and multi-specialty clinics',
      icon: Star,
      features: ['Department Page Optimization', 'Medical Staff Profiles', 'Treatment Page SEO', 'Emergency Services SEO', 'Medical Equipment Pages', 'Patient Portal Optimization']
    },
    {
      type: 'Medical Tourism SEO',
      description: 'International SEO for medical tourism and healthcare destinations',
      icon: Crown,
      features: ['International Patient SEO', 'Medical Package Optimization', 'Treatment Cost Pages', 'Medical Visa Information', 'Recovery Facility SEO', 'Medical Travel SEO']
    },
    {
      type: 'Healthcare Compliance SEO',
      description: 'HIPAA-compliant SEO strategies for healthcare organizations',
      icon: Target,
      features: ['HIPAA-Compliant Content', 'Medical Disclaimer Optimization', 'Patient Privacy SEO', 'Medical Certification Pages', 'Regulatory Compliance SEO', 'Medical Ethics Content']
    }
  ];

  const specialties = [
    { name: 'Cardiology', clients: '180+', growth: '1,650%' },
    { name: 'Orthopedics', clients: '220+', growth: '1,420%' },
    { name: 'Dermatology', clients: '160+', growth: '1,800%' },
    { name: 'Dentistry', clients: '280+', growth: '1,350%' },
    { name: 'Gynecology', clients: '140+', growth: '1,550%' },
    { name: 'Pediatrics', clients: '120+', growth: '1,480%' }
  ];

  const caseStudies = [
    {
      client: 'FH Wellness',
      industry: 'Corporate Wellness',
      challenge: 'Healthcare company needed to establish authority and reach corporate clients',
      result: '2,100% traffic growth in 5 months',
      metrics: ['320+ healthcare keywords in top 5', '₹1.8Cr+ revenue generated', '380% increase in corporate inquiries']
    },
    {
      client: 'Golden Rose Med Spa',
      industry: 'Medical Aesthetics',
      challenge: 'Luxury med spa needed to attract high-end clients in competitive market',
      result: '1,600% traffic increase in 4 months',
      metrics: ['280+ aesthetic keywords in top 3', '₹2.2Cr+ revenue generated', '520% increase in treatment bookings']
    },
    {
      client: 'Multi-Specialty Hospital',
      industry: 'Hospital & Healthcare',
      challenge: 'Large hospital needed to improve online visibility for all departments',
      result: '1,850% patient inquiry increase',
      metrics: ['450+ medical keywords ranking', '₹3.5Cr+ revenue boost', '280% emergency service calls']
    }
  ];

  return (
    <div className="min-h-screen bg-slate-900 relative">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        <div className="container mx-auto px-6 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            <div className="lg:col-span-3">
              <section className="pt-24 pb-16">
                <div className="max-w-4xl">
                  <div className="inline-flex items-center space-x-2 bg-red-500/20 border border-red-500/30 rounded-full px-6 py-2 mb-8">
                    <Heart className="w-4 h-4 text-red-400" />
                    <span className="text-red-400 font-medium">Healthcare SEO • Medical Practice Optimization Excellence</span>
                  </div>
                  
                  <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
                    #1 Healthcare SEO Company in
                    <span className="bg-gradient-to-r from-red-400 to-red-600 bg-clip-text text-transparent"> India</span>
                  </h1>
                  
                  <p className="text-xl text-slate-300 mb-8">
                    Premier healthcare SEO services offering comprehensive search engine optimization solutions for medical practices including hospital SEO, clinic optimization, doctor SEO, medical practice marketing, and healthcare digital marketing. Our healthcare SEO company provides professional SEO services with medical content optimization, patient acquisition strategies, healthcare compliance SEO, and medical tourism optimization. Serving 1,200+ healthcare practices across all specialties with proven ₹85Cr+ revenue generation and 1,420% average patient growth for healthcare clients through strategic search engine optimization and medical digital marketing excellence.
                  </p>
                  
                  <div className="flex flex-col sm:flex-row gap-4 mb-8">
                    <Button asChild size="lg" className="bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white text-lg px-8 py-4">
                      <Link to="/quote">Get Free Healthcare SEO Audit</Link>
                    </Button>
                    <Button asChild size="lg" variant="outline" className="border-red-500 text-red-400 hover:bg-red-500 hover:text-white text-lg px-8 py-4">
                      <Link to="/contact">Call Healthcare SEO Experts: +91-**********</Link>
                    </Button>
                  </div>

                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                    {achievements.map((achievement, index) => (
                      <div key={index} className="flex items-center justify-center space-x-2 bg-slate-800/50 rounded-lg p-3">
                        <CheckCircle className="w-4 h-4 text-red-400 flex-shrink-0" />
                        <span className="text-slate-300">{achievement}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </section>

              <section className="mb-16 bg-slate-800/50 rounded-2xl p-8">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Healthcare SEO
                    <span className="bg-gradient-to-r from-red-400 to-red-600 bg-clip-text text-transparent"> Success Metrics</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Measurable healthcare SEO results from medical practices across all specialties and healthcare sectors.
                  </p>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
                  {stats.map((stat, index) => (
                    <div key={index} className="text-center">
                      <div className="text-4xl font-bold text-red-400 mb-2">{stat.metric}</div>
                      <div className="text-white font-semibold mb-1">{stat.description}</div>
                      <div className="text-slate-400">{stat.detail}</div>
                    </div>
                  ))}
                </div>
              </section>

              <section className="mb-16">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Healthcare SEO Strategies We
                    <span className="bg-gradient-to-r from-red-400 to-red-600 bg-clip-text text-transparent"> Master</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Specialized SEO strategies designed for healthcare success across all medical specialties and practice types.
                  </p>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  {healthcareSpecializations.map((spec, index) => (
                    <Card key={index} className="bg-slate-900/80 border-red-500/20 hover:border-red-500/40 transition-all duration-300">
                      <CardContent className="p-8">
                        <div className="w-12 h-12 bg-gradient-to-r from-red-500 to-red-600 rounded-lg flex items-center justify-center mb-6">
                          <spec.icon className="w-6 h-6 text-white" />
                        </div>
                        <h3 className="text-xl font-bold text-white mb-4">{spec.type}</h3>
                        <p className="text-slate-300 mb-6">{spec.description}</p>
                        <ul className="space-y-2">
                          {spec.features.map((feature, idx) => (
                            <li key={idx} className="flex items-center text-slate-400 text-sm">
                              <CheckCircle className="w-3 h-3 text-red-400 mr-2 flex-shrink-0" />
                              {feature}
                            </li>
                          ))}
                        </ul>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>

              {/* Medical Specialties */}
              <section className="mb-16 bg-slate-800/30 rounded-2xl p-8">
                <h3 className="text-2xl font-bold text-white mb-6 text-center">
                  Medical Specialty Expertise
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-6">
                  {specialties.map((specialty, index) => (
                    <div key={index} className="text-center bg-slate-900/50 rounded-lg p-4">
                      <h4 className="text-red-400 font-semibold mb-2">{specialty.name}</h4>
                      <div className="text-2xl font-bold text-white mb-1">{specialty.clients}</div>
                      <div className="text-slate-400 text-sm mb-2">Practices Served</div>
                      <div className="text-green-400 font-semibold text-sm">{specialty.growth} avg growth</div>
                    </div>
                  ))}
                </div>
              </section>

              {/* Case Studies */}
              <section className="mb-16">
                <h3 className="text-2xl font-bold text-white mb-8 text-center">
                  Healthcare Success Stories
                </h3>
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                  {caseStudies.map((study, index) => (
                    <Card key={index} className="bg-slate-900/60 border-red-500/20 hover:border-red-500/40 transition-all duration-300">
                      <CardContent className="p-6">
                        <h4 className="text-red-400 font-semibold mb-2">{study.client}</h4>
                        <p className="text-slate-400 text-sm mb-3">{study.industry}</p>
                        <p className="text-slate-300 text-sm mb-4">{study.challenge}</p>
                        <div className="bg-slate-800/50 rounded p-3 mb-4">
                          <div className="text-green-400 font-semibold mb-2">{study.result}</div>
                          <ul className="space-y-1">
                            {study.metrics.map((metric, idx) => (
                              <li key={idx} className="text-slate-400 text-xs flex items-center">
                                <CheckCircle className="w-3 h-3 text-red-400 mr-2 flex-shrink-0" />
                                {metric}
                              </li>
                            ))}
                          </ul>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>

              {/* Internal Linking Section */}
              <section className="mb-16 bg-slate-800/30 rounded-2xl p-8">
                <h3 className="text-2xl font-bold text-white mb-6 text-center">
                  Complete Digital Marketing Solutions for Healthcare
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  <Link to="/services/ppc" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-red-400 font-semibold mb-2 group-hover:text-red-300">Healthcare Google Ads</h4>
                    <p className="text-slate-400 text-sm">PPC advertising for medical practices and patient acquisition</p>
                  </Link>
                  <Link to="/services/social-media" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-red-400 font-semibold mb-2 group-hover:text-red-300">Healthcare Social Media</h4>
                    <p className="text-slate-400 text-sm">Social media marketing for medical practices and hospitals</p>
                  </Link>
                  <Link to="/services/content" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-red-400 font-semibold mb-2 group-hover:text-red-300">Medical Content Marketing</h4>
                    <p className="text-slate-400 text-sm">Healthcare content creation and medical blog writing</p>
                  </Link>
                  <Link to="/services/email" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-red-400 font-semibold mb-2 group-hover:text-red-300">Healthcare Email Marketing</h4>
                    <p className="text-slate-400 text-sm">Patient communication and healthcare email campaigns</p>
                  </Link>
                  <Link to="/services/web-development" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-red-400 font-semibold mb-2 group-hover:text-red-300">Medical Website Development</h4>
                    <p className="text-slate-400 text-sm">Healthcare website design and medical portal development</p>
                  </Link>
                  <Link to="/portfolio" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-red-400 font-semibold mb-2 group-hover:text-red-300">Healthcare Success Stories</h4>
                    <p className="text-slate-400 text-sm">See proven results from our healthcare clients</p>
                  </Link>
                </div>
              </section>

              <section className="bg-gradient-to-r from-red-600 to-red-500 rounded-2xl p-8 text-center">
                <h2 className="text-3xl md:text-5xl font-bold mb-6">Ready to Grow Your Medical Practice?</h2>
                <p className="text-xl mb-8">
                  Join 1,200+ healthcare practices that trust GOD Digital Marketing for SEO success. Proven strategies that deliver 1,420% patient growth and ₹85Cr+ revenue generation.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button asChild size="lg" className="bg-white text-red-600 hover:bg-slate-100 text-lg px-8 py-4">
                    <Link to="/quote">Get Free Healthcare SEO Audit</Link>
                  </Button>
                  <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-red-600 text-lg px-8 py-4">
                    <Link to="/contact">Call Healthcare SEO Experts: +91-**********</Link>
                  </Button>
                </div>
              </section>
            </div>

            <div className="lg:col-span-1">
              <Sidebar currentService="seo" />
            </div>
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
};

export default HealthcareSeo;
