import React from 'react';
import { Link } from 'react-router-dom';
import { ArrowRight, Zap, TrendingUp, Users, CheckCircle, Mail, Target, Settings } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';

const MarketingAutomationServices = () => {
  const features = [
    'Email Marketing Automation & Drip Campaigns',
    'Lead Scoring & Nurturing Workflows',
    'Customer Journey Mapping & Automation',
    'Social Media Automation & Scheduling',
    'CRM Integration & Data Synchronization',
    'Behavioral Trigger Campaigns',
    'Personalized Content Delivery',
    'Marketing Analytics & Attribution',
    'A/B Testing & Campaign Optimization',
    'Multi-Channel Campaign Orchestration'
  ];

  const packages = [
    {
      name: 'Automation Starter',
      price: '₹35,000',
      period: '/month',
      description: 'Perfect for businesses starting marketing automation',
      features: [
        'Email Marketing Automation',
        'Basic Lead Nurturing',
        'CRM Integration Setup',
        'Simple Workflow Creation',
        'Monthly Performance Reports'
      ]
    },
    {
      name: 'Automation Pro',
      price: '₹65,000',
      period: '/month',
      description: 'Comprehensive automation for growing businesses',
      features: [
        'Everything in Starter',
        'Advanced Lead Scoring',
        'Multi-Channel Campaigns',
        'Behavioral Triggers',
        'Customer Journey Mapping',
        'Weekly Optimization Reviews'
      ],
      popular: true
    },
    {
      name: 'Automation Enterprise',
      price: '₹110,000',
      period: '/month',
      description: 'Enterprise automation for large-scale marketing',
      features: [
        'Everything in Pro',
        'Custom Automation Development',
        'Advanced Analytics & Attribution',
        'Multi-Brand Campaign Management',
        'Dedicated Automation Specialist',
        'Real-time Optimization'
      ]
    }
  ];

  const results = [
    {
      metric: '300%',
      description: 'Increase in lead conversion',
      timeframe: 'through automation'
    },
    {
      metric: '60%',
      description: 'Time savings achieved',
      timeframe: 'in marketing tasks'
    },
    {
      metric: '₹3Cr+',
      description: 'Revenue generated',
      timeframe: 'through automated campaigns'
    }
  ];

  const automationTypes = [
    {
      type: 'Email Marketing Automation',
      description: 'Automated email sequences that nurture leads and drive conversions',
      icon: Mail,
      benefits: ['Drip campaigns', 'Welcome sequences', 'Abandoned cart recovery', 'Re-engagement campaigns']
    },
    {
      type: 'Lead Nurturing Automation',
      description: 'Intelligent lead scoring and nurturing based on behavior and engagement',
      icon: Target,
      benefits: ['Lead scoring', 'Behavioral triggers', 'Personalized content', 'Sales handoff automation']
    },
    {
      type: 'Customer Journey Automation',
      description: 'End-to-end customer journey orchestration across all touchpoints',
      icon: Settings,
      benefits: ['Journey mapping', 'Cross-channel coordination', 'Lifecycle marketing', 'Retention automation']
    }
  ];

  return (
    <div className="min-h-screen bg-slate-900 relative">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        {/* Hero Section */}
        <section className="pt-32 pb-20">
          <div className="container mx-auto px-6">
            <div className="max-w-4xl mx-auto text-center">
              <div className="inline-flex items-center space-x-2 bg-cyan-500/20 border border-cyan-500/30 rounded-full px-6 py-2 mb-8">
                <Zap className="w-4 h-4 text-cyan-400" />
                <span className="text-cyan-400 font-medium">Marketing Automation • AI-Powered Campaigns</span>
              </div>
              
              <h1 className="text-5xl md:text-6xl font-bold mb-6 leading-tight">
                Marketing Automation That
                <span className="bg-gradient-to-r from-cyan-400 to-cyan-600 bg-clip-text text-transparent"> Converts Leads Automatically</span>
              </h1>
              
              <p className="text-xl text-slate-300 mb-12 max-w-3xl mx-auto">
                Expert marketing automation services that nurture leads, increase conversions, and scale your marketing efforts. With AI-powered workflows and 7 years of international experience, we've generated ₹3Cr+ revenue through automated marketing campaigns.
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button asChild size="lg" className="bg-gradient-to-r from-cyan-500 to-cyan-600 hover:from-cyan-600 hover:to-cyan-700 text-white text-lg px-8 py-4">
                  <Link to="/quote">Get Automation Strategy</Link>
                </Button>
                <Button asChild size="lg" variant="outline" className="border-cyan-500 text-cyan-400 hover:bg-cyan-500 hover:text-white text-lg px-8 py-4">
                  <Link to="/case-studies">View Automation Results</Link>
                </Button>
              </div>
            </div>
          </div>
        </section>

        {/* Results Section */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Marketing Automation
                <span className="bg-gradient-to-r from-cyan-400 to-cyan-600 bg-clip-text text-transparent"> Performance Results</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto mb-12">
                Real results from marketing automation campaigns across industries and international markets.
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
              {results.map((result, index) => (
                <div key={index} className="text-center">
                  <div className="text-5xl font-bold text-cyan-400 mb-2">{result.metric}</div>
                  <div className="text-white font-semibold mb-1">{result.description}</div>
                  <div className="text-slate-400">{result.timeframe}</div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Automation Types Section */}
        <section className="py-20">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Marketing Automation Solutions We
                <span className="bg-gradient-to-r from-cyan-400 to-cyan-600 bg-clip-text text-transparent"> Implement</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto">
                Comprehensive marketing automation solutions that streamline your marketing and drive results.
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-20">
              {automationTypes.map((automation, index) => (
                <Card key={index} className="bg-slate-900/80 border-cyan-500/20 hover:border-cyan-500/40 transition-all duration-300">
                  <CardContent className="p-8">
                    <div className="w-12 h-12 bg-gradient-to-r from-cyan-500 to-cyan-600 rounded-lg flex items-center justify-center mb-6">
                      <automation.icon className="w-6 h-6 text-white" />
                    </div>
                    <h3 className="text-xl font-bold text-white mb-4">{automation.type}</h3>
                    <p className="text-slate-300 mb-6">{automation.description}</p>
                    <ul className="space-y-2">
                      {automation.benefits.map((benefit, idx) => (
                        <li key={idx} className="flex items-center text-slate-400 text-sm">
                          <CheckCircle className="w-3 h-3 text-cyan-400 mr-2 flex-shrink-0" />
                          {benefit}
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Features Section */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Marketing Automation
                <span className="bg-gradient-to-r from-cyan-400 to-cyan-600 bg-clip-text text-transparent"> Features</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto">
                Complete marketing automation services that maximize your lead conversion and customer retention.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-w-4xl mx-auto">
              {features.map((feature, index) => (
                <div key={index} className="flex items-center space-x-3">
                  <CheckCircle className="w-6 h-6 text-cyan-400 flex-shrink-0" />
                  <span className="text-slate-300">{feature}</span>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Pricing Section */}
        <section className="py-20">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Marketing Automation
                <span className="bg-gradient-to-r from-cyan-400 to-cyan-600 bg-clip-text text-transparent"> Investment</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto">
                Choose the perfect automation package to scale your marketing and maximize conversions.
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
              {packages.map((pkg, index) => (
                <Card key={index} className={`bg-slate-900/80 border-cyan-500/20 hover:border-cyan-500/40 transition-all duration-300 relative ${pkg.popular ? 'ring-2 ring-cyan-500' : ''}`}>
                  {pkg.popular && (
                    <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                      <span className="bg-cyan-500 text-white px-4 py-2 rounded-full text-sm font-semibold">Most Popular</span>
                    </div>
                  )}
                  <CardContent className="p-8">
                    <h3 className="text-2xl font-bold text-white mb-2">{pkg.name}</h3>
                    <p className="text-slate-300 mb-6">{pkg.description}</p>
                    <div className="mb-6">
                      <span className="text-4xl font-bold text-cyan-400">{pkg.price}</span>
                      <span className="text-slate-400">{pkg.period}</span>
                    </div>
                    <ul className="space-y-3 mb-8">
                      {pkg.features.map((feature, idx) => (
                        <li key={idx} className="flex items-center text-slate-300">
                          <CheckCircle className="w-4 h-4 text-cyan-400 mr-3 flex-shrink-0" />
                          {feature}
                        </li>
                      ))}
                    </ul>
                    <Button asChild className={`w-full ${pkg.popular ? 'bg-gradient-to-r from-cyan-500 to-cyan-600 hover:from-cyan-600 hover:to-cyan-700' : 'bg-slate-700 hover:bg-slate-600'} text-white`}>
                      <Link to="/quote">Get Started</Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 bg-gradient-to-r from-cyan-600 to-cyan-500">
          <div className="container mx-auto px-6 text-center">
            <h2 className="text-4xl md:text-5xl font-bold mb-6">Ready to Automate Your Marketing?</h2>
            <p className="text-xl mb-8 max-w-3xl mx-auto">
              Join businesses generating ₹3Cr+ revenue through strategic marketing automation. Scale your marketing with AI-powered workflows and proven strategies.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button asChild size="lg" className="bg-white text-cyan-600 hover:bg-slate-100 text-lg px-8 py-4">
                <Link to="/quote">Get Automation Strategy</Link>
              </Button>
              <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-cyan-600 text-lg px-8 py-4">
                <Link to="/contact">Book Automation Consultation</Link>
              </Button>
            </div>
          </div>
        </section>
      </main>
      
      <Footer />
    </div>
  );
};

export default MarketingAutomationServices;
