
import React, { useState } from 'react';
import { Phone, Mail, MapPin, Clock, Send } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import DigitalValueStream from '@/components/DigitalValueStream';
import { toast } from '@/hooks/use-toast';
import { Helmet } from 'react-helmet-async';

const Contact = () => {
  // Advanced SEO Optimization Data - Contact Page
  const primaryKeyword = "contact GOD Digital Marketing";
  const secondaryKeywords = [
    "digital marketing agency contact",
    "SEO services contact",
    "Nitin Tyagi contact",
    "international SEO consultant contact",
    "digital marketing expert contact",
    "GOD Digital Marketing phone number"
  ];

  // LSI Keywords for H2/H3 headings
  const lsiKeywords = [
    "digital marketing agency contact",
    "SEO services inquiry",
    "international SEO consultation",
    "digital marketing consultation",
    "professional SEO contact",
    "digital marketing expert consultation",
    "business growth consultation",
    "digital marketing project inquiry",
    "SEO strategy consultation",
    "international marketing contact"
  ];

  // Generate comprehensive keywords for meta tags
  const allKeywords = [
    primaryKeyword,
    ...secondaryKeywords,
    ...lsiKeywords,
    "best digital marketing agency contact",
    "top SEO consultant contact",
    "professional digital marketing contact",
    "GOD Digital Marketing contact details",
    "Nitin Tyagi digital marketing contact"
  ].join(", ");

  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    company: '',
    service: '',
    budget: '',
    message: ''
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    toast({
      title: "Message Sent!",
      description: "Thank you for contacting us. We'll get back to you within 24 hours.",
    });
    setFormData({
      name: '',
      email: '',
      phone: '',
      company: '',
      service: '',
      budget: '',
      message: ''
    });
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const contactInfo = [
    {
      icon: Phone,
      title: 'Phone',
      details: ['+91 8708577598'],
      description: 'Available Mon-Fri, 9AM-6PM IST'
    },
    {
      icon: Mail,
      title: 'Email',
      details: ['<EMAIL>'],
      description: 'We respond within 2 hours'
    },
    {
      icon: MapPin,
      title: 'Address',
      details: ['India'],
      description: 'Serving clients globally'
    },
    {
      icon: Clock,
      title: 'Business Hours',
      details: ['Monday - Friday: 9AM - 6PM', 'Saturday: 10AM - 4PM'],
      description: 'Sunday: Closed'
    }
  ];

  return (
    <div className="min-h-screen bg-slate-900 text-white relative overflow-hidden">
      {/* Advanced SEO Meta Tags - Contact Page Optimization */}
      <Helmet>
        {/* Primary Meta Tags - Target Keywords Integration */}
        <title>Contact GOD Digital Marketing | Digital Marketing Agency Contact | Nitin Tyagi</title>
        <meta name="description" content="Contact GOD Digital Marketing - Get in touch with Nitin Tyagi, international SEO consultant and digital marketing expert. Professional digital marketing agency contact for SEO services, PPC management, social media marketing. Phone: +91-8708577598, Email: <EMAIL>. Free consultation available." />
        <meta name="keywords" content={allKeywords} />
        <link rel="canonical" href="https://goddigitalmarketing.com/contact" />

        {/* Advanced SEO Meta Tags */}
        <meta name="author" content="Nitin Tyagi" />
        <meta name="robots" content="index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1" />

        {/* Open Graph Meta Tags */}
        <meta property="og:title" content="Contact GOD Digital Marketing | Digital Marketing Agency Contact" />
        <meta property="og:description" content="Contact GOD Digital Marketing - Get in touch with Nitin Tyagi, international SEO consultant. Professional digital marketing agency contact for expert services." />
        <meta property="og:type" content="website" />
        <meta property="og:url" content="https://goddigitalmarketing.com/contact" />
        <meta property="og:image" content="https://goddigitalmarketing.com/contact-god-digital-marketing.jpg" />

        {/* Schema.org Structured Data - ContactPage */}
        <script type="application/ld+json">
          {JSON.stringify({
            "@context": "https://schema.org",
            "@type": "ContactPage",
            "name": "Contact GOD Digital Marketing",
            "description": "Contact page for GOD Digital Marketing - International digital marketing agency founded by Nitin Tyagi",
            "url": "https://goddigitalmarketing.com/contact",
            "mainEntity": {
              "@type": "Organization",
              "name": "GOD Digital Marketing",
              "founder": {
                "@type": "Person",
                "name": "Nitin Tyagi"
              },
              "contactPoint": [
                {
                  "@type": "ContactPoint",
                  "telephone": "+91-8708577598",
                  "contactType": "customer service",
                  "email": "<EMAIL>",
                  "availableLanguage": ["English", "Hindi"],
                  "hoursAvailable": {
                    "@type": "OpeningHoursSpecification",
                    "dayOfWeek": ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"],
                    "opens": "09:00",
                    "closes": "18:00"
                  }
                }
              ],
              "address": {
                "@type": "PostalAddress",
                "addressCountry": "India"
              },
              "hasOfferCatalog": {
                "@type": "OfferCatalog",
                "name": "Digital Marketing Services",
                "itemListElement": [
                  {
                    "@type": "Offer",
                    "itemOffered": {
                      "@type": "Service",
                      "name": "SEO Services"
                    }
                  },
                  {
                    "@type": "Offer",
                    "itemOffered": {
                      "@type": "Service",
                      "name": "PPC Management"
                    }
                  },
                  {
                    "@type": "Offer",
                    "itemOffered": {
                      "@type": "Service",
                      "name": "Social Media Marketing"
                    }
                  }
                ]
              }
            }
          })}
        </script>
      </Helmet>

      <DigitalValueStream />
      <Navigation />
      
      <main className="relative z-10">
        {/* Hero Section */}
        <section className="pt-32 pb-20">
          <div className="container mx-auto px-6">
            <div className="max-w-4xl mx-auto text-center">
              <h1 className="text-5xl md:text-6xl font-bold mb-6 leading-tight">
                Contact Digital Marketing
                <span className="bg-gradient-to-r from-amber-400 to-amber-600 bg-clip-text text-transparent"> Experts</span>
              </h1>
              <p className="text-xl text-slate-300 mb-12 max-w-3xl mx-auto">
                Contact GOD Digital Marketing for professional digital marketing services including SEO services, Google Ads management, social media marketing, content marketing, email marketing, and web development. Our digital marketing agency provides comprehensive digital marketing solutions with proven results. Get in touch with our digital marketing experts for SEO consultation, PPC management, social media strategy, and complete digital marketing services.
              </p>
            </div>
          </div>
        </section>

        {/* Contact Form and Info */}
        <section className="py-20">
          <div className="container mx-auto px-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
              {/* Contact Form */}
              <Card className="bg-slate-900/80 border-amber-500/20">
                <CardContent className="p-8">
                  <h2 className="text-3xl font-bold text-white mb-6">Send us a Message</h2>
                  <form onSubmit={handleSubmit} className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-slate-300 mb-2">Full Name *</label>
                        <Input
                          value={formData.name}
                          onChange={(e) => handleInputChange('name', e.target.value)}
                          className="bg-slate-800 border-slate-600 text-white"
                          placeholder="John Doe"
                          required
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-slate-300 mb-2">Email Address *</label>
                        <Input
                          type="email"
                          value={formData.email}
                          onChange={(e) => handleInputChange('email', e.target.value)}
                          className="bg-slate-800 border-slate-600 text-white"
                          placeholder="<EMAIL>"
                          required
                        />
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-slate-300 mb-2">Phone Number</label>
                        <Input
                          value={formData.phone}
                          onChange={(e) => handleInputChange('phone', e.target.value)}
                          className="bg-slate-800 border-slate-600 text-white"
                          placeholder="+****************"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-slate-300 mb-2">Company Name</label>
                        <Input
                          value={formData.company}
                          onChange={(e) => handleInputChange('company', e.target.value)}
                          className="bg-slate-800 border-slate-600 text-white"
                          placeholder="Your Company"
                        />
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-slate-300 mb-2">Service Interested In</label>
                        <Select value={formData.service} onValueChange={(value) => handleInputChange('service', value)}>
                          <SelectTrigger className="bg-slate-800 border-slate-600 text-white">
                            <SelectValue placeholder="Select a service" />
                          </SelectTrigger>
                          <SelectContent className="bg-slate-800 border-slate-600">
                            <SelectItem value="seo">SEO Services</SelectItem>
                            <SelectItem value="ppc">PPC Advertising</SelectItem>
                            <SelectItem value="social">Social Media Marketing</SelectItem>
                            <SelectItem value="email">Email Marketing</SelectItem>
                            <SelectItem value="content">Content Marketing</SelectItem>
                            <SelectItem value="web-design">Web Design</SelectItem>
                            <SelectItem value="all">All Services</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-slate-300 mb-2">Monthly Budget</label>
                        <Select value={formData.budget} onValueChange={(value) => handleInputChange('budget', value)}>
                          <SelectTrigger className="bg-slate-800 border-slate-600 text-white">
                            <SelectValue placeholder="Select budget range" />
                          </SelectTrigger>
                          <SelectContent className="bg-slate-800 border-slate-600">
                            <SelectItem value="under-5k">Under $5,000</SelectItem>
                            <SelectItem value="5k-10k">$5,000 - $10,000</SelectItem>
                            <SelectItem value="10k-25k">$10,000 - $25,000</SelectItem>
                            <SelectItem value="25k-50k">$25,000 - $50,000</SelectItem>
                            <SelectItem value="over-50k">Over $50,000</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-slate-300 mb-2">Project Details *</label>
                      <Textarea
                        value={formData.message}
                        onChange={(e) => handleInputChange('message', e.target.value)}
                        className="bg-slate-800 border-slate-600 text-white min-h-[120px]"
                        placeholder="Tell us about your business goals, current challenges, and what you're looking to achieve..."
                        required
                      />
                    </div>

                    <Button type="submit" className="w-full bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700 text-white text-lg py-3">
                      <Send className="w-5 h-5 mr-2" />
                      Send Message
                    </Button>
                  </form>
                </CardContent>
              </Card>

              {/* Contact Information */}
              <div className="space-y-8">
                <div>
                  <h2 className="text-3xl font-bold text-white mb-6">Get in Touch</h2>
                  <p className="text-slate-300 mb-8">
                    Ready to accelerate your business growth? Our team of digital marketing experts is here to help you achieve extraordinary results. Contact us today for a free consultation.
                  </p>
                </div>

                <div className="space-y-6">
                  {contactInfo.map((info, index) => (
                    <Card key={index} className="bg-slate-900/80 border-amber-500/20 hover:border-amber-500/40 transition-colors">
                      <CardContent className="p-6">
                        <div className="flex items-start space-x-4">
                          <div className="w-12 h-12 bg-gradient-to-r from-amber-500 to-amber-600 rounded-lg flex items-center justify-center flex-shrink-0">
                            <info.icon className="w-6 h-6 text-white" />
                          </div>
                          <div>
                            <h3 className="text-xl font-bold text-white mb-2">{info.title}</h3>
                            {info.details.map((detail, idx) => (
                              <p key={idx} className="text-slate-300 mb-1">{detail}</p>
                            ))}
                            <p className="text-amber-400 text-sm mt-2">{info.description}</p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>

                <Card className="bg-gradient-to-r from-amber-600 to-amber-500 border-none">
                  <CardContent className="p-8 text-center">
                    <h3 className="text-2xl font-bold text-white mb-4">Need Immediate Assistance?</h3>
                    <p className="text-white/90 mb-6">
                      For urgent inquiries or immediate support, call us directly and speak with one of our specialists.
                    </p>
                    <Button className="bg-white text-amber-600 hover:bg-slate-100">
                      <Phone className="w-4 h-4 mr-2" />
                      Call Now: +91 8708577598
                    </Button>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </section>
      </main>
      
      <Footer />
    </div>
  );
};

export default Contact;
