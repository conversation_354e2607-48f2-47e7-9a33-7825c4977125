import React from 'react';
import { Link } from 'react-router-dom';
import { ArrowRight, FileText, TrendingUp, Users, CheckCircle, Building, Star, Crown, Target, Zap } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';
import Sidebar from '@/components/Sidebar';

const ContentMarketingShimla = () => {
  const contentServices = [
    'Content Marketing Strategy Shimla',
    'Content Creation Services Shimla',
    'Blog Writing Services Shimla',
    'Tourism Content Writing Shimla',
    'Hotel Content Marketing Shimla',
    'Adventure Tourism Content Shimla',
    'Hill Station Marketing Content Shimla',
    'Travel Blog Writing Shimla',
    'Hospitality Content Creation Shimla',
    'Local Business Content Shimla',
    'Seasonal Tourism Content Shimla',
    'Heritage Content Writing Shimla',
    'Mountain Tourism Content Shimla',
    'Eco-Tourism Content Shimla',
    'Cultural Content Marketing Shimla',
    'Apple Industry Content Shimla',
    'Handicraft Content Writing Shimla',
    'Government Content Services Shimla',
    'Educational Content Shimla',
    'Healthcare Content Marketing Shimla'
  ];

  const industryFocus = [
    {
      industry: 'Tourism & Hospitality Content',
      description: 'Specialized content marketing for Shimla\'s thriving tourism industry and hospitality sector',
      icon: Building,
      features: ['Hotel Content Marketing', 'Travel Package Content', 'Adventure Tourism Writing', 'Seasonal Campaign Content']
    },
    {
      industry: 'Heritage & Cultural Content',
      description: 'Strategic content for Shimla\'s rich colonial heritage and cultural tourism promotion',
      icon: Star,
      features: ['Heritage Site Content', 'Cultural Event Marketing', 'Historical Content Writing', 'Museum Content Creation']
    },
    {
      industry: 'Agriculture & Horticulture Content',
      description: 'Comprehensive content for Himachal\'s apple industry and agricultural businesses',
      icon: Crown,
      features: ['Apple Industry Content', 'Organic Farming Content', 'Horticulture Marketing', 'Agricultural Export Content']
    },
    {
      industry: 'Government & Administrative Content',
      description: 'Professional content services for Shimla\'s government offices and administrative bodies',
      icon: Target,
      features: ['Government Communication', 'Policy Content Writing', 'Public Awareness Content', 'Administrative Documentation']
    }
  ];

  const localContentFeatures = [
    'Shimla Tourism Content Specialization',
    'Himachal Pradesh Content Coverage',
    'Hill Station Marketing Expertise',
    'Colonial Heritage Content',
    'Adventure Tourism Writing',
    'Seasonal Content Strategies',
    'Local Business Storytelling',
    'Cultural Event Marketing',
    'Mountain Tourism Content',
    'Eco-Tourism Content Creation'
  ];

  const achievements = [
    '120+ Shimla Businesses Served',
    '1,950% Average Content Engagement',
    '₹22Cr+ Revenue Generated for Clients',
    '96% Content Campaign Success Rate',
    'Top Content Agency in Himachal Pradesh',
    'Tourism Industry Content Specialists'
  ];

  const contentTypes = [
    {
      type: 'Tourism Content Marketing',
      description: 'Compelling content for hotels, resorts, and tourism businesses in Shimla',
      metrics: ['18.5% Avg Engagement Rate', '2.2M+ Content Reach', '85% Booking Conversion', '22% CTR Average']
    },
    {
      type: 'Heritage Content Creation',
      description: 'Historical and cultural content showcasing Shimla\'s colonial legacy',
      metrics: ['16.8% Avg Engagement Rate', '1.8M+ Heritage Content Views', '28% Cultural Interest', '19% CTR Average']
    },
    {
      type: 'Adventure Content Writing',
      description: 'Exciting content for adventure tourism and outdoor activities',
      metrics: ['21.2% Avg Engagement Rate', '1.5M+ Adventure Content Reach', '32% Activity Bookings', '25% CTR Average']
    },
    {
      type: 'Local Business Content',
      description: 'Authentic content for local Shimla businesses and services',
      metrics: ['14.5% Avg Engagement Rate', '950K+ Local Content Views', '18% Local Conversion', '16% CTR Average']
    }
  ];

  return (
    <div className="min-h-screen bg-slate-900 relative">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        <div className="container mx-auto px-6 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            <div className="lg:col-span-3">
              <section className="pt-24 pb-16">
                <div className="max-w-4xl">
                  <div className="inline-flex items-center space-x-2 bg-indigo-500/20 border border-indigo-500/30 rounded-full px-6 py-2 mb-8">
                    <FileText className="w-4 h-4 text-indigo-400" />
                    <span className="text-indigo-400 font-medium">Content Marketing Shimla • Himachal's Digital Storytelling Leaders</span>
                  </div>
                  
                  <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
                    #1 Content Marketing Company in
                    <span className="bg-gradient-to-r from-indigo-400 to-indigo-600 bg-clip-text text-transparent"> Shimla</span>
                  </h1>
                  
                  <p className="text-xl text-slate-300 mb-8">
                    Premier content marketing services in Shimla offering comprehensive content creation and digital storytelling solutions for tourism businesses, hospitality sector, heritage organizations, and local enterprises. Our Shimla content marketing agency provides professional content services including tourism content writing, heritage content creation, adventure tourism content, hotel content marketing Shimla, and government content services. Serving 120+ Shimla businesses with proven ₹22Cr+ revenue generation and 1,950% average content engagement through strategic content marketing and digital storytelling excellence in Himachal Pradesh's capital city.
                  </p>
                  
                  <div className="flex flex-col sm:flex-row gap-4 mb-8">
                    <Button asChild size="lg" className="bg-gradient-to-r from-indigo-500 to-indigo-600 hover:from-indigo-600 hover:to-indigo-700 text-white text-lg px-8 py-4">
                      <Link to="/quote">Get Free Content Audit Shimla</Link>
                    </Button>
                    <Button asChild size="lg" variant="outline" className="border-indigo-500 text-indigo-400 hover:bg-indigo-500 hover:text-white text-lg px-8 py-4">
                      <Link to="/contact">Call Shimla Content Experts: +91-8708577598</Link>
                    </Button>
                  </div>

                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                    {achievements.map((achievement, index) => (
                      <div key={index} className="flex items-center justify-center space-x-2 bg-slate-800/50 rounded-lg p-3">
                        <CheckCircle className="w-4 h-4 text-indigo-400 flex-shrink-0" />
                        <span className="text-slate-300">{achievement}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </section>

              <section className="mb-16">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Shimla Industry-Specific
                    <span className="bg-gradient-to-r from-indigo-400 to-indigo-600 bg-clip-text text-transparent"> Content Solutions</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Specialized content strategies tailored for Shimla's key industries and business sectors.
                  </p>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  {industryFocus.map((industry, index) => (
                    <Card key={index} className="bg-slate-900/80 border-indigo-500/20 hover:border-indigo-500/40 transition-all duration-300">
                      <CardContent className="p-8">
                        <div className="w-12 h-12 bg-gradient-to-r from-indigo-500 to-indigo-600 rounded-lg flex items-center justify-center mb-6">
                          <industry.icon className="w-6 h-6 text-white" />
                        </div>
                        <h3 className="text-xl font-bold text-white mb-4">{industry.industry}</h3>
                        <p className="text-slate-300 mb-6">{industry.description}</p>
                        <ul className="space-y-2">
                          {industry.features.map((feature, idx) => (
                            <li key={idx} className="flex items-center text-slate-400 text-sm">
                              <CheckCircle className="w-3 h-3 text-indigo-400 mr-2 flex-shrink-0" />
                              {feature}
                            </li>
                          ))}
                        </ul>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>

              <section className="mb-16 bg-slate-800/30 rounded-2xl p-8">
                <h3 className="text-2xl font-bold text-white mb-6 text-center">
                  Comprehensive Content Marketing Services in Shimla
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  {contentServices.map((service, index) => (
                    <div key={index} className="bg-slate-900/50 rounded-lg p-4 text-center">
                      <div className="text-indigo-400 font-medium text-sm">{service}</div>
                    </div>
                  ))}
                </div>
              </section>

              <section className="mb-16 bg-slate-800/30 rounded-2xl p-8">
                <h3 className="text-2xl font-bold text-white mb-6 text-center">
                  Content Performance Metrics
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                  {contentTypes.map((content, index) => (
                    <div key={index} className="bg-slate-900/50 rounded-lg p-6">
                      <h4 className="text-indigo-400 font-semibold mb-3">{content.type}</h4>
                      <p className="text-slate-300 text-sm mb-4">{content.description}</p>
                      <div className="space-y-2">
                        {content.metrics.map((metric, idx) => (
                          <div key={idx} className="text-slate-400 text-xs">{metric}</div>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </section>

              <section className="mb-16 bg-slate-800/30 rounded-2xl p-8">
                <h3 className="text-2xl font-bold text-white mb-6 text-center">
                  Local Content Marketing Shimla Specialization
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                  {localContentFeatures.map((feature, index) => (
                    <div key={index} className="bg-slate-900/50 rounded-lg p-4 text-center">
                      <div className="text-indigo-400 font-medium text-sm">{feature}</div>
                    </div>
                  ))}
                </div>
              </section>

              <section className="mb-16 bg-slate-800/30 rounded-2xl p-8">
                <h3 className="text-2xl font-bold text-white mb-6 text-center">
                  Why Choose Our Shimla Content Marketing Services?
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  <div className="text-center">
                    <div className="w-16 h-16 bg-gradient-to-r from-indigo-500 to-indigo-600 rounded-full flex items-center justify-center mx-auto mb-4">
                      <Building className="w-8 h-8 text-white" />
                    </div>
                    <h4 className="text-white font-semibold mb-2">Tourism Industry Expertise</h4>
                    <p className="text-slate-400 text-sm">Deep understanding of Shimla's tourism landscape, heritage sites, and hospitality sector for compelling content creation.</p>
                  </div>
                  <div className="text-center">
                    <div className="w-16 h-16 bg-gradient-to-r from-indigo-500 to-indigo-600 rounded-full flex items-center justify-center mx-auto mb-4">
                      <TrendingUp className="w-8 h-8 text-white" />
                    </div>
                    <h4 className="text-white font-semibold mb-2">Proven Engagement</h4>
                    <p className="text-slate-400 text-sm">120+ successful content campaigns in Shimla with 1,950% average engagement growth and ₹22Cr+ revenue generation.</p>
                  </div>
                  <div className="text-center">
                    <div className="w-16 h-16 bg-gradient-to-r from-indigo-500 to-indigo-600 rounded-full flex items-center justify-center mx-auto mb-4">
                      <Users className="w-8 h-8 text-white" />
                    </div>
                    <h4 className="text-white font-semibold mb-2">Local Market Knowledge</h4>
                    <p className="text-slate-400 text-sm">Specialized expertise in tourism, heritage, agriculture, and government sectors prominent in Shimla market.</p>
                  </div>
                </div>
              </section>

              <section className="mb-16 bg-slate-800/30 rounded-2xl p-8">
                <h3 className="text-2xl font-bold text-white mb-6 text-center">
                  Complete Digital Marketing Solutions Shimla
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  <Link to="/services/seo/shimla" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-indigo-400 font-semibold mb-2 group-hover:text-indigo-300">SEO Services Shimla</h4>
                    <p className="text-slate-400 text-sm">Search engine optimization for Shimla tourism and businesses</p>
                  </Link>
                  <Link to="/services/ppc/shimla" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-indigo-400 font-semibold mb-2 group-hover:text-indigo-300">Google Ads Shimla</h4>
                    <p className="text-slate-400 text-sm">PPC advertising and Google Ads management for Shimla</p>
                  </Link>
                  <Link to="/services/social-media/shimla" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-indigo-400 font-semibold mb-2 group-hover:text-indigo-300">Social Media Marketing Shimla</h4>
                    <p className="text-slate-400 text-sm">Social media management for Shimla tourism and businesses</p>
                  </Link>
                  <Link to="/services/web-development/shimla" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-indigo-400 font-semibold mb-2 group-hover:text-indigo-300">Web Development Shimla</h4>
                    <p className="text-slate-400 text-sm">Professional website development for Shimla businesses</p>
                  </Link>
                  <Link to="/services/email/shimla" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-indigo-400 font-semibold mb-2 group-hover:text-indigo-300">Email Marketing Shimla</h4>
                    <p className="text-slate-400 text-sm">Email marketing campaigns for Shimla tourism and businesses</p>
                  </Link>
                  <Link to="/portfolio" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-indigo-400 font-semibold mb-2 group-hover:text-indigo-300">Shimla Success Stories</h4>
                    <p className="text-slate-400 text-sm">See our proven results from Shimla tourism and business clients</p>
                  </Link>
                </div>
              </section>

              <section className="bg-gradient-to-r from-indigo-600 to-indigo-500 rounded-2xl p-8 text-center">
                <h2 className="text-3xl md:text-5xl font-bold mb-6">Ready to Tell Your Shimla Story?</h2>
                <p className="text-xl mb-8">
                  Join 120+ successful Shimla businesses that trust GOD Digital Marketing for content excellence. Proven strategies delivering 1,950% engagement growth and ₹22Cr+ revenue generation.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button asChild size="lg" className="bg-white text-indigo-600 hover:bg-slate-100 text-lg px-8 py-4">
                    <Link to="/quote">Get Free Content Audit</Link>
                  </Button>
                  <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-indigo-600 text-lg px-8 py-4">
                    <Link to="/contact">Call Shimla Content Experts: +91-8708577598</Link>
                  </Button>
                </div>
              </section>
            </div>

            <div className="lg:col-span-1">
              <Sidebar currentService="content" />
            </div>
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
};

export default ContentMarketingShimla;
