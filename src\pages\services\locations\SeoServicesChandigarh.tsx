import React from 'react';
import { Link } from 'react-router-dom';
import { ArrowRight, Search, TrendingUp, Users, CheckCircle, Building, Star, Crown, Target, Zap } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';
import Sidebar from '@/components/Sidebar';

const SeoServicesChandigarh = () => {
  const stats = [
    {
      metric: '1350+',
      description: 'Chandigarh Businesses Ranked',
      detail: 'Across all industries'
    },
    {
      metric: '620%',
      description: 'Average Traffic Increase',
      detail: 'For Chandigarh clients'
    },
    {
      metric: '₹38Cr+',
      description: 'Revenue Generated',
      detail: 'Through SEO campaigns'
    },
    {
      metric: '94%',
      description: 'First Page Rankings',
      detail: 'Target keyword success'
    }
  ];

  const achievements = [
    'Top SEO Company in Chandigarh',
    'Government Sector SEO Leaders',
    'Education Industry SEO Experts',
    'IT Services SEO Specialists',
    'Healthcare SEO Champions',
    'Planned City Digital Marketing Pioneers'
  ];

  const seoSpecializations = [
    {
      type: 'Government & Administrative SEO',
      description: 'Specialized SEO for Chandigarh\'s government offices and administrative sector',
      icon: Building,
      features: ['Government Office SEO', 'Administrative Services SEO', 'Public Sector SEO', 'Municipal SEO']
    },
    {
      type: 'Education & Research SEO',
      description: 'Expert SEO for Chandigarh\'s educational institutions and research centers',
      icon: Star,
      features: ['University SEO', 'College SEO', 'Research Institute SEO', 'Educational Services SEO']
    },
    {
      type: 'IT & Technology SEO',
      description: 'Professional SEO for Chandigarh\'s growing IT and technology sector',
      icon: Crown,
      features: ['IT Company SEO', 'Software Services SEO', 'Tech Startup SEO', 'Digital Services SEO']
    },
    {
      type: 'Healthcare & Medical SEO',
      description: 'Comprehensive SEO for Chandigarh\'s healthcare and medical institutions',
      icon: Target,
      features: ['Hospital SEO', 'Medical Practice SEO', 'Healthcare Services SEO', 'Medical Tourism SEO']
    }
  ];

  return (
    <div className="min-h-screen bg-slate-900 relative">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        <div className="container mx-auto px-6 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            <div className="lg:col-span-3">
              <section className="pt-24 pb-16">
                <div className="max-w-4xl">
                  <div className="inline-flex items-center space-x-2 bg-emerald-500/20 border border-emerald-500/30 rounded-full px-6 py-2 mb-8">
                    <Search className="w-4 h-4 text-emerald-400" />
                    <span className="text-emerald-400 font-medium">SEO Services Chandigarh • Planned City Digital Excellence</span>
                  </div>
                  
                  <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
                    #1 SEO Company in
                    <span className="bg-gradient-to-r from-emerald-400 to-emerald-600 bg-clip-text text-transparent"> Chandigarh</span>
                  </h1>
                  
                  <p className="text-xl text-slate-300 mb-8">
                    Premier SEO services in Chandigarh offering comprehensive search engine optimization solutions including local SEO, technical SEO, on-page optimization, off-page SEO, and enterprise SEO strategies. Our Chandigarh SEO company provides professional SEO services with keyword research, content optimization, link building, SEO audits, and Google ranking optimization. Serving 1350+ Chandigarh businesses across all industries - from government offices in Sector 17 to IT companies in IT Park. Expert SEO solutions with proven ₹38Cr+ revenue generation and 620% average organic traffic increase for Chandigarh clients in India's first planned city through strategic search engine optimization and digital marketing excellence.
                  </p>
                  
                  <div className="flex flex-col sm:flex-row gap-4 mb-8">
                    <Button asChild size="lg" className="bg-gradient-to-r from-emerald-500 to-emerald-600 hover:from-emerald-600 hover:to-emerald-700 text-white text-lg px-8 py-4">
                      <Link to="/quote">Get Free Chandigarh SEO Audit</Link>
                    </Button>
                    <Button asChild size="lg" variant="outline" className="border-emerald-500 text-emerald-400 hover:bg-emerald-500 hover:text-white text-lg px-8 py-4">
                      <Link to="/contact">Call Chandigarh SEO Experts: +91-8708577598</Link>
                    </Button>
                  </div>

                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                    {achievements.map((achievement, index) => (
                      <div key={index} className="flex items-center justify-center space-x-2 bg-slate-800/50 rounded-lg p-3">
                        <CheckCircle className="w-4 h-4 text-emerald-400 flex-shrink-0" />
                        <span className="text-slate-300">{achievement}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </section>

              <section className="mb-16 bg-slate-800/50 rounded-2xl p-8">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Chandigarh SEO
                    <span className="bg-gradient-to-r from-emerald-400 to-emerald-600 bg-clip-text text-transparent"> Success Metrics</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Measurable SEO results from Chandigarh businesses across government, education, IT, and healthcare sectors.
                  </p>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
                  {stats.map((stat, index) => (
                    <div key={index} className="text-center">
                      <div className="text-4xl font-bold text-emerald-400 mb-2">{stat.metric}</div>
                      <div className="text-white font-semibold mb-1">{stat.description}</div>
                      <div className="text-slate-400">{stat.detail}</div>
                    </div>
                  ))}
                </div>
              </section>

              <section className="mb-16">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    SEO Strategies We
                    <span className="bg-gradient-to-r from-emerald-400 to-emerald-600 bg-clip-text text-transparent"> Master</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Specialized SEO strategies designed for Chandigarh's unique government, education, IT, and healthcare business landscape.
                  </p>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  {seoSpecializations.map((seo, index) => (
                    <Card key={index} className="bg-slate-900/80 border-emerald-500/20 hover:border-emerald-500/40 transition-all duration-300">
                      <CardContent className="p-8">
                        <div className="w-12 h-12 bg-gradient-to-r from-emerald-500 to-emerald-600 rounded-lg flex items-center justify-center mb-6">
                          <seo.icon className="w-6 h-6 text-white" />
                        </div>
                        <h3 className="text-xl font-bold text-white mb-4">{seo.type}</h3>
                        <p className="text-slate-300 mb-6">{seo.description}</p>
                        <ul className="space-y-2">
                          {seo.features.map((feature, idx) => (
                            <li key={idx} className="flex items-center text-slate-400 text-sm">
                              <CheckCircle className="w-3 h-3 text-emerald-400 mr-2 flex-shrink-0" />
                              {feature}
                            </li>
                          ))}
                        </ul>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>

              {/* Internal Linking Section */}
              <section className="mb-16 bg-slate-800/30 rounded-2xl p-8">
                <h3 className="text-2xl font-bold text-white mb-6 text-center">
                  Complete Digital Marketing Solutions in Chandigarh
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  <Link to="/services/ppc/chandigarh" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-emerald-400 font-semibold mb-2 group-hover:text-emerald-300">Google Ads Chandigarh</h4>
                    <p className="text-slate-400 text-sm">PPC advertising for government and education sectors</p>
                  </Link>
                  <Link to="/services/social-media/chandigarh" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-emerald-400 font-semibold mb-2 group-hover:text-emerald-300">Social Media Chandigarh</h4>
                    <p className="text-slate-400 text-sm">Social media marketing for planned city businesses</p>
                  </Link>
                  <Link to="/services/content/chandigarh" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-emerald-400 font-semibold mb-2 group-hover:text-emerald-300">Content Marketing Chandigarh</h4>
                    <p className="text-slate-400 text-sm">Content creation for IT and healthcare industries</p>
                  </Link>
                  <Link to="/services/email/chandigarh" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-emerald-400 font-semibold mb-2 group-hover:text-emerald-300">Email Marketing Chandigarh</h4>
                    <p className="text-slate-400 text-sm">Email campaigns for government and education sectors</p>
                  </Link>
                  <Link to="/services/web-development/chandigarh" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-emerald-400 font-semibold mb-2 group-hover:text-emerald-300">Web Development Chandigarh</h4>
                    <p className="text-slate-400 text-sm">Website development for planned city businesses</p>
                  </Link>
                  <Link to="/portfolio" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-emerald-400 font-semibold mb-2 group-hover:text-emerald-300">Our Success Stories</h4>
                    <p className="text-slate-400 text-sm">See how we've helped Chandigarh businesses grow</p>
                  </Link>
                </div>
              </section>

              <section className="bg-gradient-to-r from-emerald-600 to-emerald-500 rounded-2xl p-8 text-center">
                <h2 className="text-3xl md:text-5xl font-bold mb-6">Ready to Dominate Chandigarh Search Results?</h2>
                <p className="text-xl mb-8">
                  Join 1350+ Chandigarh businesses that trust GOD Digital Marketing for SEO success. Proven strategies that deliver 620% traffic increase and ₹38Cr+ revenue generation.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button asChild size="lg" className="bg-white text-emerald-600 hover:bg-slate-100 text-lg px-8 py-4">
                    <Link to="/quote">Get Free SEO Audit</Link>
                  </Button>
                  <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-emerald-600 text-lg px-8 py-4">
                    <Link to="/contact">Call SEO Experts: +91-8708577598</Link>
                  </Button>
                </div>
              </section>
            </div>

            <div className="lg:col-span-1">
              <Sidebar currentService="seo" currentLocation="chandigarh" />
            </div>
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
};

export default SeoServicesChandigarh;
