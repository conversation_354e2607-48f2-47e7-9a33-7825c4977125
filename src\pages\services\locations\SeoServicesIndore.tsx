import React from 'react';
import { Link } from 'react-router-dom';
import { ArrowRight, Search, TrendingUp, Users, CheckCircle, Building, Star, Crown, Target, Zap } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';
import Sidebar from '@/components/Sidebar';

const SeoServicesIndore = () => {
  const seoServices = [
    'SEO Services Indore',
    'Local SEO Indore',
    'Technical SEO Indore',
    'On-Page SEO Indore',
    'Off-Page SEO Indore',
    'E-commerce SEO Indore',
    'Enterprise SEO Indore',
    'SEO Audit Indore',
    'Keyword Research Indore',
    'Content Optimization Indore',
    'Link Building Indore',
    'Google Ranking Indore',
    'Manufacturing SEO Indore',
    'Pharmaceutical SEO Indore',
    'Automotive SEO Indore',
    'Food Processing SEO Indore',
    'Textile SEO Indore',
    'IT Services SEO Indore',
    'Real Estate SEO Indore',
    'Education SEO Indore'
  ];

  const seoSpecializations = [
    {
      type: 'Manufacturing & Industrial SEO',
      description: 'Specialized SEO for Indore\'s manufacturing hub and industrial sector businesses',
      icon: Building,
      features: ['Manufacturing Company SEO', 'Industrial Services SEO', 'B2B Manufacturing SEO', 'Export Business SEO']
    },
    {
      type: 'Pharmaceutical & Healthcare SEO',
      description: 'Expert SEO for Indore\'s pharmaceutical and healthcare industry leaders',
      icon: Star,
      features: ['Pharmaceutical Company SEO', 'Healthcare Services SEO', 'Medical Device SEO', 'Biotech SEO']
    },
    {
      type: 'Automotive & Engineering SEO',
      description: 'Professional SEO for Indore\'s automotive and engineering sector',
      icon: Crown,
      features: ['Automotive Company SEO', 'Engineering Services SEO', 'Auto Parts SEO', 'Manufacturing SEO']
    },
    {
      type: 'Food Processing & Agriculture SEO',
      description: 'Comprehensive SEO for Indore\'s food processing and agriculture businesses',
      icon: Target,
      features: ['Food Processing SEO', 'Agriculture Business SEO', 'FMCG Company SEO', 'Agri-tech SEO']
    }
  ];

  const seoPackages = [
    {
      name: 'SEO Indore Starter',
      price: '₹24,000',
      period: '/month',
      description: 'Perfect for small Indore businesses and manufacturing units',
      features: [
        'Local Indore SEO Optimization',
        'Google My Business Setup',
        '20 Target Keywords',
        'On-Page SEO (10 pages)',
        'Monthly Reporting',
        'Manufacturing Industry Focus'
      ]
    },
    {
      name: 'Indore SEO Professional',
      price: '₹44,000',
      period: '/month',
      description: 'Comprehensive SEO for growing Indore businesses',
      features: [
        'Advanced Local + National SEO',
        '50 Target Keywords',
        'Technical SEO Audit',
        'Content Marketing Strategy',
        'Link Building Campaign',
        'Pharmaceutical/Automotive Specialization',
        'Bi-weekly Reporting'
      ],
      popular: true
    },
    {
      name: 'Enterprise SEO Indore',
      price: '₹82,000',
      period: '/month',
      description: 'Advanced SEO for large Indore enterprises and exporters',
      features: [
        'Enterprise-Level SEO Strategy',
        'Unlimited Keywords',
        'Advanced Technical SEO',
        'Multi-location Optimization',
        'International SEO',
        'Export Market Targeting',
        'Dedicated SEO Manager'
      ]
    }
  ];

  const stats = [
    {
      metric: '1250+',
      description: 'Indore Businesses Ranked',
      detail: 'Across all industries'
    },
    {
      metric: '590%',
      description: 'Average Traffic Increase',
      detail: 'For Indore clients'
    },
    {
      metric: '₹34Cr+',
      description: 'Revenue Generated',
      detail: 'Through SEO campaigns'
    },
    {
      metric: '93%',
      description: 'First Page Rankings',
      detail: 'Target keyword success'
    }
  ];

  const achievements = [
    'Top SEO Company in Indore',
    'Manufacturing Industry SEO Leaders',
    'Pharmaceutical SEO Experts',
    'Automotive Industry SEO Specialists',
    'Food Processing SEO Champions',
    'Commercial Hub Digital Marketing Pioneers'
  ];

  return (
    <div className="min-h-screen bg-slate-900 relative">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        <div className="container mx-auto px-6 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            {/* Main Content */}
            <div className="lg:col-span-3">
              {/* Hero Section */}
              <section className="pt-24 pb-16">
                <div className="max-w-4xl">
                  <div className="inline-flex items-center space-x-2 bg-indigo-500/20 border border-indigo-500/30 rounded-full px-6 py-2 mb-8">
                    <Search className="w-4 h-4 text-indigo-400" />
                    <span className="text-indigo-400 font-medium">SEO Services Indore • Commercial Hub Digital Excellence</span>
                  </div>
                  
                  <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
                    #1 SEO Company in
                    <span className="bg-gradient-to-r from-indigo-400 to-indigo-600 bg-clip-text text-transparent"> Indore</span>
                  </h1>
                  
                  <p className="text-xl text-slate-300 mb-8">
                    Premier SEO services in Indore offering comprehensive search engine optimization solutions including local SEO, technical SEO, on-page optimization, off-page SEO, and enterprise SEO strategies. Our Indore SEO company provides professional SEO services with keyword research, content optimization, link building, SEO audits, and Google ranking optimization. Serving 1250+ Indore businesses across all industries - from manufacturing companies in Pithampur to pharmaceutical firms in Sanwer Road. Expert SEO solutions with proven ₹34Cr+ revenue generation and 590% average organic traffic increase for Indore clients in Madhya Pradesh's commercial hub through strategic search engine optimization and digital marketing excellence.
                  </p>
                  
                  <div className="flex flex-col sm:flex-row gap-4 mb-8">
                    <Button asChild size="lg" className="bg-gradient-to-r from-indigo-500 to-indigo-600 hover:from-indigo-600 hover:to-indigo-700 text-white text-lg px-8 py-4">
                      <Link to="/quote">Get Free Indore SEO Audit</Link>
                    </Button>
                    <Button asChild size="lg" variant="outline" className="border-indigo-500 text-indigo-400 hover:bg-indigo-500 hover:text-white text-lg px-8 py-4">
                      <Link to="/contact">Call Indore SEO Experts: +91-8708577598</Link>
                    </Button>
                  </div>

                  {/* Achievements */}
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                    {achievements.map((achievement, index) => (
                      <div key={index} className="flex items-center justify-center space-x-2 bg-slate-800/50 rounded-lg p-3">
                        <CheckCircle className="w-4 h-4 text-indigo-400 flex-shrink-0" />
                        <span className="text-slate-300">{achievement}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </section>

              {/* Stats Section */}
              <section className="mb-16 bg-slate-800/50 rounded-2xl p-8">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Indore SEO
                    <span className="bg-gradient-to-r from-indigo-400 to-indigo-600 bg-clip-text text-transparent"> Success Metrics</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Measurable SEO results from Indore businesses across manufacturing, pharmaceutical, automotive, and food processing sectors.
                  </p>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
                  {stats.map((stat, index) => (
                    <div key={index} className="text-center">
                      <div className="text-4xl font-bold text-indigo-400 mb-2">{stat.metric}</div>
                      <div className="text-white font-semibold mb-1">{stat.description}</div>
                      <div className="text-slate-400">{stat.detail}</div>
                    </div>
                  ))}
                </div>
              </section>

              {/* SEO Specializations Section */}
              <section className="mb-16">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    SEO Strategies We
                    <span className="bg-gradient-to-r from-indigo-400 to-indigo-600 bg-clip-text text-transparent"> Master</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Specialized SEO strategies designed for Indore's unique manufacturing, pharmaceutical, automotive, and food processing business landscape.
                  </p>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  {seoSpecializations.map((seo, index) => (
                    <Card key={index} className="bg-slate-900/80 border-indigo-500/20 hover:border-indigo-500/40 transition-all duration-300">
                      <CardContent className="p-8">
                        <div className="w-12 h-12 bg-gradient-to-r from-indigo-500 to-indigo-600 rounded-lg flex items-center justify-center mb-6">
                          <seo.icon className="w-6 h-6 text-white" />
                        </div>
                        <h3 className="text-xl font-bold text-white mb-4">{seo.type}</h3>
                        <p className="text-slate-300 mb-6">{seo.description}</p>
                        <ul className="space-y-2">
                          {seo.features.map((feature, idx) => (
                            <li key={idx} className="flex items-center text-slate-400 text-sm">
                              <CheckCircle className="w-3 h-3 text-indigo-400 mr-2 flex-shrink-0" />
                              {feature}
                            </li>
                          ))}
                        </ul>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>

              {/* Pricing Section */}
              <section className="mb-16">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Indore SEO
                    <span className="bg-gradient-to-r from-indigo-400 to-indigo-600 bg-clip-text text-transparent"> Pricing</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Competitive SEO pricing designed for Indore's manufacturing, pharmaceutical, and business environment.
                  </p>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                  {seoPackages.map((pkg, index) => (
                    <Card key={index} className={`bg-slate-900/80 border-indigo-500/20 hover:border-indigo-500/40 transition-all duration-300 relative ${pkg.popular ? 'ring-2 ring-indigo-500' : ''}`}>
                      {pkg.popular && (
                        <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                          <span className="bg-indigo-500 text-white px-4 py-2 rounded-full text-sm font-semibold">Most Popular</span>
                        </div>
                      )}
                      <CardContent className="p-8">
                        <h3 className="text-2xl font-bold text-white mb-2">{pkg.name}</h3>
                        <p className="text-slate-300 mb-6">{pkg.description}</p>
                        <div className="mb-6">
                          <span className="text-4xl font-bold text-indigo-400">{pkg.price}</span>
                          <span className="text-slate-400">{pkg.period}</span>
                        </div>
                        <ul className="space-y-3 mb-8">
                          {pkg.features.map((feature, idx) => (
                            <li key={idx} className="flex items-center text-slate-300">
                              <CheckCircle className="w-4 h-4 text-indigo-400 mr-3 flex-shrink-0" />
                              {feature}
                            </li>
                          ))}
                        </ul>
                        <Button asChild className={`w-full ${pkg.popular ? 'bg-gradient-to-r from-indigo-500 to-indigo-600 hover:from-indigo-600 hover:to-indigo-700' : 'bg-slate-700 hover:bg-slate-600'} text-white`}>
                          <Link to="/quote">Get Started</Link>
                        </Button>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>

              {/* CTA Section */}
              <section className="bg-gradient-to-r from-indigo-600 to-indigo-500 rounded-2xl p-8 text-center">
                <h2 className="text-3xl md:text-5xl font-bold mb-6">Ready to Dominate Indore Search Results?</h2>
                <p className="text-xl mb-8">
                  Join 1250+ Indore businesses that trust GOD Digital Marketing for SEO success. Proven strategies that deliver 590% traffic increase and ₹34Cr+ revenue generation.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button asChild size="lg" className="bg-white text-indigo-600 hover:bg-slate-100 text-lg px-8 py-4">
                    <Link to="/quote">Get Free SEO Audit</Link>
                  </Button>
                  <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-indigo-600 text-lg px-8 py-4">
                    <Link to="/contact">Call SEO Experts: +91-8708577598</Link>
                  </Button>
                </div>
              </section>
            </div>

            {/* Sidebar */}
            <div className="lg:col-span-1">
              <Sidebar currentService="seo" currentLocation="indore" />
            </div>
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
};

export default SeoServicesIndore;
