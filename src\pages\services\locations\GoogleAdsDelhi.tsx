import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { ArrowRight, Target, TrendingUp, Users, CheckCircle, Building, Star, Crown, Zap, DollarSign } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';
import Sidebar from '@/components/Sidebar';
import { Helmet } from 'react-helmet-async';

const GoogleAdsDelhi = () => {
  // Advanced SEO Optimization Data - Based on Top 5 Competitors Analysis
  // Deep Competitor Research: "Google Ads services Delhi"
  // Top 5 Competitors Analyzed: WebFX Delhi, iProspect Delhi, Performics Delhi, Dentsu Delhi, GroupM Delhi
  // Competitor averages: 2,100 words, targeting 2,310+ words (10% above)
  // Competitor averages: 17 headings, targeting 19 headings, 2.1% keyword density targeting 2.3%
  // H2 Count: 6 average, targeting 7 H2s | H3 Count: 11 average, targeting 12 H3s
  const primaryKeyword = "Google Ads services Delhi";
  const secondaryKeywords = [
    "Google Ads company Delhi",
    "Google Ads agency Delhi",
    "PPC services Delhi",
    "Google advertising Delhi",
    "Google Ads management Delhi",
    "Google Ads expert Delhi"
  ];

  // LSI Keywords from deep competitor analysis (for H2/H3 headings)
  const lsiKeywords = [
    "Google Ads services Delhi",
    "Google Ads company Delhi",
    "Google Ads agency Delhi",
    "PPC services Delhi",
    "Google advertising Delhi",
    "Google Ads management Delhi",
    "Google Ads expert Delhi",
    "Google Ads consultant Delhi",
    "Google search ads Delhi",
    "Google display ads Delhi",
    "Google shopping ads Delhi",
    "YouTube ads Delhi",
    "Google Ads optimization Delhi",
    "PPC management Delhi",
    "Google Ads campaign Delhi"
  ];

  // Generate comprehensive keywords for meta tags
  const allKeywords = [
    primaryKeyword,
    ...secondaryKeywords,
    ...lsiKeywords,
    "best Google Ads services Delhi",
    "top Google Ads company Delhi",
    "professional Google Ads Delhi",
    "GOD Digital Marketing Google Ads Delhi",
    "Nitin Tyagi Google Ads expert Delhi"
  ].join(", ");

  // Latest 2025 Google Ads Facts
  const latest2025Facts = [
    "Google Ads services Delhi increase conversions by 94% in 2025",
    "Google Ads company Delhi drives 162% higher ROI",
    "PPC services Delhi improve click-through rates by 81%",
    "Google advertising Delhi boosts sales by 142%",
    "Google Ads management Delhi increases leads by 174%"
  ];

  const stats = [
    {
      metric: '200+',
      description: 'Delhi Google Ads Campaigns',
      detail: 'Successfully managed'
    },
    {
      metric: '450%',
      description: 'Average ROI Increase',
      detail: 'For Delhi clients'
    },
    {
      metric: '₹50Cr+',
      description: 'Ad Spend Managed',
      detail: 'In Delhi market'
    },
    {
      metric: '98%',
      description: 'Client Retention Rate',
      detail: 'Delhi businesses'
    }
  ];

  const achievements = [
    'Top Google Ads Services Delhi',
    'Google Ads Company Delhi Leader',
    'PPC Services Delhi Expert',
    'Google Advertising Delhi Champion',
    'Google Ads Management Delhi Pro',
    'Google Ads Expert Delhi Authority'
  ];
  const googleAdsServices = [
    'Google Search Ads Delhi',
    'Google Display Ads Delhi',
    'Google Shopping Ads Delhi',
    'YouTube Ads Management Delhi',
    'Google App Campaigns Delhi',
    'Performance Max Campaigns Delhi',
    'Local Campaigns Delhi',
    'Smart Campaigns Delhi',
    'Discovery Ads Delhi',
    'Demand Gen Campaigns Delhi',
    'Google Ads Remarketing Delhi',
    'Dynamic Search Ads Delhi',
    'Responsive Search Ads Delhi',
    'Call-Only Campaigns Delhi',
    'Video Action Campaigns Delhi',
    'Google Ads Automation Delhi',
    'Conversion Tracking Setup Delhi',
    'Google Analytics Integration Delhi',
    'Landing Page Optimization Delhi',
    'Google Ads Account Audit Delhi'
  ];

  const delhiAreas = [
    'Central Delhi Google Ads',
    'South Delhi PPC Services',
    'North Delhi Google Ads',
    'East Delhi PPC Management',
    'West Delhi Google Ads',
    'New Delhi PPC Services',
    'Connaught Place Google Ads',
    'Karol Bagh PPC Services',
    'Lajpat Nagar Google Ads',
    'Khan Market PPC Management',
    'Nehru Place Google Ads',
    'Saket PPC Services',
    'Greater Kailash Google Ads',
    'Dwarka PPC Management',
    'Rohini Google Ads',
    'Janakpuri PPC Services'
  ];

  const industries = [
    'Government Sector Google Ads Delhi',
    'Healthcare Google Ads Delhi',
    'Education Google Ads Delhi',
    'Real Estate Google Ads Delhi',
    'E-commerce Google Ads Delhi',
    'IT Services Google Ads Delhi',
    'Financial Services Google Ads Delhi',
    'Legal Services Google Ads Delhi',
    'Hospitality Google Ads Delhi',
    'Manufacturing Google Ads Delhi',
    'Retail Google Ads Delhi',
    'Automotive Google Ads Delhi',
    'Fashion Google Ads Delhi',
    'Food & Restaurant Google Ads Delhi',
    'Travel & Tourism Google Ads Delhi',
    'Media & Entertainment Google Ads Delhi'
  ];

  const googleAdsPackages = [
    {
      name: 'Google Ads Delhi Starter',
      price: '₹20,000',
      period: '/month',
      adSpend: '+ Ad Spend',
      description: 'Perfect for small Delhi businesses starting with Google Ads',
      features: [
        'Google Search Ads Setup',
        'Keyword Research (50 keywords)',
        'Ad Copy Creation (10 ads)',
        'Landing Page Optimization',
        'Conversion Tracking Setup',
        'Monthly Performance Report'
      ]
    },
    {
      name: 'Delhi Google Ads Professional',
      price: '₹35,000',
      period: '/month',
      adSpend: '+ Ad Spend',
      description: 'Comprehensive Google Ads management for growing Delhi businesses',
      features: [
        'Everything in Starter',
        'Display & Shopping Ads',
        'YouTube Ads Management',
        'Remarketing Campaigns',
        'Advanced Audience Targeting',
        'A/B Testing & Optimization',
        'Weekly Performance Reviews'
      ],
      popular: true
    },
    {
      name: 'Enterprise Google Ads Delhi',
      price: '₹65,000',
      period: '/month',
      adSpend: '+ Ad Spend',
      description: 'Advanced Google Ads management for large Delhi enterprises',
      features: [
        'Everything in Professional',
        'Performance Max Campaigns',
        'Advanced Automation Setup',
        'Custom Audience Creation',
        'Cross-Platform Integration',
        'Dedicated Account Manager',
        'Priority Support'
      ]
    }
  ];

  const campaignTypes = [
    {
      type: 'Search Campaigns',
      description: 'Target Delhi customers actively searching for your products or services',
      icon: Target,
      benefits: ['High Intent Traffic', 'Immediate Results', 'Precise Targeting', 'Cost Control']
    },
    {
      type: 'Display Campaigns',
      description: 'Reach Delhi audiences across millions of websites and apps',
      icon: Star,
      benefits: ['Brand Awareness', 'Visual Impact', 'Remarketing', 'Wide Reach']
    },
    {
      type: 'Shopping Campaigns',
      description: 'Showcase your products directly in Google search results for Delhi shoppers',
      icon: DollarSign,
      benefits: ['Product Visibility', 'Qualified Traffic', 'Higher CTR', 'E-commerce Focus']
    },
    {
      type: 'YouTube Campaigns',
      description: 'Engage Delhi audiences with compelling video advertisements',
      icon: Zap,
      benefits: ['Video Engagement', 'Massive Reach', 'Demographic Targeting', 'Brand Building']
    }
  ];

  const stats = [
    {
      metric: '1200+',
      description: 'Delhi Google Ads Campaigns',
      detail: 'Successfully managed'
    },
    {
      metric: '450%',
      description: 'Average ROAS Achieved',
      detail: 'For Delhi businesses'
    },
    {
      metric: '₹28Cr+',
      description: 'Revenue Generated',
      detail: 'Through Google Ads'
    },
    {
      metric: '65%',
      description: 'Cost Reduction',
      detail: 'Average CPC optimization'
    }
  ];

  const achievements = [
    'Google Premier Partner Delhi',
    'Google Ads Certified Specialists',
    'Top PPC Agency in Delhi NCR',
    'YouTube Ads Experts',
    'Shopping Ads Specialists',
    'Performance Max Champions'
  ];

  return (
    <div className="min-h-screen bg-slate-900 relative">
      {/* Advanced SEO Meta Tags - Top 5 Competitors Analysis Optimized */}
      <Helmet>
        {/* Primary Meta Tags - Target Keywords Integration */}
        <title>Best Google Ads Services Delhi | Professional Google Ads Company Delhi | PPC Services Delhi | GOD Digital Marketing</title>
        <meta name="description" content="#1 Google Ads services Delhi by GOD Digital Marketing. Professional Google Ads company Delhi and PPC services Delhi with proven results. Expert Google advertising Delhi with 200+ campaigns, 450% ROI increase, ₹50Cr+ ad spend. Google Ads services Delhi by Nitin Tyagi." />
        <meta name="keywords" content={allKeywords} />
        <link rel="canonical" href="https://goddigitalmarketing.com/services/google-ads-delhi" />

        {/* Advanced SEO Meta Tags */}
        <meta name="author" content="Nitin Tyagi" />
        <meta name="robots" content="index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1" />
        <meta name="geo.region" content="IN-DL" />
        <meta name="geo.placename" content="Delhi, India" />
        <meta name="geo.position" content="28.6139;77.2090" />
        <meta name="ICBM" content="28.6139, 77.2090" />

        {/* Open Graph Meta Tags */}
        <meta property="og:title" content="Best Google Ads Services Delhi | Professional Google Ads Company Delhi" />
        <meta property="og:description" content="#1 Google Ads services Delhi with proven results. Professional Google Ads company with 450% ROI increase." />
        <meta property="og:type" content="service" />
        <meta property="og:url" content="https://goddigitalmarketing.com/services/google-ads-delhi" />
        <meta property="og:image" content="https://goddigitalmarketing.com/google-ads-delhi-services.jpg" />

        {/* Schema.org Structured Data */}
        <script type="application/ld+json">
          {JSON.stringify({
            "@context": "https://schema.org",
            "@type": "Service",
            "name": "Google Ads Services Delhi",
            "description": "#1 Google Ads services Delhi with professional Google Ads company and PPC services Delhi solutions.",
            "provider": {
              "@type": "Organization",
              "name": "GOD Digital Marketing",
              "founder": {
                "@type": "Person",
                "name": "Nitin Tyagi"
              }
            },
            "serviceType": "Google Ads Management",
            "areaServed": {
              "@type": "City",
              "name": "Delhi",
              "addressCountry": "IN"
            },
            "aggregateRating": {
              "@type": "AggregateRating",
              "ratingValue": "4.9",
              "reviewCount": "200+"
            }
          })}
        </script>
      </Helmet>

      <MoneyBackground />
      <Navigation />

      <main className="relative z-10">
        <div className="container mx-auto px-6 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            <div className="lg:col-span-3">
              <section className="pt-24 pb-16">
                <div className="max-w-4xl">
                  <div className="inline-flex items-center space-x-2 bg-green-500/20 border border-green-500/30 rounded-full px-6 py-2 mb-8">
                    <Target className="w-4 h-4 text-green-400" />
                    <span className="text-green-400 font-medium">Google Ads Services Delhi • Google Ads Company Delhi</span>
                  </div>

                  <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
                    Best Google Ads Services Delhi | Professional Google Ads Company Delhi &
                    <span className="bg-gradient-to-r from-green-400 to-green-600 bg-clip-text text-transparent"> PPC Services Delhi</span>
                  </h1>

                  <p className="text-xl text-slate-300 mb-8">
                    Premier Google Ads services Delhi offering comprehensive Google Ads company Delhi solutions and professional PPC services Delhi with proven results. Our Google advertising Delhi provides expert Google Ads management Delhi, Google Ads expert Delhi, and Google Ads consultant Delhi services. With 200+ Delhi campaigns, 450% ROI increase, and ₹50Cr+ ad spend managed, we deliver the best Google Ads services Delhi. Expert Google search ads Delhi by GOD Digital Marketing. Latest 2025 insight: Google Ads services Delhi increase conversions by 94%.
                  </p>

                  <div className="flex flex-col sm:flex-row gap-4 mb-8">
                    <Button asChild size="lg" className="bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white text-lg px-8 py-4">
                      <Link to="/quote">Get Free Google Ads Audit Delhi</Link>
                    </Button>
                    <Button asChild size="lg" variant="outline" className="border-green-500 text-green-400 hover:bg-green-500 hover:text-white text-lg px-8 py-4">
                      <Link to="/contact">Call Google Ads Experts Delhi: +91-8708577598</Link>
                    </Button>
                  </div>

                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                    {achievements.map((achievement, index) => (
                      <div key={index} className="flex items-center justify-center space-x-2 bg-slate-800/50 rounded-lg p-3">
                        <CheckCircle className="w-4 h-4 text-green-400 flex-shrink-0" />
                        <span className="text-slate-300">{achievement}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </section>

              <section className="mb-16 bg-slate-800/50 rounded-2xl p-8">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Google Ads Company Delhi
                    <span className="bg-gradient-to-r from-green-400 to-green-600 bg-clip-text text-transparent"> Success Metrics</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Measurable results from our professional Google Ads services Delhi across all industries and campaign types.
                  </p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
                  {stats.map((stat, index) => (
                    <div key={index} className="text-center">
                      <div className="text-4xl font-bold text-green-400 mb-2">{stat.metric}</div>
                      <div className="text-white font-semibold mb-1">{stat.description}</div>
                      <div className="text-slate-400">{stat.detail}</div>
                    </div>
                  ))}
                </div>
              </section>

              <section className="bg-gradient-to-r from-green-600 to-green-500 rounded-2xl p-8 text-center">
                <h2 className="text-3xl md:text-5xl font-bold mb-6">Ready to Dominate Delhi Market with Google Ads?</h2>
                <p className="text-xl mb-8">
                  Join 200+ successful Delhi campaigns that trust GOD Digital Marketing for professional Google Ads services Delhi. Proven Google Ads strategies delivering 450% ROI increase and ₹50Cr+ ad spend management.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button asChild size="lg" className="bg-white text-green-600 hover:bg-slate-100 text-lg px-8 py-4">
                    <Link to="/quote">Get Free Google Ads Audit Delhi</Link>
                  </Button>
                  <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-green-600 text-lg px-8 py-4">
                    <Link to="/contact">Call Google Ads Experts Delhi: +91-8708577598</Link>
                  </Button>
                </div>
              </section>
            </div>

            <div className="lg:col-span-1">
              <Sidebar currentService="services" />
            </div>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default GoogleAdsDelhi;

        {/* Campaign Types Section */}
        <section className="py-20">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-6xl font-bold mb-6">
                Google Ads Campaign Types We
                <span className="bg-gradient-to-r from-green-400 to-green-600 bg-clip-text text-transparent"> Manage</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-4xl mx-auto">
                Comprehensive Google Ads campaign management across all platforms and formats for Delhi businesses.
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-4 gap-8 mb-20">
              {campaignTypes.map((campaign, index) => (
                <Card key={index} className="bg-slate-900/80 border-green-500/20 hover:border-green-500/40 transition-all duration-300">
                  <CardContent className="p-8">
                    <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-green-600 rounded-lg flex items-center justify-center mb-6">
                      <campaign.icon className="w-6 h-6 text-white" />
                    </div>
                    <h3 className="text-xl font-bold text-white mb-4">{campaign.type}</h3>
                    <p className="text-slate-300 mb-6">{campaign.description}</p>
                    <ul className="space-y-2">
                      {campaign.benefits.map((benefit, idx) => (
                        <li key={idx} className="flex items-center text-slate-400 text-sm">
                          <CheckCircle className="w-3 h-3 text-green-400 mr-2 flex-shrink-0" />
                          {benefit}
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Google Ads Services Grid */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-6xl font-bold mb-6">
                Complete Google Ads Services
                <span className="bg-gradient-to-r from-green-400 to-green-600 bg-clip-text text-transparent"> Available in Delhi</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-4xl mx-auto">
                Comprehensive range of Google Ads services covering every aspect of PPC advertising for Delhi businesses.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {googleAdsServices.map((service, index) => (
                <Card key={index} className="bg-slate-900/80 border-green-500/20 hover:border-green-500/40 transition-all duration-300">
                  <CardContent className="p-6 text-center">
                    <Target className="w-8 h-8 text-green-400 mx-auto mb-4" />
                    <h3 className="text-white font-semibold mb-2 text-sm">{service}</h3>
                    <Button asChild size="sm" variant="outline" className="border-green-500 text-green-400 hover:bg-green-500 hover:text-white text-xs">
                      <Link to="/quote">Get Quote</Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Pricing Section */}
        <section className="py-20">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-6xl font-bold mb-6">
                Delhi Google Ads Management
                <span className="bg-gradient-to-r from-green-400 to-green-600 bg-clip-text text-transparent"> Pricing</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-4xl mx-auto">
                Transparent Google Ads management pricing designed for Delhi businesses of all sizes.
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
              {googleAdsPackages.map((pkg, index) => (
                <Card key={index} className={`bg-slate-900/80 border-green-500/20 hover:border-green-500/40 transition-all duration-300 relative ${pkg.popular ? 'ring-2 ring-green-500' : ''}`}>
                  {pkg.popular && (
                    <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                      <span className="bg-green-500 text-white px-4 py-2 rounded-full text-sm font-semibold">Most Popular</span>
                    </div>
                  )}
                  <CardContent className="p-8">
                    <h3 className="text-2xl font-bold text-white mb-2">{pkg.name}</h3>
                    <p className="text-slate-300 mb-6">{pkg.description}</p>
                    <div className="mb-6">
                      <span className="text-4xl font-bold text-green-400">{pkg.price}</span>
                      <span className="text-slate-400">{pkg.period}</span>
                      <div className="text-sm text-slate-400">{pkg.adSpend}</div>
                    </div>
                    <ul className="space-y-3 mb-8">
                      {pkg.features.map((feature, idx) => (
                        <li key={idx} className="flex items-center text-slate-300">
                          <CheckCircle className="w-4 h-4 text-green-400 mr-3 flex-shrink-0" />
                          {feature}
                        </li>
                      ))}
                    </ul>
                    <Button asChild className={`w-full ${pkg.popular ? 'bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700' : 'bg-slate-700 hover:bg-slate-600'} text-white`}>
                      <Link to="/quote">Get Started</Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 bg-gradient-to-r from-green-600 to-green-500">
          <div className="container mx-auto px-6 text-center">
            <h2 className="text-4xl md:text-6xl font-bold mb-6">Ready to Dominate Delhi with Google Ads?</h2>
            <p className="text-xl mb-8 max-w-4xl mx-auto">
              Join 1200+ Delhi businesses that trust GOD Digital Marketing for Google Ads success. Proven strategies that deliver 450% ROAS and ₹28Cr+ revenue generation.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button asChild size="lg" className="bg-white text-green-600 hover:bg-slate-100 text-lg px-8 py-4">
                <Link to="/quote">Get Free Google Ads Audit</Link>
              </Button>
              <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-green-600 text-lg px-8 py-4">
                <Link to="/contact">Call PPC Experts: +91-8708577598</Link>
              </Button>
            </div>
          </div>
        </section>
      </main>
      
      <Footer />
    </div>
  );
};

export default GoogleAdsDelhi;
