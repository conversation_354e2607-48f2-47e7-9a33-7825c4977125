
import React from 'react';
import { Link } from 'react-router-dom';
import { ArrowRight, CheckCircle, Star, Play, TrendingUp, Users, Award, Globe } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { motion } from 'framer-motion';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import DigitalValueStream from '@/components/DigitalValueStream';
import TestimonialsSection from '@/components/TestimonialsSection';
import IndustryShowcase from '@/components/IndustryShowcase';
import ImmersiveHero from '@/components/ImmersiveHero';
import ServicesOverview from '@/components/home/<USER>';
import ResultsSection from '@/components/home/<USER>';
import BlogPreview from '@/components/home/<USER>';
import FloatingActionButton from '@/components/FloatingActionButton';
import { Helmet } from 'react-helmet-async';

const Index = () => {
  // Advanced SEO Optimization Data
  const primaryKeyword = "digital marketing agency India";
  const secondaryKeywords = [
    "SEO services India",
    "PPC management India",
    "social media marketing India",
    "web development India",
    "content marketing India",
    "email marketing India"
  ];

  const lsiKeywords = [
    "digital marketing company",
    "online marketing services",
    "internet marketing agency",
    "digital advertising",
    "search engine optimization",
    "pay per click advertising",
    "social media management",
    "website development",
    "content creation services",
    "email campaign management",
    "conversion optimization",
    "digital strategy",
    "online presence",
    "brand awareness",
    "lead generation"
  ];

  const entities = [
    "GOD Digital Marketing",
    "Nitin Tyagi",
    "Google",
    "Facebook",
    "Instagram",
    "LinkedIn",
    "YouTube",
    "Google Analytics",
    "Google Ads",
    "Search Console"
  ];

  // Generate all keywords for meta tags
  const allKeywords = [
    primaryKeyword,
    ...secondaryKeywords,
    ...lsiKeywords,
    "best digital marketing agency India",
    "top SEO company India",
    "professional PPC services",
    "social media experts India",
    "digital marketing consultant",
    "online marketing specialist"
  ].join(", ");

  return (
    <div className="min-h-screen bg-slate-900 text-white relative overflow-hidden">
      {/* Advanced SEO Meta Tags */}
      <Helmet>
        {/* Primary Meta Tags - Optimized for Target Keywords */}
        <title>Best Digital Marketing Agency India | GOD Digital Marketing | Nitin Tyagi</title>
        <meta name="description" content="#1 Digital Marketing Agency India by Nitin Tyagi. 7+ years international SEO experience, ₹2,850Cr+ revenue generated. Expert SEO services, PPC management, social media marketing, web development. Serving 500+ businesses across 6 countries with proven results." />
        <meta name="keywords" content={allKeywords} />
        <link rel="canonical" href="https://goddigitalmarketing.com/" />

        {/* Advanced SEO Meta Tags */}
        <meta name="author" content="Nitin Tyagi" />
        <meta name="robots" content="index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1" />
        <meta name="googlebot" content="index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1" />
        <meta name="bingbot" content="index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1" />

        {/* Geographic & Location SEO */}
        <meta name="geo.region" content="IN" />
        <meta name="geo.country" content="India" />
        <meta name="geo.placename" content="India" />
        <meta name="coverage" content="India" />
        <meta name="distribution" content="global" />
        <meta name="target" content="all" />

        {/* Open Graph Meta Tags */}
        <meta property="og:title" content="Best Digital Marketing Agency India | GOD Digital Marketing" />
        <meta property="og:description" content="#1 Digital Marketing Agency India by Nitin Tyagi. 7+ years international SEO experience, ₹2,850Cr+ revenue generated. Expert SEO, PPC, social media marketing services." />
        <meta property="og:type" content="website" />
        <meta property="og:url" content="https://goddigitalmarketing.com/" />
        <meta property="og:image" content="https://goddigitalmarketing.com/og-image.jpg" />
        <meta property="og:image:width" content="1200" />
        <meta property="og:image:height" content="630" />
        <meta property="og:site_name" content="GOD Digital Marketing" />
        <meta property="og:locale" content="en_IN" />

        {/* Twitter Card Meta Tags */}
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:site" content="@goddigitalmarketing" />
        <meta name="twitter:creator" content="@nitintyagi" />
        <meta name="twitter:title" content="Best Digital Marketing Agency India | GOD Digital Marketing" />
        <meta name="twitter:description" content="#1 Digital Marketing Agency India by Nitin Tyagi. 7+ years international SEO experience, ₹2,850Cr+ revenue generated." />
        <meta name="twitter:image" content="https://goddigitalmarketing.com/og-image.jpg" />

        {/* Schema.org Structured Data */}
        <script type="application/ld+json">
          {JSON.stringify({
            "@context": "https://schema.org",
            "@type": "Organization",
            "name": "GOD Digital Marketing",
            "alternateName": "GOD Digital Marketing Agency",
            "url": "https://goddigitalmarketing.com",
            "logo": "https://goddigitalmarketing.com/logo.png",
            "description": "#1 Digital Marketing Agency India by Nitin Tyagi. 7+ years international SEO experience, ₹2,850Cr+ revenue generated.",
            "founder": {
              "@type": "Person",
              "name": "Nitin Tyagi",
              "jobTitle": "Founder & CEO",
              "email": "<EMAIL>",
              "telephone": "+91-8708577598"
            },
            "foundingDate": "2018",
            "numberOfEmployees": "50+",
            "address": {
              "@type": "PostalAddress",
              "addressCountry": "India",
              "addressRegion": "India"
            },
            "contactPoint": {
              "@type": "ContactPoint",
              "telephone": "+91-8708577598",
              "email": "<EMAIL>",
              "contactType": "Customer Service",
              "areaServed": "IN",
              "availableLanguage": ["English", "Hindi"]
            },
            "sameAs": [
              "https://www.linkedin.com/company/god-digital-marketing",
              "https://www.facebook.com/goddigitalmarketing",
              "https://twitter.com/goddigitalmarketing",
              "https://www.instagram.com/goddigitalmarketing"
            ],
            "aggregateRating": {
              "@type": "AggregateRating",
              "ratingValue": "4.9",
              "reviewCount": "500+",
              "bestRating": "5",
              "worstRating": "1"
            },
            "offers": {
              "@type": "Offer",
              "description": "Digital Marketing Services",
              "priceRange": "₹60,000 - ₹150,000"
            }
          })}
        </script>

        {/* Local Business Schema */}
        <script type="application/ld+json">
          {JSON.stringify({
            "@context": "https://schema.org",
            "@type": "LocalBusiness",
            "name": "GOD Digital Marketing",
            "image": "https://goddigitalmarketing.com/logo.png",
            "telephone": "+91-8708577598",
            "email": "<EMAIL>",
            "address": {
              "@type": "PostalAddress",
              "addressCountry": "India"
            },
            "geo": {
              "@type": "GeoCoordinates",
              "latitude": "28.6139",
              "longitude": "77.2090"
            },
            "url": "https://goddigitalmarketing.com",
            "priceRange": "₹₹₹",
            "openingHours": "Mo-Fr 09:00-18:00",
            "aggregateRating": {
              "@type": "AggregateRating",
              "ratingValue": "4.9",
              "reviewCount": "500+"
            }
          })}
        </script>
      </Helmet>

      <DigitalValueStream />
      <Navigation />
      
      <main className="relative z-10">
        <ImmersiveHero />
        <ServicesOverview />
        <ResultsSection />
        <IndustryShowcase />
        <TestimonialsSection />
        <BlogPreview />
        
        {/* Final CTA Section */}
        <section className="py-20 bg-gradient-to-r from-amber-600 to-amber-500 relative overflow-hidden">
          {/* Animated background elements */}
          <div className="absolute inset-0 bg-black/20"></div>
          <motion.div
            className="absolute top-10 left-10 w-32 h-32 bg-white/10 rounded-full blur-xl"
            animate={{
              x: [0, 50, 0],
              y: [0, -30, 0],
              scale: [1, 1.2, 1],
            }}
            transition={{
              duration: 8,
              repeat: Infinity,
              ease: "easeInOut"
            }}
          />
          <motion.div
            className="absolute bottom-10 right-10 w-24 h-24 bg-white/10 rounded-full blur-xl"
            animate={{
              x: [0, -40, 0],
              y: [0, 20, 0],
              scale: [1, 0.8, 1],
            }}
            transition={{
              duration: 6,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 2
            }}
          />

          <div className="container mx-auto px-6 text-center relative z-10">
            <motion.h2
              className="text-4xl font-bold mb-6"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              Ready to Scale Your Business with Digital Marketing?
            </motion.h2>
            <motion.p
              className="text-xl mb-8 max-w-3xl mx-auto"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              viewport={{ once: true }}
            >
              Partner with India's leading digital marketing agency. Our comprehensive digital marketing services include SEO, Google Ads, social media marketing, content marketing, email marketing, and web development. 7 years proven track record delivering results across 6 countries with ₹60K-150K monthly investment plans.
            </motion.p>
            <motion.div
              className="flex flex-col sm:flex-row gap-4 justify-center"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              viewport={{ once: true }}
            >
              <motion.div
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Button asChild size="lg" className="bg-white text-amber-600 hover:bg-slate-100 text-lg px-8 py-4 shadow-lg hover:shadow-xl transition-shadow duration-300">
                  <Link to="/quote" className="flex items-center gap-2">
                    Get Digital Marketing Strategy
                    <motion.div
                      animate={{ x: [0, 5, 0] }}
                      transition={{ duration: 1.5, repeat: Infinity, ease: "easeInOut" }}
                    >
                      <ArrowRight className="w-5 h-5" />
                    </motion.div>
                  </Link>
                </Button>
              </motion.div>
              <motion.div
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-amber-600 text-lg px-8 py-4 backdrop-blur-sm">
                  <Link to="/contact">Book Digital Marketing Consultation</Link>
                </Button>
              </motion.div>
            </motion.div>
          </div>
        </section>
      </main>

      <Footer />
      <FloatingActionButton />
    </div>
  );
};

export default Index;
