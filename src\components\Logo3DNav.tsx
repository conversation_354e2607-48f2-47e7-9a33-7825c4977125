
import React, { useRef, useMemo } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import { Text } from '@react-three/drei';
import { Mesh, Group } from 'three';

// Floating 3D Logo for Navigation
const FloatingLogo = () => {
  const groupRef = useRef<Group>(null);
  const sphereRef = useRef<Mesh>(null);

  useFrame((state) => {
    if (groupRef.current) {
      groupRef.current.rotation.y = Math.sin(state.clock.elapsedTime * 0.5) * 0.1;
      groupRef.current.position.y = Math.sin(state.clock.elapsedTime * 2) * 0.02;
    }
    if (sphereRef.current) {
      sphereRef.current.rotation.x += 0.01;
      sphereRef.current.rotation.y += 0.01;
    }
  });

  return (
    <group ref={groupRef}>
      {/* Main sphere with gradient material */}
      <mesh ref={sphereRef}>
        <sphereGeometry args={[0.4, 32, 32]} />
        <meshStandardMaterial 
          color="#fbbf24" 
          metalness={0.9}
          roughness={0.1}
          emissive="#f59e0b"
          emissiveIntensity={0.2}
        />
      </mesh>

      {/* Orbital ring */}
      <mesh rotation={[0.5, 0, 0]}>
        <torusGeometry args={[0.6, 0.02, 8, 64]} />
        <meshStandardMaterial 
          color="#0ea5e9" 
          metalness={0.8}
          roughness={0.2}
          emissive="#0ea5e9"
          emissiveIntensity={0.1}
        />
      </mesh>

      {/* G letter */}
      <Text
        fontSize={0.3}
        color="#1e293b"
        anchorX="center"
        anchorY="middle"
        position={[0, 0, 0.42]}
        font="/fonts/Inter-Bold.woff"
      >
        G
      </Text>

      {/* Floating particles */}
      <mesh position={[0.8, 0.3, 0.2]}>
        <sphereGeometry args={[0.03, 8, 8]} />
        <meshStandardMaterial color="#10b981" emissive="#10b981" emissiveIntensity={0.3} />
      </mesh>
      <mesh position={[-0.7, -0.2, 0.3]}>
        <sphereGeometry args={[0.025, 8, 8]} />
        <meshStandardMaterial color="#8b5cf6" emissive="#8b5cf6" emissiveIntensity={0.3} />
      </mesh>
    </group>
  );
};

// Scene with optimized lighting
const LogoScene = () => {
  return (
    <>
      <ambientLight intensity={0.4} />
      <pointLight position={[2, 2, 2]} intensity={0.6} color="#ffffff" />
      <pointLight position={[-1, -1, 1]} intensity={0.3} color="#fbbf24" />
      <FloatingLogo />
    </>
  );
};

// Main 3D Navigation Logo Component
const Logo3DNav = () => {
  const canvasSettings = useMemo(() => ({
    camera: { 
      position: [0, 0, 2] as [number, number, number], 
      fov: 45
    },
    gl: { 
      antialias: false,
      alpha: true,
      powerPreference: "default" as const
    },
    dpr: Math.min(window.devicePixelRatio, 2)
  }), []);

  return (
    <div className="w-12 h-12 relative">
      <Canvas {...canvasSettings} frameloop="demand">
        <LogoScene />
      </Canvas>
      {/* Fallback overlay for loading */}
      <div className="absolute inset-0 bg-gradient-to-br from-amber-400 via-amber-500 to-amber-600 rounded-xl flex items-center justify-center font-black text-slate-900 text-xl opacity-0 animate-pulse">
        G
      </div>
    </div>
  );
};

export default Logo3DNav;
