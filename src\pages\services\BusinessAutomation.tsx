import React from 'react';
import { Link } from 'react-router-dom';
import { Workflow, Zap, TrendingUp, Users, CheckCircle, Building, Star, Crown, Target, ArrowRight, Settings, Cog, BarChart } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';
import Sidebar from '@/components/Sidebar';

const BusinessAutomation = () => {
  const stats = [
    {
      metric: '1,200+',
      description: 'Business Processes Automated',
      detail: 'Across all industries'
    },
    {
      metric: '80%',
      description: 'Average Time Savings',
      detail: 'Through automation'
    },
    {
      metric: '₹200Cr+',
      description: 'Operational Cost Savings',
      detail: 'For our clients'
    },
    {
      metric: '98%',
      description: 'Process Accuracy Improvement',
      detail: 'Error reduction achieved'
    }
  ];

  const achievements = [
    'Top Business Automation Company in India',
    'Workflow Optimization Specialists',
    'Process Automation Experts',
    'Digital Transformation Leaders',
    'Business Efficiency Champions',
    'Automation Integration Pioneers'
  ];

  const automationServices = [
    {
      type: 'Workflow Automation',
      description: 'Streamline business workflows and eliminate manual processes for maximum efficiency',
      icon: Workflow,
      features: ['Custom Workflow Design', 'Process Mapping & Optimization', 'Task Automation', 'Approval Workflow Systems', 'Document Workflow Automation', 'Multi-department Integration']
    },
    {
      type: 'CRM & Sales Automation',
      description: 'Automate sales processes, lead management, and customer relationship workflows',
      icon: Users,
      features: ['Lead Capture Automation', 'Sales Pipeline Management', 'Customer Onboarding Automation', 'Follow-up Automation', 'Quote Generation Systems', 'Sales Reporting Automation']
    },
    {
      type: 'Financial Process Automation',
      description: 'Automate accounting, invoicing, and financial management processes',
      icon: BarChart,
      features: ['Invoice Generation Automation', 'Payment Processing Systems', 'Expense Management Automation', 'Financial Reporting', 'Tax Calculation Automation', 'Budget Tracking Systems']
    },
    {
      type: 'HR & Operations Automation',
      description: 'Streamline human resources and operational processes for better efficiency',
      icon: Settings,
      features: ['Employee Onboarding Automation', 'Payroll Processing Systems', 'Leave Management Automation', 'Performance Review Automation', 'Inventory Management Systems', 'Supply Chain Automation']
    }
  ];

  const industryAutomation = [
    {
      industry: 'Manufacturing',
      processes: ['Production Planning', 'Quality Control', 'Inventory Management', 'Supply Chain Optimization'],
      clients: '280+',
      efficiency: '85%'
    },
    {
      industry: 'Healthcare',
      processes: ['Patient Management', 'Appointment Scheduling', 'Billing Automation', 'Medical Records'],
      clients: '220+',
      efficiency: '78%'
    },
    {
      industry: 'E-commerce',
      processes: ['Order Processing', 'Inventory Updates', 'Customer Service', 'Shipping Automation'],
      clients: '350+',
      efficiency: '82%'
    },
    {
      industry: 'Finance',
      processes: ['Document Processing', 'Compliance Reporting', 'Risk Assessment', 'Customer Onboarding'],
      clients: '180+',
      efficiency: '88%'
    },
    {
      industry: 'Real Estate',
      processes: ['Lead Management', 'Property Listings', 'Contract Processing', 'Client Communication'],
      clients: '150+',
      efficiency: '75%'
    },
    {
      industry: 'Education',
      processes: ['Student Enrollment', 'Grade Management', 'Communication Systems', 'Resource Planning'],
      clients: '120+',
      efficiency: '80%'
    }
  ];

  const caseStudies = [
    {
      client: 'Manufacturing Enterprise',
      industry: 'Industrial Manufacturing',
      challenge: 'Manual production planning and inventory management causing delays and excess costs',
      solution: 'Comprehensive automation system integrating production, inventory, and supply chain management',
      results: ['70% reduction in planning time', '85% inventory accuracy improvement', '₹5.2Cr annual cost savings', '40% faster order fulfillment']
    },
    {
      client: 'Healthcare Network',
      industry: 'Healthcare Services',
      challenge: 'Manual patient scheduling and billing processes creating inefficiencies and errors',
      solution: 'Integrated patient management system with automated scheduling and billing workflows',
      results: ['90% appointment scheduling automation', '95% billing accuracy improvement', '₹3.8Cr operational savings', '60% reduction in administrative time']
    },
    {
      client: 'E-commerce Platform',
      industry: 'Online Retail',
      challenge: 'Manual order processing and customer service handling causing delays and poor experience',
      solution: 'End-to-end automation from order processing to customer communication and fulfillment',
      results: ['95% order processing automation', '80% faster order fulfillment', '₹4.5Cr efficiency savings', '92% customer satisfaction score']
    }
  ];

  return (
    <div className="min-h-screen bg-slate-900 relative">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        <div className="container mx-auto px-6 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            <div className="lg:col-span-3">
              <section className="pt-24 pb-16">
                <div className="max-w-4xl">
                  <div className="inline-flex items-center space-x-2 bg-green-500/20 border border-green-500/30 rounded-full px-6 py-2 mb-8">
                    <Workflow className="w-4 h-4 text-green-400" />
                    <span className="text-green-400 font-medium">Business Automation • Process Optimization Excellence</span>
                  </div>
                  
                  <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
                    #1 Business Automation Company in
                    <span className="bg-gradient-to-r from-green-400 to-green-600 bg-clip-text text-transparent"> India</span>
                  </h1>
                  
                  <p className="text-xl text-slate-300 mb-8">
                    Premier business automation services offering comprehensive process optimization solutions including workflow automation, CRM automation, financial process automation, and operational efficiency systems. Our business automation company provides professional automation services with custom workflow design, process mapping, task automation, and digital transformation strategies. Serving 1,200+ businesses across all industries with proven ₹200Cr+ operational cost savings and 80% average time reduction through strategic business automation and process optimization excellence.
                  </p>
                  
                  <div className="flex flex-col sm:flex-row gap-4 mb-8">
                    <Button asChild size="lg" className="bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white text-lg px-8 py-4">
                      <Link to="/quote">Get Free Business Automation Audit</Link>
                    </Button>
                    <Button asChild size="lg" variant="outline" className="border-green-500 text-green-400 hover:bg-green-500 hover:text-white text-lg px-8 py-4">
                      <Link to="/contact">Call Automation Experts: +91-8708577598</Link>
                    </Button>
                  </div>

                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                    {achievements.map((achievement, index) => (
                      <div key={index} className="flex items-center justify-center space-x-2 bg-slate-800/50 rounded-lg p-3">
                        <CheckCircle className="w-4 h-4 text-green-400 flex-shrink-0" />
                        <span className="text-slate-300">{achievement}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </section>

              <section className="mb-16 bg-slate-800/50 rounded-2xl p-8">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Business Automation
                    <span className="bg-gradient-to-r from-green-400 to-green-600 bg-clip-text text-transparent"> Success Metrics</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Measurable business automation results from companies across all industries and business sizes.
                  </p>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
                  {stats.map((stat, index) => (
                    <div key={index} className="text-center">
                      <div className="text-4xl font-bold text-green-400 mb-2">{stat.metric}</div>
                      <div className="text-white font-semibold mb-1">{stat.description}</div>
                      <div className="text-slate-400">{stat.detail}</div>
                    </div>
                  ))}
                </div>
              </section>

              <section className="mb-16">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Business Automation Services We
                    <span className="bg-gradient-to-r from-green-400 to-green-600 bg-clip-text text-transparent"> Provide</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Comprehensive automation solutions designed to transform your business operations and maximize efficiency.
                  </p>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  {automationServices.map((service, index) => (
                    <Card key={index} className="bg-slate-900/80 border-green-500/20 hover:border-green-500/40 transition-all duration-300">
                      <CardContent className="p-8">
                        <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-green-600 rounded-lg flex items-center justify-center mb-6">
                          <service.icon className="w-6 h-6 text-white" />
                        </div>
                        <h3 className="text-xl font-bold text-white mb-4">{service.type}</h3>
                        <p className="text-slate-300 mb-6">{service.description}</p>
                        <ul className="space-y-2">
                          {service.features.map((feature, idx) => (
                            <li key={idx} className="flex items-center text-slate-400 text-sm">
                              <CheckCircle className="w-3 h-3 text-green-400 mr-2 flex-shrink-0" />
                              {feature}
                            </li>
                          ))}
                        </ul>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>

              {/* Industry Automation */}
              <section className="mb-16 bg-slate-800/30 rounded-2xl p-8">
                <h3 className="text-2xl font-bold text-white mb-6 text-center">
                  Industry-Specific Automation Solutions
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {industryAutomation.map((industry, index) => (
                    <div key={index} className="bg-slate-900/50 rounded-lg p-6">
                      <h4 className="text-green-400 font-semibold mb-3">{industry.industry}</h4>
                      <ul className="space-y-2 mb-4">
                        {industry.processes.map((process, idx) => (
                          <li key={idx} className="text-slate-300 text-sm flex items-center">
                            <CheckCircle className="w-3 h-3 text-green-400 mr-2 flex-shrink-0" />
                            {process}
                          </li>
                        ))}
                      </ul>
                      <div className="border-t border-slate-700 pt-3">
                        <div className="text-white font-semibold">{industry.clients}</div>
                        <div className="text-slate-400 text-sm mb-1">Clients Served</div>
                        <div className="text-green-400 font-semibold">{industry.efficiency} efficiency gain</div>
                      </div>
                    </div>
                  ))}
                </div>
              </section>

              {/* Case Studies */}
              <section className="mb-16">
                <h3 className="text-2xl font-bold text-white mb-8 text-center">
                  Business Automation Success Stories
                </h3>
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                  {caseStudies.map((study, index) => (
                    <Card key={index} className="bg-slate-900/60 border-green-500/20 hover:border-green-500/40 transition-all duration-300">
                      <CardContent className="p-6">
                        <h4 className="text-green-400 font-semibold mb-2">{study.client}</h4>
                        <p className="text-slate-400 text-sm mb-3">{study.industry}</p>
                        <div className="mb-4">
                          <h5 className="text-white font-medium text-sm mb-1">Challenge:</h5>
                          <p className="text-slate-300 text-sm mb-3">{study.challenge}</p>
                          <h5 className="text-white font-medium text-sm mb-1">Solution:</h5>
                          <p className="text-slate-300 text-sm mb-3">{study.solution}</p>
                        </div>
                        <div className="bg-slate-800/50 rounded p-3">
                          <h5 className="text-green-400 font-semibold mb-2 text-sm">Results:</h5>
                          <ul className="space-y-1">
                            {study.results.map((result, idx) => (
                              <li key={idx} className="text-slate-400 text-xs flex items-center">
                                <CheckCircle className="w-3 h-3 text-green-400 mr-2 flex-shrink-0" />
                                {result}
                              </li>
                            ))}
                          </ul>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>

              {/* Internal Linking Section */}
              <section className="mb-16 bg-slate-800/30 rounded-2xl p-8">
                <h3 className="text-2xl font-bold text-white mb-6 text-center">
                  Complete Digital Transformation Solutions
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  <Link to="/services/ai-automation" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-green-400 font-semibold mb-2 group-hover:text-green-300">AI Automation</h4>
                    <p className="text-slate-400 text-sm">Artificial intelligence powered automation and intelligent systems</p>
                  </Link>
                  <Link to="/services/web-development" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-green-400 font-semibold mb-2 group-hover:text-green-300">Custom Software Development</h4>
                    <p className="text-slate-400 text-sm">Tailored software solutions for business automation</p>
                  </Link>
                  <Link to="/services/business-development" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-green-400 font-semibold mb-2 group-hover:text-green-300">Business Development</h4>
                    <p className="text-slate-400 text-sm">Strategic business growth and development services</p>
                  </Link>
                  <Link to="/services/seo" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-green-400 font-semibold mb-2 group-hover:text-green-300">SEO Automation</h4>
                    <p className="text-slate-400 text-sm">Automated SEO processes and optimization workflows</p>
                  </Link>
                  <Link to="/services/ppc" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-green-400 font-semibold mb-2 group-hover:text-green-300">PPC Automation</h4>
                    <p className="text-slate-400 text-sm">Automated advertising campaigns and bid management</p>
                  </Link>
                  <Link to="/portfolio" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-green-400 font-semibold mb-2 group-hover:text-green-300">Automation Success Stories</h4>
                    <p className="text-slate-400 text-sm">See proven results from our automation projects</p>
                  </Link>
                </div>
              </section>

              <section className="bg-gradient-to-r from-green-600 to-green-500 rounded-2xl p-8 text-center">
                <h2 className="text-3xl md:text-5xl font-bold mb-6">Ready to Automate Your Business Processes?</h2>
                <p className="text-xl mb-8">
                  Join 1,200+ businesses that trust GOD Digital Marketing for automation success. Proven solutions that deliver 80% time savings and ₹200Cr+ cost reduction.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button asChild size="lg" className="bg-white text-green-600 hover:bg-slate-100 text-lg px-8 py-4">
                    <Link to="/quote">Get Free Automation Audit</Link>
                  </Button>
                  <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-green-600 text-lg px-8 py-4">
                    <Link to="/contact">Call Automation Experts: +91-8708577598</Link>
                  </Button>
                </div>
              </section>
            </div>

            <div className="lg:col-span-1">
              <Sidebar currentService="business-automation" />
            </div>
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
};

export default BusinessAutomation;
