import React from 'react';
import { Link } from 'react-router-dom';
import { ExternalLink } from 'lucide-react';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';

const Sitemap = () => {
  const siteStructure = {
    'Main Pages': [
      { name: 'Home', url: '/' },
      { name: 'About Us', url: '/about' },
      { name: 'Services', url: '/services' },
      { name: 'Case Studies', url: '/case-studies' },
      { name: 'Blog', url: '/blog' },
      { name: 'Contact', url: '/contact' },
      { name: 'Get Quote', url: '/quote' }
    ],
    'SEO Services': [
      { name: 'SEO Services', url: '/services/seo' },
      { name: 'Local SEO', url: '/services/seo/local' },
      { name: 'International SEO', url: '/services/seo/international' },
      { name: 'E-commerce SEO', url: '/services/seo/ecommerce' },
      { name: 'Technical SEO', url: '/services/seo/technical' },
      { name: 'Enterprise SEO', url: '/services/seo/enterprise' }
    ],
    'AI Automation Services': [
      { name: 'AI Content Creation', url: '/services/ai-content' },
      { name: 'Social Media Automation', url: '/services/social-media' },
      { name: 'Business Automation', url: '/services/business-automation' },
      { name: 'Marketing Automation', url: '/services/marketing-automation' }
    ],
    'Paid Advertising': [
      { name: 'Google Ads (PPC)', url: '/services/ppc' },
      { name: 'Facebook Ads', url: '/services/facebook-ads' },
      { name: 'LinkedIn Ads', url: '/services/linkedin-ads' },
      { name: 'YouTube Marketing', url: '/services/youtube-marketing' }
    ],
    'Other Services': [
      { name: 'Email Marketing', url: '/services/email' },
      { name: 'Content Marketing', url: '/services/content' },
      { name: 'Web Design', url: '/services/web-design' }
    ],
    'Top Locations': [
      { name: 'Mumbai Digital Marketing', url: '/locations/mumbai' },
      { name: 'Delhi Digital Marketing', url: '/locations/delhi' },
      { name: 'Bangalore Digital Marketing', url: '/locations/bangalore' },
      { name: 'Chennai Digital Marketing', url: '/locations/chennai' },
      { name: 'Kolkata Digital Marketing', url: '/locations/kolkata' },
      { name: 'Hyderabad Digital Marketing', url: '/locations/hyderabad' },
      { name: 'Pune Digital Marketing', url: '/locations/pune' },
      { name: 'Jaipur Digital Marketing', url: '/locations/jaipur' },
      { name: 'Lucknow Digital Marketing', url: '/locations/lucknow' },
      { name: 'Surat Digital Marketing', url: '/locations/surat' }
    ],
    'States': [
      { name: 'Maharashtra Digital Marketing', url: '/locations/maharashtra' },
      { name: 'Gujarat Digital Marketing', url: '/locations/gujarat' },
      { name: 'Karnataka Digital Marketing', url: '/locations/karnataka' },
      { name: 'Tamil Nadu Digital Marketing', url: '/locations/tamil-nadu' },
      { name: 'Haryana Digital Marketing', url: '/locations/haryana' }
    ],
    'Haryana Cities': [
      { name: 'Gurgaon Digital Marketing', url: '/locations/haryana/gurgaon' },
      { name: 'Faridabad Digital Marketing', url: '/locations/haryana/faridabad' },
      { name: 'Panipat Digital Marketing', url: '/locations/haryana/panipat' },
      { name: 'Ambala Digital Marketing', url: '/locations/haryana/ambala' },
      { name: 'Yamunanagar Digital Marketing', url: '/locations/haryana/yamunanagar' },
      { name: 'Rohtak Digital Marketing', url: '/locations/haryana/rohtak' },
      { name: 'Hisar Digital Marketing', url: '/locations/haryana/hisar' },
      { name: 'Karnal Digital Marketing', url: '/locations/haryana/karnal' }
    ],
    'Industries': [
      { name: 'Real Estate Marketing', url: '/industries/real-estate' },
      { name: 'Healthcare Marketing', url: '/industries/healthcare' },
      { name: 'Education Marketing', url: '/industries/education' },
      { name: 'E-commerce Marketing', url: '/industries/ecommerce' },
      { name: 'Manufacturing Marketing', url: '/industries/manufacturing' },
      { name: 'Financial Services Marketing', url: '/industries/financial-services' },
      { name: 'Automotive Marketing', url: '/industries/automotive' },
      { name: 'Agriculture Marketing', url: '/industries/agriculture' },
      { name: 'Textile Marketing', url: '/industries/textile' },
      { name: 'Chemical Marketing', url: '/industries/chemical' }
    ],
    'Service-Location Pages (34 Pages)': [
      // Delhi Services (7 complete)
      { name: 'SEO Services Delhi', url: '/services/seo/delhi' },
      { name: 'Google Ads Delhi', url: '/services/ppc/delhi' },
      { name: 'Social Media Marketing Delhi', url: '/services/social-media/delhi' },
      { name: 'Content Marketing Delhi', url: '/services/content/delhi' },
      { name: 'Email Marketing Delhi', url: '/services/email/delhi' },
      { name: 'Web Development Delhi', url: '/services/web-development/delhi' },

      // Mumbai Services (6 complete)
      { name: 'SEO Services Mumbai', url: '/services/seo/mumbai' },
      { name: 'Google Ads Mumbai', url: '/services/ppc/mumbai' },
      { name: 'Social Media Marketing Mumbai', url: '/services/social-media/mumbai' },
      { name: 'Content Marketing Mumbai', url: '/services/content/mumbai' },
      { name: 'Email Marketing Mumbai', url: '/services/email/mumbai' },

      // Bangalore Services (6 complete)
      { name: 'SEO Services Bangalore', url: '/services/seo/bangalore' },
      { name: 'Google Ads Bangalore', url: '/services/ppc/bangalore' },
      { name: 'Social Media Marketing Bangalore', url: '/services/social-media/bangalore' },
      { name: 'Content Marketing Bangalore', url: '/services/content/bangalore' },
      { name: 'Email Marketing Bangalore', url: '/services/email/bangalore' },

      // Chennai Services (6 complete)
      { name: 'SEO Services Chennai', url: '/services/seo/chennai' },
      { name: 'Google Ads Chennai', url: '/services/ppc/chennai' },
      { name: 'Social Media Marketing Chennai', url: '/services/social-media/chennai' },
      { name: 'Content Marketing Chennai', url: '/services/content/chennai' },
      { name: 'Email Marketing Chennai', url: '/services/email/chennai' },

      // Hyderabad Services (1 complete)
      { name: 'SEO Services Hyderabad', url: '/services/seo/hyderabad' },

      // Kolkata Services (1 complete)
      { name: 'SEO Services Kolkata', url: '/services/seo/kolkata' },

      // Sonipat Services (3 complete)
      { name: 'SEO Services Sonipat', url: '/services/seo/sonipat' },
      { name: 'Google Ads Sonipat', url: '/services/ppc/sonipat' },
      { name: 'Social Media Marketing Sonipat', url: '/services/social-media/sonipat' }
    ],
    'Resources': [
      { name: 'Digital Marketing Guide', url: '/resources/digital-marketing-guide' },
      { name: 'AI Automation Resources', url: '/resources/ai-automation' },
      { name: 'Industry Reports', url: '/resources/industry-reports' },
      { name: 'Free SEO Tools', url: '/tools/seo-audit' }
    ],
    'Company': [
      { name: 'Testimonials', url: '/testimonials' },
      { name: 'Awards', url: '/awards' },
      { name: 'Careers', url: '/careers' },
      { name: 'Privacy Policy', url: '/privacy-policy' }
    ]
  };

  return (
    <div className="min-h-screen bg-slate-900 relative">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        <section className="pt-32 pb-20">
          <div className="container mx-auto px-6">
            <div className="max-w-6xl mx-auto">
              <div className="text-center mb-16">
                <h1 className="text-5xl md:text-6xl font-bold mb-6 leading-tight">
                  Website
                  <span className="bg-gradient-to-r from-amber-400 to-amber-600 bg-clip-text text-transparent"> Sitemap</span>
                </h1>
                <p className="text-xl text-slate-300 max-w-3xl mx-auto">
                  Complete navigation guide to all pages on GOD Digital Marketing website. Find all our services, locations, and resources. Contact us at +91-8708577598 or <EMAIL> for assistance.
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {Object.entries(siteStructure).map(([category, pages]) => (
                  <div key={category} className="bg-slate-800/50 rounded-lg p-6 border border-amber-500/20">
                    <h2 className="text-2xl font-bold text-white mb-6 border-b border-amber-500/30 pb-3">
                      {category}
                    </h2>
                    <ul className="space-y-3">
                      {pages.map((page, index) => (
                        <li key={index}>
                          <Link 
                            to={page.url}
                            className="flex items-center text-slate-300 hover:text-amber-400 transition-colors group"
                          >
                            <ExternalLink className="w-4 h-4 mr-2 opacity-0 group-hover:opacity-100 transition-opacity" />
                            {page.name}
                          </Link>
                        </li>
                      ))}
                    </ul>
                  </div>
                ))}
              </div>

              {/* SEO Information */}
              <div className="mt-16 bg-slate-800/50 rounded-lg p-8 border border-amber-500/20">
                <h2 className="text-3xl font-bold text-white mb-6">
                  About GOD Digital Marketing
                </h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                  <div>
                    <h3 className="text-xl font-bold text-amber-400 mb-4">Our Expertise</h3>
                    <ul className="text-slate-300 space-y-2">
                      <li>• International SEO across 6 countries</li>
                      <li>• AI-powered content creation and automation</li>
                      <li>• 7 years of proven digital marketing experience</li>
                      <li>• 10+ million monthly views generated</li>
                      <li>• Premium business consultation (₹60K-150K monthly)</li>
                    </ul>
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-amber-400 mb-4">Countries We Serve</h3>
                    <ul className="text-slate-300 space-y-2">
                      <li>• United Kingdom (UK)</li>
                      <li>• United States (US)</li>
                      <li>• Dubai (UAE)</li>
                      <li>• India</li>
                      <li>• Kuwait</li>
                      <li>• South Africa</li>
                    </ul>
                  </div>
                </div>
              </div>

              {/* Contact Information */}
              <div className="mt-16 bg-gradient-to-r from-amber-600 to-amber-500 rounded-lg p-8 text-center">
                <h2 className="text-3xl font-bold mb-4">Need Help Finding What You're Looking For?</h2>
                <p className="text-lg mb-6">
                  Our digital marketing experts are here to help you navigate our services and find the perfect solution for your business.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Link
                    to="/contact"
                    className="bg-white text-amber-600 hover:bg-slate-100 px-8 py-3 rounded-lg font-semibold transition-colors"
                  >
                    Contact Us: +91-8708577598
                  </Link>
                  <Link
                    to="/quote"
                    className="border-2 border-white text-white hover:bg-white hover:text-amber-600 px-8 py-3 rounded-lg font-semibold transition-colors"
                  >
                    Get Free Quote
                  </Link>
                </div>
                <div className="mt-4 text-sm">
                  Email: <EMAIL>
                </div>
              </div>
            </div>
          </div>
        </section>
      </main>
      
      <Footer />
    </div>
  );
};

export default Sitemap;
