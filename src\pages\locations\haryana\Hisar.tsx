import React from 'react';
import { Link } from 'react-router-dom';
import { ArrowRight, MapPin, TrendingUp, Users, CheckCircle, Building, Star, Wheat } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';

const Hisar = () => {
  const services = [
    'SEO Services Hisar',
    'Google Ads Management Hisar',
    'Social Media Marketing Hisar',
    'Local SEO Hisar',
    'E-commerce SEO Hisar',
    'Content Marketing Hisar',
    'Website Development Hisar',
    'Digital Marketing Consulting Hisar'
  ];

  const industries = [
    'Agriculture & Farming Hisar',
    'Steel Industry Hisar',
    'Automobile Industry Hisar',
    'Textile Manufacturing Hisar',
    'Real Estate Hisar',
    'Healthcare Hisar',
    'Education Hisar',
    'Retail & E-commerce Hisar'
  ];

  const areas = [
    'Red Square Digital Marketing',
    'Balsamand SEO Services',
    'Sector 15 Digital Marketing',
    'Jindal Nagar SEO Services',
    'Hansi Digital Marketing',
    'Barwala SEO Services',
    'Adampur Digital Marketing',
    'Uklana SEO Services'
  ];

  const stats = [
    {
      metric: '95+',
      description: 'Hisar Businesses Served',
      detail: 'Across all sectors'
    },
    {
      metric: '390%',
      description: 'Average Traffic Increase',
      detail: 'For Hisar clients'
    },
    {
      metric: '₹32L+',
      description: 'Revenue Generated',
      detail: 'For Hisar businesses'
    }
  ];

  return (
    <div className="min-h-screen bg-slate-900 relative">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        {/* Hero Section */}
        <section className="pt-32 pb-20">
          <div className="container mx-auto px-6">
            <div className="max-w-4xl mx-auto text-center">
              <div className="inline-flex items-center space-x-2 bg-yellow-500/20 border border-yellow-500/30 rounded-full px-6 py-2 mb-8">
                <Wheat className="w-4 h-4 text-yellow-400" />
                <span className="text-yellow-400 font-medium">Hisar Digital Marketing • Steel City & Agricultural Hub</span>
              </div>
              
              <h1 className="text-5xl md:text-6xl font-bold mb-6 leading-tight">
                #1 Digital Marketing Company in
                <span className="bg-gradient-to-r from-yellow-400 to-yellow-600 bg-clip-text text-transparent"> Hisar</span>
              </h1>
              
              <p className="text-xl text-slate-300 mb-12 max-w-3xl mx-auto">
                Leading digital marketing agency in Hisar helping businesses dominate online. From steel manufacturing to agricultural enterprises, we've helped 95+ businesses grow with proven SEO, Google Ads, and digital marketing strategies across all Hisar areas.
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button asChild size="lg" className="bg-gradient-to-r from-yellow-500 to-yellow-600 hover:from-yellow-600 hover:to-yellow-700 text-white text-lg px-8 py-4">
                  <Link to="/quote">Get Free Hisar SEO Audit</Link>
                </Button>
                <Button asChild size="lg" variant="outline" className="border-yellow-500 text-yellow-400 hover:bg-yellow-500 hover:text-white text-lg px-8 py-4">
                  <Link to="/contact">Call Hisar Office</Link>
                </Button>
              </div>
            </div>
          </div>
        </section>

        {/* Stats Section */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Hisar Digital Marketing
                <span className="bg-gradient-to-r from-yellow-400 to-yellow-600 bg-clip-text text-transparent"> Success Stories</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto mb-12">
                Real results from Hisar businesses across industries - from steel manufacturers to agricultural enterprises in this industrial and farming hub.
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
              {stats.map((stat, index) => (
                <div key={index} className="text-center">
                  <div className="text-5xl font-bold text-yellow-400 mb-2">{stat.metric}</div>
                  <div className="text-white font-semibold mb-1">{stat.description}</div>
                  <div className="text-slate-400">{stat.detail}</div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Services Section */}
        <section className="py-20">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Digital Marketing Services in
                <span className="bg-gradient-to-r from-yellow-400 to-yellow-600 bg-clip-text text-transparent"> Hisar</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto">
                Comprehensive digital marketing solutions for Hisar businesses looking to dominate online in the Steel City & Agricultural Hub.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {services.map((service, index) => (
                <Card key={index} className="bg-slate-900/80 border-yellow-500/20 hover:border-yellow-500/40 transition-all duration-300">
                  <CardContent className="p-6 text-center">
                    <Building className="w-8 h-8 text-yellow-400 mx-auto mb-4" />
                    <h3 className="text-white font-semibold mb-2">{service}</h3>
                    <Button asChild size="sm" variant="outline" className="border-yellow-500 text-yellow-400 hover:bg-yellow-500 hover:text-white">
                      <Link to="/quote">Learn More</Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Industries Section */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Hisar Industries We
                <span className="bg-gradient-to-r from-yellow-400 to-yellow-600 bg-clip-text text-transparent"> Serve</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto">
                Specialized digital marketing strategies for Hisar's steel, agriculture, and manufacturing industries.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {industries.map((industry, index) => (
                <Card key={index} className="bg-slate-900/80 border-yellow-500/20 hover:border-yellow-500/40 transition-all duration-300">
                  <CardContent className="p-6 text-center">
                    <Star className="w-8 h-8 text-yellow-400 mx-auto mb-4" />
                    <h3 className="text-white font-semibold mb-2">{industry}</h3>
                    <Button asChild size="sm" variant="outline" className="border-yellow-500 text-yellow-400 hover:bg-yellow-500 hover:text-white">
                      <Link to="/quote">Get Strategy</Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Areas Section */}
        <section className="py-20">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Hisar Areas We
                <span className="bg-gradient-to-r from-yellow-400 to-yellow-600 bg-clip-text text-transparent"> Cover</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto">
                Local digital marketing expertise across all major Hisar areas including industrial zones and agricultural districts.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {areas.map((area, index) => (
                <Card key={index} className="bg-slate-900/80 border-yellow-500/20 hover:border-yellow-500/40 transition-all duration-300">
                  <CardContent className="p-6 text-center">
                    <MapPin className="w-8 h-8 text-yellow-400 mx-auto mb-4" />
                    <h3 className="text-white font-semibold mb-2">{area}</h3>
                    <Button asChild size="sm" variant="outline" className="border-yellow-500 text-yellow-400 hover:bg-yellow-500 hover:text-white">
                      <Link to="/contact">Contact Us</Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Why Choose Us Section */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Why Choose GOD Digital Marketing in
                <span className="bg-gradient-to-r from-yellow-400 to-yellow-600 bg-clip-text text-transparent"> Hisar?</span>
              </h2>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              <Card className="bg-slate-900/80 border-yellow-500/20">
                <CardContent className="p-8">
                  <TrendingUp className="w-12 h-12 text-yellow-400 mb-6" />
                  <h3 className="text-xl font-bold text-white mb-4">Industrial & Agricultural Expertise</h3>
                  <p className="text-slate-300">Deep understanding of Hisar's steel industry, agricultural sector, and manufacturing business dynamics.</p>
                </CardContent>
              </Card>

              <Card className="bg-slate-900/80 border-yellow-500/20">
                <CardContent className="p-8">
                  <Users className="w-12 h-12 text-yellow-400 mb-6" />
                  <h3 className="text-xl font-bold text-white mb-4">Proven Hisar Results</h3>
                  <p className="text-slate-300">95+ successful Hisar campaigns with measurable ROI improvements and business growth across industrial and agricultural sectors.</p>
                </CardContent>
              </Card>

              <Card className="bg-slate-900/80 border-yellow-500/20">
                <CardContent className="p-8">
                  <CheckCircle className="w-12 h-12 text-yellow-400 mb-6" />
                  <h3 className="text-xl font-bold text-white mb-4">B2B Manufacturing Focus</h3>
                  <p className="text-slate-300">Specialized expertise in B2B marketing for steel, automobile, and agricultural businesses with proven lead generation strategies.</p>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 bg-gradient-to-r from-yellow-600 to-yellow-500">
          <div className="container mx-auto px-6 text-center">
            <h2 className="text-4xl md:text-5xl font-bold mb-6">Ready to Dominate Hisar's Digital Market?</h2>
            <p className="text-xl mb-8 max-w-3xl mx-auto">
              Join 95+ Hisar businesses that trust GOD Digital Marketing for their online growth. From steel industry to agriculture, we deliver results.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button asChild size="lg" className="bg-white text-yellow-600 hover:bg-slate-100 text-lg px-8 py-4">
                <Link to="/quote">Get Free Hisar SEO Audit</Link>
              </Button>
              <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-yellow-600 text-lg px-8 py-4">
                <Link to="/contact">Call Hisar Office Now</Link>
              </Button>
            </div>
          </div>
        </section>
      </main>
      
      <Footer />
    </div>
  );
};

export default Hisar;
