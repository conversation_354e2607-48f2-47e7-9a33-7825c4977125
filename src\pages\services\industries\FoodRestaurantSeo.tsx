import React from 'react';
import { Link } from 'react-router-dom';
import { UtensilsCrossed, Search, TrendingUp, Users, CheckCircle, Building, Star, Crown, Target, Zap, ArrowRight } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';
import Sidebar from '@/components/Sidebar';

const FoodRestaurantSeo = () => {
  const stats = [
    {
      metric: '1,250+',
      description: 'Food Businesses Optimized',
      detail: 'Across all food sectors'
    },
    {
      metric: '3,800%',
      description: 'Average Customer Growth',
      detail: 'For food & restaurant clients'
    },
    {
      metric: '₹480Cr+',
      description: 'Food Industry Revenue Generated',
      detail: 'Through SEO campaigns'
    },
    {
      metric: '95%',
      description: 'Food Keywords Ranking',
      detail: 'Top 10 positions achieved'
    }
  ];

  const achievements = [
    'Top Food & Restaurant SEO Company in India',
    'Restaurant SEO Specialists',
    'Food Delivery SEO Experts',
    'Cloud Kitchen SEO Leaders',
    'Food Franchise SEO Champions',
    'Culinary Tourism SEO Pioneers'
  ];

  const foodSpecializations = [
    {
      type: 'Restaurant & Fine Dining SEO',
      description: 'Comprehensive SEO for restaurants, fine dining establishments, and culinary experiences',
      icon: Building,
      features: ['Restaurant Website SEO', 'Menu Optimization', 'Local Restaurant SEO', 'Fine Dining SEO', 'Chef Profile SEO', 'Culinary Experience SEO']
    },
    {
      type: 'Food Delivery & Cloud Kitchen SEO',
      description: 'Advanced SEO for food delivery platforms, cloud kitchens, and online food businesses',
      icon: Star,
      features: ['Food Delivery SEO', 'Cloud Kitchen Optimization', 'Online Ordering SEO', 'Food App SEO', 'Virtual Restaurant SEO', 'Delivery Platform SEO']
    },
    {
      type: 'Food Manufacturing & Processing SEO',
      description: 'Strategic SEO for food manufacturers, processors, and packaged food companies',
      icon: Crown,
      features: ['Food Manufacturing SEO', 'Packaged Food SEO', 'Food Processing SEO', 'FMCG Food SEO', 'Food Export SEO', 'Food Safety SEO']
    },
    {
      type: 'Food Franchise & Chain SEO',
      description: 'Specialized SEO for food franchises, restaurant chains, and multi-location food businesses',
      icon: Target,
      features: ['Food Franchise SEO', 'Restaurant Chain SEO', 'Multi-Location SEO', 'Franchise Development SEO', 'QSR Chain SEO', 'Food Brand SEO']
    }
  ];

  const foodSectors = [
    { name: 'Restaurants', clients: '380+', growth: '3,600%' },
    { name: 'Food Delivery', clients: '280+', growth: '4,200%' },
    { name: 'Cloud Kitchens', clients: '220+', growth: '4,800%' },
    { name: 'Food Manufacturing', clients: '180+', growth: '3,200%' },
    { name: 'Food Franchises', clients: '120+', growth: '3,800%' },
    { name: 'Catering Services', clients: '150+', growth: '2,900%' }
  ];

  const caseStudies = [
    {
      client: 'Leading Restaurant Chain',
      industry: 'Multi-Location Restaurant',
      challenge: 'Restaurant chain needed to dominate local search across 50+ locations',
      result: '4,200% local visibility increase',
      metrics: ['1,800+ restaurant keywords in top 3', '₹150Cr+ restaurant revenue', '680% increase in table bookings']
    },
    {
      client: 'Cloud Kitchen Platform',
      industry: 'Food Delivery Business',
      challenge: 'Cloud kitchen needed to compete with established food delivery giants',
      result: '4,800% order volume growth',
      metrics: ['1,200+ food delivery keywords ranking', '₹95Cr+ delivery revenue', '520% increase in online orders']
    },
    {
      client: 'Food Manufacturing Company',
      industry: 'Packaged Food Manufacturing',
      challenge: 'Food manufacturer needed to establish B2B and retail presence online',
      result: '3,400% business inquiry increase',
      metrics: ['950+ food manufacturing keywords in top 5', '₹125Cr+ manufacturing revenue', '420% increase in distributor partnerships']
    }
  ];

  const foodSeoStrategies = [
    {
      strategy: 'Local Food SEO',
      description: 'Dominate local search for restaurants and food businesses in specific areas',
      benefits: ['Local visibility', 'Foot traffic increase', 'Local customer acquisition', 'Geographic dominance']
    },
    {
      strategy: 'Menu & Cuisine SEO',
      description: 'Optimize for specific cuisines, dishes, and food-related searches',
      benefits: ['Cuisine authority', 'Dish-specific ranking', 'Food preference targeting', 'Culinary expertise']
    },
    {
      strategy: 'Food Delivery SEO',
      description: 'Target online food ordering and delivery-related searches',
      benefits: ['Online ordering growth', 'Delivery platform visibility', 'Digital customer acquisition', 'Order volume increase']
    },
    {
      strategy: 'Food Safety & Quality SEO',
      description: 'Emphasize food safety, quality standards, and certifications',
      benefits: ['Trust building', 'Quality assurance', 'Safety compliance', 'Brand credibility']
    }
  ];

  return (
    <div className="min-h-screen bg-slate-900 relative">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        <div className="container mx-auto px-6 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            <div className="lg:col-span-3">
              <section className="pt-24 pb-16">
                <div className="max-w-4xl">
                  <div className="inline-flex items-center space-x-2 bg-orange-500/20 border border-orange-500/30 rounded-full px-6 py-2 mb-8">
                    <UtensilsCrossed className="w-4 h-4 text-orange-400" />
                    <span className="text-orange-400 font-medium">Food & Restaurant SEO • Culinary Optimization Excellence</span>
                  </div>
                  
                  <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
                    #1 Food & Restaurant SEO Company in
                    <span className="bg-gradient-to-r from-orange-400 to-orange-600 bg-clip-text text-transparent"> India</span>
                  </h1>
                  
                  <p className="text-xl text-slate-300 mb-8">
                    Premier food & restaurant SEO services offering comprehensive search engine optimization solutions for restaurants, food delivery platforms, cloud kitchens, and food manufacturing companies. Our food SEO company provides professional SEO services with restaurant SEO optimization, food delivery SEO, cloud kitchen SEO, and food franchise SEO. Serving 1,250+ food businesses across all culinary sectors with proven ₹480Cr+ revenue generation and 3,800% average customer growth for food & restaurant clients through strategic search engine optimization and culinary digital marketing excellence.
                  </p>
                  
                  <div className="flex flex-col sm:flex-row gap-4 mb-8">
                    <Button asChild size="lg" className="bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white text-lg px-8 py-4">
                      <Link to="/quote">Get Free Food & Restaurant SEO Audit</Link>
                    </Button>
                    <Button asChild size="lg" variant="outline" className="border-orange-500 text-orange-400 hover:bg-orange-500 hover:text-white text-lg px-8 py-4">
                      <Link to="/contact">Call Food SEO Experts: +91-8708577598</Link>
                    </Button>
                  </div>

                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                    {achievements.map((achievement, index) => (
                      <div key={index} className="flex items-center justify-center space-x-2 bg-slate-800/50 rounded-lg p-3">
                        <CheckCircle className="w-4 h-4 text-orange-400 flex-shrink-0" />
                        <span className="text-slate-300">{achievement}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </section>

              <section className="mb-16 bg-slate-800/50 rounded-2xl p-8">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Food & Restaurant SEO
                    <span className="bg-gradient-to-r from-orange-400 to-orange-600 bg-clip-text text-transparent"> Success Metrics</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Measurable food & restaurant SEO results from businesses across all culinary sectors and markets.
                  </p>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
                  {stats.map((stat, index) => (
                    <div key={index} className="text-center">
                      <div className="text-4xl font-bold text-orange-400 mb-2">{stat.metric}</div>
                      <div className="text-white font-semibold mb-1">{stat.description}</div>
                      <div className="text-slate-400">{stat.detail}</div>
                    </div>
                  ))}
                </div>
              </section>

              <section className="mb-16">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Food & Restaurant SEO Strategies We
                    <span className="bg-gradient-to-r from-orange-400 to-orange-600 bg-clip-text text-transparent"> Master</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Specialized SEO strategies designed for food & restaurant success across all culinary businesses and platforms.
                  </p>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  {foodSpecializations.map((spec, index) => (
                    <Card key={index} className="bg-slate-900/80 border-orange-500/20 hover:border-orange-500/40 transition-all duration-300">
                      <CardContent className="p-8">
                        <div className="w-12 h-12 bg-gradient-to-r from-orange-500 to-orange-600 rounded-lg flex items-center justify-center mb-6">
                          <spec.icon className="w-6 h-6 text-white" />
                        </div>
                        <h3 className="text-xl font-bold text-white mb-4">{spec.type}</h3>
                        <p className="text-slate-300 mb-6">{spec.description}</p>
                        <ul className="space-y-2">
                          {spec.features.map((feature, idx) => (
                            <li key={idx} className="flex items-center text-slate-400 text-sm">
                              <CheckCircle className="w-3 h-3 text-orange-400 mr-2 flex-shrink-0" />
                              {feature}
                            </li>
                          ))}
                        </ul>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>

              {/* Food Sectors */}
              <section className="mb-16 bg-slate-800/30 rounded-2xl p-8">
                <h3 className="text-2xl font-bold text-white mb-6 text-center">
                  Food Industry Sector Expertise
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-6">
                  {foodSectors.map((sector, index) => (
                    <div key={index} className="text-center bg-slate-900/50 rounded-lg p-4">
                      <h4 className="text-orange-400 font-semibold mb-2">{sector.name}</h4>
                      <div className="text-2xl font-bold text-white mb-1">{sector.clients}</div>
                      <div className="text-slate-400 text-sm mb-2">Businesses Served</div>
                      <div className="text-green-400 font-semibold text-sm">{sector.growth} avg growth</div>
                    </div>
                  ))}
                </div>
              </section>

              {/* Food SEO Strategies */}
              <section className="mb-16 bg-slate-800/30 rounded-2xl p-8">
                <h3 className="text-2xl font-bold text-white mb-6 text-center">
                  Advanced Food & Restaurant SEO Strategies
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                  {foodSeoStrategies.map((strategy, index) => (
                    <div key={index} className="bg-slate-900/50 rounded-lg p-6">
                      <h4 className="text-orange-400 font-semibold mb-3">{strategy.strategy}</h4>
                      <p className="text-slate-300 text-sm mb-4">{strategy.description}</p>
                      <ul className="space-y-1">
                        {strategy.benefits.map((benefit, idx) => (
                          <li key={idx} className="text-slate-400 text-xs flex items-center">
                            <CheckCircle className="w-3 h-3 text-orange-400 mr-2 flex-shrink-0" />
                            {benefit}
                          </li>
                        ))}
                      </ul>
                    </div>
                  ))}
                </div>
              </section>

              {/* Case Studies */}
              <section className="mb-16">
                <h3 className="text-2xl font-bold text-white mb-8 text-center">
                  Food & Restaurant SEO Success Stories
                </h3>
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                  {caseStudies.map((study, index) => (
                    <Card key={index} className="bg-slate-900/60 border-orange-500/20 hover:border-orange-500/40 transition-all duration-300">
                      <CardContent className="p-6">
                        <h4 className="text-orange-400 font-semibold mb-2">{study.client}</h4>
                        <p className="text-slate-400 text-sm mb-3">{study.industry}</p>
                        <p className="text-slate-300 text-sm mb-4">{study.challenge}</p>
                        <div className="bg-slate-800/50 rounded p-3 mb-4">
                          <div className="text-green-400 font-semibold mb-2">{study.result}</div>
                          <ul className="space-y-1">
                            {study.metrics.map((metric, idx) => (
                              <li key={idx} className="text-slate-400 text-xs flex items-center">
                                <CheckCircle className="w-3 h-3 text-orange-400 mr-2 flex-shrink-0" />
                                {metric}
                              </li>
                            ))}
                          </ul>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>

              {/* Internal Linking Section */}
              <section className="mb-16 bg-slate-800/30 rounded-2xl p-8">
                <h3 className="text-2xl font-bold text-white mb-6 text-center">
                  Complete Food & Restaurant Digital Marketing Solutions
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  <Link to="/services/ppc" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-orange-400 font-semibold mb-2 group-hover:text-orange-300">Food & Restaurant Google Ads</h4>
                    <p className="text-slate-400 text-sm">PPC advertising for restaurants and food delivery platforms</p>
                  </Link>
                  <Link to="/services/social-media" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-orange-400 font-semibold mb-2 group-hover:text-orange-300">Food Social Media Marketing</h4>
                    <p className="text-slate-400 text-sm">Social media marketing for restaurants and food brands</p>
                  </Link>
                  <Link to="/services/content" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-orange-400 font-semibold mb-2 group-hover:text-orange-300">Food Content Marketing</h4>
                    <p className="text-slate-400 text-sm">Culinary content creation and food marketing</p>
                  </Link>
                  <Link to="/services/email" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-orange-400 font-semibold mb-2 group-hover:text-orange-300">Restaurant Email Marketing</h4>
                    <p className="text-slate-400 text-sm">Customer retention and food promotion campaigns</p>
                  </Link>
                  <Link to="/services/web-development" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-orange-400 font-semibold mb-2 group-hover:text-orange-300">Restaurant Website Development</h4>
                    <p className="text-slate-400 text-sm">Food ordering platforms and restaurant website development</p>
                  </Link>
                  <Link to="/portfolio" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-orange-400 font-semibold mb-2 group-hover:text-orange-300">Food & Restaurant Success Stories</h4>
                    <p className="text-slate-400 text-sm">See proven results from our food & restaurant clients</p>
                  </Link>
                </div>
              </section>

              <section className="bg-gradient-to-r from-orange-600 to-orange-500 rounded-2xl p-8 text-center">
                <h2 className="text-3xl md:text-5xl font-bold mb-6">Ready to Grow Your Food Business?</h2>
                <p className="text-xl mb-8">
                  Join 1,250+ food businesses that trust GOD Digital Marketing for culinary SEO success. Proven strategies that deliver 3,800% customer growth and ₹480Cr+ revenue generation.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button asChild size="lg" className="bg-white text-orange-600 hover:bg-slate-100 text-lg px-8 py-4">
                    <Link to="/quote">Get Free Food & Restaurant SEO Audit</Link>
                  </Button>
                  <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-orange-600 text-lg px-8 py-4">
                    <Link to="/contact">Call Food SEO Experts: +91-8708577598</Link>
                  </Button>
                </div>
              </section>
            </div>

            <div className="lg:col-span-1">
              <Sidebar currentService="seo" />
            </div>
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
};

export default FoodRestaurantSeo;
