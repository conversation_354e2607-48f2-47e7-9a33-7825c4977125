import React from 'react';
import { Link } from 'react-router-dom';
import { DollarSign, Search, TrendingUp, Users, CheckCircle, Building, Star, Crown, Target, Zap, ArrowRight } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';
import Sidebar from '@/components/Sidebar';

const FinanceSeo = () => {
  const stats = [
    {
      metric: '850+',
      description: 'Financial Institutions Optimized',
      detail: 'Across all finance sectors'
    },
    {
      metric: '2,800%',
      description: 'Average Lead Growth',
      detail: 'For finance clients'
    },
    {
      metric: '₹420Cr+',
      description: 'Finance Revenue Generated',
      detail: 'Through SEO campaigns'
    },
    {
      metric: '98%',
      description: 'Finance Keywords Ranking',
      detail: 'Top 10 positions achieved'
    }
  ];

  const achievements = [
    'Top Finance SEO Company in India',
    'FinTech SEO Specialists',
    'Banking SEO Experts',
    'Investment Platform Leaders',
    'Insurance SEO Champions',
    'Cryptocurrency SEO Pioneers'
  ];

  const financeSpecializations = [
    {
      type: 'Banking & Financial Services SEO',
      description: 'Comprehensive SEO for banks, credit unions, and traditional financial institutions',
      icon: Building,
      features: ['Bank Website SEO', 'Loan Product Optimization', 'Credit Card SEO', 'Investment Service SEO', 'Financial Advisory SEO', 'Branch Location SEO']
    },
    {
      type: 'FinTech & Digital Finance SEO',
      description: 'Advanced SEO for FinTech startups, digital payment platforms, and financial technology companies',
      icon: Star,
      features: ['Payment App SEO', 'Digital Wallet Optimization', 'Lending Platform SEO', 'Investment App SEO', 'Cryptocurrency Exchange SEO', 'Blockchain Technology SEO']
    },
    {
      type: 'Insurance & Risk Management SEO',
      description: 'Strategic SEO for insurance companies, brokers, and risk management services',
      icon: Crown,
      features: ['Life Insurance SEO', 'Health Insurance Optimization', 'Auto Insurance SEO', 'Business Insurance SEO', 'Insurance Broker SEO', 'Claims Process SEO']
    },
    {
      type: 'Investment & Wealth Management SEO',
      description: 'Specialized SEO for investment firms, wealth managers, and financial advisors',
      icon: Target,
      features: ['Investment Platform SEO', 'Wealth Management SEO', 'Mutual Fund SEO', 'Stock Trading SEO', 'Financial Planning SEO', 'Retirement Planning SEO']
    }
  ];

  const financeSectors = [
    { name: 'Banking Services', clients: '280+', growth: '2,600%' },
    { name: 'FinTech Platforms', clients: '220+', growth: '3,200%' },
    { name: 'Insurance Companies', clients: '180+', growth: '2,400%' },
    { name: 'Investment Firms', clients: '120+', growth: '2,800%' },
    { name: 'Cryptocurrency', clients: '80+', growth: '4,500%' },
    { name: 'Financial Advisory', clients: '150+', growth: '2,200%' }
  ];

  const caseStudies = [
    {
      client: 'Leading FinTech Startup',
      industry: 'Digital Payments',
      challenge: 'New FinTech platform needed to compete with established financial giants',
      result: '4,200% user acquisition growth',
      metrics: ['920+ FinTech keywords in top 3', '₹85Cr+ platform revenue', '580% increase in app downloads']
    },
    {
      client: 'Regional Bank',
      industry: 'Banking Services',
      challenge: 'Traditional bank needed digital transformation and online customer acquisition',
      result: '2,800% digital banking growth',
      metrics: ['650+ banking keywords ranking', '₹125Cr+ loan disbursements', '420% increase in online applications']
    },
    {
      client: 'Insurance Brokerage',
      industry: 'Insurance Services',
      challenge: 'Insurance broker needed to compete with direct insurance companies online',
      result: '3,500% policy inquiry increase',
      metrics: ['780+ insurance keywords in top 5', '₹65Cr+ premium generated', '380% increase in policy sales']
    }
  ];

  const financeSeoStrategies = [
    {
      strategy: 'Compliance-First SEO',
      description: 'SEO strategies that comply with financial regulations and industry standards',
      benefits: ['Regulatory compliance', 'Trust building', 'Risk mitigation', 'Authority establishment']
    },
    {
      strategy: 'Trust & Authority SEO',
      description: 'Build financial authority through expert content and credible optimization',
      benefits: ['Financial expertise', 'Customer trust', 'Brand credibility', 'Thought leadership']
    },
    {
      strategy: 'Local Financial SEO',
      description: 'Target local customers seeking nearby financial services and advisors',
      benefits: ['Local market dominance', 'Branch visibility', 'Community engagement', 'Regional authority']
    },
    {
      strategy: 'Security-Focused SEO',
      description: 'Emphasize security, privacy, and data protection in SEO strategies',
      benefits: ['Security assurance', 'Privacy compliance', 'Customer confidence', 'Risk management']
    }
  ];

  return (
    <div className="min-h-screen bg-slate-900 relative">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        <div className="container mx-auto px-6 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            <div className="lg:col-span-3">
              <section className="pt-24 pb-16">
                <div className="max-w-4xl">
                  <div className="inline-flex items-center space-x-2 bg-green-500/20 border border-green-500/30 rounded-full px-6 py-2 mb-8">
                    <DollarSign className="w-4 h-4 text-green-400" />
                    <span className="text-green-400 font-medium">Finance SEO • FinTech Optimization Excellence</span>
                  </div>
                  
                  <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
                    #1 Finance SEO Company in
                    <span className="bg-gradient-to-r from-green-400 to-green-600 bg-clip-text text-transparent"> India</span>
                  </h1>
                  
                  <p className="text-xl text-slate-300 mb-8">
                    Premier finance SEO services offering comprehensive search engine optimization solutions for banks, FinTech platforms, insurance companies, and investment firms. Our finance SEO company provides professional SEO services with banking SEO optimization, FinTech platform SEO, insurance company SEO, and investment firm SEO. Serving 850+ financial institutions across all finance sectors with proven ₹420Cr+ revenue generation and 2,800% average lead growth for finance clients through strategic search engine optimization and financial digital marketing excellence.
                  </p>
                  
                  <div className="flex flex-col sm:flex-row gap-4 mb-8">
                    <Button asChild size="lg" className="bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white text-lg px-8 py-4">
                      <Link to="/quote">Get Free Finance SEO Audit</Link>
                    </Button>
                    <Button asChild size="lg" variant="outline" className="border-green-500 text-green-400 hover:bg-green-500 hover:text-white text-lg px-8 py-4">
                      <Link to="/contact">Call Finance SEO Experts: +91-**********</Link>
                    </Button>
                  </div>

                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                    {achievements.map((achievement, index) => (
                      <div key={index} className="flex items-center justify-center space-x-2 bg-slate-800/50 rounded-lg p-3">
                        <CheckCircle className="w-4 h-4 text-green-400 flex-shrink-0" />
                        <span className="text-slate-300">{achievement}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </section>

              <section className="mb-16 bg-slate-800/50 rounded-2xl p-8">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Finance SEO
                    <span className="bg-gradient-to-r from-green-400 to-green-600 bg-clip-text text-transparent"> Success Metrics</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Measurable finance SEO results from institutions across all financial sectors and services.
                  </p>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
                  {stats.map((stat, index) => (
                    <div key={index} className="text-center">
                      <div className="text-4xl font-bold text-green-400 mb-2">{stat.metric}</div>
                      <div className="text-white font-semibold mb-1">{stat.description}</div>
                      <div className="text-slate-400">{stat.detail}</div>
                    </div>
                  ))}
                </div>
              </section>

              <section className="mb-16">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Finance SEO Strategies We
                    <span className="bg-gradient-to-r from-green-400 to-green-600 bg-clip-text text-transparent"> Master</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Specialized SEO strategies designed for finance success across all financial services and platforms.
                  </p>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  {financeSpecializations.map((spec, index) => (
                    <Card key={index} className="bg-slate-900/80 border-green-500/20 hover:border-green-500/40 transition-all duration-300">
                      <CardContent className="p-8">
                        <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-green-600 rounded-lg flex items-center justify-center mb-6">
                          <spec.icon className="w-6 h-6 text-white" />
                        </div>
                        <h3 className="text-xl font-bold text-white mb-4">{spec.type}</h3>
                        <p className="text-slate-300 mb-6">{spec.description}</p>
                        <ul className="space-y-2">
                          {spec.features.map((feature, idx) => (
                            <li key={idx} className="flex items-center text-slate-400 text-sm">
                              <CheckCircle className="w-3 h-3 text-green-400 mr-2 flex-shrink-0" />
                              {feature}
                            </li>
                          ))}
                        </ul>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>

              {/* Finance Sectors */}
              <section className="mb-16 bg-slate-800/30 rounded-2xl p-8">
                <h3 className="text-2xl font-bold text-white mb-6 text-center">
                  Finance Sector Expertise
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-6">
                  {financeSectors.map((sector, index) => (
                    <div key={index} className="text-center bg-slate-900/50 rounded-lg p-4">
                      <h4 className="text-green-400 font-semibold mb-2">{sector.name}</h4>
                      <div className="text-2xl font-bold text-white mb-1">{sector.clients}</div>
                      <div className="text-slate-400 text-sm mb-2">Institutions Served</div>
                      <div className="text-green-400 font-semibold text-sm">{sector.growth} avg growth</div>
                    </div>
                  ))}
                </div>
              </section>

              {/* Finance SEO Strategies */}
              <section className="mb-16 bg-slate-800/30 rounded-2xl p-8">
                <h3 className="text-2xl font-bold text-white mb-6 text-center">
                  Advanced Finance SEO Strategies
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                  {financeSeoStrategies.map((strategy, index) => (
                    <div key={index} className="bg-slate-900/50 rounded-lg p-6">
                      <h4 className="text-green-400 font-semibold mb-3">{strategy.strategy}</h4>
                      <p className="text-slate-300 text-sm mb-4">{strategy.description}</p>
                      <ul className="space-y-1">
                        {strategy.benefits.map((benefit, idx) => (
                          <li key={idx} className="text-slate-400 text-xs flex items-center">
                            <CheckCircle className="w-3 h-3 text-green-400 mr-2 flex-shrink-0" />
                            {benefit}
                          </li>
                        ))}
                      </ul>
                    </div>
                  ))}
                </div>
              </section>

              {/* Case Studies */}
              <section className="mb-16">
                <h3 className="text-2xl font-bold text-white mb-8 text-center">
                  Finance SEO Success Stories
                </h3>
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                  {caseStudies.map((study, index) => (
                    <Card key={index} className="bg-slate-900/60 border-green-500/20 hover:border-green-500/40 transition-all duration-300">
                      <CardContent className="p-6">
                        <h4 className="text-green-400 font-semibold mb-2">{study.client}</h4>
                        <p className="text-slate-400 text-sm mb-3">{study.industry}</p>
                        <p className="text-slate-300 text-sm mb-4">{study.challenge}</p>
                        <div className="bg-slate-800/50 rounded p-3 mb-4">
                          <div className="text-green-400 font-semibold mb-2">{study.result}</div>
                          <ul className="space-y-1">
                            {study.metrics.map((metric, idx) => (
                              <li key={idx} className="text-slate-400 text-xs flex items-center">
                                <CheckCircle className="w-3 h-3 text-green-400 mr-2 flex-shrink-0" />
                                {metric}
                              </li>
                            ))}
                          </ul>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>

              {/* Internal Linking Section */}
              <section className="mb-16 bg-slate-800/30 rounded-2xl p-8">
                <h3 className="text-2xl font-bold text-white mb-6 text-center">
                  Complete Finance Digital Marketing Solutions
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  <Link to="/services/ppc" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-green-400 font-semibold mb-2 group-hover:text-green-300">Finance Google Ads</h4>
                    <p className="text-slate-400 text-sm">PPC advertising for financial services and FinTech platforms</p>
                  </Link>
                  <Link to="/services/social-media" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-green-400 font-semibold mb-2 group-hover:text-green-300">Finance Social Media</h4>
                    <p className="text-slate-400 text-sm">Social media marketing for financial institutions</p>
                  </Link>
                  <Link to="/services/content" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-green-400 font-semibold mb-2 group-hover:text-green-300">Finance Content Marketing</h4>
                    <p className="text-slate-400 text-sm">Financial content creation and thought leadership</p>
                  </Link>
                  <Link to="/services/email" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-green-400 font-semibold mb-2 group-hover:text-green-300">Finance Email Marketing</h4>
                    <p className="text-slate-400 text-sm">Financial newsletters and customer communication</p>
                  </Link>
                  <Link to="/services/web-development" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-green-400 font-semibold mb-2 group-hover:text-green-300">FinTech Website Development</h4>
                    <p className="text-slate-400 text-sm">Secure financial platform and website development</p>
                  </Link>
                  <Link to="/portfolio" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-green-400 font-semibold mb-2 group-hover:text-green-300">Finance Success Stories</h4>
                    <p className="text-slate-400 text-sm">See proven results from our finance clients</p>
                  </Link>
                </div>
              </section>

              <section className="bg-gradient-to-r from-green-600 to-green-500 rounded-2xl p-8 text-center">
                <h2 className="text-3xl md:text-5xl font-bold mb-6">Ready to Grow Your Financial Business?</h2>
                <p className="text-xl mb-8">
                  Join 850+ financial institutions that trust GOD Digital Marketing for finance SEO success. Proven strategies that deliver 2,800% lead growth and ₹420Cr+ revenue generation.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button asChild size="lg" className="bg-white text-green-600 hover:bg-slate-100 text-lg px-8 py-4">
                    <Link to="/quote">Get Free Finance SEO Audit</Link>
                  </Button>
                  <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-green-600 text-lg px-8 py-4">
                    <Link to="/contact">Call Finance SEO Experts: +91-**********</Link>
                  </Button>
                </div>
              </section>
            </div>

            <div className="lg:col-span-1">
              <Sidebar currentService="seo" />
            </div>
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
};

export default FinanceSeo;
