
import React from 'react';
import { Link } from 'react-router-dom';
import { Share2, Users, TrendingUp, Heart, CheckCircle, MapPin, Award } from 'lucide-react';
import { Button } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';

const DelhiSocialMediaServices = () => {
  return (
    <div className="min-h-screen bg-slate-900 text-white relative overflow-hidden">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        {/* Hero Section */}
        <section className="pt-32 pb-20">
          <div className="container mx-auto px-6">
            <div className="max-w-4xl mx-auto text-center">
              <div className="inline-flex items-center space-x-2 bg-amber-500/20 border border-amber-500/30 rounded-full px-6 py-2 mb-8">
                <MapPin className="w-4 h-4 text-amber-400" />
                <span className="text-amber-400 font-medium">Delhi Social Media Marketing</span>
              </div>
              
              <h1 className="text-5xl md:text-6xl font-bold mb-6 leading-tight">
                Delhi's Premier 
                <span className="bg-gradient-to-r from-amber-400 to-amber-600 bg-clip-text text-transparent"> Social Media Agency</span>
              </h1>
              
              <p className="text-xl text-slate-300 mb-12 max-w-3xl mx-auto">
                Build powerful brand presence and engage Delhi's diverse audience with data-driven social media strategies. 
                Trusted by 300+ Delhi businesses across all major platforms.
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
                <Button asChild size="lg" className="bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700 text-white text-lg px-8 py-4">
                  <Link to="/quote">Get Social Media Audit</Link>
                </Button>
                <Button asChild size="lg" variant="outline" className="border-amber-500 text-amber-400 hover:bg-amber-500 hover:text-white text-lg px-8 py-4">
                  <Link to="/case-studies">View Delhi Brand Stories</Link>
                </Button>
              </div>

              {/* Trust Indicators */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-16">
                <div className="text-center">
                  <div className="text-3xl font-bold text-amber-400 mb-2">300+</div>
                  <div className="text-slate-300">Delhi Brands Managed</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-amber-400 mb-2">10M+</div>
                  <div className="text-slate-300">Monthly Reach</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-amber-400 mb-2">500%</div>
                  <div className="text-slate-300">Avg Engagement Growth</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-amber-400 mb-2">95%</div>
                  <div className="text-slate-300">Client Satisfaction</div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Platform Expertise */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="max-w-6xl mx-auto">
              <h2 className="text-4xl font-bold text-center mb-16">Mastering Every Platform for Delhi Audiences</h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <div className="bg-slate-700/30 border border-amber-500/20 rounded-xl p-6">
                  <Share2 className="w-12 h-12 text-amber-400 mb-4" />
                  <h3 className="text-xl font-semibold mb-3">Instagram Marketing</h3>
                  <p className="text-slate-300 mb-4">Visual storytelling that resonates with Delhi's style-conscious audience.</p>
                  <ul className="space-y-2 text-sm text-slate-400">
                    <li>• Feed & Story Optimization</li>
                    <li>• Reels & IGTV Strategy</li>
                    <li>• Influencer Collaborations</li>
                    <li>• Shopping Integration</li>
                  </ul>
                </div>
                
                <div className="bg-slate-700/30 border border-amber-500/20 rounded-xl p-6">
                  <Users className="w-12 h-12 text-amber-400 mb-4" />
                  <h3 className="text-xl font-semibold mb-3">Facebook Marketing</h3>
                  <p className="text-slate-300 mb-4">Community building and targeted advertising for Delhi's diverse demographics.</p>
                  <ul className="space-y-2 text-sm text-slate-400">
                    <li>• Business Page Management</li>
                    <li>• Group Community Building</li>
                    <li>• Event Marketing</li>
                    <li>• Messenger Automation</li>
                  </ul>
                </div>
                
                <div className="bg-slate-700/30 border border-amber-500/20 rounded-xl p-6">
                  <TrendingUp className="w-12 h-12 text-amber-400 mb-4" />
                  <h3 className="text-xl font-semibold mb-3">LinkedIn Marketing</h3>
                  <p className="text-slate-300 mb-4">B2B networking and thought leadership for Delhi's corporate sector.</p>
                  <ul className="space-y-2 text-sm text-slate-400">
                    <li>• Professional Branding</li>
                    <li>• Content Strategy</li>
                    <li>• Lead Generation</li>
                    <li>• Employee Advocacy</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Delhi Market Understanding */}
        <section className="py-20">
          <div className="container mx-auto px-6">
            <div className="max-w-6xl mx-auto">
              <h2 className="text-4xl font-bold text-center mb-6">Deep Delhi Market Intelligence</h2>
              <p className="text-xl text-slate-300 text-center mb-16 max-w-3xl mx-auto">
                Our strategies are built on comprehensive understanding of Delhi's unique social media landscape and consumer behavior.
              </p>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-12">
                <div>
                  <h3 className="text-2xl font-semibold mb-6 text-amber-400">Cultural Insights</h3>
                  <ul className="space-y-4 text-slate-300">
                    <li className="flex items-start">
                      <CheckCircle className="w-5 h-5 text-green-400 mr-3 mt-1" />
                      <span>Festival and seasonal marketing strategies tailored to Delhi's celebrations</span>
                    </li>
                    <li className="flex items-start">
                      <CheckCircle className="w-5 h-5 text-green-400 mr-3 mt-1" />
                      <span>Multilingual content creation in Hindi, English, and Punjabi</span>
                    </li>
                    <li className="flex items-start">
                      <CheckCircle className="w-5 h-5 text-green-400 mr-3 mt-1" />
                      <span>Local trend identification and viral content creation</span>
                    </li>
                    <li className="flex items-start">
                      <CheckCircle className="w-5 h-5 text-green-400 mr-3 mt-1" />
                      <span>Delhi-specific hashtag research and optimization</span>
                    </li>
                  </ul>
                </div>
                
                <div>
                  <h3 className="text-2xl font-semibold mb-6 text-amber-400">Demographic Targeting</h3>
                  <ul className="space-y-4 text-slate-300">
                    <li className="flex items-start">
                      <CheckCircle className="w-5 h-5 text-green-400 mr-3 mt-1" />
                      <span>Young professionals in Gurgaon and Noida tech corridors</span>
                    </li>
                    <li className="flex items-start">
                      <CheckCircle className="w-5 h-5 text-green-400 mr-3 mt-1" />
                      <span>High-income families in South Delhi and Greater Kailash</span>
                    </li>
                    <li className="flex items-start">
                      <CheckCircle className="w-5 h-5 text-green-400 mr-3 mt-1" />
                      <span>Students and youth in North Campus and Lajpat Nagar</span>
                    </li>
                    <li className="flex items-start">
                      <CheckCircle className="w-5 h-5 text-green-400 mr-3 mt-1" />
                      <span>Business owners in Connaught Place and Karol Bagh</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Service Areas */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="max-w-6xl mx-auto text-center">
              <h2 className="text-4xl font-bold mb-16">Serving Delhi NCR's Business Districts</h2>
              
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <Link to="/delhi/connaught-place/social-media" className="bg-slate-700/30 border border-amber-500/20 rounded-lg p-4 hover:bg-slate-700/50 transition-colors">
                  <div className="font-semibold text-amber-400">CP & Central Delhi</div>
                  <div className="text-sm text-slate-300">Commercial Hub</div>
                </Link>
                <Link to="/delhi/south-delhi/social-media" className="bg-slate-700/30 border border-amber-500/20 rounded-lg p-4 hover:bg-slate-700/50 transition-colors">
                  <div className="font-semibold text-amber-400">South Delhi</div>
                  <div className="text-sm text-slate-300">Premium Market</div>
                </Link>
                <Link to="/delhi/gurgaon/social-media" className="bg-slate-700/30 border border-amber-500/20 rounded-lg p-4 hover:bg-slate-700/50 transition-colors">
                  <div className="font-semibold text-amber-400">Gurgaon</div>
                  <div className="text-sm text-slate-300">Corporate Center</div>
                </Link>
                <Link to="/delhi/noida/social-media" className="bg-slate-700/30 border border-amber-500/20 rounded-lg p-4 hover:bg-slate-700/50 transition-colors">
                  <div className="font-semibold text-amber-400">Noida</div>
                  <div className="text-sm text-slate-300">IT Corridor</div>
                </Link>
              </div>
            </div>
          </div>
        </section>

        {/* FAQ Section */}
        <section className="py-20">
          <div className="container mx-auto px-6">
            <div className="max-w-4xl mx-auto">
              <h2 className="text-4xl font-bold text-center mb-16">Social Media Marketing FAQs</h2>
              
              <div className="space-y-8">
                <div className="bg-slate-800/50 border border-amber-500/20 rounded-xl p-6">
                  <h3 className="text-xl font-semibold mb-3 text-amber-400">How do you create content that resonates with Delhi audiences?</h3>
                  <p className="text-slate-300">We blend local cultural insights with trending topics, using Delhi-specific references, festivals, and language preferences. Our content calendar includes Karva Chauth, Dussehra, Diwali, and local events like Delhi Shopping Festival to maximize engagement.</p>
                </div>

                <div className="bg-slate-800/50 border border-amber-500/20 rounded-xl p-6">
                  <h3 className="text-xl font-semibold mb-3 text-amber-400">Which social media platforms work best for Delhi businesses?</h3>
                  <p className="text-slate-300">Instagram and Facebook dominate for B2C businesses, especially in South Delhi and Gurgaon. LinkedIn is crucial for B2B companies in Connaught Place and corporate hubs. YouTube works excellently for educational content targeting Delhi's student population.</p>
                </div>

                <div className="bg-slate-800/50 border border-amber-500/20 rounded-xl p-6">
                  <h3 className="text-xl font-semibold mb-3 text-amber-400">Do you manage influencer partnerships in Delhi?</h3>
                  <p className="text-slate-300">Yes, we have partnerships with 200+ Delhi-based micro and macro influencers across fashion, food, lifestyle, and business niches. We match brands with influencers who have authentic Delhi followings for maximum impact.</p>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 bg-gradient-to-r from-amber-600 to-amber-500">
          <div className="container mx-auto px-6 text-center">
            <h2 className="text-4xl md:text-5xl font-bold mb-6">Ready to Build Your Delhi Social Presence?</h2>
            <p className="text-xl mb-8 max-w-3xl mx-auto">
              Join 300+ Delhi brands creating meaningful connections and driving growth through strategic social media marketing.
            </p>
            <Button asChild size="lg" className="bg-white text-amber-600 hover:bg-slate-100 text-lg px-8 py-4">
              <Link to="/quote">Start Your Social Media Journey</Link>
            </Button>
          </div>
        </section>
      </main>
      
      <Footer />
    </div>
  );
};

export default DelhiSocialMediaServices;
