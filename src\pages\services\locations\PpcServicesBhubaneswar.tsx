import React from 'react';
import { Link } from 'react-router-dom';
import { ArrowRight, Target, TrendingUp, Users, CheckCircle, Building, Star, Crown, Zap, BarChart } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';
import Sidebar from '@/components/Sidebar';

const PpcServicesBhubaneswar = () => {
  const ppcServices = [
    'Google Ads Management Bhubaneswar',
    'PPC Campaign Optimization Bhubaneswar',
    'Search Ads Bhubaneswar',
    'Display Advertising Bhubaneswar',
    'Shopping Ads Bhubaneswar',
    'YouTube Ads Bhubaneswar',
    'Facebook Ads Bhubaneswar',
    'Instagram Ads Bhubaneswar',
    'LinkedIn Ads Bhubaneswar',
    'Local PPC Bhubaneswar',
    'E-commerce PPC Bhubaneswar',
    'Lead Generation Ads Bhubaneswar',
    'Remarketing Campaigns Bhubaneswar',
    'Conversion Tracking Bhubaneswar',
    'Landing Page Optimization Bhubaneswar',
    'Government Sector PPC Bhubaneswar',
    'IT Industry PPC Bhubaneswar',
    'Tourism PPC Bhubaneswar',
    'Healthcare PPC Bhubaneswar',
    'Education PPC Bhubaneswar'
  ];

  const industryFocus = [
    {
      industry: 'Government & Public Sector PPC',
      description: 'Specialized PPC campaigns for Bhubaneswar\'s government offices and public sector organizations',
      icon: Building,
      features: ['Government Service Ads', 'Public Awareness Campaigns', 'Citizen Engagement Ads', 'Policy Promotion PPC']
    },
    {
      industry: 'IT & Technology PPC',
      description: 'Strategic PPC for Bhubaneswar\'s growing IT sector and technology companies',
      icon: Star,
      features: ['Software Company PPC', 'IT Service Ads', 'Tech Startup Campaigns', 'B2B Technology PPC']
    },
    {
      industry: 'Tourism & Heritage PPC',
      description: 'Comprehensive PPC for Odisha\'s tourism industry and cultural heritage promotion',
      icon: Crown,
      features: ['Temple Tourism Ads', 'Cultural Event PPC', 'Hotel Booking Ads', 'Travel Package Promotion']
    },
    {
      industry: 'Education & Training PPC',
      description: 'Targeted PPC campaigns for Bhubaneswar\'s educational institutions and training centers',
      icon: Target,
      features: ['University Admission Ads', 'Coaching Center PPC', 'Online Course Promotion', 'Skill Development Ads']
    }
  ];

  const localPpcFeatures = [
    'Bhubaneswar Local PPC Targeting',
    'Odisha State Campaign Management',
    'Temple City Tourism PPC',
    'Government Sector Advertising',
    'IT Hub Promotion Campaigns',
    'Bhubaneswar Map Ads',
    'Regional Language Targeting',
    'Local Business Promotion',
    'Cultural Event Marketing',
    'Seasonal Tourism Campaigns'
  ];

  const achievements = [
    '200+ Bhubaneswar Businesses Served',
    '1,850% Average ROAS Achieved',
    '₹35Cr+ Revenue Generated for Clients',
    '92% Campaign Success Rate',
    'Top PPC Agency in Odisha',
    'Government Sector PPC Specialists'
  ];

  const campaignTypes = [
    {
      type: 'Search Campaigns',
      description: 'Target high-intent keywords for Bhubaneswar businesses',
      metrics: ['95% Quality Score', '₹12 Avg CPC', '8.5% CTR', '15% Conversion Rate']
    },
    {
      type: 'Display Campaigns',
      description: 'Visual advertising across Odisha and Eastern India',
      metrics: ['2.8% CTR', '₹8 Avg CPC', '12% Conversion Rate', '85% Reach']
    },
    {
      type: 'Shopping Campaigns',
      description: 'E-commerce product promotion for Bhubaneswar retailers',
      metrics: ['18% ROAS', '₹15 Avg CPC', '6.2% CTR', '22% Conversion Rate']
    },
    {
      type: 'Local Campaigns',
      description: 'Location-based advertising for Bhubaneswar local businesses',
      metrics: ['25% Store Visits', '₹10 Avg CPC', '12% CTR', '18% Conversion Rate']
    }
  ];

  return (
    <div className="min-h-screen bg-slate-900 relative">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        <div className="container mx-auto px-6 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            <div className="lg:col-span-3">
              <section className="pt-24 pb-16">
                <div className="max-w-4xl">
                  <div className="inline-flex items-center space-x-2 bg-orange-500/20 border border-orange-500/30 rounded-full px-6 py-2 mb-8">
                    <Target className="w-4 h-4 text-orange-400" />
                    <span className="text-orange-400 font-medium">PPC Services Bhubaneswar • Odisha's Digital Advertising Leaders</span>
                  </div>
                  
                  <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
                    #1 Google Ads Company in
                    <span className="bg-gradient-to-r from-orange-400 to-orange-600 bg-clip-text text-transparent"> Bhubaneswar</span>
                  </h1>
                  
                  <p className="text-xl text-slate-300 mb-8">
                    Premier PPC services in Bhubaneswar offering comprehensive Google Ads management and paid advertising solutions for government organizations, IT companies, tourism businesses, and educational institutions. Our Bhubaneswar PPC agency provides professional Google Ads services including search campaigns, display advertising, shopping ads, local PPC Bhubaneswar, and government sector PPC. Serving 200+ Bhubaneswar businesses with proven ₹35Cr+ revenue generation and 1,850% average ROAS through strategic pay-per-click advertising and digital marketing excellence in Odisha's capital city.
                  </p>
                  
                  <div className="flex flex-col sm:flex-row gap-4 mb-8">
                    <Button asChild size="lg" className="bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white text-lg px-8 py-4">
                      <Link to="/quote">Get Free PPC Audit Bhubaneswar</Link>
                    </Button>
                    <Button asChild size="lg" variant="outline" className="border-orange-500 text-orange-400 hover:bg-orange-500 hover:text-white text-lg px-8 py-4">
                      <Link to="/contact">Call Bhubaneswar PPC Experts: +91-8708577598</Link>
                    </Button>
                  </div>

                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                    {achievements.map((achievement, index) => (
                      <div key={index} className="flex items-center justify-center space-x-2 bg-slate-800/50 rounded-lg p-3">
                        <CheckCircle className="w-4 h-4 text-orange-400 flex-shrink-0" />
                        <span className="text-slate-300">{achievement}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </section>

              <section className="mb-16">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Bhubaneswar Industry-Specific
                    <span className="bg-gradient-to-r from-orange-400 to-orange-600 bg-clip-text text-transparent"> PPC Solutions</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Specialized PPC strategies tailored for Bhubaneswar's key industries and business sectors.
                  </p>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  {industryFocus.map((industry, index) => (
                    <Card key={index} className="bg-slate-900/80 border-orange-500/20 hover:border-orange-500/40 transition-all duration-300">
                      <CardContent className="p-8">
                        <div className="w-12 h-12 bg-gradient-to-r from-orange-500 to-orange-600 rounded-lg flex items-center justify-center mb-6">
                          <industry.icon className="w-6 h-6 text-white" />
                        </div>
                        <h3 className="text-xl font-bold text-white mb-4">{industry.industry}</h3>
                        <p className="text-slate-300 mb-6">{industry.description}</p>
                        <ul className="space-y-2">
                          {industry.features.map((feature, idx) => (
                            <li key={idx} className="flex items-center text-slate-400 text-sm">
                              <CheckCircle className="w-3 h-3 text-orange-400 mr-2 flex-shrink-0" />
                              {feature}
                            </li>
                          ))}
                        </ul>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>

              <section className="mb-16 bg-slate-800/30 rounded-2xl p-8">
                <h3 className="text-2xl font-bold text-white mb-6 text-center">
                  Comprehensive PPC Services in Bhubaneswar
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  {ppcServices.map((service, index) => (
                    <div key={index} className="bg-slate-900/50 rounded-lg p-4 text-center">
                      <div className="text-orange-400 font-medium text-sm">{service}</div>
                    </div>
                  ))}
                </div>
              </section>

              <section className="mb-16 bg-slate-800/30 rounded-2xl p-8">
                <h3 className="text-2xl font-bold text-white mb-6 text-center">
                  Campaign Performance Metrics
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                  {campaignTypes.map((campaign, index) => (
                    <div key={index} className="bg-slate-900/50 rounded-lg p-6">
                      <h4 className="text-orange-400 font-semibold mb-3">{campaign.type}</h4>
                      <p className="text-slate-300 text-sm mb-4">{campaign.description}</p>
                      <div className="space-y-2">
                        {campaign.metrics.map((metric, idx) => (
                          <div key={idx} className="text-slate-400 text-xs">{metric}</div>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </section>

              <section className="mb-16 bg-slate-800/30 rounded-2xl p-8">
                <h3 className="text-2xl font-bold text-white mb-6 text-center">
                  Local PPC Bhubaneswar Specialization
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                  {localPpcFeatures.map((feature, index) => (
                    <div key={index} className="bg-slate-900/50 rounded-lg p-4 text-center">
                      <div className="text-orange-400 font-medium text-sm">{feature}</div>
                    </div>
                  ))}
                </div>
              </section>

              <section className="mb-16 bg-slate-800/30 rounded-2xl p-8">
                <h3 className="text-2xl font-bold text-white mb-6 text-center">
                  Why Choose Our Bhubaneswar PPC Services?
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  <div className="text-center">
                    <div className="w-16 h-16 bg-gradient-to-r from-orange-500 to-orange-600 rounded-full flex items-center justify-center mx-auto mb-4">
                      <Building className="w-8 h-8 text-white" />
                    </div>
                    <h4 className="text-white font-semibold mb-2">Local Market Expertise</h4>
                    <p className="text-slate-400 text-sm">Deep understanding of Bhubaneswar's business landscape, government sector, and cultural nuances for targeted PPC campaigns.</p>
                  </div>
                  <div className="text-center">
                    <div className="w-16 h-16 bg-gradient-to-r from-orange-500 to-orange-600 rounded-full flex items-center justify-center mx-auto mb-4">
                      <TrendingUp className="w-8 h-8 text-white" />
                    </div>
                    <h4 className="text-white font-semibold mb-2">Proven ROAS</h4>
                    <p className="text-slate-400 text-sm">200+ successful PPC campaigns in Bhubaneswar with 1,850% average ROAS and ₹35Cr+ revenue generation.</p>
                  </div>
                  <div className="text-center">
                    <div className="w-16 h-16 bg-gradient-to-r from-orange-500 to-orange-600 rounded-full flex items-center justify-center mx-auto mb-4">
                      <Users className="w-8 h-8 text-white" />
                    </div>
                    <h4 className="text-white font-semibold mb-2">Industry Specialization</h4>
                    <p className="text-slate-400 text-sm">Specialized expertise in government, IT, tourism, and education sectors prominent in Bhubaneswar market.</p>
                  </div>
                </div>
              </section>

              <section className="mb-16 bg-slate-800/30 rounded-2xl p-8">
                <h3 className="text-2xl font-bold text-white mb-6 text-center">
                  Complete Digital Marketing Solutions Bhubaneswar
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  <Link to="/services/seo/bhubaneswar" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-orange-400 font-semibold mb-2 group-hover:text-orange-300">SEO Services Bhubaneswar</h4>
                    <p className="text-slate-400 text-sm">Search engine optimization and organic marketing for Bhubaneswar businesses</p>
                  </Link>
                  <Link to="/services/social-media/bhubaneswar" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-orange-400 font-semibold mb-2 group-hover:text-orange-300">Social Media Marketing Bhubaneswar</h4>
                    <p className="text-slate-400 text-sm">Social media management and marketing for Bhubaneswar brands</p>
                  </Link>
                  <Link to="/services/content/bhubaneswar" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-orange-400 font-semibold mb-2 group-hover:text-orange-300">Content Marketing Bhubaneswar</h4>
                    <p className="text-slate-400 text-sm">Content creation and marketing strategies for Bhubaneswar businesses</p>
                  </Link>
                  <Link to="/services/web-development/bhubaneswar" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-orange-400 font-semibold mb-2 group-hover:text-orange-300">Web Development Bhubaneswar</h4>
                    <p className="text-slate-400 text-sm">Professional website development and design services</p>
                  </Link>
                  <Link to="/services/email/bhubaneswar" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-orange-400 font-semibold mb-2 group-hover:text-orange-300">Email Marketing Bhubaneswar</h4>
                    <p className="text-slate-400 text-sm">Email marketing campaigns and automation for Bhubaneswar businesses</p>
                  </Link>
                  <Link to="/portfolio" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-orange-400 font-semibold mb-2 group-hover:text-orange-300">Bhubaneswar Success Stories</h4>
                    <p className="text-slate-400 text-sm">See our proven results and case studies from Bhubaneswar clients</p>
                  </Link>
                </div>
              </section>

              <section className="bg-gradient-to-r from-orange-600 to-orange-500 rounded-2xl p-8 text-center">
                <h2 className="text-3xl md:text-5xl font-bold mb-6">Ready to Maximize Your Bhubaneswar ROI?</h2>
                <p className="text-xl mb-8">
                  Join 200+ successful Bhubaneswar businesses that trust GOD Digital Marketing for PPC excellence. Proven strategies delivering 1,850% ROAS and ₹35Cr+ revenue generation.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button asChild size="lg" className="bg-white text-orange-600 hover:bg-slate-100 text-lg px-8 py-4">
                    <Link to="/quote">Get Free PPC Audit</Link>
                  </Button>
                  <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-orange-600 text-lg px-8 py-4">
                    <Link to="/contact">Call Bhubaneswar PPC Experts: +91-8708577598</Link>
                  </Button>
                </div>
              </section>
            </div>

            <div className="lg:col-span-1">
              <Sidebar currentService="ppc" />
            </div>
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
};

export default PpcServicesBhubaneswar;
