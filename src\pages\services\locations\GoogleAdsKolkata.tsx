import React from 'react';
import { Link } from 'react-router-dom';
import { ArrowRight, Target, TrendingUp, Users, CheckCircle, Building, Star, Crown, Zap, DollarSign } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';
import Sidebar from '@/components/Sidebar';

const GoogleAdsKolkata = () => {
  const googleAdsServices = [
    'Google Ads Management Kolkata',
    'PPC Campaign Optimization Kolkata',
    'Search Ads Kolkata',
    'Display Advertising Kolkata',
    'Shopping Ads Kolkata',
    'YouTube Ads Kolkata',
    'Google Ads Audit Kolkata',
    'Landing Page Optimization Kolkata',
    'Conversion Tracking Setup Kolkata',
    'Remarketing Campaigns Kolkata',
    'Local Google Ads Kolkata',
    'Mobile App Promotion Kolkata',
    'Performance Max Campaigns Kolkata',
    'Smart Bidding Strategies Kolkata',
    'Ad Extensions Optimization Kolkata',
    'Competitor Analysis Kolkata',
    'Education Google Ads Kolkata',
    'Healthcare Google Ads Kolkata',
    'Manufacturing Google Ads Kolkata',
    'Cultural Events Google Ads Kolkata'
  ];

  const campaignTypes = [
    {
      type: 'Education Campaigns',
      description: 'Specialized Google Ads for Kolkata\'s educational institutions and coaching centers',
      icon: Target,
      features: ['Student Recruitment Ads', 'Course Promotion', 'Educational Services', 'Admission Campaigns']
    },
    {
      type: 'Healthcare Campaigns',
      description: 'Medical and healthcare Google Ads for Kolkata\'s healthcare sector',
      icon: Star,
      features: ['Medical Services Ads', 'Hospital Promotion', 'Healthcare Awareness', 'Patient Acquisition']
    },
    {
      type: 'Manufacturing Campaigns',
      description: 'Industrial and manufacturing Google Ads for Kolkata\'s industrial sector',
      icon: Zap,
      features: ['B2B Manufacturing', 'Industrial Equipment', 'Export Promotion', 'Supply Chain Ads']
    },
    {
      type: 'Cultural & Tourism Campaigns',
      description: 'Tourism and cultural Google Ads for Kolkata\'s heritage and tourism industry',
      icon: Building,
      features: ['Tourism Promotion', 'Cultural Events', 'Heritage Tours', 'Festival Marketing']
    }
  ];

  const pricingPackages = [
    {
      name: 'Google Ads Kolkata Starter',
      price: '₹24,000',
      period: '/month',
      description: 'Perfect for small Kolkata businesses starting with Google Ads',
      features: [
        'Ad Spend: Up to ₹40,000/month',
        'Search & Display Campaigns',
        'Basic Keyword Research',
        'Landing Page Setup',
        'Conversion Tracking',
        'Monthly Performance Report'
      ]
    },
    {
      name: 'Kolkata Google Ads Professional',
      price: '₹44,000',
      period: '/month',
      description: 'Comprehensive Google Ads management for growing Kolkata businesses',
      features: [
        'Ad Spend: Up to ₹1,50,000/month',
        'All Campaign Types',
        'Advanced Keyword Strategy',
        'A/B Testing & Optimization',
        'Remarketing Campaigns',
        'Dedicated Account Manager',
        'Bi-weekly Optimization'
      ],
      popular: true
    },
    {
      name: 'Enterprise Google Ads Kolkata',
      price: '₹78,000',
      period: '/month',
      description: 'Advanced Google Ads solutions for large Kolkata enterprises',
      features: [
        'Unlimited Ad Spend Management',
        'Multi-location Campaigns',
        'Advanced Attribution Modeling',
        'Custom Audience Development',
        'Competitor Intelligence',
        'Priority Support',
        'Weekly Strategy Sessions'
      ]
    }
  ];

  const stats = [
    {
      metric: '950+',
      description: 'Google Ads Campaigns Managed',
      detail: 'For Kolkata businesses'
    },
    {
      metric: '420%',
      description: 'Average ROAS Achieved',
      detail: 'Return on ad spend'
    },
    {
      metric: '₹28Cr+',
      description: 'Revenue Generated',
      detail: 'Through Google Ads campaigns'
    },
    {
      metric: '86%',
      description: 'Client Retention Rate',
      detail: 'Long-term partnerships'
    }
  ];

  const achievements = [
    'Google Premier Partner for Kolkata',
    'Top Google Ads Agency in Cultural Capital',
    'Education Sector PPC Specialists',
    'Healthcare Ads Experts',
    'Manufacturing B2B Leaders',
    'Tourism Marketing Champions'
  ];

  return (
    <div className="min-h-screen bg-slate-900 relative">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        <div className="container mx-auto px-6 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            {/* Main Content */}
            <div className="lg:col-span-3">
              {/* Hero Section */}
              <section className="pt-24 pb-16">
                <div className="max-w-4xl">
                  <div className="inline-flex items-center space-x-2 bg-green-500/20 border border-green-500/30 rounded-full px-6 py-2 mb-8">
                    <Target className="w-4 h-4 text-green-400" />
                    <span className="text-green-400 font-medium">Google Ads Kolkata • Cultural Capital PPC Excellence</span>
                  </div>
                  
                  <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
                    #1 Google Ads Company in
                    <span className="bg-gradient-to-r from-green-400 to-green-600 bg-clip-text text-transparent"> Kolkata</span>
                  </h1>
                  
                  <p className="text-xl text-slate-300 mb-8">
                    Premier Google Ads management in Kolkata offering comprehensive PPC campaign solutions from search ads to performance max campaigns. Serving 950+ Kolkata businesses across all areas - from educational institutions in College Street to manufacturing companies in Howrah. Expert Google Ads solutions with proven ₹28Cr+ revenue generation and 420% average ROAS for Kolkata clients in India's Cultural Capital.
                  </p>
                  
                  <div className="flex flex-col sm:flex-row gap-4 mb-8">
                    <Button asChild size="lg" className="bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white text-lg px-8 py-4">
                      <Link to="/quote">Get Free Kolkata Google Ads Audit</Link>
                    </Button>
                    <Button asChild size="lg" variant="outline" className="border-green-500 text-green-400 hover:bg-green-500 hover:text-white text-lg px-8 py-4">
                      <Link to="/contact">Call PPC Experts: +91-8708577598</Link>
                    </Button>
                  </div>

                  {/* Achievements */}
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                    {achievements.map((achievement, index) => (
                      <div key={index} className="flex items-center justify-center space-x-2 bg-slate-800/50 rounded-lg p-3">
                        <CheckCircle className="w-4 h-4 text-green-400 flex-shrink-0" />
                        <span className="text-slate-300">{achievement}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </section>

              {/* Stats Section */}
              <section className="mb-16 bg-slate-800/50 rounded-2xl p-8">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Kolkata Google Ads
                    <span className="bg-gradient-to-r from-green-400 to-green-600 bg-clip-text text-transparent"> Success Metrics</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Measurable Google Ads results from Kolkata businesses across all industries.
                  </p>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
                  {stats.map((stat, index) => (
                    <div key={index} className="text-center">
                      <div className="text-4xl font-bold text-green-400 mb-2">{stat.metric}</div>
                      <div className="text-white font-semibold mb-1">{stat.description}</div>
                      <div className="text-slate-400">{stat.detail}</div>
                    </div>
                  ))}
                </div>
              </section>

              {/* Campaign Types Section */}
              <section className="mb-16">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Google Ads Campaign Types We
                    <span className="bg-gradient-to-r from-green-400 to-green-600 bg-clip-text text-transparent"> Master</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Specialized Google Ads campaigns designed for Kolkata's education, healthcare, and cultural landscape.
                  </p>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  {campaignTypes.map((campaign, index) => (
                    <Card key={index} className="bg-slate-900/80 border-green-500/20 hover:border-green-500/40 transition-all duration-300">
                      <CardContent className="p-8">
                        <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-green-600 rounded-lg flex items-center justify-center mb-6">
                          <campaign.icon className="w-6 h-6 text-white" />
                        </div>
                        <h3 className="text-xl font-bold text-white mb-4">{campaign.type}</h3>
                        <p className="text-slate-300 mb-6">{campaign.description}</p>
                        <ul className="space-y-2">
                          {campaign.features.map((feature, idx) => (
                            <li key={idx} className="flex items-center text-slate-400 text-sm">
                              <CheckCircle className="w-3 h-3 text-green-400 mr-2 flex-shrink-0" />
                              {feature}
                            </li>
                          ))}
                        </ul>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>

              {/* Pricing Section */}
              <section className="mb-16">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Kolkata Google Ads
                    <span className="bg-gradient-to-r from-green-400 to-green-600 bg-clip-text text-transparent"> Pricing</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Competitive Google Ads pricing designed for Kolkata's education and cultural business environment.
                  </p>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                  {pricingPackages.map((pkg, index) => (
                    <Card key={index} className={`bg-slate-900/80 border-green-500/20 hover:border-green-500/40 transition-all duration-300 relative ${pkg.popular ? 'ring-2 ring-green-500' : ''}`}>
                      {pkg.popular && (
                        <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                          <span className="bg-green-500 text-white px-4 py-2 rounded-full text-sm font-semibold">Most Popular</span>
                        </div>
                      )}
                      <CardContent className="p-8">
                        <h3 className="text-2xl font-bold text-white mb-2">{pkg.name}</h3>
                        <p className="text-slate-300 mb-6">{pkg.description}</p>
                        <div className="mb-6">
                          <span className="text-4xl font-bold text-green-400">{pkg.price}</span>
                          <span className="text-slate-400">{pkg.period}</span>
                        </div>
                        <ul className="space-y-3 mb-8">
                          {pkg.features.map((feature, idx) => (
                            <li key={idx} className="flex items-center text-slate-300">
                              <CheckCircle className="w-4 h-4 text-green-400 mr-3 flex-shrink-0" />
                              {feature}
                            </li>
                          ))}
                        </ul>
                        <Button asChild className={`w-full ${pkg.popular ? 'bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700' : 'bg-slate-700 hover:bg-slate-600'} text-white`}>
                          <Link to="/quote">Get Started</Link>
                        </Button>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>

              {/* CTA Section */}
              <section className="bg-gradient-to-r from-green-600 to-green-500 rounded-2xl p-8 text-center">
                <h2 className="text-3xl md:text-5xl font-bold mb-6">Ready to Dominate Kolkata with Google Ads?</h2>
                <p className="text-xl mb-8">
                  Join 950+ Kolkata businesses that trust GOD Digital Marketing for Google Ads success. Proven strategies that deliver 420% ROAS and ₹28Cr+ revenue generation.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button asChild size="lg" className="bg-white text-green-600 hover:bg-slate-100 text-lg px-8 py-4">
                    <Link to="/quote">Get Free Google Ads Audit</Link>
                  </Button>
                  <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-green-600 text-lg px-8 py-4">
                    <Link to="/contact">Call PPC Experts: +91-8708577598</Link>
                  </Button>
                </div>
              </section>
            </div>

            {/* Sidebar */}
            <div className="lg:col-span-1">
              <Sidebar currentService="ppc" currentLocation="kolkata" />
            </div>
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
};

export default GoogleAdsKolkata;
