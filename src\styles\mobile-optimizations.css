/* Mobile-First Performance Optimizations */

/* Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Optimize font loading */
@font-face {
  font-family: 'Inter';
  font-display: swap;
  src: url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');
}

/* Mobile-specific optimizations */
@media (max-width: 768px) {
  /* Reduce complex animations on mobile */
  .complex-animation {
    animation: none !important;
    transform: none !important;
  }
  
  /* Optimize touch targets */
  button,
  a,
  [role="button"] {
    min-height: 44px;
    min-width: 44px;
    padding: 12px 16px;
  }
  
  /* Improve scrolling performance */
  .scroll-container {
    -webkit-overflow-scrolling: touch;
    overflow-scrolling: touch;
  }
  
  /* Reduce blur effects on mobile */
  .backdrop-blur-sm {
    backdrop-filter: none;
    background-color: rgba(30, 41, 59, 0.8);
  }
  
  .backdrop-blur-md {
    backdrop-filter: none;
    background-color: rgba(30, 41, 59, 0.9);
  }
  
  /* Optimize text rendering */
  body {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeSpeed;
  }
  
  /* Reduce shadow complexity */
  .shadow-2xl {
    box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1);
  }
  
  .shadow-xl {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  }
}

/* High DPI display optimizations */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  /* Optimize for retina displays */
  .crisp-edges {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

/* Performance-critical utilities */
.gpu-accelerated {
  transform: translateZ(0);
  will-change: transform;
}

.contain-layout {
  contain: layout;
}

.contain-paint {
  contain: paint;
}

.contain-strict {
  contain: strict;
}

/* Optimize for dark mode */
@media (prefers-color-scheme: dark) {
  :root {
    color-scheme: dark;
  }
}

/* Reduce motion for battery saving */
@media (prefers-reduced-motion: reduce) {
  .animate-spin {
    animation: none;
  }
  
  .animate-pulse {
    animation: none;
  }
  
  .animate-bounce {
    animation: none;
  }
}

/* Touch-friendly spacing */
@media (max-width: 640px) {
  .touch-spacing {
    padding: 16px;
    margin: 8px 0;
  }
  
  /* Larger tap targets */
  .mobile-tap-target {
    min-height: 48px;
    min-width: 48px;
  }
  
  /* Optimize form inputs */
  input,
  textarea,
  select {
    font-size: 16px; /* Prevents zoom on iOS */
    padding: 12px 16px;
  }
}

/* Loading states */
.loading-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Critical CSS for above-the-fold content */
.hero-critical {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Optimize images */
img {
  max-width: 100%;
  height: auto;
  loading: lazy;
}

img[loading="eager"] {
  loading: eager;
}

/* Smooth scrolling with performance consideration */
@media (prefers-reduced-motion: no-preference) {
  html {
    scroll-behavior: smooth;
  }
}

/* Container queries for responsive design */
@container (max-width: 768px) {
  .container-responsive {
    padding: 1rem;
  }
}

/* Focus management for accessibility */
.focus-visible:focus-visible {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Print optimizations */
@media print {
  .no-print {
    display: none !important;
  }
  
  * {
    background: transparent !important;
    color: black !important;
    box-shadow: none !important;
    text-shadow: none !important;
  }
}
