import React from 'react';
import { Link } from 'react-router-dom';
import { ArrowRight, Share2, TrendingUp, Users, CheckCircle, Building, Star, Crown, Heart, MessageCircle } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';

const SocialMediaDelhi = () => {
  const socialMediaServices = [
    'Social Media Marketing Delhi',
    'Social Media Management Delhi',
    'Facebook Marketing Delhi',
    'Instagram Marketing Delhi',
    'LinkedIn Marketing Delhi',
    'Twitter Marketing Delhi',
    'YouTube Marketing Delhi',
    'Social Media Automation Delhi',
    'Influencer Marketing Delhi',
    'Community Management Delhi',
    'Social Media Advertising Delhi',
    'Content Creation Delhi',
    'Social Media Strategy Delhi',
    'Brand Management Delhi',
    'Social Media Analytics Delhi',
    'Reputation Management Delhi',
    'Social Commerce Delhi',
    'Live Streaming Services Delhi',
    'Social Media Training Delhi',
    'Crisis Management Delhi'
  ];

  const delhiAreas = [
    'Central Delhi Social Media',
    'South Delhi Social Marketing',
    'North Delhi Social Media',
    'East Delhi Social Marketing',
    'West Delhi Social Media',
    'New Delhi Social Marketing',
    'Connaught Place Social Media',
    'Karol Bagh Social Marketing',
    'Lajpat Nagar Social Media',
    'Khan Market Social Marketing',
    'Nehru Place Social Media',
    'Saket Social Marketing'
  ];

  const platforms = [
    {
      name: 'Facebook Marketing',
      description: 'Comprehensive Facebook marketing for Delhi businesses',
      icon: Share2,
      features: ['Page Management', 'Facebook Ads', 'Community Building', 'Analytics']
    },
    {
      name: 'Instagram Marketing',
      description: 'Visual storytelling and engagement for Delhi brands',
      icon: Heart,
      features: ['Content Creation', 'Stories & Reels', 'Influencer Partnerships', 'Shopping Integration']
    },
    {
      name: 'LinkedIn Marketing',
      description: 'B2B networking and lead generation for Delhi professionals',
      icon: Users,
      features: ['Company Pages', 'LinkedIn Ads', 'Thought Leadership', 'Lead Generation']
    },
    {
      name: 'YouTube Marketing',
      description: 'Video content strategy and channel growth for Delhi businesses',
      icon: MessageCircle,
      features: ['Channel Optimization', 'Video SEO', 'Content Strategy', 'Analytics']
    }
  ];

  const socialMediaPackages = [
    {
      name: 'Social Media Delhi Starter',
      price: '₹18,000',
      period: '/month',
      description: 'Perfect for small Delhi businesses starting with social media',
      features: [
        '2 Social Media Platforms',
        '15 Posts per Month',
        'Basic Graphics Design',
        'Community Management',
        'Monthly Analytics Report',
        'Delhi Local Targeting'
      ]
    },
    {
      name: 'Delhi Social Media Professional',
      price: '₹32,000',
      period: '/month',
      description: 'Comprehensive social media management for growing Delhi businesses',
      features: [
        '4 Social Media Platforms',
        '30 Posts per Month',
        'Professional Content Creation',
        'Paid Social Advertising',
        'Influencer Partnerships',
        'Advanced Analytics',
        'Crisis Management'
      ],
      popular: true
    },
    {
      name: 'Enterprise Social Media Delhi',
      price: '₹55,000',
      period: '/month',
      description: 'Advanced social media strategy for large Delhi enterprises',
      features: [
        'All Major Platforms',
        'Unlimited Content Creation',
        'Video Content Production',
        'Advanced Automation',
        'Custom Strategy Development',
        'Dedicated Social Media Manager',
        'Priority Support'
      ]
    }
  ];

  const stats = [
    {
      metric: '800+',
      description: 'Delhi Social Media Accounts',
      detail: 'Successfully managed'
    },
    {
      metric: '250%',
      description: 'Average Engagement Increase',
      detail: 'For Delhi businesses'
    },
    {
      metric: '₹12Cr+',
      description: 'Revenue Generated',
      detail: 'Through social media'
    },
    {
      metric: '95%',
      description: 'Client Satisfaction Rate',
      detail: 'Consistent quality delivery'
    }
  ];

  const achievements = [
    'Top Social Media Agency in Delhi',
    'Facebook Marketing Partners',
    'Instagram Certified Specialists',
    'LinkedIn Marketing Experts',
    'YouTube Channel Growth Specialists',
    'Influencer Marketing Leaders'
  ];

  return (
    <div className="min-h-screen bg-slate-900 relative">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        {/* Hero Section */}
        <section className="pt-32 pb-20">
          <div className="container mx-auto px-6">
            <div className="max-w-5xl mx-auto text-center">
              <div className="inline-flex items-center space-x-2 bg-pink-500/20 border border-pink-500/30 rounded-full px-6 py-2 mb-8">
                <Share2 className="w-4 h-4 text-pink-400" />
                <span className="text-pink-400 font-medium">Social Media Marketing Delhi • Digital Engagement Excellence</span>
              </div>
              
              <h1 className="text-5xl md:text-7xl font-bold mb-6 leading-tight">
                #1 Social Media Marketing Company in
                <span className="bg-gradient-to-r from-pink-400 to-pink-600 bg-clip-text text-transparent"> Delhi NCR</span>
              </h1>
              
              <p className="text-xl text-slate-300 mb-12 max-w-4xl mx-auto">
                Premier social media marketing in Delhi offering comprehensive social media management across all platforms. Serving 800+ Delhi businesses across all districts - from government sector enterprises in Central Delhi to tech startups in Gurgaon. Expert social media solutions with proven ₹12Cr+ revenue generation and 250% average engagement increase for Delhi clients.
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
                <Button asChild size="lg" className="bg-gradient-to-r from-pink-500 to-pink-600 hover:from-pink-600 hover:to-pink-700 text-white text-lg px-8 py-4">
                  <Link to="/quote">Get Free Delhi Social Media Audit</Link>
                </Button>
                <Button asChild size="lg" variant="outline" className="border-pink-500 text-pink-400 hover:bg-pink-500 hover:text-white text-lg px-8 py-4">
                  <Link to="/contact">Call Social Media Experts: +91-8708577598</Link>
                </Button>
              </div>

              {/* Achievements */}
              <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                {achievements.map((achievement, index) => (
                  <div key={index} className="flex items-center justify-center space-x-2 bg-slate-800/50 rounded-lg p-3">
                    <CheckCircle className="w-4 h-4 text-pink-400 flex-shrink-0" />
                    <span className="text-slate-300">{achievement}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* Stats Section */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-6xl font-bold mb-6">
                Delhi Social Media Marketing
                <span className="bg-gradient-to-r from-pink-400 to-pink-600 bg-clip-text text-transparent"> Success Metrics</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-4xl mx-auto mb-12">
                Measurable social media results from Delhi businesses across all industries - delivering exceptional engagement and revenue growth.
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-4 gap-8 mb-16">
              {stats.map((stat, index) => (
                <div key={index} className="text-center">
                  <div className="text-5xl font-bold text-pink-400 mb-2">{stat.metric}</div>
                  <div className="text-white font-semibold mb-1">{stat.description}</div>
                  <div className="text-slate-400">{stat.detail}</div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Platforms Section */}
        <section className="py-20">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-6xl font-bold mb-6">
                Social Media Platforms We
                <span className="bg-gradient-to-r from-pink-400 to-pink-600 bg-clip-text text-transparent"> Master</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-4xl mx-auto">
                Comprehensive social media management across all major platforms for Delhi businesses.
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-4 gap-8 mb-20">
              {platforms.map((platform, index) => (
                <Card key={index} className="bg-slate-900/80 border-pink-500/20 hover:border-pink-500/40 transition-all duration-300">
                  <CardContent className="p-8">
                    <div className="w-12 h-12 bg-gradient-to-r from-pink-500 to-pink-600 rounded-lg flex items-center justify-center mb-6">
                      <platform.icon className="w-6 h-6 text-white" />
                    </div>
                    <h3 className="text-xl font-bold text-white mb-4">{platform.name}</h3>
                    <p className="text-slate-300 mb-6">{platform.description}</p>
                    <ul className="space-y-2">
                      {platform.features.map((feature, idx) => (
                        <li key={idx} className="flex items-center text-slate-400 text-sm">
                          <CheckCircle className="w-3 h-3 text-pink-400 mr-2 flex-shrink-0" />
                          {feature}
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Services Grid */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-6xl font-bold mb-6">
                Complete Social Media Services
                <span className="bg-gradient-to-r from-pink-400 to-pink-600 bg-clip-text text-transparent"> Available in Delhi</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-4xl mx-auto">
                Comprehensive range of social media services covering every aspect of digital engagement for Delhi businesses.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {socialMediaServices.map((service, index) => (
                <Card key={index} className="bg-slate-900/80 border-pink-500/20 hover:border-pink-500/40 transition-all duration-300">
                  <CardContent className="p-6 text-center">
                    <Share2 className="w-8 h-8 text-pink-400 mx-auto mb-4" />
                    <h3 className="text-white font-semibold mb-2 text-sm">{service}</h3>
                    <Button asChild size="sm" variant="outline" className="border-pink-500 text-pink-400 hover:bg-pink-500 hover:text-white text-xs">
                      <Link to="/quote">Get Quote</Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Pricing Section */}
        <section className="py-20">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-6xl font-bold mb-6">
                Delhi Social Media Marketing
                <span className="bg-gradient-to-r from-pink-400 to-pink-600 bg-clip-text text-transparent"> Pricing</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-4xl mx-auto">
                Transparent social media marketing pricing designed for Delhi businesses of all sizes.
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
              {socialMediaPackages.map((pkg, index) => (
                <Card key={index} className={`bg-slate-900/80 border-pink-500/20 hover:border-pink-500/40 transition-all duration-300 relative ${pkg.popular ? 'ring-2 ring-pink-500' : ''}`}>
                  {pkg.popular && (
                    <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                      <span className="bg-pink-500 text-white px-4 py-2 rounded-full text-sm font-semibold">Most Popular</span>
                    </div>
                  )}
                  <CardContent className="p-8">
                    <h3 className="text-2xl font-bold text-white mb-2">{pkg.name}</h3>
                    <p className="text-slate-300 mb-6">{pkg.description}</p>
                    <div className="mb-6">
                      <span className="text-4xl font-bold text-pink-400">{pkg.price}</span>
                      <span className="text-slate-400">{pkg.period}</span>
                    </div>
                    <ul className="space-y-3 mb-8">
                      {pkg.features.map((feature, idx) => (
                        <li key={idx} className="flex items-center text-slate-300">
                          <CheckCircle className="w-4 h-4 text-pink-400 mr-3 flex-shrink-0" />
                          {feature}
                        </li>
                      ))}
                    </ul>
                    <Button asChild className={`w-full ${pkg.popular ? 'bg-gradient-to-r from-pink-500 to-pink-600 hover:from-pink-600 hover:to-pink-700' : 'bg-slate-700 hover:bg-slate-600'} text-white`}>
                      <Link to="/quote">Get Started</Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 bg-gradient-to-r from-pink-600 to-pink-500">
          <div className="container mx-auto px-6 text-center">
            <h2 className="text-4xl md:text-6xl font-bold mb-6">Ready to Dominate Delhi Social Media?</h2>
            <p className="text-xl mb-8 max-w-4xl mx-auto">
              Join 800+ Delhi businesses that trust GOD Digital Marketing for social media success. Proven strategies that deliver 250% engagement increase and ₹12Cr+ revenue generation.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button asChild size="lg" className="bg-white text-pink-600 hover:bg-slate-100 text-lg px-8 py-4">
                <Link to="/quote">Get Free Social Media Audit</Link>
              </Button>
              <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-pink-600 text-lg px-8 py-4">
                <Link to="/contact">Call Social Media Experts: +91-8708577598</Link>
              </Button>
            </div>
          </div>
        </section>
      </main>
      
      <Footer />
    </div>
  );
};

export default SocialMediaDelhi;
