import React from 'react';
import { Link } from 'react-router-dom';
import { ArrowRight, Target, TrendingUp, Users, CheckCircle, Building, Star, Crown, Zap, DollarSign } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';
import Sidebar from '@/components/Sidebar';

const GoogleAdsHyderabad = () => {
  const googleAdsServices = [
    'Google Ads Management Hyderabad',
    'PPC Campaign Optimization Hyderabad',
    'Search Ads Hyderabad',
    'Display Advertising Hyderabad',
    'Shopping Ads Hyderabad',
    'YouTube Ads Hyderabad',
    'Google Ads Audit Hyderabad',
    'Landing Page Optimization Hyderabad',
    'Conversion Tracking Setup Hyderabad',
    'Remarketing Campaigns Hyderabad',
    'Local Google Ads Hyderabad',
    'Mobile App Promotion Hyderabad',
    'Performance Max Campaigns Hyderabad',
    'Smart Bidding Strategies Hyderabad',
    'Ad Extensions Optimization Hyderabad',
    'Competitor Analysis Hyderabad',
    'IT Services Google Ads Hyderabad',
    'Pharmaceutical Google Ads Hyderabad',
    'Healthcare Google Ads Hyderabad',
    'B2B Google Ads Hyderabad'
  ];

  const campaignTypes = [
    {
      type: 'IT Services Campaigns',
      description: 'Specialized Google Ads for Hyderabad\'s booming IT and software industry',
      icon: Target,
      features: ['Software Development Ads', 'Tech Recruitment Campaigns', 'B2B Lead Generation', 'International Targeting']
    },
    {
      type: 'Pharmaceutical Campaigns',
      description: 'Healthcare and pharmaceutical Google Ads for Hyderabad\'s pharma hub',
      icon: Star,
      features: ['Medical Device Ads', 'Pharma B2B Campaigns', 'Healthcare Services', 'Compliance-Ready Ads']
    },
    {
      type: 'Startup Campaigns',
      description: 'Growth-focused Google Ads for Hyderabad\'s thriving startup ecosystem',
      icon: Zap,
      features: ['Startup Launch Campaigns', 'Investor Targeting', 'Product Launch Ads', 'Growth Hacking']
    },
    {
      type: 'Local Business Campaigns',
      description: 'Location-based Google Ads targeting Hyderabad\'s local market',
      icon: Building,
      features: ['Local Search Ads', 'Map Pack Optimization', 'Call Extensions', 'Location Targeting']
    }
  ];

  const pricingPackages = [
    {
      name: 'Google Ads Hyderabad Starter',
      price: '₹28,000',
      period: '/month',
      description: 'Perfect for small Hyderabad businesses starting with Google Ads',
      features: [
        'Ad Spend: Up to ₹50,000/month',
        'Search & Display Campaigns',
        'Basic Keyword Research',
        'Landing Page Setup',
        'Conversion Tracking',
        'Monthly Performance Report'
      ]
    },
    {
      name: 'Hyderabad Google Ads Professional',
      price: '₹52,000',
      period: '/month',
      description: 'Comprehensive Google Ads management for growing Hyderabad businesses',
      features: [
        'Ad Spend: Up to ₹2,00,000/month',
        'All Campaign Types',
        'Advanced Keyword Strategy',
        'A/B Testing & Optimization',
        'Remarketing Campaigns',
        'Dedicated Account Manager',
        'Bi-weekly Optimization'
      ],
      popular: true
    },
    {
      name: 'Enterprise Google Ads Hyderabad',
      price: '₹95,000',
      period: '/month',
      description: 'Advanced Google Ads solutions for large Hyderabad enterprises',
      features: [
        'Unlimited Ad Spend Management',
        'Multi-location Campaigns',
        'Advanced Attribution Modeling',
        'Custom Audience Development',
        'Competitor Intelligence',
        'Priority Support',
        'Weekly Strategy Sessions'
      ]
    }
  ];

  const stats = [
    {
      metric: '1200+',
      description: 'Google Ads Campaigns Managed',
      detail: 'For Hyderabad businesses'
    },
    {
      metric: '480%',
      description: 'Average ROAS Achieved',
      detail: 'Return on ad spend'
    },
    {
      metric: '₹35Cr+',
      description: 'Revenue Generated',
      detail: 'Through Google Ads campaigns'
    },
    {
      metric: '89%',
      description: 'Client Retention Rate',
      detail: 'Long-term partnerships'
    }
  ];

  const achievements = [
    'Google Premier Partner for Hyderabad',
    'Top Google Ads Agency in Cyberabad',
    'IT Services PPC Specialists',
    'Pharmaceutical Ads Experts',
    'B2B Lead Generation Leaders',
    'Performance Marketing Champions'
  ];

  return (
    <div className="min-h-screen bg-slate-900 relative">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        <div className="container mx-auto px-6 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            {/* Main Content */}
            <div className="lg:col-span-3">
              {/* Hero Section */}
              <section className="pt-24 pb-16">
                <div className="max-w-4xl">
                  <div className="inline-flex items-center space-x-2 bg-green-500/20 border border-green-500/30 rounded-full px-6 py-2 mb-8">
                    <Target className="w-4 h-4 text-green-400" />
                    <span className="text-green-400 font-medium">Google Ads Hyderabad • Cyberabad PPC Excellence</span>
                  </div>
                  
                  <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
                    #1 Google Ads Company in
                    <span className="bg-gradient-to-r from-green-400 to-green-600 bg-clip-text text-transparent"> Hyderabad</span>
                  </h1>
                  
                  <p className="text-xl text-slate-300 mb-8">
                    Premier Google Ads management in Hyderabad offering comprehensive PPC campaign solutions from search ads to performance max campaigns. Serving 1200+ Hyderabad businesses across all areas - from IT companies in Hitech City to pharmaceutical companies in Genome Valley. Expert Google Ads solutions with proven ₹35Cr+ revenue generation and 480% average ROAS for Hyderabad clients in India's Cyberabad.
                  </p>
                  
                  <div className="flex flex-col sm:flex-row gap-4 mb-8">
                    <Button asChild size="lg" className="bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white text-lg px-8 py-4">
                      <Link to="/quote">Get Free Hyderabad Google Ads Audit</Link>
                    </Button>
                    <Button asChild size="lg" variant="outline" className="border-green-500 text-green-400 hover:bg-green-500 hover:text-white text-lg px-8 py-4">
                      <Link to="/contact">Call PPC Experts: +91-8708577598</Link>
                    </Button>
                  </div>

                  {/* Achievements */}
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                    {achievements.map((achievement, index) => (
                      <div key={index} className="flex items-center justify-center space-x-2 bg-slate-800/50 rounded-lg p-3">
                        <CheckCircle className="w-4 h-4 text-green-400 flex-shrink-0" />
                        <span className="text-slate-300">{achievement}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </section>

              {/* Stats Section */}
              <section className="mb-16 bg-slate-800/50 rounded-2xl p-8">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Hyderabad Google Ads
                    <span className="bg-gradient-to-r from-green-400 to-green-600 bg-clip-text text-transparent"> Success Metrics</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Measurable Google Ads results from Hyderabad businesses across all industries.
                  </p>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
                  {stats.map((stat, index) => (
                    <div key={index} className="text-center">
                      <div className="text-4xl font-bold text-green-400 mb-2">{stat.metric}</div>
                      <div className="text-white font-semibold mb-1">{stat.description}</div>
                      <div className="text-slate-400">{stat.detail}</div>
                    </div>
                  ))}
                </div>
              </section>

              {/* Campaign Types Section */}
              <section className="mb-16">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Google Ads Campaign Types We
                    <span className="bg-gradient-to-r from-green-400 to-green-600 bg-clip-text text-transparent"> Master</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Specialized Google Ads campaigns designed for Hyderabad's IT, pharmaceutical, and startup landscape.
                  </p>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  {campaignTypes.map((campaign, index) => (
                    <Card key={index} className="bg-slate-900/80 border-green-500/20 hover:border-green-500/40 transition-all duration-300">
                      <CardContent className="p-8">
                        <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-green-600 rounded-lg flex items-center justify-center mb-6">
                          <campaign.icon className="w-6 h-6 text-white" />
                        </div>
                        <h3 className="text-xl font-bold text-white mb-4">{campaign.type}</h3>
                        <p className="text-slate-300 mb-6">{campaign.description}</p>
                        <ul className="space-y-2">
                          {campaign.features.map((feature, idx) => (
                            <li key={idx} className="flex items-center text-slate-400 text-sm">
                              <CheckCircle className="w-3 h-3 text-green-400 mr-2 flex-shrink-0" />
                              {feature}
                            </li>
                          ))}
                        </ul>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>

              {/* Pricing Section */}
              <section className="mb-16">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Hyderabad Google Ads
                    <span className="bg-gradient-to-r from-green-400 to-green-600 bg-clip-text text-transparent"> Pricing</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Competitive Google Ads pricing designed for Hyderabad's IT and pharmaceutical business environment.
                  </p>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                  {pricingPackages.map((pkg, index) => (
                    <Card key={index} className={`bg-slate-900/80 border-green-500/20 hover:border-green-500/40 transition-all duration-300 relative ${pkg.popular ? 'ring-2 ring-green-500' : ''}`}>
                      {pkg.popular && (
                        <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                          <span className="bg-green-500 text-white px-4 py-2 rounded-full text-sm font-semibold">Most Popular</span>
                        </div>
                      )}
                      <CardContent className="p-8">
                        <h3 className="text-2xl font-bold text-white mb-2">{pkg.name}</h3>
                        <p className="text-slate-300 mb-6">{pkg.description}</p>
                        <div className="mb-6">
                          <span className="text-4xl font-bold text-green-400">{pkg.price}</span>
                          <span className="text-slate-400">{pkg.period}</span>
                        </div>
                        <ul className="space-y-3 mb-8">
                          {pkg.features.map((feature, idx) => (
                            <li key={idx} className="flex items-center text-slate-300">
                              <CheckCircle className="w-4 h-4 text-green-400 mr-3 flex-shrink-0" />
                              {feature}
                            </li>
                          ))}
                        </ul>
                        <Button asChild className={`w-full ${pkg.popular ? 'bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700' : 'bg-slate-700 hover:bg-slate-600'} text-white`}>
                          <Link to="/quote">Get Started</Link>
                        </Button>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>

              {/* CTA Section */}
              <section className="bg-gradient-to-r from-green-600 to-green-500 rounded-2xl p-8 text-center">
                <h2 className="text-3xl md:text-5xl font-bold mb-6">Ready to Dominate Hyderabad with Google Ads?</h2>
                <p className="text-xl mb-8">
                  Join 1200+ Hyderabad businesses that trust GOD Digital Marketing for Google Ads success. Proven strategies that deliver 480% ROAS and ₹35Cr+ revenue generation.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button asChild size="lg" className="bg-white text-green-600 hover:bg-slate-100 text-lg px-8 py-4">
                    <Link to="/quote">Get Free Google Ads Audit</Link>
                  </Button>
                  <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-green-600 text-lg px-8 py-4">
                    <Link to="/contact">Call PPC Experts: +91-8708577598</Link>
                  </Button>
                </div>
              </section>
            </div>

            {/* Sidebar */}
            <div className="lg:col-span-1">
              <Sidebar currentService="ppc" currentLocation="hyderabad" />
            </div>
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
};

export default GoogleAdsHyderabad;
