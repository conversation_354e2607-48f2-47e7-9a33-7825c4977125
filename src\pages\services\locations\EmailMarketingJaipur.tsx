import React from 'react';
import { Link } from 'react-router-dom';
import { ArrowRight, Mail, TrendingUp, Users, CheckCircle, Building, Star, Crown, Send, Zap } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';
import Sidebar from '@/components/Sidebar';

const EmailMarketingJaipur = () => {
  const emailServices = [
    'Email Marketing Services Jaipur',
    'Email Campaign Management Jaipur',
    'Newsletter Marketing Jaipur',
    'Email Automation Jaipur',
    'Drip Campaign Setup Jaipur',
    'Email Template Design Jaipur',
    'Email List Building Jaipur',
    'Email Segmentation Jaipur',
    'Transactional Emails Jaipur',
    'Email Analytics Jaipur',
    'Tourism Email Marketing Jaipur',
    'Heritage Business Email Jaipur',
    'Handicraft Email Campaigns Jaipur',
    'Jewelry Email Marketing Jaipur',
    'Hotel Email Marketing Jaipur',
    'Restaurant Email Campaigns Jaipur',
    'Real Estate Email Marketing Jaipur',
    'Fashion Email Campaigns Jaipur',
    'B2B Email Marketing Jaipur',
    'E-commerce Email Marketing Jaipur'
  ];

  const emailTypes = [
    {
      type: 'Tourism & Heritage Email Marketing',
      description: 'Targeted email campaigns for Jaipur\'s tourism capital and heritage industry businesses',
      icon: Building,
      features: ['Tourism Newsletters', 'Heritage Event Emails', 'Travel Package Promotions', 'Cultural Event Updates']
    },
    {
      type: 'Handicraft & Jewelry Email Marketing',
      description: 'Artisan-focused email marketing for Jaipur\'s famous handicraft and jewelry manufacturing sector',
      icon: Star,
      features: ['Craft Collection Emails', 'Jewelry Launch Campaigns', 'Artisan Story Newsletters', 'Export Client Communications']
    },
    {
      type: 'Fashion & Textile Email Marketing',
      description: 'Trendy email campaigns for Jaipur\'s textile and fashion industry leaders',
      icon: Crown,
      features: ['Fashion Collection Emails', 'Textile Industry Updates', 'Designer Showcase Campaigns', 'Style Guide Newsletters']
    },
    {
      type: 'Business & Commercial Email Marketing',
      description: 'Professional email marketing for Jaipur\'s growing business and commercial sector',
      icon: Send,
      features: ['B2B Newsletters', 'Industry Updates', 'Lead Nurturing Campaigns', 'Client Communication']
    }
  ];

  const emailPackages = [
    {
      name: 'Email Marketing Jaipur Starter',
      price: '₹15,000',
      period: '/month',
      description: 'Perfect for small Jaipur businesses and tourism operators',
      features: [
        'Up to 5,000 Subscribers',
        '8 Email Campaigns per Month',
        'Basic Email Templates',
        'List Management',
        'Basic Analytics',
        'Tourism Industry Focus'
      ]
    },
    {
      name: 'Jaipur Email Marketing Professional',
      price: '₹28,000',
      period: '/month',
      description: 'Comprehensive email marketing for growing Jaipur businesses',
      features: [
        'Up to 25,000 Subscribers',
        'Unlimited Email Campaigns',
        'Custom Email Templates',
        'Advanced Automation',
        'Segmentation & Personalization',
        'A/B Testing',
        'Heritage/Handicraft Specialization'
      ],
      popular: true
    },
    {
      name: 'Enterprise Email Marketing Jaipur',
      price: '₹48,000',
      period: '/month',
      description: 'Advanced email marketing for large Jaipur enterprises and exporters',
      features: [
        'Unlimited Subscribers',
        'Advanced Automation Workflows',
        'Custom Integrations',
        'Advanced Analytics & Reporting',
        'Dedicated Account Manager',
        'Export Market Campaigns',
        'Multi-language Support'
      ]
    }
  ];

  const stats = [
    {
      metric: '950+',
      description: 'Email Campaigns Managed',
      detail: 'For Jaipur businesses'
    },
    {
      metric: '42%',
      description: 'Average Open Rate',
      detail: 'Above industry standard'
    },
    {
      metric: '₹15Cr+',
      description: 'Revenue Generated',
      detail: 'Through email campaigns'
    },
    {
      metric: '78%',
      description: 'Customer Retention Rate',
      detail: 'With email marketing'
    }
  ];

  const achievements = [
    'Top Email Marketing Agency in Jaipur',
    'Tourism Industry Email Leaders',
    'Heritage Business Email Experts',
    'Handicraft Industry Email Specialists',
    'Jewelry Business Email Champions',
    'Pink City Email Marketing Pioneers'
  ];

  return (
    <div className="min-h-screen bg-slate-900 relative">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        <div className="container mx-auto px-6 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            {/* Main Content */}
            <div className="lg:col-span-3">
              {/* Hero Section */}
              <section className="pt-24 pb-16">
                <div className="max-w-4xl">
                  <div className="inline-flex items-center space-x-2 bg-orange-500/20 border border-orange-500/30 rounded-full px-6 py-2 mb-8">
                    <Mail className="w-4 h-4 text-orange-400" />
                    <span className="text-orange-400 font-medium">Email Marketing Jaipur • Pink City Email Excellence</span>
                  </div>
                  
                  <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
                    #1 Email Marketing Company in
                    <span className="bg-gradient-to-r from-orange-400 to-orange-600 bg-clip-text text-transparent"> Jaipur</span>
                  </h1>
                  
                  <p className="text-xl text-slate-300 mb-8">
                    Professional email marketing services in Jaipur delivering comprehensive email campaign management and email automation solutions. Our Jaipur email marketing agency provides complete email marketing services including newsletter marketing, email automation, drip campaigns, email template design, and email list building. Expert email marketing solutions with proven customer engagement, retention, and revenue generation through strategic email campaigns and email marketing automation for 950+ Jaipur businesses across tourism, handicraft, jewelry, and heritage sectors.
                  </p>
                  
                  <div className="flex flex-col sm:flex-row gap-4 mb-8">
                    <Button asChild size="lg" className="bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white text-lg px-8 py-4">
                      <Link to="/quote">Get Free Jaipur Email Audit</Link>
                    </Button>
                    <Button asChild size="lg" variant="outline" className="border-orange-500 text-orange-400 hover:bg-orange-500 hover:text-white text-lg px-8 py-4">
                      <Link to="/contact">Call Email Experts: +91-8708577598</Link>
                    </Button>
                  </div>

                  {/* Achievements */}
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                    {achievements.map((achievement, index) => (
                      <div key={index} className="flex items-center justify-center space-x-2 bg-slate-800/50 rounded-lg p-3">
                        <CheckCircle className="w-4 h-4 text-orange-400 flex-shrink-0" />
                        <span className="text-slate-300">{achievement}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </section>

              {/* Stats Section */}
              <section className="mb-16 bg-slate-800/50 rounded-2xl p-8">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Jaipur Email Marketing
                    <span className="bg-gradient-to-r from-orange-400 to-orange-600 bg-clip-text text-transparent"> Success Metrics</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Measurable email marketing results from Jaipur businesses across tourism, handicraft, jewelry, and heritage sectors.
                  </p>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
                  {stats.map((stat, index) => (
                    <div key={index} className="text-center">
                      <div className="text-4xl font-bold text-orange-400 mb-2">{stat.metric}</div>
                      <div className="text-white font-semibold mb-1">{stat.description}</div>
                      <div className="text-slate-400">{stat.detail}</div>
                    </div>
                  ))}
                </div>
              </section>

              {/* Email Types Section */}
              <section className="mb-16">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Email Campaigns We
                    <span className="bg-gradient-to-r from-orange-400 to-orange-600 bg-clip-text text-transparent"> Create</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Specialized email marketing campaigns designed for Jaipur's unique tourism, handicraft, jewelry, and heritage business landscape.
                  </p>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  {emailTypes.map((email, index) => (
                    <Card key={index} className="bg-slate-900/80 border-orange-500/20 hover:border-orange-500/40 transition-all duration-300">
                      <CardContent className="p-8">
                        <div className="w-12 h-12 bg-gradient-to-r from-orange-500 to-orange-600 rounded-lg flex items-center justify-center mb-6">
                          <email.icon className="w-6 h-6 text-white" />
                        </div>
                        <h3 className="text-xl font-bold text-white mb-4">{email.type}</h3>
                        <p className="text-slate-300 mb-6">{email.description}</p>
                        <ul className="space-y-2">
                          {email.features.map((feature, idx) => (
                            <li key={idx} className="flex items-center text-slate-400 text-sm">
                              <CheckCircle className="w-3 h-3 text-orange-400 mr-2 flex-shrink-0" />
                              {feature}
                            </li>
                          ))}
                        </ul>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>

              {/* Pricing Section */}
              <section className="mb-16">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Jaipur Email Marketing
                    <span className="bg-gradient-to-r from-orange-400 to-orange-600 bg-clip-text text-transparent"> Pricing</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Competitive email marketing pricing designed for Jaipur's tourism, handicraft, and heritage business environment.
                  </p>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                  {emailPackages.map((pkg, index) => (
                    <Card key={index} className={`bg-slate-900/80 border-orange-500/20 hover:border-orange-500/40 transition-all duration-300 relative ${pkg.popular ? 'ring-2 ring-orange-500' : ''}`}>
                      {pkg.popular && (
                        <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                          <span className="bg-orange-500 text-white px-4 py-2 rounded-full text-sm font-semibold">Most Popular</span>
                        </div>
                      )}
                      <CardContent className="p-8">
                        <h3 className="text-2xl font-bold text-white mb-2">{pkg.name}</h3>
                        <p className="text-slate-300 mb-6">{pkg.description}</p>
                        <div className="mb-6">
                          <span className="text-4xl font-bold text-orange-400">{pkg.price}</span>
                          <span className="text-slate-400">{pkg.period}</span>
                        </div>
                        <ul className="space-y-3 mb-8">
                          {pkg.features.map((feature, idx) => (
                            <li key={idx} className="flex items-center text-slate-300">
                              <CheckCircle className="w-4 h-4 text-orange-400 mr-3 flex-shrink-0" />
                              {feature}
                            </li>
                          ))}
                        </ul>
                        <Button asChild className={`w-full ${pkg.popular ? 'bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700' : 'bg-slate-700 hover:bg-slate-600'} text-white`}>
                          <Link to="/quote">Get Started</Link>
                        </Button>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>

              {/* CTA Section */}
              <section className="bg-gradient-to-r from-orange-600 to-orange-500 rounded-2xl p-8 text-center">
                <h2 className="text-3xl md:text-5xl font-bold mb-6">Ready to Dominate Jaipur with Email Marketing?</h2>
                <p className="text-xl mb-8">
                  Join 950+ Jaipur businesses that trust GOD Digital Marketing for email marketing success. Proven strategies that deliver 42% open rates and ₹15Cr+ revenue generation.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button asChild size="lg" className="bg-white text-orange-600 hover:bg-slate-100 text-lg px-8 py-4">
                    <Link to="/quote">Get Free Email Audit</Link>
                  </Button>
                  <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-orange-600 text-lg px-8 py-4">
                    <Link to="/contact">Call Email Experts: +91-8708577598</Link>
                  </Button>
                </div>
              </section>
            </div>

            {/* Sidebar */}
            <div className="lg:col-span-1">
              <Sidebar currentService="email" currentLocation="jaipur" />
            </div>
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
};

export default EmailMarketingJaipur;
