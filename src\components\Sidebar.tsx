import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { Search, Target, Share2, FileText, Mail, Code, MapPin, Building, Star, ArrowRight } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';

interface SidebarProps {
  currentService?: string;
  currentLocation?: string;
}

const Sidebar: React.FC<SidebarProps> = ({ currentService, currentLocation }) => {
  const location = useLocation();

  const services = [
    { name: 'SEO Services', path: '/services/seo', icon: Search, color: 'blue' },
    { name: 'Google Ads', path: '/services/ppc', icon: Target, color: 'green' },
    { name: 'Social Media Marketing', path: '/services/social-media', icon: Share2, color: 'pink' },
    { name: 'Content Marketing', path: '/services/content', icon: FileText, color: 'indigo' },
    { name: 'Email Marketing', path: '/services/email', icon: Mail, color: 'cyan' },
    { name: 'Web Development', path: '/services/web-development', icon: Code, color: 'emerald' },
  ];

  const locations = [
    { name: 'Delhi', path: '/services/seo/delhi', description: 'National Capital' },
    { name: 'Mumbai', path: '/services/seo/mumbai', description: 'Financial Capital' },
    { name: 'Bangalore', path: '/services/seo/bangalore', description: 'Silicon Valley of India' },
    { name: 'Chennai', path: '/services/seo/chennai', description: 'Detroit of India' },
    { name: 'Hyderabad', path: '/services/seo/hyderabad', description: 'Cyberabad' },
    { name: 'Kolkata', path: '/services/seo/kolkata', description: 'Cultural Capital' },
    { name: 'Pune', path: '/services/seo/pune', description: 'Educational Hub' },
    { name: 'Ahmedabad', path: '/services/seo/ahmedabad', description: 'Commercial Capital' },
    { name: 'Jaipur', path: '/services/seo/jaipur', description: 'Pink City' },
    { name: 'Lucknow', path: '/services/seo/lucknow', description: 'City of Nawabs' },
    { name: 'Surat', path: '/services/seo/surat', description: 'Diamond City' },
    { name: 'Indore', path: '/services/seo/indore', description: 'Commercial Hub' },
    { name: 'Bhopal', path: '/services/seo/bhopal', description: 'City of Lakes' },
    { name: 'Chandigarh', path: '/services/seo/chandigarh', description: 'Planned City' },
  ];

  const getServiceLocationCombinations = () => {
    if (currentService && currentLocation) {
      return services
        .filter(service => service.path !== `/services/${currentService}`)
        .map(service => ({
          name: `${service.name} ${currentLocation.charAt(0).toUpperCase() + currentLocation.slice(1)}`,
          path: `${service.path}/${currentLocation}`,
          icon: service.icon,
          color: service.color
        }));
    }
    
    if (currentService) {
      return locations.map(loc => ({
        name: `${currentService.toUpperCase()} Services ${loc.name}`,
        path: `/services/${currentService}/${loc.name.toLowerCase()}`,
        icon: MapPin,
        color: 'amber'
      }));
    }

    if (currentLocation) {
      return services.map(service => ({
        name: `${service.name} ${currentLocation.charAt(0).toUpperCase() + currentLocation.slice(1)}`,
        path: `${service.path}/${currentLocation}`,
        icon: service.icon,
        color: service.color
      }));
    }

    return [];
  };

  const relatedServices = getServiceLocationCombinations();

  const topServiceLocationCombinations = [
    { name: 'SEO Services Delhi', path: '/services/seo/delhi', icon: Search, color: 'blue' },
    { name: 'Google Ads Mumbai', path: '/services/ppc/mumbai', icon: Target, color: 'green' },
    { name: 'Social Media Bangalore', path: '/services/social-media/bangalore', icon: Share2, color: 'pink' },
    { name: 'Content Marketing Mumbai', path: '/services/content/mumbai', icon: FileText, color: 'indigo' },
    { name: 'Email Marketing Delhi', path: '/services/email/delhi', icon: Mail, color: 'cyan' },
    { name: 'Web Development Delhi', path: '/services/web-development/delhi', icon: Code, color: 'emerald' },
  ];

  const industries = [
    { name: 'Real Estate', path: '/industries/real-estate', icon: Building },
    { name: 'Healthcare', path: '/industries/healthcare', icon: Building },
    { name: 'Education', path: '/industries/education', icon: Building },
    { name: 'E-commerce', path: '/industries/ecommerce', icon: Building },
    { name: 'Manufacturing', path: '/industries/manufacturing', icon: Building },
    { name: 'Financial Services', path: '/industries/financial-services', icon: Building },
  ];

  const getColorClasses = (color: string) => {
    const colorMap = {
      blue: 'text-blue-400 border-blue-500/20 hover:border-blue-500/40',
      green: 'text-green-400 border-green-500/20 hover:border-green-500/40',
      pink: 'text-pink-400 border-pink-500/20 hover:border-pink-500/40',
      indigo: 'text-indigo-400 border-indigo-500/20 hover:border-indigo-500/40',
      cyan: 'text-cyan-400 border-cyan-500/20 hover:border-cyan-500/40',
      emerald: 'text-emerald-400 border-emerald-500/20 hover:border-emerald-500/40',
      amber: 'text-amber-400 border-amber-500/20 hover:border-amber-500/40',
    };
    return colorMap[color as keyof typeof colorMap] || 'text-slate-400 border-slate-500/20 hover:border-slate-500/40';
  };

  return (
    <div className="space-y-6">
      {/* Related Services Section */}
      {relatedServices.length > 0 && (
        <Card className="bg-slate-900/80 border-amber-500/20">
          <CardContent className="p-6">
            <h3 className="text-white font-semibold mb-4 flex items-center">
              <Star className="w-5 h-5 text-amber-400 mr-2" />
              Related Services
            </h3>
            <div className="space-y-3">
              {relatedServices.slice(0, 6).map((service, index) => (
                <Link
                  key={index}
                  to={service.path}
                  className={`flex items-center justify-between p-3 rounded-lg border transition-all duration-300 ${getColorClasses(service.color)} hover:bg-slate-800/50`}
                >
                  <div className="flex items-center space-x-3">
                    <service.icon className={`w-4 h-4 ${getColorClasses(service.color).split(' ')[0]}`} />
                    <span className="text-white text-sm font-medium">{service.name}</span>
                  </div>
                  <ArrowRight className="w-4 h-4 text-slate-400" />
                </Link>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Top Service-Location Combinations */}
      <Card className="bg-slate-900/80 border-amber-500/20">
        <CardContent className="p-6">
          <h3 className="text-white font-semibold mb-4 flex items-center">
            <MapPin className="w-5 h-5 text-amber-400 mr-2" />
            Popular Service-Location Combinations
          </h3>
          <div className="space-y-3">
            {topServiceLocationCombinations.map((combo, index) => (
              <Link
                key={index}
                to={combo.path}
                className={`flex items-center justify-between p-3 rounded-lg border transition-all duration-300 ${getColorClasses(combo.color)} hover:bg-slate-800/50`}
              >
                <div className="flex items-center space-x-3">
                  <combo.icon className={`w-4 h-4 ${getColorClasses(combo.color).split(' ')[0]}`} />
                  <span className="text-white text-sm font-medium">{combo.name}</span>
                </div>
                <ArrowRight className="w-4 h-4 text-slate-400" />
              </Link>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* All Services */}
      <Card className="bg-slate-900/80 border-amber-500/20">
        <CardContent className="p-6">
          <h3 className="text-white font-semibold mb-4 flex items-center">
            <Search className="w-5 h-5 text-amber-400 mr-2" />
            All Services
          </h3>
          <div className="space-y-2">
            {services.map((service, index) => (
              <Link
                key={index}
                to={service.path}
                className={`flex items-center space-x-3 p-2 rounded-lg transition-all duration-300 ${getColorClasses(service.color)} hover:bg-slate-800/50`}
              >
                <service.icon className={`w-4 h-4 ${getColorClasses(service.color).split(' ')[0]}`} />
                <span className="text-white text-sm">{service.name}</span>
              </Link>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* All Locations */}
      <Card className="bg-slate-900/80 border-amber-500/20">
        <CardContent className="p-6">
          <h3 className="text-white font-semibold mb-4 flex items-center">
            <MapPin className="w-5 h-5 text-amber-400 mr-2" />
            All Locations
          </h3>
          <div className="space-y-2">
            {locations.map((location, index) => (
              <Link
                key={index}
                to={location.path}
                className="flex items-center justify-between p-2 rounded-lg text-slate-300 hover:text-amber-400 hover:bg-slate-800/50 transition-all duration-300"
              >
                <div>
                  <div className="text-white text-sm font-medium">{location.name}</div>
                  <div className="text-xs text-slate-400">{location.description}</div>
                </div>
                <ArrowRight className="w-4 h-4 text-slate-400" />
              </Link>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Industries */}
      <Card className="bg-slate-900/80 border-amber-500/20">
        <CardContent className="p-6">
          <h3 className="text-white font-semibold mb-4 flex items-center">
            <Building className="w-5 h-5 text-amber-400 mr-2" />
            Industries We Serve
          </h3>
          <div className="space-y-2">
            {industries.map((industry, index) => (
              <Link
                key={index}
                to={industry.path}
                className="flex items-center space-x-3 p-2 rounded-lg text-slate-300 hover:text-amber-400 hover:bg-slate-800/50 transition-all duration-300"
              >
                <industry.icon className="w-4 h-4 text-amber-400" />
                <span className="text-sm">{industry.name}</span>
              </Link>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* CTA Section */}
      <Card className="bg-gradient-to-br from-amber-500/20 to-amber-600/20 border-amber-500/30">
        <CardContent className="p-6 text-center">
          <h3 className="text-white font-semibold mb-2">Ready to Get Started?</h3>
          <p className="text-slate-300 text-sm mb-4">Get a free consultation and custom strategy for your business.</p>
          <Button asChild className="w-full bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700 text-white">
            <Link to="/quote">Get Free Quote</Link>
          </Button>
        </CardContent>
      </Card>
    </div>
  );
};

export default Sidebar;
