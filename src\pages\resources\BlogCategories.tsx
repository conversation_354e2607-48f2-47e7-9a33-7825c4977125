import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { BookOpen, Search, TrendingUp, Users, CheckCircle, Building, Star, Crown, Target, Zap, ArrowRight } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';
import Sidebar from '@/components/Sidebar';

const BlogCategories = () => {
  const stats = [
    {
      metric: '500+',
      description: 'Published Articles',
      detail: 'Comprehensive digital marketing content'
    },
    {
      metric: '50,000+',
      description: 'Monthly Readers',
      detail: 'Industry professionals and businesses'
    },
    {
      metric: '25+',
      description: 'Expert Contributors',
      detail: 'Industry specialists and thought leaders'
    },
    {
      metric: '95%',
      description: 'Reader Satisfaction',
      detail: 'Valuable and actionable content'
    }
  ];

  const blogCategories = [
    {
      category: 'SEO & Search Marketing',
      description: 'Latest SEO strategies, algorithm updates, and search engine optimization techniques',
      icon: Search,
      articleCount: '120+',
      topics: ['On-Page SEO', 'Technical SEO', 'Local SEO', 'E-commerce SEO', 'SEO Tools', 'Algorithm Updates'],
      featured: ['Complete SEO Guide 2025', 'Google Algorithm Updates', 'Local SEO Strategies', 'E-commerce SEO Best Practices']
    },
    {
      category: 'PPC & Paid Advertising',
      description: 'Google Ads, Facebook Ads, and paid advertising strategies for maximum ROI',
      icon: Target,
      articleCount: '85+',
      topics: ['Google Ads', 'Facebook Ads', 'LinkedIn Ads', 'PPC Strategy', 'Ad Optimization', 'ROAS Improvement'],
      featured: ['Google Ads Mastery Guide', 'Facebook Ads for E-commerce', 'PPC Budget Optimization', 'Ad Copy Best Practices']
    },
    {
      category: 'Social Media Marketing',
      description: 'Social media strategies, content creation, and platform-specific marketing tactics',
      icon: Users,
      articleCount: '95+',
      topics: ['Content Strategy', 'Instagram Marketing', 'LinkedIn B2B', 'Social Commerce', 'Influencer Marketing', 'Social Analytics'],
      featured: ['Social Media Strategy 2025', 'Instagram Growth Hacks', 'LinkedIn B2B Marketing', 'Social Commerce Trends']
    },
    {
      category: 'Content Marketing',
      description: 'Content creation strategies, storytelling, and content optimization techniques',
      icon: BookOpen,
      articleCount: '75+',
      topics: ['Content Strategy', 'Blog Writing', 'Video Marketing', 'Content SEO', 'Content Distribution', 'Content Analytics'],
      featured: ['Content Marketing Playbook', 'Video Content Strategy', 'Blog SEO Optimization', 'Content Distribution Channels']
    },
    {
      category: 'E-commerce Marketing',
      description: 'Online store optimization, conversion strategies, and e-commerce growth tactics',
      icon: Building,
      articleCount: '65+',
      topics: ['Conversion Optimization', 'Product SEO', 'Shopping Ads', 'Email Marketing', 'Customer Retention', 'Mobile Commerce'],
      featured: ['E-commerce SEO Guide', 'Conversion Rate Optimization', 'Product Page Optimization', 'E-commerce Email Marketing']
    },
    {
      category: 'Digital Marketing Trends',
      description: 'Latest industry trends, emerging technologies, and future of digital marketing',
      icon: TrendingUp,
      articleCount: '55+',
      topics: ['AI in Marketing', 'Voice Search', 'Marketing Automation', 'Personalization', 'Privacy Changes', 'Future Trends'],
      featured: ['Digital Marketing Trends 2025', 'AI Marketing Revolution', 'Voice Search Optimization', 'Privacy-First Marketing']
    }
  ];

  const featuredArticles = [
    {
      title: 'Complete Digital Marketing Guide 2025',
      category: 'Digital Marketing Strategy',
      readTime: '15 min read',
      description: 'Comprehensive guide covering all aspects of digital marketing for businesses in 2025',
      tags: ['Strategy', 'SEO', 'PPC', 'Social Media']
    },
    {
      title: 'AI-Powered SEO: The Future of Search Optimization',
      category: 'SEO & AI',
      readTime: '12 min read',
      description: 'How artificial intelligence is revolutionizing SEO strategies and search optimization',
      tags: ['AI', 'SEO', 'Technology', 'Future']
    },
    {
      title: 'E-commerce Growth Strategies That Actually Work',
      category: 'E-commerce Marketing',
      readTime: '10 min read',
      description: 'Proven strategies for scaling e-commerce businesses and increasing online sales',
      tags: ['E-commerce', 'Growth', 'Conversion', 'Sales']
    },
    {
      title: 'Local SEO Mastery: Dominate Local Search Results',
      category: 'Local SEO',
      readTime: '8 min read',
      description: 'Complete guide to local SEO optimization for businesses targeting local customers',
      tags: ['Local SEO', 'GMB', 'Local Marketing', 'Small Business']
    }
  ];

  const benefits = [
    {
      benefit: 'Expert Insights',
      description: 'Content written by industry experts with 7+ years of international experience',
      icon: Crown
    },
    {
      benefit: 'Actionable Strategies',
      description: 'Practical tips and strategies you can implement immediately for results',
      icon: Zap
    },
    {
      benefit: 'Latest Trends',
      description: 'Stay updated with the latest digital marketing trends and algorithm changes',
      icon: TrendingUp
    },
    {
      benefit: 'Case Studies',
      description: 'Real-world examples and case studies from successful campaigns',
      icon: Star
    }
  ];

  return (
    <div className="min-h-screen bg-slate-900 relative">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        <div className="container mx-auto px-6 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            <div className="lg:col-span-3">
              <section className="pt-24 pb-16">
                <div className="max-w-4xl">
                  <div className="inline-flex items-center space-x-2 bg-purple-500/20 border border-purple-500/30 rounded-full px-6 py-2 mb-8">
                    <BookOpen className="w-4 h-4 text-purple-400" />
                    <span className="text-purple-400 font-medium">Digital Marketing Blog • Expert Insights & Strategies</span>
                  </div>
                  
                  <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
                    Digital Marketing Blog by
                    <span className="bg-gradient-to-r from-purple-400 to-purple-600 bg-clip-text text-transparent"> GOD Digital Marketing</span>
                  </h1>
                  
                  <p className="text-xl text-slate-300 mb-8">
                    Comprehensive digital marketing blog featuring expert insights on SEO, PPC, social media marketing, content marketing, and e-commerce strategies. Our blog covers latest digital marketing trends, algorithm updates, case studies, and actionable strategies from industry experts with 7+ years international experience. Read 500+ articles across SEO optimization, Google Ads, Facebook advertising, content creation, and digital marketing best practices. Stay updated with the latest marketing trends and grow your business with proven strategies.
                  </p>
                  
                  <div className="flex flex-col sm:flex-row gap-4 mb-8">
                    <Button asChild size="lg" className="bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white text-lg px-8 py-4">
                      <Link to="#categories">Explore Blog Categories</Link>
                    </Button>
                    <Button asChild size="lg" variant="outline" className="border-purple-500 text-purple-400 hover:bg-purple-500 hover:text-white text-lg px-8 py-4">
                      <Link to="/contact">Get Marketing Advice: +91-8708577598</Link>
                    </Button>
                  </div>
                </div>
              </section>

              <section className="mb-16 bg-slate-800/50 rounded-2xl p-8">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Blog
                    <span className="bg-gradient-to-r from-purple-400 to-purple-600 bg-clip-text text-transparent"> Statistics</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Trusted source of digital marketing knowledge for professionals and businesses worldwide.
                  </p>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
                  {stats.map((stat, index) => (
                    <div key={index} className="text-center">
                      <div className="text-4xl font-bold text-purple-400 mb-2">{stat.metric}</div>
                      <div className="text-white font-semibold mb-1">{stat.description}</div>
                      <div className="text-slate-400">{stat.detail}</div>
                    </div>
                  ))}
                </div>
              </section>

              {/* Featured Articles */}
              <section className="mb-16">
                <h3 className="text-2xl font-bold text-white mb-8 text-center">
                  Featured Articles
                </h3>
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {featuredArticles.map((article, index) => (
                    <Card key={index} className="bg-slate-900/60 border-purple-500/20 hover:border-purple-500/40 transition-all duration-300">
                      <CardContent className="p-6">
                        <div className="flex items-center justify-between mb-3">
                          <span className="text-purple-400 text-sm font-medium">{article.category}</span>
                          <span className="text-slate-400 text-sm">{article.readTime}</span>
                        </div>
                        <h4 className="text-white font-semibold mb-3 hover:text-purple-400 cursor-pointer">{article.title}</h4>
                        <p className="text-slate-300 text-sm mb-4">{article.description}</p>
                        <div className="flex flex-wrap gap-2">
                          {article.tags.map((tag, idx) => (
                            <span key={idx} className="bg-slate-800 text-purple-400 text-xs px-2 py-1 rounded">
                              {tag}
                            </span>
                          ))}
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>

              <section id="categories" className="mb-16">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Blog
                    <span className="bg-gradient-to-r from-purple-400 to-purple-600 bg-clip-text text-transparent"> Categories</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Explore our comprehensive collection of digital marketing articles organized by category.
                  </p>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  {blogCategories.map((category, index) => (
                    <Card key={index} className="bg-slate-900/80 border-purple-500/20 hover:border-purple-500/40 transition-all duration-300">
                      <CardContent className="p-8">
                        <div className="flex items-center justify-between mb-6">
                          <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg flex items-center justify-center">
                            <category.icon className="w-6 h-6 text-white" />
                          </div>
                          <span className="text-purple-400 font-semibold">{category.articleCount} articles</span>
                        </div>
                        <h3 className="text-xl font-bold text-white mb-4">{category.category}</h3>
                        <p className="text-slate-300 mb-6">{category.description}</p>
                        
                        <div className="mb-6">
                          <h4 className="text-white font-medium mb-3">Topics Covered:</h4>
                          <div className="flex flex-wrap gap-2">
                            {category.topics.map((topic, idx) => (
                              <span key={idx} className="bg-slate-800 text-purple-400 text-xs px-2 py-1 rounded">
                                {topic}
                              </span>
                            ))}
                          </div>
                        </div>

                        <div>
                          <h4 className="text-white font-medium mb-3">Featured Articles:</h4>
                          <ul className="space-y-2">
                            {category.featured.map((article, idx) => (
                              <li key={idx} className="flex items-center text-slate-400 text-sm">
                                <CheckCircle className="w-3 h-3 text-purple-400 mr-2 flex-shrink-0" />
                                {article}
                              </li>
                            ))}
                          </ul>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>

              {/* Benefits */}
              <section className="mb-16">
                <h3 className="text-2xl font-bold text-white mb-8 text-center">
                  Why Read Our Blog?
                </h3>
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {benefits.map((benefit, index) => (
                    <Card key={index} className="bg-slate-900/60 border-purple-500/20 hover:border-purple-500/40 transition-all duration-300">
                      <CardContent className="p-6">
                        <div className="flex items-center mb-4">
                          <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg flex items-center justify-center mr-4">
                            <benefit.icon className="w-5 h-5 text-white" />
                          </div>
                          <h4 className="text-purple-400 font-semibold">{benefit.benefit}</h4>
                        </div>
                        <p className="text-slate-300 text-sm">{benefit.description}</p>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>

              {/* Internal Linking Section */}
              <section className="mb-16 bg-slate-800/30 rounded-2xl p-8">
                <h3 className="text-2xl font-bold text-white mb-6 text-center">
                  Digital Marketing Services
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  <Link to="/services/seo" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-purple-400 font-semibold mb-2 group-hover:text-purple-300">SEO Services</h4>
                    <p className="text-slate-400 text-sm">Professional SEO optimization and strategy implementation</p>
                  </Link>
                  <Link to="/services/ppc" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-purple-400 font-semibold mb-2 group-hover:text-purple-300">PPC Advertising</h4>
                    <p className="text-slate-400 text-sm">Google Ads and paid advertising campaign management</p>
                  </Link>
                  <Link to="/services/social-media" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-purple-400 font-semibold mb-2 group-hover:text-purple-300">Social Media Marketing</h4>
                    <p className="text-slate-400 text-sm">Social media strategy and content creation</p>
                  </Link>
                  <Link to="/services/content" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-purple-400 font-semibold mb-2 group-hover:text-purple-300">Content Marketing</h4>
                    <p className="text-slate-400 text-sm">Content creation and marketing strategy</p>
                  </Link>
                  <Link to="/services/ecommerce-seo" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-purple-400 font-semibold mb-2 group-hover:text-purple-300">E-commerce Marketing</h4>
                    <p className="text-slate-400 text-sm">Online store optimization and e-commerce growth</p>
                  </Link>
                  <Link to="/contact" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-purple-400 font-semibold mb-2 group-hover:text-purple-300">Marketing Consultation</h4>
                    <p className="text-slate-400 text-sm">Expert marketing advice and strategy consultation</p>
                  </Link>
                </div>
              </section>

              <section className="bg-gradient-to-r from-purple-600 to-purple-500 rounded-2xl p-8 text-center">
                <h2 className="text-3xl md:text-5xl font-bold mb-6">Ready to Implement These Strategies?</h2>
                <p className="text-xl mb-8">
                  Our blog provides the knowledge, but our expert team can implement these strategies for your business with proven results.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button asChild size="lg" className="bg-white text-purple-600 hover:bg-slate-100 text-lg px-8 py-4">
                    <Link to="/quote">Get Free Marketing Audit</Link>
                  </Button>
                  <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-purple-600 text-lg px-8 py-4">
                    <Link to="/contact">Call Marketing Experts: +91-8708577598</Link>
                  </Button>
                </div>
              </section>
            </div>

            <div className="lg:col-span-1">
              <Sidebar currentService="blog" />
            </div>
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
};

export default BlogCategories;
