import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { ArrowRight, Search, TrendingUp, Users, CheckCircle, Building, Star, Factory, Target, Zap } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';

const SeoServicesSonipat = () => {
  const seoServices = [
    'Local SEO Services Sonipat',
    'Industrial SEO Sonipat',
    'Manufacturing SEO Sonipat',
    'E-commerce SEO Sonipat',
    'Technical SEO Audit Sonipat',
    'Small Business SEO Sonipat',
    'Automobile Industry SEO Sonipat',
    'Textile Industry SEO Sonipat',
    'B2B SEO Services Sonipat',
    'Google My Business Optimization Sonipat',
    'Local Citation Building Sonipat',
    'Competitor SEO Analysis Sonipat',
    'Keyword Research Sonipat',
    'Content SEO Strategy Sonipat',
    'Link Building Sonipat',
    'Mobile SEO Optimization Sonipat',
    'Page Speed Optimization Sonipat',
    'Schema Markup Implementation Sonipat',
    'Voice Search SEO Sonipat',
    'Video SEO Services Sonipat'
  ];

  const sonipatAreas = [
    'Sector 14 SEO Services',
    'Model Town SEO Sonipat',
    'Rai SEO Services',
    'Gohana SEO Company',
    'Kharkhoda SEO Services',
    'Mundlana SEO Agency',
    'Kathura SEO Services',
    'Gannaur SEO Company',
    'Industrial Area SEO Sonipat',
    'Sonipat City SEO Services',
    'Delhi Road SEO Sonipat',
    'GT Road SEO Services'
  ];

  const industries = [
    'Automobile Industry SEO Sonipat',
    'Textile Manufacturing SEO Sonipat',
    'Agriculture & Farming SEO Sonipat',
    'Food Processing SEO Sonipat',
    'Real Estate SEO Sonipat',
    'Healthcare SEO Sonipat',
    'Education SEO Sonipat',
    'Retail & E-commerce SEO Sonipat',
    'Construction SEO Sonipat',
    'Transportation SEO Sonipat',
    'Financial Services SEO Sonipat',
    'Government Sector SEO Sonipat'
  ];

  const seoPackages = [
    {
      name: 'Local SEO Sonipat Starter',
      price: '₹15,000',
      period: '/month',
      description: 'Perfect for small Sonipat businesses targeting local customers',
      features: [
        'Google My Business Optimization',
        'Local Keyword Research (30 keywords)',
        'On-Page SEO (5 pages)',
        'Local Citation Building (15 citations)',
        'Monthly Local SEO Report',
        'Sonipat Area Targeting'
      ]
    },
    {
      name: 'Sonipat SEO Professional',
      price: '₹28,000',
      period: '/month',
      description: 'Comprehensive SEO for growing Sonipat businesses',
      features: [
        'Everything in Starter',
        'Advanced Keyword Research (100 keywords)',
        'Technical SEO Audit & Fixes',
        'Content Marketing Strategy',
        'Link Building Campaign',
        'Competitor Analysis',
        'Industrial SEO Focus'
      ],
      popular: true
    },
    {
      name: 'Enterprise SEO Sonipat',
      price: '₹55,000',
      period: '/month',
      description: 'Advanced SEO for large Sonipat manufacturers',
      features: [
        'Everything in Professional',
        'Enterprise-level SEO Strategy',
        'Advanced Technical SEO',
        'B2B SEO Optimization',
        'Custom SEO Dashboard',
        'Dedicated SEO Manager',
        'Priority Support'
      ]
    }
  ];

  const seoProcess = [
    {
      step: '1',
      title: 'Sonipat Market SEO Audit',
      description: 'Comprehensive analysis of your website\'s SEO performance in Sonipat industrial market',
      icon: Search
    },
    {
      step: '2',
      title: 'Industrial Keyword Strategy',
      description: 'Strategic keyword research targeting Sonipat customers with industrial and local search intent',
      icon: Target
    },
    {
      step: '3',
      title: 'Technical SEO Implementation',
      description: 'Advanced technical SEO optimization for manufacturing and industrial websites',
      icon: Zap
    },
    {
      step: '4',
      title: 'B2B Content & Link Building',
      description: 'Industry-specific content creation and B2B link building for Sonipat market dominance',
      icon: TrendingUp
    }
  ];

  const stats = [
    {
      metric: '150+',
      description: 'Sonipat Websites Optimized',
      detail: 'Across all industries'
    },
    {
      metric: '480%',
      description: 'Average Organic Traffic Increase',
      detail: 'For Sonipat businesses'
    },
    {
      metric: '₹8Cr+',
      description: 'Revenue Generated Through SEO',
      detail: 'For Sonipat clients'
    },
    {
      metric: '88%',
      description: 'First Page Rankings Achieved',
      detail: 'For target keywords'
    }
  ];

  const achievements = [
    'Top SEO Company in Sonipat',
    'Industrial SEO Specialists',
    'Automobile Industry SEO Experts',
    'Textile Manufacturing SEO Leaders',
    'B2B SEO Solutions Provider',
    'Local Sonipat SEO Champions'
  ];

  return (
    <div className="min-h-screen bg-slate-900 relative">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        {/* Hero Section */}
        <section className="pt-32 pb-20">
          <div className="container mx-auto px-6">
            <div className="max-w-5xl mx-auto text-center">
              <div className="inline-flex items-center space-x-2 bg-orange-500/20 border border-orange-500/30 rounded-full px-6 py-2 mb-8">
                <Factory className="w-4 h-4 text-orange-400" />
                <span className="text-orange-400 font-medium">SEO Services Sonipat • Industrial Gateway SEO Excellence</span>
              </div>
              
              <h1 className="text-5xl md:text-7xl font-bold mb-6 leading-tight">
                #1 SEO Services Company in
                <span className="bg-gradient-to-r from-orange-400 to-orange-600 bg-clip-text text-transparent"> Sonipat</span>
              </h1>
              
              <p className="text-xl text-slate-300 mb-12 max-w-4xl mx-auto">
                Premier SEO services in Sonipat offering comprehensive search engine optimization solutions for industrial and manufacturing businesses. Serving 150+ Sonipat businesses across all sectors - from automobile manufacturers to textile industries in this industrial gateway. Expert SEO solutions with proven ₹8Cr+ revenue generation and 480% average traffic increase for Sonipat clients.
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
                <Button asChild size="lg" className="bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white text-lg px-8 py-4">
                  <Link to="/quote">Get Free Sonipat SEO Audit</Link>
                </Button>
                <Button asChild size="lg" variant="outline" className="border-orange-500 text-orange-400 hover:bg-orange-500 hover:text-white text-lg px-8 py-4">
                  <Link to="/contact">Call Sonipat SEO Experts: +91-8708577598</Link>
                </Button>
              </div>

              {/* Achievements */}
              <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                {achievements.map((achievement, index) => (
                  <div key={index} className="flex items-center justify-center space-x-2 bg-slate-800/50 rounded-lg p-3">
                    <CheckCircle className="w-4 h-4 text-orange-400 flex-shrink-0" />
                    <span className="text-slate-300">{achievement}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* Stats Section */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-6xl font-bold mb-6">
                Sonipat SEO Services
                <span className="bg-gradient-to-r from-orange-400 to-orange-600 bg-clip-text text-transparent"> Success Metrics</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-4xl mx-auto mb-12">
                Measurable SEO results from Sonipat businesses across all industries - from automobile manufacturers to textile industries in this industrial hub.
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-4 gap-8 mb-16">
              {stats.map((stat, index) => (
                <div key={index} className="text-center">
                  <div className="text-5xl font-bold text-orange-400 mb-2">{stat.metric}</div>
                  <div className="text-white font-semibold mb-1">{stat.description}</div>
                  <div className="text-slate-400">{stat.detail}</div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* SEO Process Section */}
        <section className="py-20">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-6xl font-bold mb-6">
                Our Sonipat SEO
                <span className="bg-gradient-to-r from-orange-400 to-orange-600 bg-clip-text text-transparent"> Process</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-4xl mx-auto">
                Proven 4-step SEO methodology specifically designed for Sonipat's industrial market dynamics and B2B search behavior.
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-4 gap-8 mb-20">
              {seoProcess.map((process, index) => (
                <Card key={index} className="bg-slate-900/80 border-orange-500/20 hover:border-orange-500/40 transition-all duration-300">
                  <CardContent className="p-8 text-center">
                    <div className="w-16 h-16 bg-gradient-to-r from-orange-500 to-orange-600 rounded-full flex items-center justify-center mx-auto mb-6">
                      <process.icon className="w-8 h-8 text-white" />
                    </div>
                    <div className="text-3xl font-bold text-orange-400 mb-4">Step {process.step}</div>
                    <h3 className="text-xl font-bold text-white mb-4">{process.title}</h3>
                    <p className="text-slate-300">{process.description}</p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* SEO Services Grid */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-6xl font-bold mb-6">
                Complete SEO Services
                <span className="bg-gradient-to-r from-orange-400 to-orange-600 bg-clip-text text-transparent"> Available in Sonipat</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-4xl mx-auto">
                Comprehensive range of SEO services covering every aspect of search engine optimization for Sonipat industrial businesses.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {seoServices.map((service, index) => (
                <Card key={index} className="bg-slate-900/80 border-orange-500/20 hover:border-orange-500/40 transition-all duration-300">
                  <CardContent className="p-6 text-center">
                    <Search className="w-8 h-8 text-orange-400 mx-auto mb-4" />
                    <h3 className="text-white font-semibold mb-2 text-sm">{service}</h3>
                    <Button asChild size="sm" variant="outline" className="border-orange-500 text-orange-400 hover:bg-orange-500 hover:text-white text-xs">
                      <Link to="/quote">Get Quote</Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Areas Section */}
        <section className="py-20">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-6xl font-bold mb-6">
                Sonipat Areas We
                <span className="bg-gradient-to-r from-orange-400 to-orange-600 bg-clip-text text-transparent"> Serve</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-4xl mx-auto">
                Local SEO expertise across all major Sonipat areas and industrial zones.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {sonipatAreas.map((area, index) => (
                <Card key={index} className="bg-slate-900/80 border-orange-500/20 hover:border-orange-500/40 transition-all duration-300">
                  <CardContent className="p-6 text-center">
                    <Building className="w-8 h-8 text-orange-400 mx-auto mb-4" />
                    <h3 className="text-white font-semibold mb-2 text-sm">{area}</h3>
                    <Button asChild size="sm" variant="outline" className="border-orange-500 text-orange-400 hover:bg-orange-500 hover:text-white text-xs">
                      <Link to="/contact">Contact Us</Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Pricing Section */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-6xl font-bold mb-6">
                Sonipat SEO Services
                <span className="bg-gradient-to-r from-orange-400 to-orange-600 bg-clip-text text-transparent"> Pricing</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-4xl mx-auto">
                Affordable SEO pricing packages designed for Sonipat businesses of all sizes.
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
              {seoPackages.map((pkg, index) => (
                <Card key={index} className={`bg-slate-900/80 border-orange-500/20 hover:border-orange-500/40 transition-all duration-300 relative ${pkg.popular ? 'ring-2 ring-orange-500' : ''}`}>
                  {pkg.popular && (
                    <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                      <span className="bg-orange-500 text-white px-4 py-2 rounded-full text-sm font-semibold">Most Popular</span>
                    </div>
                  )}
                  <CardContent className="p-8">
                    <h3 className="text-2xl font-bold text-white mb-2">{pkg.name}</h3>
                    <p className="text-slate-300 mb-6">{pkg.description}</p>
                    <div className="mb-6">
                      <span className="text-4xl font-bold text-orange-400">{pkg.price}</span>
                      <span className="text-slate-400">{pkg.period}</span>
                    </div>
                    <ul className="space-y-3 mb-8">
                      {pkg.features.map((feature, idx) => (
                        <li key={idx} className="flex items-center text-slate-300">
                          <CheckCircle className="w-4 h-4 text-orange-400 mr-3 flex-shrink-0" />
                          {feature}
                        </li>
                      ))}
                    </ul>
                    <Button asChild className={`w-full ${pkg.popular ? 'bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700' : 'bg-slate-700 hover:bg-slate-600'} text-white`}>
                      <Link to="/quote">Get Started</Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 bg-gradient-to-r from-orange-600 to-orange-500">
          <div className="container mx-auto px-6 text-center">
            <h2 className="text-4xl md:text-6xl font-bold mb-6">Ready to Dominate Sonipat Search Results?</h2>
            <p className="text-xl mb-8 max-w-4xl mx-auto">
              Join 150+ Sonipat businesses that trust GOD Digital Marketing for SEO success. Proven strategies that deliver 480% traffic increase and ₹8Cr+ revenue generation.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button asChild size="lg" className="bg-white text-orange-600 hover:bg-slate-100 text-lg px-8 py-4">
                <Link to="/quote">Get Free Sonipat SEO Audit</Link>
              </Button>
              <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-orange-600 text-lg px-8 py-4">
                <Link to="/contact">Call SEO Experts: +91-8708577598</Link>
              </Button>
            </div>
          </div>
        </section>
      </main>
      
      <Footer />
    </div>
  );
};

export default SeoServicesSonipat;
