import React from 'react';
import { Link } from 'react-router-dom';
import { ArrowRight, Building, TrendingUp, Users, CheckCircle, Target, Star } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';

const RealEstate = () => {
  const services = [
    'Real Estate SEO',
    'Property Lead Generation',
    'Google Ads for Real Estate',
    'Social Media Marketing',
    'Real Estate Website Development',
    'Content Marketing for Properties',
    'Local SEO for Real Estate',
    'Real Estate PPC Management'
  ];

  const propertyTypes = [
    'Residential Properties',
    'Commercial Real Estate',
    'Luxury Properties',
    'Rental Properties',
    'New Construction',
    'Property Investment',
    'Real Estate Development',
    'Property Management'
  ];

  const challenges = [
    {
      challenge: 'Lead Generation',
      solution: 'Advanced lead generation funnels that convert property seekers into qualified leads',
      icon: Target
    },
    {
      challenge: 'Local Competition',
      solution: 'Local SEO strategies that dominate area-specific property searches',
      icon: Building
    },
    {
      challenge: 'Trust Building',
      solution: 'Content marketing and social proof that establishes real estate authority',
      icon: Star
    }
  ];

  const stats = [
    {
      metric: '500%',
      description: 'Increase in Property Leads',
      detail: 'For real estate clients'
    },
    {
      metric: '₹2Cr+',
      description: 'Property Sales Generated',
      detail: 'Through digital marketing'
    },
    {
      metric: '300+',
      description: 'Real Estate Projects',
      detail: 'Successfully marketed'
    }
  ];

  return (
    <div className="min-h-screen bg-slate-900 relative">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        {/* Hero Section */}
        <section className="pt-32 pb-20">
          <div className="container mx-auto px-6">
            <div className="max-w-4xl mx-auto text-center">
              <div className="inline-flex items-center space-x-2 bg-amber-500/20 border border-amber-500/30 rounded-full px-6 py-2 mb-8">
                <Building className="w-4 h-4 text-amber-400" />
                <span className="text-amber-400 font-medium">Real Estate Digital Marketing • Property Lead Generation</span>
              </div>
              
              <h1 className="text-5xl md:text-6xl font-bold mb-6 leading-tight">
                Real Estate Digital Marketing That
                <span className="bg-gradient-to-r from-amber-400 to-amber-600 bg-clip-text text-transparent"> Generates Property Leads</span>
              </h1>
              
              <p className="text-xl text-slate-300 mb-12 max-w-3xl mx-auto">
                Specialized digital marketing for real estate professionals, developers, and agencies. Generate qualified property leads, dominate local search results, and build trust with potential buyers and sellers.
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button asChild size="lg" className="bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700 text-white text-lg px-8 py-4">
                  <Link to="/quote">Get Real Estate Marketing Strategy</Link>
                </Button>
                <Button asChild size="lg" variant="outline" className="border-amber-500 text-amber-400 hover:bg-amber-500 hover:text-white text-lg px-8 py-4">
                  <Link to="/case-studies">View Property Marketing Results</Link>
                </Button>
              </div>
            </div>
          </div>
        </section>

        {/* Stats Section */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Real Estate Marketing
                <span className="bg-gradient-to-r from-amber-400 to-amber-600 bg-clip-text text-transparent"> Success Stories</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto mb-12">
                Real results from real estate professionals across India - from individual agents to large developers.
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
              {stats.map((stat, index) => (
                <div key={index} className="text-center">
                  <div className="text-5xl font-bold text-amber-400 mb-2">{stat.metric}</div>
                  <div className="text-white font-semibold mb-1">{stat.description}</div>
                  <div className="text-slate-400">{stat.detail}</div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Challenges & Solutions */}
        <section className="py-20">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Real Estate Marketing
                <span className="bg-gradient-to-r from-amber-400 to-amber-600 bg-clip-text text-transparent"> Challenges We Solve</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto">
                Understanding the unique challenges of real estate marketing and providing targeted solutions.
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              {challenges.map((item, index) => (
                <Card key={index} className="bg-slate-900/80 border-amber-500/20 hover:border-amber-500/40 transition-all duration-300">
                  <CardContent className="p-8">
                    <div className="w-12 h-12 bg-gradient-to-r from-amber-500 to-amber-600 rounded-lg flex items-center justify-center mb-6">
                      <item.icon className="w-6 h-6 text-white" />
                    </div>
                    <h3 className="text-xl font-bold text-white mb-4">Challenge: {item.challenge}</h3>
                    <p className="text-slate-300">{item.solution}</p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Services Section */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Real Estate Digital Marketing
                <span className="bg-gradient-to-r from-amber-400 to-amber-600 bg-clip-text text-transparent"> Services</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto">
                Comprehensive digital marketing solutions designed specifically for real estate professionals.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {services.map((service, index) => (
                <Card key={index} className="bg-slate-900/80 border-amber-500/20 hover:border-amber-500/40 transition-all duration-300">
                  <CardContent className="p-6 text-center">
                    <Building className="w-8 h-8 text-amber-400 mx-auto mb-4" />
                    <h3 className="text-white font-semibold mb-2">{service}</h3>
                    <Button asChild size="sm" variant="outline" className="border-amber-500 text-amber-400 hover:bg-amber-500 hover:text-white">
                      <Link to="/quote">Learn More</Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Property Types Section */}
        <section className="py-20">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Property Types We
                <span className="bg-gradient-to-r from-amber-400 to-amber-600 bg-clip-text text-transparent"> Market</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto">
                Specialized marketing strategies for different types of real estate properties and services.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {propertyTypes.map((type, index) => (
                <Card key={index} className="bg-slate-900/80 border-amber-500/20 hover:border-amber-500/40 transition-all duration-300">
                  <CardContent className="p-6 text-center">
                    <Star className="w-8 h-8 text-amber-400 mx-auto mb-4" />
                    <h3 className="text-white font-semibold mb-2">{type}</h3>
                    <Button asChild size="sm" variant="outline" className="border-amber-500 text-amber-400 hover:bg-amber-500 hover:text-white">
                      <Link to="/quote">Get Strategy</Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Why Choose Us Section */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Why Real Estate Professionals Choose
                <span className="bg-gradient-to-r from-amber-400 to-amber-600 bg-clip-text text-transparent"> GOD Digital Marketing</span>
              </h2>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              <Card className="bg-slate-900/80 border-amber-500/20">
                <CardContent className="p-8">
                  <TrendingUp className="w-12 h-12 text-amber-400 mb-6" />
                  <h3 className="text-xl font-bold text-white mb-4">Real Estate Expertise</h3>
                  <p className="text-slate-300">Deep understanding of real estate market dynamics, buyer behavior, and property marketing strategies.</p>
                </CardContent>
              </Card>

              <Card className="bg-slate-900/80 border-amber-500/20">
                <CardContent className="p-8">
                  <Users className="w-12 h-12 text-amber-400 mb-6" />
                  <h3 className="text-xl font-bold text-white mb-4">Proven Lead Generation</h3>
                  <p className="text-slate-300">Specialized lead generation systems that convert property seekers into qualified buyers and sellers.</p>
                </CardContent>
              </Card>

              <Card className="bg-slate-900/80 border-amber-500/20">
                <CardContent className="p-8">
                  <CheckCircle className="w-12 h-12 text-amber-400 mb-6" />
                  <h3 className="text-xl font-bold text-white mb-4">Measurable ROI</h3>
                  <p className="text-slate-300">Track every lead, conversion, and property sale generated through our digital marketing campaigns.</p>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 bg-gradient-to-r from-amber-600 to-amber-500">
          <div className="container mx-auto px-6 text-center">
            <h2 className="text-4xl md:text-5xl font-bold mb-6">Ready to Generate More Property Leads?</h2>
            <p className="text-xl mb-8 max-w-3xl mx-auto">
              Join 300+ real estate professionals who trust GOD Digital Marketing to generate qualified leads and grow their property business.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button asChild size="lg" className="bg-white text-amber-600 hover:bg-slate-100 text-lg px-8 py-4">
                <Link to="/quote">Get Real Estate Marketing Strategy</Link>
              </Button>
              <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-amber-600 text-lg px-8 py-4">
                <Link to="/contact">Book Real Estate Consultation</Link>
              </Button>
            </div>
          </div>
        </section>
      </main>
      
      <Footer />
    </div>
  );
};

export default RealEstate;
