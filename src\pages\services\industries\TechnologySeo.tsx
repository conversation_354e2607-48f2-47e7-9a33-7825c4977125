import React from 'react';
import { Link } from 'react-router-dom';
import { Monitor, Search, TrendingUp, Users, CheckCircle, Building, Star, Crown, Target, Zap, ArrowRight } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';
import Sidebar from '@/components/Sidebar';

const TechnologySeo = () => {
  const stats = [
    {
      metric: '1,100+',
      description: 'Technology Companies Optimized',
      detail: 'Across all tech sectors'
    },
    {
      metric: '4,200%',
      description: 'Average Tech Lead Growth',
      detail: 'For technology clients'
    },
    {
      metric: '₹850Cr+',
      description: 'Technology Revenue Generated',
      detail: 'Through SEO campaigns'
    },
    {
      metric: '97%',
      description: 'Technology Keywords Ranking',
      detail: 'Top 10 positions achieved'
    }
  ];

  const achievements = [
    'Top Technology SEO Company in India',
    'Software SEO Specialists',
    'IT Company SEO Experts',
    'SaaS Platform SEO Leaders',
    'Tech Startup SEO Champions',
    'AI & ML SEO Pioneers'
  ];

  const technologySpecializations = [
    {
      type: 'Software Development & IT Services SEO',
      description: 'Comprehensive SEO for software companies, IT service providers, and development agencies',
      icon: Building,
      features: ['Software Company SEO', 'IT Service Provider SEO', 'Development Agency SEO', 'Custom Software SEO', 'Enterprise Software SEO', 'IT Consulting SEO']
    },
    {
      type: 'SaaS & Cloud Platform SEO',
      description: 'Advanced SEO for SaaS platforms, cloud services, and subscription-based technology solutions',
      icon: Star,
      features: ['SaaS Platform SEO', 'Cloud Service SEO', 'Subscription Software SEO', 'API Documentation SEO', 'Platform Integration SEO', 'Cloud Migration SEO']
    },
    {
      type: 'AI, ML & Emerging Technology SEO',
      description: 'Strategic SEO for artificial intelligence, machine learning, and cutting-edge technology companies',
      icon: Crown,
      features: ['AI Company SEO', 'Machine Learning SEO', 'Blockchain Technology SEO', 'IoT Platform SEO', 'Robotics Company SEO', 'Quantum Computing SEO']
    },
    {
      type: 'Tech Startup & Innovation SEO',
      description: 'Specialized SEO for technology startups, innovation labs, and disruptive technology companies',
      icon: Target,
      features: ['Tech Startup SEO', 'Innovation Lab SEO', 'Venture Capital SEO', 'Incubator Program SEO', 'Tech Accelerator SEO', 'Disruptive Technology SEO']
    }
  ];

  const technologySectors = [
    { name: 'Software Development', clients: '320+', growth: '4,000%' },
    { name: 'SaaS Platforms', clients: '280+', growth: '4,800%' },
    { name: 'AI & ML Companies', clients: '180+', growth: '5,200%' },
    { name: 'Tech Startups', clients: '220+', growth: '3,800%' },
    { name: 'IT Services', clients: '200+', growth: '3,400%' },
    { name: 'Cloud Services', clients: '150+', growth: '4,600%' }
  ];

  const caseStudies = [
    {
      client: 'Leading SaaS Platform',
      industry: 'Software as a Service',
      challenge: 'SaaS platform needed to compete with established enterprise software giants',
      result: '5,200% user acquisition growth',
      metrics: ['1,400+ SaaS keywords in top 3', '₹280Cr+ platform revenue', '720% increase in enterprise subscriptions']
    },
    {
      client: 'AI Technology Startup',
      industry: 'Artificial Intelligence',
      challenge: 'AI startup needed to establish thought leadership and attract enterprise clients',
      result: '4,800% lead generation increase',
      metrics: ['980+ AI keywords ranking', '₹185Cr+ AI solution revenue', '580% increase in enterprise partnerships']
    },
    {
      client: 'Software Development Company',
      industry: 'Custom Software Development',
      challenge: 'Software company needed to compete with global development agencies',
      result: '3,600% project inquiry growth',
      metrics: ['1,100+ development keywords in top 5', '₹145Cr+ development revenue', '450% increase in enterprise projects']
    }
  ];

  const technologySeoStrategies = [
    {
      strategy: 'Technical SEO Excellence',
      description: 'Advanced technical SEO for complex technology platforms and software solutions',
      benefits: ['Technical authority', 'Developer targeting', 'Platform optimization', 'API documentation SEO']
    },
    {
      strategy: 'Thought Leadership SEO',
      description: 'Establish technology companies as industry thought leaders and innovators',
      benefits: ['Industry authority', 'Expert positioning', 'Innovation showcase', 'Technology leadership']
    },
    {
      strategy: 'B2B Technology SEO',
      description: 'Target enterprise clients and business decision-makers in technology procurement',
      benefits: ['Enterprise targeting', 'B2B lead generation', 'Decision maker reach', 'Solution positioning']
    },
    {
      strategy: 'Developer & Technical SEO',
      description: 'Optimize for technical audiences, developers, and IT professionals',
      benefits: ['Developer community', 'Technical documentation', 'API discovery', 'Integration guidance']
    }
  ];

  return (
    <div className="min-h-screen bg-slate-900 relative">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        <div className="container mx-auto px-6 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            <div className="lg:col-span-3">
              <section className="pt-24 pb-16">
                <div className="max-w-4xl">
                  <div className="inline-flex items-center space-x-2 bg-cyan-500/20 border border-cyan-500/30 rounded-full px-6 py-2 mb-8">
                    <Monitor className="w-4 h-4 text-cyan-400" />
                    <span className="text-cyan-400 font-medium">Technology SEO • Software Optimization Excellence</span>
                  </div>
                  
                  <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
                    #1 Technology SEO Company in
                    <span className="bg-gradient-to-r from-cyan-400 to-cyan-600 bg-clip-text text-transparent"> India</span>
                  </h1>
                  
                  <p className="text-xl text-slate-300 mb-8">
                    Premier technology SEO services offering comprehensive search engine optimization solutions for software companies, SaaS platforms, AI startups, and IT service providers. Our technology SEO company provides professional SEO services with software company SEO optimization, SaaS platform SEO, tech startup SEO, and IT services SEO. Serving 1,100+ technology companies across all tech sectors with proven ₹850Cr+ revenue generation and 4,200% average tech lead growth for technology clients through strategic search engine optimization and technology digital marketing excellence.
                  </p>
                  
                  <div className="flex flex-col sm:flex-row gap-4 mb-8">
                    <Button asChild size="lg" className="bg-gradient-to-r from-cyan-500 to-cyan-600 hover:from-cyan-600 hover:to-cyan-700 text-white text-lg px-8 py-4">
                      <Link to="/quote">Get Free Technology SEO Audit</Link>
                    </Button>
                    <Button asChild size="lg" variant="outline" className="border-cyan-500 text-cyan-400 hover:bg-cyan-500 hover:text-white text-lg px-8 py-4">
                      <Link to="/contact">Call Technology SEO Experts: +91-8708577598</Link>
                    </Button>
                  </div>

                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                    {achievements.map((achievement, index) => (
                      <div key={index} className="flex items-center justify-center space-x-2 bg-slate-800/50 rounded-lg p-3">
                        <CheckCircle className="w-4 h-4 text-cyan-400 flex-shrink-0" />
                        <span className="text-slate-300">{achievement}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </section>

              <section className="mb-16 bg-slate-800/50 rounded-2xl p-8">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Technology SEO
                    <span className="bg-gradient-to-r from-cyan-400 to-cyan-600 bg-clip-text text-transparent"> Success Metrics</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Measurable technology SEO results from companies across all technology sectors and platforms.
                  </p>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
                  {stats.map((stat, index) => (
                    <div key={index} className="text-center">
                      <div className="text-4xl font-bold text-cyan-400 mb-2">{stat.metric}</div>
                      <div className="text-white font-semibold mb-1">{stat.description}</div>
                      <div className="text-slate-400">{stat.detail}</div>
                    </div>
                  ))}
                </div>
              </section>

              <section className="mb-16">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Technology SEO Strategies We
                    <span className="bg-gradient-to-r from-cyan-400 to-cyan-600 bg-clip-text text-transparent"> Master</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Specialized SEO strategies designed for technology success across all software platforms and tech solutions.
                  </p>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  {technologySpecializations.map((spec, index) => (
                    <Card key={index} className="bg-slate-900/80 border-cyan-500/20 hover:border-cyan-500/40 transition-all duration-300">
                      <CardContent className="p-8">
                        <div className="w-12 h-12 bg-gradient-to-r from-cyan-500 to-cyan-600 rounded-lg flex items-center justify-center mb-6">
                          <spec.icon className="w-6 h-6 text-white" />
                        </div>
                        <h3 className="text-xl font-bold text-white mb-4">{spec.type}</h3>
                        <p className="text-slate-300 mb-6">{spec.description}</p>
                        <ul className="space-y-2">
                          {spec.features.map((feature, idx) => (
                            <li key={idx} className="flex items-center text-slate-400 text-sm">
                              <CheckCircle className="w-3 h-3 text-cyan-400 mr-2 flex-shrink-0" />
                              {feature}
                            </li>
                          ))}
                        </ul>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>

              {/* Technology Sectors */}
              <section className="mb-16 bg-slate-800/30 rounded-2xl p-8">
                <h3 className="text-2xl font-bold text-white mb-6 text-center">
                  Technology Sector Expertise
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-6">
                  {technologySectors.map((sector, index) => (
                    <div key={index} className="text-center bg-slate-900/50 rounded-lg p-4">
                      <h4 className="text-cyan-400 font-semibold mb-2">{sector.name}</h4>
                      <div className="text-2xl font-bold text-white mb-1">{sector.clients}</div>
                      <div className="text-slate-400 text-sm mb-2">Companies Served</div>
                      <div className="text-green-400 font-semibold text-sm">{sector.growth} avg growth</div>
                    </div>
                  ))}
                </div>
              </section>

              {/* Technology SEO Strategies */}
              <section className="mb-16 bg-slate-800/30 rounded-2xl p-8">
                <h3 className="text-2xl font-bold text-white mb-6 text-center">
                  Advanced Technology SEO Strategies
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                  {technologySeoStrategies.map((strategy, index) => (
                    <div key={index} className="bg-slate-900/50 rounded-lg p-6">
                      <h4 className="text-cyan-400 font-semibold mb-3">{strategy.strategy}</h4>
                      <p className="text-slate-300 text-sm mb-4">{strategy.description}</p>
                      <ul className="space-y-1">
                        {strategy.benefits.map((benefit, idx) => (
                          <li key={idx} className="text-slate-400 text-xs flex items-center">
                            <CheckCircle className="w-3 h-3 text-cyan-400 mr-2 flex-shrink-0" />
                            {benefit}
                          </li>
                        ))}
                      </ul>
                    </div>
                  ))}
                </div>
              </section>

              {/* Case Studies */}
              <section className="mb-16">
                <h3 className="text-2xl font-bold text-white mb-8 text-center">
                  Technology SEO Success Stories
                </h3>
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                  {caseStudies.map((study, index) => (
                    <Card key={index} className="bg-slate-900/60 border-cyan-500/20 hover:border-cyan-500/40 transition-all duration-300">
                      <CardContent className="p-6">
                        <h4 className="text-cyan-400 font-semibold mb-2">{study.client}</h4>
                        <p className="text-slate-400 text-sm mb-3">{study.industry}</p>
                        <p className="text-slate-300 text-sm mb-4">{study.challenge}</p>
                        <div className="bg-slate-800/50 rounded p-3 mb-4">
                          <div className="text-green-400 font-semibold mb-2">{study.result}</div>
                          <ul className="space-y-1">
                            {study.metrics.map((metric, idx) => (
                              <li key={idx} className="text-slate-400 text-xs flex items-center">
                                <CheckCircle className="w-3 h-3 text-cyan-400 mr-2 flex-shrink-0" />
                                {metric}
                              </li>
                            ))}
                          </ul>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>

              {/* Internal Linking Section */}
              <section className="mb-16 bg-slate-800/30 rounded-2xl p-8">
                <h3 className="text-2xl font-bold text-white mb-6 text-center">
                  Complete Technology Digital Marketing Solutions
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  <Link to="/services/ppc" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-cyan-400 font-semibold mb-2 group-hover:text-cyan-300">Technology Google Ads</h4>
                    <p className="text-slate-400 text-sm">PPC advertising for software companies and tech platforms</p>
                  </Link>
                  <Link to="/services/social-media" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-cyan-400 font-semibold mb-2 group-hover:text-cyan-300">Technology Social Media</h4>
                    <p className="text-slate-400 text-sm">Social media marketing for technology companies</p>
                  </Link>
                  <Link to="/services/content" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-cyan-400 font-semibold mb-2 group-hover:text-cyan-300">Technology Content Marketing</h4>
                    <p className="text-slate-400 text-sm">Technical content creation and thought leadership</p>
                  </Link>
                  <Link to="/services/email" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-cyan-400 font-semibold mb-2 group-hover:text-cyan-300">Technology Email Marketing</h4>
                    <p className="text-slate-400 text-sm">Technical newsletters and developer communication</p>
                  </Link>
                  <Link to="/services/web-development" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-cyan-400 font-semibold mb-2 group-hover:text-cyan-300">Technology Website Development</h4>
                    <p className="text-slate-400 text-sm">Advanced technology platform and software website development</p>
                  </Link>
                  <Link to="/portfolio" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-cyan-400 font-semibold mb-2 group-hover:text-cyan-300">Technology Success Stories</h4>
                    <p className="text-slate-400 text-sm">See proven results from our technology clients</p>
                  </Link>
                </div>
              </section>

              <section className="bg-gradient-to-r from-cyan-600 to-cyan-500 rounded-2xl p-8 text-center">
                <h2 className="text-3xl md:text-5xl font-bold mb-6">Ready to Scale Your Technology Business?</h2>
                <p className="text-xl mb-8">
                  Join 1,100+ technology companies that trust GOD Digital Marketing for tech SEO success. Proven strategies that deliver 4,200% lead growth and ₹850Cr+ revenue generation.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button asChild size="lg" className="bg-white text-cyan-600 hover:bg-slate-100 text-lg px-8 py-4">
                    <Link to="/quote">Get Free Technology SEO Audit</Link>
                  </Button>
                  <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-cyan-600 text-lg px-8 py-4">
                    <Link to="/contact">Call Technology SEO Experts: +91-8708577598</Link>
                  </Button>
                </div>
              </section>
            </div>

            <div className="lg:col-span-1">
              <Sidebar currentService="seo" />
            </div>
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
};

export default TechnologySeo;
