import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';

const RedirectHandler = () => {
  const navigate = useNavigate();

  useEffect(() => {
    // <PERSON>le redirects from 404.html for GitHub Pages and other static hosts
    const redirectPath = sessionStorage.getItem('redirectPath');
    if (redirectPath) {
      sessionStorage.removeItem('redirectPath');
      navigate(redirectPath, { replace: true });
    }

    // Handle hash-based routing fallback
    const hash = window.location.hash;
    if (hash && hash.startsWith('#/')) {
      const path = hash.substring(1); // Remove the #
      navigate(path, { replace: true });
    }
  }, [navigate]);

  return null;
};

export default RedirectHandler;
