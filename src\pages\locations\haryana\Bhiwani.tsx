import React from 'react';
import { Link } from 'react-router-dom';
import { ArrowRight, MapPin, TrendingUp, Users, CheckCircle, Building, Star, Shirt } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';

const Bhiwani = () => {
  const allServices = [
    'SEO Services Bhiwani',
    'Local SEO Bhiwani', 
    'International SEO Bhiwani',
    'E-commerce SEO Bhiwani',
    'Technical SEO Bhiwani',
    'Enterprise SEO Bhiwani',
    'Google Ads Management Bhiwani',
    'Facebook Ads Bhiwani',
    'LinkedIn Ads Bhiwani',
    'YouTube Marketing Bhiwani',
    'Social Media Marketing Bhiwani',
    'Content Marketing Bhiwani',
    'AI Content Creation Bhiwani',
    'Social Media Automation Bhiwani',
    'Business Process Automation Bhiwani',
    'Marketing Automation Bhiwani',
    'Lead Generation Bhiwani',
    'Website Development Bhiwani',
    'Conversion Optimization Bhiwani',
    'Email Marketing Bhiwani',
    'Influencer Marketing Bhiwani',
    'Video Marketing Bhiwani',
    'AI Chatbot Development Bhiwani',
    'CRM Solutions Bhiwani',
    'Sales Funnel Automation Bhiwani',
    'Digital Strategy Consulting Bhiwani',
    'Online Reputation Management Bhiwani'
  ];

  const industries = [
    'Textile & Cotton Industry Bhiwani',
    'Agriculture & Farming Bhiwani',
    'Sports Equipment Bhiwani',
    'Manufacturing Bhiwani',
    'Real Estate Bhiwani',
    'Healthcare Bhiwani',
    'Education Bhiwani',
    'Retail & E-commerce Bhiwani',
    'Financial Services Bhiwani',
    'Export-Import Bhiwani'
  ];

  const areas = [
    'Loharu Digital Marketing',
    'Tosham SEO Services', 
    'Siwani Digital Marketing',
    'Bawani Khera SEO Services',
    'Kairu Digital Marketing',
    'Behal SEO Services',
    'Dadri Digital Marketing',
    'Bond Kalan SEO Services',
    'Bhiwani City Digital Marketing',
    'Industrial Area SEO Services'
  ];

  const serviceCategories = [
    {
      category: 'SEO Services Bhiwani',
      services: ['Local SEO', 'International SEO', 'E-commerce SEO', 'Technical SEO', 'Enterprise SEO'],
      description: 'Complete SEO solutions for Bhiwani textile and agricultural businesses'
    },
    {
      category: 'Paid Advertising Bhiwani', 
      services: ['Google Ads', 'Facebook Ads', 'LinkedIn Ads', 'YouTube Marketing'],
      description: 'Strategic paid advertising campaigns for Bhiwani cotton and sports industries'
    },
    {
      category: 'AI Automation Bhiwani',
      services: ['AI Content Creation', 'Social Media Automation', 'Business Process Automation', 'Marketing Automation'],
      description: 'Advanced AI-powered automation solutions for Bhiwani manufacturing sector'
    },
    {
      category: 'Digital Strategy Bhiwani',
      services: ['Content Marketing', 'Social Media Marketing', 'Email Marketing', 'Digital Consulting'],
      description: 'Comprehensive digital strategy for Bhiwani export and agricultural businesses'
    }
  ];

  const stats = [
    {
      metric: '65+',
      description: 'Bhiwani Businesses Served',
      detail: 'Across all sectors'
    },
    {
      metric: '380%',
      description: 'Average Traffic Increase',
      detail: 'For Bhiwani clients'
    },
    {
      metric: '₹28L+',
      description: 'Revenue Generated',
      detail: 'For Bhiwani businesses'
    }
  ];

  return (
    <div className="min-h-screen bg-slate-900 relative">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        {/* Hero Section */}
        <section className="pt-32 pb-20">
          <div className="container mx-auto px-6">
            <div className="max-w-4xl mx-auto text-center">
              <div className="inline-flex items-center space-x-2 bg-orange-500/20 border border-orange-500/30 rounded-full px-6 py-2 mb-8">
                <Shirt className="w-4 h-4 text-orange-400" />
                <span className="text-orange-400 font-medium">Bhiwani Digital Marketing • Cotton City of Haryana</span>
              </div>
              
              <h1 className="text-5xl md:text-6xl font-bold mb-6 leading-tight">
                Complete Digital Marketing Services in
                <span className="bg-gradient-to-r from-orange-400 to-orange-600 bg-clip-text text-transparent"> Bhiwani</span>
              </h1>
              
              <p className="text-xl text-slate-300 mb-12 max-w-3xl mx-auto">
                Leading digital marketing agency in Bhiwani offering ALL 25+ services from SEO to AI automation. From cotton textile industries to sports equipment manufacturers, we've helped 65+ businesses grow with proven strategies across all Bhiwani sectors.
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button asChild size="lg" className="bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white text-lg px-8 py-4">
                  <Link to="/quote">Get Free Bhiwani Digital Marketing Audit</Link>
                </Button>
                <Button asChild size="lg" variant="outline" className="border-orange-500 text-orange-400 hover:bg-orange-500 hover:text-white text-lg px-8 py-4">
                  <Link to="/contact">Call Bhiwani Office</Link>
                </Button>
              </div>
            </div>
          </div>
        </section>

        {/* Stats Section */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Bhiwani Digital Marketing
                <span className="bg-gradient-to-r from-orange-400 to-orange-600 bg-clip-text text-transparent"> Success Stories</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto mb-12">
                Real results from Bhiwani businesses across all industries - from cotton textile manufacturers to sports equipment exporters in the Cotton City.
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
              {stats.map((stat, index) => (
                <div key={index} className="text-center">
                  <div className="text-5xl font-bold text-orange-400 mb-2">{stat.metric}</div>
                  <div className="text-white font-semibold mb-1">{stat.description}</div>
                  <div className="text-slate-400">{stat.detail}</div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Service Categories Section */}
        <section className="py-20">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Complete Digital Marketing Services in
                <span className="bg-gradient-to-r from-orange-400 to-orange-600 bg-clip-text text-transparent"> Bhiwani</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto">
                All 25+ digital marketing services available for Bhiwani businesses - from SEO to AI automation.
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-20">
              {serviceCategories.map((category, index) => (
                <Card key={index} className="bg-slate-900/80 border-orange-500/20 hover:border-orange-500/40 transition-all duration-300">
                  <CardContent className="p-8">
                    <h3 className="text-xl font-bold text-white mb-4">{category.category}</h3>
                    <p className="text-slate-300 mb-6">{category.description}</p>
                    <div className="grid grid-cols-2 gap-2">
                      {category.services.map((service, idx) => (
                        <div key={idx} className="flex items-center text-slate-400 text-sm">
                          <CheckCircle className="w-3 h-3 text-orange-400 mr-2 flex-shrink-0" />
                          {service}
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* All Services Grid */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                All Digital Marketing Services
                <span className="bg-gradient-to-r from-orange-400 to-orange-600 bg-clip-text text-transparent"> Available in Bhiwani</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto">
                Complete range of 25+ digital marketing services for every business need in Bhiwani.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {allServices.map((service, index) => (
                <Card key={index} className="bg-slate-900/80 border-orange-500/20 hover:border-orange-500/40 transition-all duration-300">
                  <CardContent className="p-4 text-center">
                    <h3 className="text-white font-semibold text-sm mb-2">{service}</h3>
                    <Button asChild size="sm" variant="outline" className="border-orange-500 text-orange-400 hover:bg-orange-500 hover:text-white text-xs">
                      <Link to="/quote">Get Quote</Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Industries Section */}
        <section className="py-20">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Bhiwani Industries We
                <span className="bg-gradient-to-r from-orange-400 to-orange-600 bg-clip-text text-transparent"> Serve</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto">
                Specialized digital marketing strategies for all major Bhiwani industries including cotton, textiles, and sports equipment.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
              {industries.map((industry, index) => (
                <Card key={index} className="bg-slate-900/80 border-orange-500/20 hover:border-orange-500/40 transition-all duration-300">
                  <CardContent className="p-6 text-center">
                    <Star className="w-8 h-8 text-orange-400 mx-auto mb-4" />
                    <h3 className="text-white font-semibold mb-2 text-sm">{industry}</h3>
                    <Button asChild size="sm" variant="outline" className="border-orange-500 text-orange-400 hover:bg-orange-500 hover:text-white text-xs">
                      <Link to="/quote">Get Strategy</Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Areas Section */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Bhiwani Areas We
                <span className="bg-gradient-to-r from-orange-400 to-orange-600 bg-clip-text text-transparent"> Cover</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto">
                Complete digital marketing services across all Bhiwani areas and surrounding regions.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
              {areas.map((area, index) => (
                <Card key={index} className="bg-slate-900/80 border-orange-500/20 hover:border-orange-500/40 transition-all duration-300">
                  <CardContent className="p-6 text-center">
                    <MapPin className="w-8 h-8 text-orange-400 mx-auto mb-4" />
                    <h3 className="text-white font-semibold mb-2 text-sm">{area}</h3>
                    <Button asChild size="sm" variant="outline" className="border-orange-500 text-orange-400 hover:bg-orange-500 hover:text-white text-xs">
                      <Link to="/contact">Contact Us</Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Why Choose Us Section */}
        <section className="py-20">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Why Choose GOD Digital Marketing in
                <span className="bg-gradient-to-r from-orange-400 to-orange-600 bg-clip-text text-transparent"> Bhiwani?</span>
              </h2>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              <Card className="bg-slate-900/80 border-orange-500/20">
                <CardContent className="p-8">
                  <TrendingUp className="w-12 h-12 text-orange-400 mb-6" />
                  <h3 className="text-xl font-bold text-white mb-4">Complete Service Portfolio</h3>
                  <p className="text-slate-300">All 25+ digital marketing services available in Bhiwani - from basic SEO to advanced AI automation for textile and sports industries.</p>
                </CardContent>
              </Card>

              <Card className="bg-slate-900/80 border-orange-500/20">
                <CardContent className="p-8">
                  <Users className="w-12 h-12 text-orange-400 mb-6" />
                  <h3 className="text-xl font-bold text-white mb-4">Proven Bhiwani Results</h3>
                  <p className="text-slate-300">65+ successful Bhiwani campaigns with measurable ROI improvements across cotton, textile, and manufacturing industries.</p>
                </CardContent>
              </Card>

              <Card className="bg-slate-900/80 border-orange-500/20">
                <CardContent className="p-8">
                  <CheckCircle className="w-12 h-12 text-orange-400 mb-6" />
                  <h3 className="text-xl font-bold text-white mb-4">Cotton City Expertise</h3>
                  <p className="text-slate-300">Deep understanding of Bhiwani's cotton industry, textile manufacturing, and sports equipment export business dynamics.</p>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 bg-gradient-to-r from-orange-600 to-orange-500">
          <div className="container mx-auto px-6 text-center">
            <h2 className="text-4xl md:text-5xl font-bold mb-6">Ready to Dominate Bhiwani's Digital Market?</h2>
            <p className="text-xl mb-8 max-w-3xl mx-auto">
              Join 65+ Bhiwani businesses that trust GOD Digital Marketing for complete digital solutions. All 25+ services available for your cotton, textile, or sports business growth.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button asChild size="lg" className="bg-white text-orange-600 hover:bg-slate-100 text-lg px-8 py-4">
                <Link to="/quote">Get Free Digital Marketing Audit</Link>
              </Button>
              <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-orange-600 text-lg px-8 py-4">
                <Link to="/contact">Call Bhiwani Office Now</Link>
              </Button>
            </div>
          </div>
        </section>
      </main>
      
      <Footer />
    </div>
  );
};

export default Bhiwani;
