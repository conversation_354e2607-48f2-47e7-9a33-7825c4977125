import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { Search, Calculator, TrendingUp, Users, CheckCircle, Building, Star, Crown, Target, Zap, ArrowRight, BarChart } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';
import Sidebar from '@/components/Sidebar';

const SeoTools = () => {
  const [keywordDensity, setKeywordDensity] = useState('');
  const [content, setContent] = useState('');
  const [densityResult, setDensityResult] = useState<number | null>(null);

  const calculateKeywordDensity = () => {
    if (!keywordDensity || !content) return;
    
    const keyword = keywordDensity.toLowerCase();
    const text = content.toLowerCase();
    const words = text.split(/\s+/).filter(word => word.length > 0);
    const keywordCount = text.split(keyword).length - 1;
    const density = (keywordCount / words.length) * 100;
    
    setDensityResult(Math.round(density * 100) / 100);
  };

  const stats = [
    {
      metric: '25,000+',
      description: 'Monthly Tool Users',
      detail: 'SEO professionals using our tools'
    },
    {
      metric: '150+',
      description: 'SEO Audits Performed',
      detail: 'Daily website audits'
    },
    {
      metric: '95%',
      description: 'Accuracy Rate',
      detail: 'Tool precision and reliability'
    },
    {
      metric: 'Free',
      description: 'All Tools Available',
      detail: 'No hidden costs or limits'
    }
  ];

  const seoTools = [
    {
      name: 'Keyword Density Calculator',
      description: 'Calculate keyword density percentage for your content optimization',
      icon: Calculator,
      features: ['Real-time calculation', 'Multiple keyword analysis', 'Content optimization tips', 'SEO recommendations']
    },
    {
      name: 'Meta Tag Generator',
      description: 'Generate optimized meta titles and descriptions for better search rankings',
      icon: Target,
      features: ['Character count tracking', 'SEO best practices', 'Preview snippets', 'Multiple variations']
    },
    {
      name: 'Website Speed Analyzer',
      description: 'Analyze your website loading speed and get optimization recommendations',
      icon: Zap,
      features: ['Core Web Vitals', 'Performance metrics', 'Optimization suggestions', 'Mobile analysis']
    },
    {
      name: 'Backlink Checker',
      description: 'Check your website backlinks and analyze link quality',
      icon: Building,
      features: ['Link quality analysis', 'Domain authority check', 'Anchor text analysis', 'Competitor comparison']
    },
    {
      name: 'SERP Position Tracker',
      description: 'Track your keyword rankings across different search engines',
      icon: BarChart,
      features: ['Multi-location tracking', 'Historical data', 'Competitor tracking', 'Ranking alerts']
    },
    {
      name: 'Schema Markup Generator',
      description: 'Generate structured data markup for better search visibility',
      icon: Star,
      features: ['Multiple schema types', 'JSON-LD format', 'Validation included', 'Rich snippets preview']
    }
  ];

  const toolCategories = [
    {
      category: 'On-Page SEO Tools',
      tools: ['Keyword Density Calculator', 'Meta Tag Generator', 'Header Tag Analyzer', 'Internal Link Checker']
    },
    {
      category: 'Technical SEO Tools',
      tools: ['Website Speed Analyzer', 'Mobile-Friendly Test', 'SSL Certificate Checker', 'Robots.txt Validator']
    },
    {
      category: 'Off-Page SEO Tools',
      tools: ['Backlink Checker', 'Domain Authority Checker', 'Link Building Opportunities', 'Competitor Analysis']
    },
    {
      category: 'Keyword Research Tools',
      tools: ['Keyword Suggestion Tool', 'Search Volume Checker', 'Keyword Difficulty Analyzer', 'Long-tail Keyword Finder']
    }
  ];

  const benefits = [
    {
      benefit: 'Free to Use',
      description: 'All our SEO tools are completely free with no hidden costs or usage limits',
      icon: CheckCircle
    },
    {
      benefit: 'Professional Grade',
      description: 'Enterprise-level tools used by SEO professionals and agencies worldwide',
      icon: Crown
    },
    {
      benefit: 'Real-time Results',
      description: 'Get instant analysis and recommendations for immediate implementation',
      icon: Zap
    },
    {
      benefit: 'Expert Support',
      description: 'Backed by our team of SEO experts with 7+ years of international experience',
      icon: Users
    }
  ];

  return (
    <div className="min-h-screen bg-slate-900 relative">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        <div className="container mx-auto px-6 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            <div className="lg:col-span-3">
              <section className="pt-24 pb-16">
                <div className="max-w-4xl">
                  <div className="inline-flex items-center space-x-2 bg-green-500/20 border border-green-500/30 rounded-full px-6 py-2 mb-8">
                    <Search className="w-4 h-4 text-green-400" />
                    <span className="text-green-400 font-medium">Free SEO Tools • Professional Grade Analysis</span>
                  </div>
                  
                  <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
                    Free SEO Tools by
                    <span className="bg-gradient-to-r from-green-400 to-green-600 bg-clip-text text-transparent"> GOD Digital Marketing</span>
                  </h1>
                  
                  <p className="text-xl text-slate-300 mb-8">
                    Professional-grade free SEO tools for keyword research, on-page optimization, technical SEO analysis, and website performance monitoring. Our comprehensive SEO toolkit includes keyword density calculator, meta tag generator, backlink checker, website speed analyzer, and SERP position tracker. Used by 25,000+ SEO professionals monthly for website optimization, keyword analysis, and search engine ranking improvements. Get instant SEO insights and recommendations from India's leading digital marketing agency.
                  </p>
                  
                  <div className="flex flex-col sm:flex-row gap-4 mb-8">
                    <Button asChild size="lg" className="bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white text-lg px-8 py-4">
                      <Link to="#tools">Explore Free SEO Tools</Link>
                    </Button>
                    <Button asChild size="lg" variant="outline" className="border-green-500 text-green-400 hover:bg-green-500 hover:text-white text-lg px-8 py-4">
                      <Link to="/contact">Get Expert SEO Help: +91-8708577598</Link>
                    </Button>
                  </div>
                </div>
              </section>

              <section className="mb-16 bg-slate-800/50 rounded-2xl p-8">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    SEO Tools
                    <span className="bg-gradient-to-r from-green-400 to-green-600 bg-clip-text text-transparent"> Usage Statistics</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Trusted by SEO professionals and businesses worldwide for accurate analysis and insights.
                  </p>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
                  {stats.map((stat, index) => (
                    <div key={index} className="text-center">
                      <div className="text-4xl font-bold text-green-400 mb-2">{stat.metric}</div>
                      <div className="text-white font-semibold mb-1">{stat.description}</div>
                      <div className="text-slate-400">{stat.detail}</div>
                    </div>
                  ))}
                </div>
              </section>

              {/* Keyword Density Calculator */}
              <section id="tools" className="mb-16 bg-slate-800/30 rounded-2xl p-8">
                <h3 className="text-2xl font-bold text-white mb-6 text-center">
                  Keyword Density Calculator
                </h3>
                <div className="max-w-2xl mx-auto">
                  <div className="space-y-4">
                    <div>
                      <label className="block text-white font-medium mb-2">Target Keyword</label>
                      <Input
                        type="text"
                        placeholder="Enter your target keyword"
                        value={keywordDensity}
                        onChange={(e) => setKeywordDensity(e.target.value)}
                        className="bg-slate-900 border-slate-700 text-white"
                      />
                    </div>
                    <div>
                      <label className="block text-white font-medium mb-2">Content Text</label>
                      <textarea
                        placeholder="Paste your content here..."
                        value={content}
                        onChange={(e) => setContent(e.target.value)}
                        rows={6}
                        className="w-full bg-slate-900 border border-slate-700 rounded-md p-3 text-white resize-none"
                      />
                    </div>
                    <Button 
                      onClick={calculateKeywordDensity}
                      className="w-full bg-green-600 hover:bg-green-700"
                    >
                      Calculate Keyword Density
                    </Button>
                    {densityResult !== null && (
                      <div className="bg-slate-900 rounded-lg p-4 text-center">
                        <div className="text-2xl font-bold text-green-400 mb-2">{densityResult}%</div>
                        <div className="text-white">Keyword Density</div>
                        <div className="text-slate-400 text-sm mt-2">
                          {densityResult < 1 ? 'Consider increasing keyword usage' : 
                           densityResult > 3 ? 'Consider reducing keyword usage to avoid over-optimization' : 
                           'Good keyword density range'}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </section>

              <section className="mb-16">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Professional SEO Tools
                    <span className="bg-gradient-to-r from-green-400 to-green-600 bg-clip-text text-transparent"> Suite</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Comprehensive collection of SEO tools for complete website optimization and analysis.
                  </p>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  {seoTools.map((tool, index) => (
                    <Card key={index} className="bg-slate-900/80 border-green-500/20 hover:border-green-500/40 transition-all duration-300">
                      <CardContent className="p-8">
                        <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-green-600 rounded-lg flex items-center justify-center mb-6">
                          <tool.icon className="w-6 h-6 text-white" />
                        </div>
                        <h3 className="text-xl font-bold text-white mb-4">{tool.name}</h3>
                        <p className="text-slate-300 mb-6">{tool.description}</p>
                        <ul className="space-y-2">
                          {tool.features.map((feature, idx) => (
                            <li key={idx} className="flex items-center text-slate-400 text-sm">
                              <CheckCircle className="w-3 h-3 text-green-400 mr-2 flex-shrink-0" />
                              {feature}
                            </li>
                          ))}
                        </ul>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>

              {/* Tool Categories */}
              <section className="mb-16 bg-slate-800/30 rounded-2xl p-8">
                <h3 className="text-2xl font-bold text-white mb-6 text-center">
                  SEO Tool Categories
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                  {toolCategories.map((category, index) => (
                    <div key={index} className="bg-slate-900/50 rounded-lg p-6">
                      <h4 className="text-green-400 font-semibold mb-3">{category.category}</h4>
                      <ul className="space-y-2">
                        {category.tools.map((tool, idx) => (
                          <li key={idx} className="text-slate-300 text-sm flex items-center">
                            <CheckCircle className="w-3 h-3 text-green-400 mr-2 flex-shrink-0" />
                            {tool}
                          </li>
                        ))}
                      </ul>
                    </div>
                  ))}
                </div>
              </section>

              {/* Benefits */}
              <section className="mb-16">
                <h3 className="text-2xl font-bold text-white mb-8 text-center">
                  Why Choose Our SEO Tools?
                </h3>
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {benefits.map((benefit, index) => (
                    <Card key={index} className="bg-slate-900/60 border-green-500/20 hover:border-green-500/40 transition-all duration-300">
                      <CardContent className="p-6">
                        <div className="flex items-center mb-4">
                          <div className="w-10 h-10 bg-gradient-to-r from-green-500 to-green-600 rounded-lg flex items-center justify-center mr-4">
                            <benefit.icon className="w-5 h-5 text-white" />
                          </div>
                          <h4 className="text-green-400 font-semibold">{benefit.benefit}</h4>
                        </div>
                        <p className="text-slate-300 text-sm">{benefit.description}</p>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>

              {/* Internal Linking Section */}
              <section className="mb-16 bg-slate-800/30 rounded-2xl p-8">
                <h3 className="text-2xl font-bold text-white mb-6 text-center">
                  Complete SEO Solutions
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  <Link to="/services/seo" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-green-400 font-semibold mb-2 group-hover:text-green-300">Professional SEO Services</h4>
                    <p className="text-slate-400 text-sm">Expert SEO optimization and strategy implementation</p>
                  </Link>
                  <Link to="/services/content" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-green-400 font-semibold mb-2 group-hover:text-green-300">Content Marketing</h4>
                    <p className="text-slate-400 text-sm">SEO-optimized content creation and strategy</p>
                  </Link>
                  <Link to="/services/web-development" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-green-400 font-semibold mb-2 group-hover:text-green-300">Technical SEO</h4>
                    <p className="text-slate-400 text-sm">Website optimization and technical SEO implementation</p>
                  </Link>
                  <Link to="/services/local-business-seo" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-green-400 font-semibold mb-2 group-hover:text-green-300">Local SEO</h4>
                    <p className="text-slate-400 text-sm">Local business optimization and Google My Business</p>
                  </Link>
                  <Link to="/services/ecommerce-seo" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-green-400 font-semibold mb-2 group-hover:text-green-300">E-commerce SEO</h4>
                    <p className="text-slate-400 text-sm">Online store optimization and product SEO</p>
                  </Link>
                  <Link to="/contact" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-green-400 font-semibold mb-2 group-hover:text-green-300">SEO Consultation</h4>
                    <p className="text-slate-400 text-sm">Expert SEO advice and strategy consultation</p>
                  </Link>
                </div>
              </section>

              <section className="bg-gradient-to-r from-green-600 to-green-500 rounded-2xl p-8 text-center">
                <h2 className="text-3xl md:text-5xl font-bold mb-6">Need Professional SEO Help?</h2>
                <p className="text-xl mb-8">
                  While our free tools provide valuable insights, our expert SEO team can deliver comprehensive optimization strategies for maximum results.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button asChild size="lg" className="bg-white text-green-600 hover:bg-slate-100 text-lg px-8 py-4">
                    <Link to="/quote">Get Free SEO Audit</Link>
                  </Button>
                  <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-green-600 text-lg px-8 py-4">
                    <Link to="/contact">Call SEO Experts: +91-8708577598</Link>
                  </Button>
                </div>
              </section>
            </div>

            <div className="lg:col-span-1">
              <Sidebar currentService="seo-tools" />
            </div>
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
};

export default SeoTools;
