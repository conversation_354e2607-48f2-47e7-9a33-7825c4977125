import React from 'react';
import { Link } from 'react-router-dom';
import { GraduationCap, Search, TrendingUp, Users, CheckCircle, Building, Star, Crown, Target, Zap, ArrowRight } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';
import Sidebar from '@/components/Sidebar';

const EducationSeo = () => {
  const stats = [
    {
      metric: '1,200+',
      description: 'Educational Institutions Optimized',
      detail: 'Across all education sectors'
    },
    {
      metric: '3,200%',
      description: 'Average Student Enrollment Growth',
      detail: 'For education clients'
    },
    {
      metric: '₹180Cr+',
      description: 'Education Revenue Generated',
      detail: 'Through SEO campaigns'
    },
    {
      metric: '97%',
      description: 'Education Keywords Ranking',
      detail: 'Top 10 positions achieved'
    }
  ];

  const achievements = [
    'Top Education SEO Company in India',
    'EdTech SEO Specialists',
    'University SEO Experts',
    'Online Learning Platform Leaders',
    'Student Recruitment Champions',
    'Educational Content Marketing Pioneers'
  ];

  const educationSpecializations = [
    {
      type: 'University & College SEO',
      description: 'Comprehensive SEO for universities, colleges, and higher education institutions',
      icon: Building,
      features: ['University Website SEO', 'Course Page Optimization', 'Faculty Profile SEO', 'Research Publication SEO', 'Campus Life Content', 'Alumni Network SEO']
    },
    {
      type: 'K-12 School SEO',
      description: 'Specialized SEO for primary and secondary schools, CBSE, ICSE, and international schools',
      icon: Star,
      features: ['School Website Optimization', 'Admission Process SEO', 'Academic Program SEO', 'Parent Portal SEO', 'School Event Marketing', 'Teacher Recruitment SEO']
    },
    {
      type: 'EdTech & Online Learning SEO',
      description: 'Advanced SEO for EdTech platforms, online courses, and e-learning solutions',
      icon: Crown,
      features: ['Online Course SEO', 'Learning Platform Optimization', 'Educational App SEO', 'MOOC Platform SEO', 'Skill Development SEO', 'Certification Program SEO']
    },
    {
      type: 'Coaching & Training Institute SEO',
      description: 'Strategic SEO for coaching centers, training institutes, and skill development centers',
      icon: Target,
      features: ['Coaching Center SEO', 'Competitive Exam SEO', 'Professional Training SEO', 'Skill Development SEO', 'Test Preparation SEO', 'Career Counseling SEO']
    }
  ];

  const educationSectors = [
    { name: 'Universities & Colleges', clients: '320+', growth: '3,500%' },
    { name: 'K-12 Schools', clients: '450+', growth: '2,800%' },
    { name: 'EdTech Platforms', clients: '280+', growth: '4,200%' },
    { name: 'Coaching Institutes', clients: '380+', growth: '3,100%' },
    { name: 'Online Learning', clients: '220+', growth: '3,800%' },
    { name: 'Skill Development', clients: '180+', growth: '2,900%' }
  ];

  const caseStudies = [
    {
      client: 'Leading University',
      industry: 'Higher Education',
      challenge: 'Top university needed to increase online visibility and attract quality students globally',
      result: '4,200% student inquiry increase',
      metrics: ['850+ education keywords in top 3', '₹45Cr+ enrollment revenue', '520% increase in international applications']
    },
    {
      client: 'EdTech Startup',
      industry: 'Online Learning Platform',
      challenge: 'New EdTech platform needed to compete with established online learning giants',
      result: '3,800% user acquisition growth',
      metrics: ['680+ EdTech keywords ranking', '₹28Cr+ platform revenue', '450% increase in course enrollments']
    },
    {
      client: 'Coaching Institute Chain',
      industry: 'Test Preparation',
      challenge: 'Multi-location coaching institute needed to dominate competitive exam preparation market',
      result: '3,200% student enrollment increase',
      metrics: ['720+ coaching keywords in top 5', '₹35Cr+ coaching revenue', '380% increase in branch enrollments']
    }
  ];

  const educationSeoStrategies = [
    {
      strategy: 'Student Journey SEO',
      description: 'Optimize for every stage of the student decision-making process',
      benefits: ['Higher enrollment rates', 'Better student quality', 'Improved conversion funnel', 'Enhanced user experience']
    },
    {
      strategy: 'Academic Content SEO',
      description: 'Create and optimize educational content that demonstrates expertise',
      benefits: ['Academic authority', 'Thought leadership', 'Student engagement', 'Faculty recognition']
    },
    {
      strategy: 'Local Education SEO',
      description: 'Target local students and parents searching for nearby educational options',
      benefits: ['Local market dominance', 'Community engagement', 'Regional authority', 'Increased foot traffic']
    },
    {
      strategy: 'Mobile Education SEO',
      description: 'Optimize for mobile-first education searches and app discovery',
      benefits: ['Mobile user engagement', 'App store optimization', 'Student accessibility', 'Modern user experience']
    }
  ];

  return (
    <div className="min-h-screen bg-slate-900 relative">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        <div className="container mx-auto px-6 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            <div className="lg:col-span-3">
              <section className="pt-24 pb-16">
                <div className="max-w-4xl">
                  <div className="inline-flex items-center space-x-2 bg-blue-500/20 border border-blue-500/30 rounded-full px-6 py-2 mb-8">
                    <GraduationCap className="w-4 h-4 text-blue-400" />
                    <span className="text-blue-400 font-medium">Education SEO • EdTech Optimization Excellence</span>
                  </div>
                  
                  <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
                    #1 Education SEO Company in
                    <span className="bg-gradient-to-r from-blue-400 to-blue-600 bg-clip-text text-transparent"> India</span>
                  </h1>
                  
                  <p className="text-xl text-slate-300 mb-8">
                    Premier education SEO services offering comprehensive search engine optimization solutions for universities, schools, EdTech platforms, and coaching institutes. Our education SEO company provides professional SEO services with student recruitment optimization, online learning platform SEO, university website optimization, and educational content marketing. Serving 1,200+ educational institutions across all education sectors with proven ₹180Cr+ revenue generation and 3,200% average student enrollment growth for education clients through strategic search engine optimization and educational digital marketing excellence.
                  </p>
                  
                  <div className="flex flex-col sm:flex-row gap-4 mb-8">
                    <Button asChild size="lg" className="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white text-lg px-8 py-4">
                      <Link to="/quote">Get Free Education SEO Audit</Link>
                    </Button>
                    <Button asChild size="lg" variant="outline" className="border-blue-500 text-blue-400 hover:bg-blue-500 hover:text-white text-lg px-8 py-4">
                      <Link to="/contact">Call Education SEO Experts: +91-8708577598</Link>
                    </Button>
                  </div>

                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                    {achievements.map((achievement, index) => (
                      <div key={index} className="flex items-center justify-center space-x-2 bg-slate-800/50 rounded-lg p-3">
                        <CheckCircle className="w-4 h-4 text-blue-400 flex-shrink-0" />
                        <span className="text-slate-300">{achievement}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </section>

              <section className="mb-16 bg-slate-800/50 rounded-2xl p-8">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Education SEO
                    <span className="bg-gradient-to-r from-blue-400 to-blue-600 bg-clip-text text-transparent"> Success Metrics</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Measurable education SEO results from institutions across all educational sectors and levels.
                  </p>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
                  {stats.map((stat, index) => (
                    <div key={index} className="text-center">
                      <div className="text-4xl font-bold text-blue-400 mb-2">{stat.metric}</div>
                      <div className="text-white font-semibold mb-1">{stat.description}</div>
                      <div className="text-slate-400">{stat.detail}</div>
                    </div>
                  ))}
                </div>
              </section>

              <section className="mb-16">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Education SEO Strategies We
                    <span className="bg-gradient-to-r from-blue-400 to-blue-600 bg-clip-text text-transparent"> Master</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Specialized SEO strategies designed for education success across all academic levels and learning platforms.
                  </p>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  {educationSpecializations.map((spec, index) => (
                    <Card key={index} className="bg-slate-900/80 border-blue-500/20 hover:border-blue-500/40 transition-all duration-300">
                      <CardContent className="p-8">
                        <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg flex items-center justify-center mb-6">
                          <spec.icon className="w-6 h-6 text-white" />
                        </div>
                        <h3 className="text-xl font-bold text-white mb-4">{spec.type}</h3>
                        <p className="text-slate-300 mb-6">{spec.description}</p>
                        <ul className="space-y-2">
                          {spec.features.map((feature, idx) => (
                            <li key={idx} className="flex items-center text-slate-400 text-sm">
                              <CheckCircle className="w-3 h-3 text-blue-400 mr-2 flex-shrink-0" />
                              {feature}
                            </li>
                          ))}
                        </ul>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>

              {/* Education Sectors */}
              <section className="mb-16 bg-slate-800/30 rounded-2xl p-8">
                <h3 className="text-2xl font-bold text-white mb-6 text-center">
                  Education Sector Expertise
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-6">
                  {educationSectors.map((sector, index) => (
                    <div key={index} className="text-center bg-slate-900/50 rounded-lg p-4">
                      <h4 className="text-blue-400 font-semibold mb-2">{sector.name}</h4>
                      <div className="text-2xl font-bold text-white mb-1">{sector.clients}</div>
                      <div className="text-slate-400 text-sm mb-2">Institutions Served</div>
                      <div className="text-green-400 font-semibold text-sm">{sector.growth} avg growth</div>
                    </div>
                  ))}
                </div>
              </section>

              {/* Education SEO Strategies */}
              <section className="mb-16 bg-slate-800/30 rounded-2xl p-8">
                <h3 className="text-2xl font-bold text-white mb-6 text-center">
                  Advanced Education SEO Strategies
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                  {educationSeoStrategies.map((strategy, index) => (
                    <div key={index} className="bg-slate-900/50 rounded-lg p-6">
                      <h4 className="text-blue-400 font-semibold mb-3">{strategy.strategy}</h4>
                      <p className="text-slate-300 text-sm mb-4">{strategy.description}</p>
                      <ul className="space-y-1">
                        {strategy.benefits.map((benefit, idx) => (
                          <li key={idx} className="text-slate-400 text-xs flex items-center">
                            <CheckCircle className="w-3 h-3 text-blue-400 mr-2 flex-shrink-0" />
                            {benefit}
                          </li>
                        ))}
                      </ul>
                    </div>
                  ))}
                </div>
              </section>

              {/* Case Studies */}
              <section className="mb-16">
                <h3 className="text-2xl font-bold text-white mb-8 text-center">
                  Education SEO Success Stories
                </h3>
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                  {caseStudies.map((study, index) => (
                    <Card key={index} className="bg-slate-900/60 border-blue-500/20 hover:border-blue-500/40 transition-all duration-300">
                      <CardContent className="p-6">
                        <h4 className="text-blue-400 font-semibold mb-2">{study.client}</h4>
                        <p className="text-slate-400 text-sm mb-3">{study.industry}</p>
                        <p className="text-slate-300 text-sm mb-4">{study.challenge}</p>
                        <div className="bg-slate-800/50 rounded p-3 mb-4">
                          <div className="text-green-400 font-semibold mb-2">{study.result}</div>
                          <ul className="space-y-1">
                            {study.metrics.map((metric, idx) => (
                              <li key={idx} className="text-slate-400 text-xs flex items-center">
                                <CheckCircle className="w-3 h-3 text-blue-400 mr-2 flex-shrink-0" />
                                {metric}
                              </li>
                            ))}
                          </ul>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>

              {/* Internal Linking Section */}
              <section className="mb-16 bg-slate-800/30 rounded-2xl p-8">
                <h3 className="text-2xl font-bold text-white mb-6 text-center">
                  Complete Education Digital Marketing Solutions
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  <Link to="/services/ppc" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-blue-400 font-semibold mb-2 group-hover:text-blue-300">Education Google Ads</h4>
                    <p className="text-slate-400 text-sm">PPC advertising for student recruitment and course promotion</p>
                  </Link>
                  <Link to="/services/social-media" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-blue-400 font-semibold mb-2 group-hover:text-blue-300">Education Social Media</h4>
                    <p className="text-slate-400 text-sm">Social media marketing for educational institutions</p>
                  </Link>
                  <Link to="/services/content" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-blue-400 font-semibold mb-2 group-hover:text-blue-300">Educational Content Marketing</h4>
                    <p className="text-slate-400 text-sm">Academic content creation and educational marketing</p>
                  </Link>
                  <Link to="/services/email" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-blue-400 font-semibold mb-2 group-hover:text-blue-300">Education Email Marketing</h4>
                    <p className="text-slate-400 text-sm">Student communication and enrollment campaigns</p>
                  </Link>
                  <Link to="/services/web-development" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-blue-400 font-semibold mb-2 group-hover:text-blue-300">Education Website Development</h4>
                    <p className="text-slate-400 text-sm">Educational website and learning platform development</p>
                  </Link>
                  <Link to="/portfolio" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-blue-400 font-semibold mb-2 group-hover:text-blue-300">Education Success Stories</h4>
                    <p className="text-slate-400 text-sm">See proven results from our education clients</p>
                  </Link>
                </div>
              </section>

              <section className="bg-gradient-to-r from-blue-600 to-blue-500 rounded-2xl p-8 text-center">
                <h2 className="text-3xl md:text-5xl font-bold mb-6">Ready to Boost Your Student Enrollment?</h2>
                <p className="text-xl mb-8">
                  Join 1,200+ educational institutions that trust GOD Digital Marketing for education SEO success. Proven strategies that deliver 3,200% enrollment growth and ₹180Cr+ revenue generation.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button asChild size="lg" className="bg-white text-blue-600 hover:bg-slate-100 text-lg px-8 py-4">
                    <Link to="/quote">Get Free Education SEO Audit</Link>
                  </Button>
                  <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-blue-600 text-lg px-8 py-4">
                    <Link to="/contact">Call Education SEO Experts: +91-8708577598</Link>
                  </Button>
                </div>
              </section>
            </div>

            <div className="lg:col-span-1">
              <Sidebar currentService="seo" />
            </div>
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
};

export default EducationSeo;
