import React from 'react';
import { Link } from 'react-router-dom';
import { ArrowLeft, Clock, Mail } from 'lucide-react';
import { Button } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';

interface ComingSoonProps {
  title?: string;
  description?: string;
  backLink?: string;
  backText?: string;
}

const ComingSoon: React.FC<ComingSoonProps> = ({ 
  title = "Coming Soon",
  description = "This page is currently under development. We're working hard to bring you amazing content and features.",
  backLink = "/",
  backText = "Back to Home"
}) => {
  return (
    <div className="min-h-screen bg-slate-900 relative">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        <section className="pt-32 pb-20 min-h-screen flex items-center">
          <div className="container mx-auto px-6">
            <div className="max-w-4xl mx-auto text-center">
              <div className="inline-flex items-center space-x-2 bg-amber-500/20 border border-amber-500/30 rounded-full px-6 py-2 mb-8">
                <Clock className="w-4 h-4 text-amber-400" />
                <span className="text-amber-400 font-medium">Under Development</span>
              </div>
              
              <h1 className="text-5xl md:text-6xl font-bold mb-6 leading-tight">
                <span className="bg-gradient-to-r from-amber-400 to-amber-600 bg-clip-text text-transparent">{title}</span>
              </h1>
              
              <p className="text-xl text-slate-300 mb-12 max-w-3xl mx-auto">
                {description}
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4 justify-center mb-16">
                <Button asChild size="lg" className="bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700 text-white text-lg px-8 py-4">
                  <Link to={backLink} className="flex items-center">
                    <ArrowLeft className="w-5 h-5 mr-2" />
                    {backText}
                  </Link>
                </Button>
                <Button asChild size="lg" variant="outline" className="border-amber-500 text-amber-400 hover:bg-amber-500 hover:text-white text-lg px-8 py-4">
                  <Link to="/contact" className="flex items-center">
                    <Mail className="w-5 h-5 mr-2" />
                    Contact Us
                  </Link>
                </Button>
              </div>

              {/* Quick Links */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
                <div className="bg-slate-800/50 rounded-lg p-6 border border-amber-500/20">
                  <h3 className="text-xl font-bold text-white mb-4">Our Services</h3>
                  <p className="text-slate-300 mb-4">Explore our comprehensive digital marketing solutions.</p>
                  <Button asChild variant="outline" className="border-amber-500 text-amber-400 hover:bg-amber-500 hover:text-white">
                    <Link to="/services">View Services</Link>
                  </Button>
                </div>

                <div className="bg-slate-800/50 rounded-lg p-6 border border-amber-500/20">
                  <h3 className="text-xl font-bold text-white mb-4">Case Studies</h3>
                  <p className="text-slate-300 mb-4">See real results from our international SEO campaigns.</p>
                  <Button asChild variant="outline" className="border-amber-500 text-amber-400 hover:bg-amber-500 hover:text-white">
                    <Link to="/case-studies">View Results</Link>
                  </Button>
                </div>

                <div className="bg-slate-800/50 rounded-lg p-6 border border-amber-500/20">
                  <h3 className="text-xl font-bold text-white mb-4">Get Started</h3>
                  <p className="text-slate-300 mb-4">Ready to dominate your market? Get a free consultation.</p>
                  <Button asChild variant="outline" className="border-amber-500 text-amber-400 hover:bg-amber-500 hover:text-white">
                    <Link to="/quote">Get Quote</Link>
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </section>
      </main>
      
      <Footer />
    </div>
  );
};

export default ComingSoon;
