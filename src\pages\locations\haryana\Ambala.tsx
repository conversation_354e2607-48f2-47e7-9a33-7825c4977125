import React from 'react';
import { Link } from 'react-router-dom';
import { ArrowRight, MapPin, TrendingUp, Users, CheckCircle, Building, Star, Train } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';

const Ambala = () => {
  const services = [
    'SEO Services Ambala',
    'Google Ads Management Ambala',
    'Social Media Marketing Ambala',
    'Local SEO Ambala',
    'E-commerce SEO Ambala',
    'Content Marketing Ambala',
    'Website Development Ambala',
    'Digital Marketing Consulting Ambala'
  ];

  const industries = [
    'Railway Industry Ambala',
    'Scientific Instruments Ambala',
    'Agriculture Ambala',
    'Real Estate Ambala',
    'Healthcare Ambala',
    'Education Ambala',
    'Manufacturing Ambala',
    'Retail & E-commerce Ambala'
  ];

  const areas = [
    'Ambala Cantonment Digital Marketing',
    'Ambala City SEO Services',
    'Mullana Digital Marketing',
    'Shahzadpur SEO Services',
    'Naraingarh Digital Marketing',
    'Barara SEO Services',
    'Saha Digital Marketing',
    'Tangri SEO Services'
  ];

  const stats = [
    {
      metric: '90+',
      description: 'Ambala Businesses Served',
      detail: 'Across all sectors'
    },
    {
      metric: '360%',
      description: 'Average Traffic Increase',
      detail: 'For Ambala clients'
    },
    {
      metric: '₹28L+',
      description: 'Revenue Generated',
      detail: 'For Ambala businesses'
    }
  ];

  return (
    <div className="min-h-screen bg-slate-900 relative">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        {/* Hero Section */}
        <section className="pt-32 pb-20">
          <div className="container mx-auto px-6">
            <div className="max-w-4xl mx-auto text-center">
              <div className="inline-flex items-center space-x-2 bg-emerald-500/20 border border-emerald-500/30 rounded-full px-6 py-2 mb-8">
                <Train className="w-4 h-4 text-emerald-400" />
                <span className="text-emerald-400 font-medium">Ambala Digital Marketing • Railway Junction City</span>
              </div>
              
              <h1 className="text-5xl md:text-6xl font-bold mb-6 leading-tight">
                #1 Digital Marketing Company in
                <span className="bg-gradient-to-r from-emerald-400 to-emerald-600 bg-clip-text text-transparent"> Ambala</span>
              </h1>
              
              <p className="text-xl text-slate-300 mb-12 max-w-3xl mx-auto">
                Leading digital marketing agency in Ambala helping businesses dominate online. From railway industry hubs to scientific instrument manufacturers, we've helped 90+ businesses grow with proven SEO, Google Ads, and digital marketing strategies across all Ambala areas.
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button asChild size="lg" className="bg-gradient-to-r from-emerald-500 to-emerald-600 hover:from-emerald-600 hover:to-emerald-700 text-white text-lg px-8 py-4">
                  <Link to="/quote">Get Free Ambala SEO Audit</Link>
                </Button>
                <Button asChild size="lg" variant="outline" className="border-emerald-500 text-emerald-400 hover:bg-emerald-500 hover:text-white text-lg px-8 py-4">
                  <Link to="/contact">Call Ambala Office</Link>
                </Button>
              </div>
            </div>
          </div>
        </section>

        {/* Stats Section */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Ambala Digital Marketing
                <span className="bg-gradient-to-r from-emerald-400 to-emerald-600 bg-clip-text text-transparent"> Success Stories</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto mb-12">
                Real results from Ambala businesses across industries - from railway contractors to scientific instrument manufacturers in this strategic junction city.
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
              {stats.map((stat, index) => (
                <div key={index} className="text-center">
                  <div className="text-5xl font-bold text-emerald-400 mb-2">{stat.metric}</div>
                  <div className="text-white font-semibold mb-1">{stat.description}</div>
                  <div className="text-slate-400">{stat.detail}</div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Services Section */}
        <section className="py-20">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Digital Marketing Services in
                <span className="bg-gradient-to-r from-emerald-400 to-emerald-600 bg-clip-text text-transparent"> Ambala</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto">
                Comprehensive digital marketing solutions for Ambala businesses looking to dominate online in the Railway Junction City.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {services.map((service, index) => (
                <Card key={index} className="bg-slate-900/80 border-emerald-500/20 hover:border-emerald-500/40 transition-all duration-300">
                  <CardContent className="p-6 text-center">
                    <Building className="w-8 h-8 text-emerald-400 mx-auto mb-4" />
                    <h3 className="text-white font-semibold mb-2">{service}</h3>
                    <Button asChild size="sm" variant="outline" className="border-emerald-500 text-emerald-400 hover:bg-emerald-500 hover:text-white">
                      <Link to="/quote">Learn More</Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Industries Section */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Ambala Industries We
                <span className="bg-gradient-to-r from-emerald-400 to-emerald-600 bg-clip-text text-transparent"> Serve</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto">
                Specialized digital marketing strategies for Ambala's railway-centric and scientific instrument manufacturing industries.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {industries.map((industry, index) => (
                <Card key={index} className="bg-slate-900/80 border-emerald-500/20 hover:border-emerald-500/40 transition-all duration-300">
                  <CardContent className="p-6 text-center">
                    <Star className="w-8 h-8 text-emerald-400 mx-auto mb-4" />
                    <h3 className="text-white font-semibold mb-2">{industry}</h3>
                    <Button asChild size="sm" variant="outline" className="border-emerald-500 text-emerald-400 hover:bg-emerald-500 hover:text-white">
                      <Link to="/quote">Get Strategy</Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Areas Section */}
        <section className="py-20">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Ambala Areas We
                <span className="bg-gradient-to-r from-emerald-400 to-emerald-600 bg-clip-text text-transparent"> Cover</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto">
                Local digital marketing expertise across all major Ambala areas including Cantonment and City divisions.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {areas.map((area, index) => (
                <Card key={index} className="bg-slate-900/80 border-emerald-500/20 hover:border-emerald-500/40 transition-all duration-300">
                  <CardContent className="p-6 text-center">
                    <MapPin className="w-8 h-8 text-emerald-400 mx-auto mb-4" />
                    <h3 className="text-white font-semibold mb-2">{area}</h3>
                    <Button asChild size="sm" variant="outline" className="border-emerald-500 text-emerald-400 hover:bg-emerald-500 hover:text-white">
                      <Link to="/contact">Contact Us</Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Why Choose Us Section */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Why Choose GOD Digital Marketing in
                <span className="bg-gradient-to-r from-emerald-400 to-emerald-600 bg-clip-text text-transparent"> Ambala?</span>
              </h2>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              <Card className="bg-slate-900/80 border-emerald-500/20">
                <CardContent className="p-8">
                  <TrendingUp className="w-12 h-12 text-emerald-400 mb-6" />
                  <h3 className="text-xl font-bold text-white mb-4">Railway Industry Expertise</h3>
                  <p className="text-slate-300">Deep understanding of Ambala's railway industry ecosystem, scientific instrument manufacturing, and strategic location advantages.</p>
                </CardContent>
              </Card>

              <Card className="bg-slate-900/80 border-emerald-500/20">
                <CardContent className="p-8">
                  <Users className="w-12 h-12 text-emerald-400 mb-6" />
                  <h3 className="text-xl font-bold text-white mb-4">Proven Ambala Results</h3>
                  <p className="text-slate-300">90+ successful Ambala campaigns with measurable ROI improvements and business growth across traditional and modern industries.</p>
                </CardContent>
              </Card>

              <Card className="bg-slate-900/80 border-emerald-500/20">
                <CardContent className="p-8">
                  <CheckCircle className="w-12 h-12 text-emerald-400 mb-6" />
                  <h3 className="text-xl font-bold text-white mb-4">Strategic Location Focus</h3>
                  <p className="text-slate-300">Expertise in leveraging Ambala's strategic railway junction position for business growth and market expansion.</p>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 bg-gradient-to-r from-emerald-600 to-emerald-500">
          <div className="container mx-auto px-6 text-center">
            <h2 className="text-4xl md:text-5xl font-bold mb-6">Ready to Dominate Ambala's Digital Market?</h2>
            <p className="text-xl mb-8 max-w-3xl mx-auto">
              Join 90+ Ambala businesses that trust GOD Digital Marketing for their online growth. From railway industry to scientific instruments, we deliver results.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button asChild size="lg" className="bg-white text-emerald-600 hover:bg-slate-100 text-lg px-8 py-4">
                <Link to="/quote">Get Free Ambala SEO Audit</Link>
              </Button>
              <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-emerald-600 text-lg px-8 py-4">
                <Link to="/contact">Call Ambala Office Now</Link>
              </Button>
            </div>
          </div>
        </section>
      </main>
      
      <Footer />
    </div>
  );
};

export default Ambala;
