import React from 'react';
import { Link } from 'react-router-dom';
import { ArrowRight, Code, TrendingUp, Users, CheckCircle, Building, Star, Crown, Smartphone, Globe } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';
import Sidebar from '@/components/Sidebar';

const WebDevelopmentAhmedabad = () => {
  const webServices = [
    'Web Development Services Ahmedabad',
    'Custom Website Development Ahmedabad',
    'E-commerce Development Ahmedabad',
    'Textile Industry Website Development Ahmedabad',
    'Chemical Company Website Development Ahmedabad',
    'Diamond & Jewelry Website Development Ahmedabad',
    'Manufacturing Website Development Ahmedabad',
    'Export Business Website Development Ahmedabad',
    'WordPress Development Ahmedabad',
    'PHP Development Ahmedabad',
    'React Development Ahmedabad',
    'Node.js Development Ahmedabad',
    'Database Development Ahmedabad',
    'API Development Ahmedabad',
    'Mobile App Development Ahmedabad',
    'UI/UX Design Ahmedabad',
    'Website Maintenance Ahmedabad',
    'B2B Website Development Ahmedabad',
    'Industrial Website Development Ahmedabad',
    'Pharmaceutical Website Development Ahmedabad'
  ];

  const developmentTypes = [
    {
      type: 'Textile & Garment Web Solutions',
      description: 'Industry-specific web solutions for Ahmedabad\'s textile capital and garment exporters',
      icon: Building,
      features: ['B2B Textile Portals', 'Fashion E-commerce', 'Export Catalogs', 'Inventory Management']
    },
    {
      type: 'Chemical & Pharmaceutical Web Solutions',
      description: 'Professional web solutions for Gujarat\'s chemical and pharmaceutical industry leaders',
      icon: Code,
      features: ['Compliance-Ready Websites', 'B2B Platforms', 'Product Databases', 'Regulatory Documentation']
    },
    {
      type: 'Diamond & Jewelry Web Platforms',
      description: 'Premium web solutions for Ahmedabad\'s diamond cutting and jewelry manufacturing sector',
      icon: Star,
      features: ['Luxury E-commerce', 'High-Security Platforms', 'Premium Catalogs', 'VIP Client Portals']
    },
    {
      type: 'Manufacturing & Engineering Web Solutions',
      description: 'Industrial web solutions for Ahmedabad\'s engineering and manufacturing companies',
      icon: Globe,
      features: ['Industrial Portals', 'B2B Platforms', 'Equipment Catalogs', 'Export Websites']
    }
  ];

  const webPackages = [
    {
      name: 'Web Development Ahmedabad Starter',
      price: '₹40,000',
      period: '/project',
      description: 'Perfect for small Ahmedabad businesses and textile traders',
      features: [
        'Custom Website Design',
        'Responsive Development',
        'Content Management System',
        'SEO Optimization',
        'Contact Forms',
        'Textile Industry Focus'
      ]
    },
    {
      name: 'Ahmedabad Web Development Professional',
      price: '₹75,000',
      period: '/project',
      description: 'Comprehensive web development for growing Ahmedabad businesses',
      features: [
        'Advanced Custom Development',
        'E-commerce Integration',
        'Database Development',
        'API Integration',
        'Performance Optimization',
        'Security Implementation',
        'Chemical/Pharma Specialization'
      ],
      popular: true
    },
    {
      name: 'Enterprise Web Development Ahmedabad',
      price: '₹1,40,000',
      period: '/project',
      description: 'Advanced web solutions for large Ahmedabad enterprises and exporters',
      features: [
        'Enterprise Architecture',
        'Custom Application Development',
        'Multi-platform Integration',
        'Advanced Security',
        'Scalable Infrastructure',
        'Export Market Features',
        '12 Months Support'
      ]
    }
  ];

  const stats = [
    {
      metric: '320+',
      description: 'Websites Developed',
      detail: 'For Ahmedabad businesses'
    },
    {
      metric: '99.9%',
      description: 'Uptime Guarantee',
      detail: 'Reliable hosting solutions'
    },
    {
      metric: '₹21Cr+',
      description: 'Revenue Generated',
      detail: 'Through web solutions'
    },
    {
      metric: '97%',
      description: 'Client Satisfaction Rate',
      detail: 'Quality delivery'
    }
  ];

  const achievements = [
    'Top Web Development Company in Ahmedabad',
    'Textile Industry Web Leaders',
    'Chemical Sector Web Experts',
    'Diamond Industry Web Specialists',
    'Manufacturing Web Champions',
    'Export Business Web Pioneers'
  ];

  return (
    <div className="min-h-screen bg-slate-900 relative">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        <div className="container mx-auto px-6 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            {/* Main Content */}
            <div className="lg:col-span-3">
              {/* Hero Section */}
              <section className="pt-24 pb-16">
                <div className="max-w-4xl">
                  <div className="inline-flex items-center space-x-2 bg-lime-500/20 border border-lime-500/30 rounded-full px-6 py-2 mb-8">
                    <Code className="w-4 h-4 text-lime-400" />
                    <span className="text-lime-400 font-medium">Web Development Ahmedabad • Gujarat's Commercial Capital Tech Excellence</span>
                  </div>
                  
                  <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
                    #1 Web Development Company in
                    <span className="bg-gradient-to-r from-lime-400 to-lime-600 bg-clip-text text-transparent"> Ahmedabad</span>
                  </h1>
                  
                  <p className="text-xl text-slate-300 mb-8">
                    Premier web development in Ahmedabad offering comprehensive website development and application solutions for textile, chemical, diamond, and manufacturing businesses. Serving 320+ Ahmedabad businesses across all areas - from textile hubs in Narol to chemical zones in Vatva. Expert web development solutions with proven ₹21Cr+ revenue generation and 99.9% uptime guarantee for Ahmedabad clients in Gujarat's commercial capital.
                  </p>
                  
                  <div className="flex flex-col sm:flex-row gap-4 mb-8">
                    <Button asChild size="lg" className="bg-gradient-to-r from-lime-500 to-lime-600 hover:from-lime-600 hover:to-lime-700 text-white text-lg px-8 py-4">
                      <Link to="/quote">Get Free Ahmedabad Web Development Quote</Link>
                    </Button>
                    <Button asChild size="lg" variant="outline" className="border-lime-500 text-lime-400 hover:bg-lime-500 hover:text-white text-lg px-8 py-4">
                      <Link to="/contact">Call Web Developers: +91-8708577598</Link>
                    </Button>
                  </div>

                  {/* Achievements */}
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                    {achievements.map((achievement, index) => (
                      <div key={index} className="flex items-center justify-center space-x-2 bg-slate-800/50 rounded-lg p-3">
                        <CheckCircle className="w-4 h-4 text-lime-400 flex-shrink-0" />
                        <span className="text-slate-300">{achievement}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </section>

              {/* Stats Section */}
              <section className="mb-16 bg-slate-800/50 rounded-2xl p-8">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Ahmedabad Web Development
                    <span className="bg-gradient-to-r from-lime-400 to-lime-600 bg-clip-text text-transparent"> Success Metrics</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Measurable web development results from Ahmedabad businesses across textile, chemical, diamond, and manufacturing sectors.
                  </p>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
                  {stats.map((stat, index) => (
                    <div key={index} className="text-center">
                      <div className="text-4xl font-bold text-lime-400 mb-2">{stat.metric}</div>
                      <div className="text-white font-semibold mb-1">{stat.description}</div>
                      <div className="text-slate-400">{stat.detail}</div>
                    </div>
                  ))}
                </div>
              </section>

              {/* Development Types Section */}
              <section className="mb-16">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Web Solutions We
                    <span className="bg-gradient-to-r from-lime-400 to-lime-600 bg-clip-text text-transparent"> Develop</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Specialized web development solutions designed for Ahmedabad's unique textile, chemical, diamond, and manufacturing landscape.
                  </p>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  {developmentTypes.map((dev, index) => (
                    <Card key={index} className="bg-slate-900/80 border-lime-500/20 hover:border-lime-500/40 transition-all duration-300">
                      <CardContent className="p-8">
                        <div className="w-12 h-12 bg-gradient-to-r from-lime-500 to-lime-600 rounded-lg flex items-center justify-center mb-6">
                          <dev.icon className="w-6 h-6 text-white" />
                        </div>
                        <h3 className="text-xl font-bold text-white mb-4">{dev.type}</h3>
                        <p className="text-slate-300 mb-6">{dev.description}</p>
                        <ul className="space-y-2">
                          {dev.features.map((feature, idx) => (
                            <li key={idx} className="flex items-center text-slate-400 text-sm">
                              <CheckCircle className="w-3 h-3 text-lime-400 mr-2 flex-shrink-0" />
                              {feature}
                            </li>
                          ))}
                        </ul>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>

              {/* Pricing Section */}
              <section className="mb-16">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Ahmedabad Web Development
                    <span className="bg-gradient-to-r from-lime-400 to-lime-600 bg-clip-text text-transparent"> Pricing</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Competitive web development pricing designed for Ahmedabad's textile, chemical, and manufacturing business environment.
                  </p>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                  {webPackages.map((pkg, index) => (
                    <Card key={index} className={`bg-slate-900/80 border-lime-500/20 hover:border-lime-500/40 transition-all duration-300 relative ${pkg.popular ? 'ring-2 ring-lime-500' : ''}`}>
                      {pkg.popular && (
                        <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                          <span className="bg-lime-500 text-white px-4 py-2 rounded-full text-sm font-semibold">Most Popular</span>
                        </div>
                      )}
                      <CardContent className="p-8">
                        <h3 className="text-2xl font-bold text-white mb-2">{pkg.name}</h3>
                        <p className="text-slate-300 mb-6">{pkg.description}</p>
                        <div className="mb-6">
                          <span className="text-4xl font-bold text-lime-400">{pkg.price}</span>
                          <span className="text-slate-400">{pkg.period}</span>
                        </div>
                        <ul className="space-y-3 mb-8">
                          {pkg.features.map((feature, idx) => (
                            <li key={idx} className="flex items-center text-slate-300">
                              <CheckCircle className="w-4 h-4 text-lime-400 mr-3 flex-shrink-0" />
                              {feature}
                            </li>
                          ))}
                        </ul>
                        <Button asChild className={`w-full ${pkg.popular ? 'bg-gradient-to-r from-lime-500 to-lime-600 hover:from-lime-600 hover:to-lime-700' : 'bg-slate-700 hover:bg-slate-600'} text-white`}>
                          <Link to="/quote">Get Started</Link>
                        </Button>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>

              {/* CTA Section */}
              <section className="bg-gradient-to-r from-lime-600 to-lime-500 rounded-2xl p-8 text-center">
                <h2 className="text-3xl md:text-5xl font-bold mb-6">Ready to Build Your Ahmedabad Web Presence?</h2>
                <p className="text-xl mb-8">
                  Join 320+ Ahmedabad businesses that trust GOD Digital Marketing for web development success. Proven solutions that deliver 99.9% uptime and ₹21Cr+ revenue generation.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button asChild size="lg" className="bg-white text-lime-600 hover:bg-slate-100 text-lg px-8 py-4">
                    <Link to="/quote">Get Free Development Quote</Link>
                  </Button>
                  <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-lime-600 text-lg px-8 py-4">
                    <Link to="/contact">Call Web Developers: +91-8708577598</Link>
                  </Button>
                </div>
              </section>
            </div>

            {/* Sidebar */}
            <div className="lg:col-span-1">
              <Sidebar currentService="web-development" currentLocation="ahmedabad" />
            </div>
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
};

export default WebDevelopmentAhmedabad;
