import React from 'react';
import { Link } from 'react-router-dom';
import { Scale, Search, TrendingUp, Users, CheckCircle, Building, Star, Crown, Target, Zap, ArrowRight } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';
import Sidebar from '@/components/Sidebar';

const LegalServicesSeo = () => {
  const stats = [
    {
      metric: '850+',
      description: 'Legal Practices Optimized',
      detail: 'Across all legal specializations'
    },
    {
      metric: '4,100%',
      description: 'Average Client Growth',
      detail: 'For legal service providers'
    },
    {
      metric: '₹380Cr+',
      description: 'Legal Revenue Generated',
      detail: 'Through SEO campaigns'
    },
    {
      metric: '97%',
      description: 'Legal Keywords Ranking',
      detail: 'Top 10 positions achieved'
    }
  ];

  const achievements = [
    'Top Legal Services SEO Company in India',
    'Law Firm SEO Specialists',
    'Legal Practice SEO Experts',
    'Attorney Marketing SEO Leaders',
    'Legal Consultation SEO Champions',
    'Court Case SEO Pioneers'
  ];

  const legalSpecializations = [
    {
      type: 'Law Firm & Legal Practice SEO',
      description: 'Comprehensive SEO for law firms, legal practices, and attorney offices',
      icon: Building,
      features: ['Law Firm SEO', 'Legal Practice SEO', 'Attorney Office SEO', 'Legal Consultation SEO', 'Lawyer Directory SEO', 'Legal Services SEO']
    },
    {
      type: 'Specialized Legal Services SEO',
      description: 'Advanced SEO for specialized legal services and niche legal practices',
      icon: Star,
      features: ['Corporate Law SEO', 'Criminal Law SEO', 'Family Law SEO', 'Property Law SEO', 'Tax Law SEO', 'Immigration Law SEO']
    },
    {
      type: 'Legal Technology & Platform SEO',
      description: 'Strategic SEO for legal technology platforms and online legal services',
      icon: Crown,
      features: ['Legal Tech Platform SEO', 'Online Legal Services SEO', 'Legal Software SEO', 'Legal Marketplace SEO', 'Legal Document SEO', 'Legal Consultation Platform SEO']
    },
    {
      type: 'Legal Education & Training SEO',
      description: 'Specialized SEO for legal education institutions and professional training',
      icon: Target,
      features: ['Law School SEO', 'Legal Training SEO', 'Legal Education SEO', 'Bar Exam Preparation SEO', 'Legal Certification SEO', 'Legal Course SEO']
    }
  ];

  const legalSectors = [
    { name: 'Law Firms', clients: '280+', growth: '4,200%' },
    { name: 'Corporate Legal', clients: '180+', growth: '3,800%' },
    { name: 'Criminal Law', clients: '150+', growth: '4,500%' },
    { name: 'Family Law', clients: '120+', growth: '3,900%' },
    { name: 'Legal Tech', clients: '80+', growth: '5,100%' },
    { name: 'Legal Education', clients: '60+', growth: '3,600%' }
  ];

  const caseStudies = [
    {
      client: 'Leading Corporate Law Firm',
      industry: 'Corporate Legal Services',
      challenge: 'Law firm needed to establish digital presence and attract high-value corporate clients',
      result: '4,500% client inquiry increase',
      metrics: ['1,800+ legal keywords in top 3', '₹180Cr+ legal revenue', '720% increase in corporate clients']
    },
    {
      client: 'Criminal Defense Practice',
      industry: 'Criminal Law Services',
      challenge: 'Criminal law practice needed to compete with established legal firms online',
      result: '4,200% case inquiry growth',
      metrics: ['1,200+ criminal law keywords ranking', '₹95Cr+ legal fees revenue', '580% increase in case consultations']
    },
    {
      client: 'Legal Technology Platform',
      industry: 'Legal Tech Services',
      challenge: 'Legal tech startup needed to establish market presence in competitive legal industry',
      result: '5,100% platform usage increase',
      metrics: ['950+ legal tech keywords in top 5', '₹65Cr+ platform revenue', '420% increase in legal professional users']
    }
  ];

  const legalSeoStrategies = [
    {
      strategy: 'Local Legal SEO',
      description: 'Dominate local search for law firms and legal services in specific jurisdictions',
      benefits: ['Local legal visibility', 'Jurisdiction targeting', 'Local client acquisition', 'Geographic legal authority']
    },
    {
      strategy: 'Practice Area SEO',
      description: 'Target specific legal practice areas and specializations',
      benefits: ['Practice area authority', 'Legal specialization ranking', 'Expert positioning', 'Niche legal targeting']
    },
    {
      strategy: 'Legal Content SEO',
      description: 'Create authoritative legal content and thought leadership',
      benefits: ['Legal authority building', 'Expert content ranking', 'Legal knowledge showcase', 'Professional credibility']
    },
    {
      strategy: 'Legal Compliance SEO',
      description: 'Ensure SEO strategies comply with legal advertising regulations',
      benefits: ['Regulatory compliance', 'Ethical marketing', 'Professional standards', 'Legal advertising compliance']
    }
  ];

  return (
    <div className="min-h-screen bg-slate-900 relative">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        <div className="container mx-auto px-6 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            <div className="lg:col-span-3">
              <section className="pt-24 pb-16">
                <div className="max-w-4xl">
                  <div className="inline-flex items-center space-x-2 bg-slate-500/20 border border-slate-500/30 rounded-full px-6 py-2 mb-8">
                    <Scale className="w-4 h-4 text-slate-400" />
                    <span className="text-slate-400 font-medium">Legal Services SEO • Justice Optimization Excellence</span>
                  </div>
                  
                  <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
                    #1 Legal Services SEO Company in
                    <span className="bg-gradient-to-r from-slate-400 to-slate-600 bg-clip-text text-transparent"> India</span>
                  </h1>
                  
                  <p className="text-xl text-slate-300 mb-8">
                    Premier legal services SEO offering comprehensive search engine optimization solutions for law firms, legal practices, attorneys, and legal technology platforms. Our legal SEO company provides professional SEO services with law firm SEO optimization, legal practice SEO, attorney marketing SEO, and legal consultation SEO. Serving 850+ legal practices across all legal specializations with proven ₹380Cr+ revenue generation and 4,100% average client growth for legal service providers through strategic search engine optimization and legal digital marketing excellence.
                  </p>
                  
                  <div className="flex flex-col sm:flex-row gap-4 mb-8">
                    <Button asChild size="lg" className="bg-gradient-to-r from-slate-500 to-slate-600 hover:from-slate-600 hover:to-slate-700 text-white text-lg px-8 py-4">
                      <Link to="/quote">Get Free Legal Services SEO Audit</Link>
                    </Button>
                    <Button asChild size="lg" variant="outline" className="border-slate-500 text-slate-400 hover:bg-slate-500 hover:text-white text-lg px-8 py-4">
                      <Link to="/contact">Call Legal SEO Experts: +91-**********</Link>
                    </Button>
                  </div>

                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                    {achievements.map((achievement, index) => (
                      <div key={index} className="flex items-center justify-center space-x-2 bg-slate-800/50 rounded-lg p-3">
                        <CheckCircle className="w-4 h-4 text-slate-400 flex-shrink-0" />
                        <span className="text-slate-300">{achievement}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </section>

              <section className="mb-16 bg-slate-800/50 rounded-2xl p-8">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Legal Services SEO
                    <span className="bg-gradient-to-r from-slate-400 to-slate-600 bg-clip-text text-transparent"> Success Metrics</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Measurable legal services SEO results from practices across all legal specializations and jurisdictions.
                  </p>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
                  {stats.map((stat, index) => (
                    <div key={index} className="text-center">
                      <div className="text-4xl font-bold text-slate-400 mb-2">{stat.metric}</div>
                      <div className="text-white font-semibold mb-1">{stat.description}</div>
                      <div className="text-slate-400">{stat.detail}</div>
                    </div>
                  ))}
                </div>
              </section>

              <section className="mb-16">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Legal Services SEO Strategies We
                    <span className="bg-gradient-to-r from-slate-400 to-slate-600 bg-clip-text text-transparent"> Master</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Specialized SEO strategies designed for legal success across all practice areas and legal platforms.
                  </p>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  {legalSpecializations.map((spec, index) => (
                    <Card key={index} className="bg-slate-900/80 border-slate-500/20 hover:border-slate-500/40 transition-all duration-300">
                      <CardContent className="p-8">
                        <div className="w-12 h-12 bg-gradient-to-r from-slate-500 to-slate-600 rounded-lg flex items-center justify-center mb-6">
                          <spec.icon className="w-6 h-6 text-white" />
                        </div>
                        <h3 className="text-xl font-bold text-white mb-4">{spec.type}</h3>
                        <p className="text-slate-300 mb-6">{spec.description}</p>
                        <ul className="space-y-2">
                          {spec.features.map((feature, idx) => (
                            <li key={idx} className="flex items-center text-slate-400 text-sm">
                              <CheckCircle className="w-3 h-3 text-slate-400 mr-2 flex-shrink-0" />
                              {feature}
                            </li>
                          ))}
                        </ul>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>

              {/* Legal Sectors */}
              <section className="mb-16 bg-slate-800/30 rounded-2xl p-8">
                <h3 className="text-2xl font-bold text-white mb-6 text-center">
                  Legal Industry Sector Expertise
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-6">
                  {legalSectors.map((sector, index) => (
                    <div key={index} className="text-center bg-slate-900/50 rounded-lg p-4">
                      <h4 className="text-slate-400 font-semibold mb-2">{sector.name}</h4>
                      <div className="text-2xl font-bold text-white mb-1">{sector.clients}</div>
                      <div className="text-slate-400 text-sm mb-2">Practices Served</div>
                      <div className="text-green-400 font-semibold text-sm">{sector.growth} avg growth</div>
                    </div>
                  ))}
                </div>
              </section>

              {/* Legal SEO Strategies */}
              <section className="mb-16 bg-slate-800/30 rounded-2xl p-8">
                <h3 className="text-2xl font-bold text-white mb-6 text-center">
                  Advanced Legal Services SEO Strategies
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                  {legalSeoStrategies.map((strategy, index) => (
                    <div key={index} className="bg-slate-900/50 rounded-lg p-6">
                      <h4 className="text-slate-400 font-semibold mb-3">{strategy.strategy}</h4>
                      <p className="text-slate-300 text-sm mb-4">{strategy.description}</p>
                      <ul className="space-y-1">
                        {strategy.benefits.map((benefit, idx) => (
                          <li key={idx} className="text-slate-400 text-xs flex items-center">
                            <CheckCircle className="w-3 h-3 text-slate-400 mr-2 flex-shrink-0" />
                            {benefit}
                          </li>
                        ))}
                      </ul>
                    </div>
                  ))}
                </div>
              </section>

              {/* Case Studies */}
              <section className="mb-16">
                <h3 className="text-2xl font-bold text-white mb-8 text-center">
                  Legal Services SEO Success Stories
                </h3>
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                  {caseStudies.map((study, index) => (
                    <Card key={index} className="bg-slate-900/60 border-slate-500/20 hover:border-slate-500/40 transition-all duration-300">
                      <CardContent className="p-6">
                        <h4 className="text-slate-400 font-semibold mb-2">{study.client}</h4>
                        <p className="text-slate-400 text-sm mb-3">{study.industry}</p>
                        <p className="text-slate-300 text-sm mb-4">{study.challenge}</p>
                        <div className="bg-slate-800/50 rounded p-3 mb-4">
                          <div className="text-green-400 font-semibold mb-2">{study.result}</div>
                          <ul className="space-y-1">
                            {study.metrics.map((metric, idx) => (
                              <li key={idx} className="text-slate-400 text-xs flex items-center">
                                <CheckCircle className="w-3 h-3 text-slate-400 mr-2 flex-shrink-0" />
                                {metric}
                              </li>
                            ))}
                          </ul>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>

              {/* Internal Linking Section */}
              <section className="mb-16 bg-slate-800/30 rounded-2xl p-8">
                <h3 className="text-2xl font-bold text-white mb-6 text-center">
                  Complete Legal Services Digital Marketing Solutions
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  <Link to="/services/ppc" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-slate-400 font-semibold mb-2 group-hover:text-slate-300">Legal Services Google Ads</h4>
                    <p className="text-slate-400 text-sm">PPC advertising for law firms and legal practices</p>
                  </Link>
                  <Link to="/services/social-media" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-slate-400 font-semibold mb-2 group-hover:text-slate-300">Legal Social Media Marketing</h4>
                    <p className="text-slate-400 text-sm">Professional social media marketing for legal services</p>
                  </Link>
                  <Link to="/services/content" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-slate-400 font-semibold mb-2 group-hover:text-slate-300">Legal Content Marketing</h4>
                    <p className="text-slate-400 text-sm">Legal content creation and thought leadership</p>
                  </Link>
                  <Link to="/services/email" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-slate-400 font-semibold mb-2 group-hover:text-slate-300">Legal Email Marketing</h4>
                    <p className="text-slate-400 text-sm">Client communication and legal newsletter campaigns</p>
                  </Link>
                  <Link to="/services/web-development" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-slate-400 font-semibold mb-2 group-hover:text-slate-300">Legal Website Development</h4>
                    <p className="text-slate-400 text-sm">Professional law firm websites and legal platform development</p>
                  </Link>
                  <Link to="/portfolio" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-slate-400 font-semibold mb-2 group-hover:text-slate-300">Legal Services Success Stories</h4>
                    <p className="text-slate-400 text-sm">See proven results from our legal services clients</p>
                  </Link>
                </div>
              </section>

              <section className="bg-gradient-to-r from-slate-600 to-slate-500 rounded-2xl p-8 text-center">
                <h2 className="text-3xl md:text-5xl font-bold mb-6">Ready to Scale Your Legal Practice?</h2>
                <p className="text-xl mb-8">
                  Join 850+ legal practices that trust GOD Digital Marketing for legal SEO success. Proven strategies that deliver 4,100% client growth and ₹380Cr+ revenue generation.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button asChild size="lg" className="bg-white text-slate-600 hover:bg-slate-100 text-lg px-8 py-4">
                    <Link to="/quote">Get Free Legal Services SEO Audit</Link>
                  </Button>
                  <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-slate-600 text-lg px-8 py-4">
                    <Link to="/contact">Call Legal SEO Experts: +91-**********</Link>
                  </Button>
                </div>
              </section>
            </div>

            <div className="lg:col-span-1">
              <Sidebar currentService="seo" />
            </div>
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
};

export default LegalServicesSeo;
