import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { ArrowR<PERSON>, Cog, TrendingUp, Users, CheckCircle, Zap, Star, Settings } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';

const BusinessAutomationServices = () => {
  const features = [
    'Customer Relationship Management (CRM) Automation',
    'Sales Process Automation & Lead Nurturing',
    'Invoice & Payment Processing Automation',
    'Inventory Management & Order Processing',
    'Employee Onboarding & HR Process Automation',
    'Social Media Posting & Content Automation',
    'Email Marketing & Follow-up Automation',
    'Data Entry & Report Generation Automation',
    'Customer Support & Chatbot Integration',
    'Workflow Optimization & Process Mapping'
  ];

  const packages = [
    {
      name: 'Automation Starter',
      price: '₹40,000',
      period: '/month',
      description: 'Perfect for small businesses starting automation journey',
      features: [
        'Basic Process Automation Setup',
        'CRM Integration & Configuration',
        'Email Marketing Automation',
        'Simple Workflow Creation',
        'Monthly Automation Reports'
      ]
    },
    {
      name: 'Automation Pro',
      price: '₹70,000',
      period: '/month',
      description: 'Comprehensive automation for growing businesses',
      features: [
        'Everything in Starter',
        'Advanced Workflow Automation',
        'Multi-Platform Integration',
        'Custom Automation Development',
        'Sales Process Automation',
        'Weekly Optimization Reviews'
      ],
      popular: true
    },
    {
      name: 'Automation Enterprise',
      price: '₹120,000',
      period: '/month',
      description: 'Enterprise automation for large-scale operations',
      features: [
        'Everything in Pro',
        'Custom Software Development',
        'API Integration & Development',
        'Advanced Analytics & Reporting',
        'Dedicated Automation Specialist',
        '24/7 Automation Support'
      ]
    }
  ];

  const results = [
    {
      metric: '70%',
      description: 'Time savings achieved',
      timeframe: 'through automation'
    },
    {
      metric: '300%',
      description: 'Productivity increase',
      timeframe: 'for automated processes'
    },
    {
      metric: '₹50L+',
      description: 'Cost savings generated',
      timeframe: 'through automation'
    }
  ];

  const automationTypes = [
    {
      type: 'Sales Automation',
      description: 'Automate lead generation, nurturing, and conversion processes',
      icon: TrendingUp,
      benefits: ['Lead scoring', 'Follow-up sequences', 'Pipeline management', 'Sales reporting']
    },
    {
      type: 'Marketing Automation',
      description: 'Streamline marketing campaigns and customer communication',
      icon: Zap,
      benefits: ['Email campaigns', 'Social media posting', 'Content distribution', 'Analytics tracking']
    },
    {
      type: 'Operations Automation',
      description: 'Optimize business operations and administrative tasks',
      icon: Settings,
      benefits: ['Invoice processing', 'Inventory management', 'HR workflows', 'Data management']
    }
  ];

  return (
    <div className="min-h-screen bg-slate-900 relative">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        {/* Hero Section */}
        <section className="pt-32 pb-20">
          <div className="container mx-auto px-6">
            <div className="max-w-4xl mx-auto text-center">
              <div className="inline-flex items-center space-x-2 bg-purple-500/20 border border-purple-500/30 rounded-full px-6 py-2 mb-8">
                <Cog className="w-4 h-4 text-purple-400" />
                <span className="text-purple-400 font-medium">Business Automation • AI-Powered Efficiency</span>
              </div>
              
              <h1 className="text-5xl md:text-6xl font-bold mb-6 leading-tight">
                Business Automation That
                <span className="bg-gradient-to-r from-purple-400 to-purple-600 bg-clip-text text-transparent"> Transforms Operations</span>
              </h1>
              
              <p className="text-xl text-slate-300 mb-12 max-w-3xl mx-auto">
                Expert business automation services that streamline operations, reduce costs, and boost productivity. With AI-powered solutions and 7 years of international experience, we've saved ₹50L+ in operational costs for businesses across multiple countries.
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button asChild size="lg" className="bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white text-lg px-8 py-4">
                  <Link to="/quote">Get Automation Strategy</Link>
                </Button>
                <Button asChild size="lg" variant="outline" className="border-purple-500 text-purple-400 hover:bg-purple-500 hover:text-white text-lg px-8 py-4">
                  <Link to="/case-studies">View Automation Results</Link>
                </Button>
              </div>
            </div>
          </div>
        </section>

        {/* Results Section */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Business Automation
                <span className="bg-gradient-to-r from-purple-400 to-purple-600 bg-clip-text text-transparent"> Impact Results</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto mb-12">
                Real efficiency gains and cost savings from business automation implementations across industries.
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
              {results.map((result, index) => (
                <div key={index} className="text-center">
                  <div className="text-5xl font-bold text-purple-400 mb-2">{result.metric}</div>
                  <div className="text-white font-semibold mb-1">{result.description}</div>
                  <div className="text-slate-400">{result.timeframe}</div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Automation Types Section */}
        <section className="py-20">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Automation Solutions We
                <span className="bg-gradient-to-r from-purple-400 to-purple-600 bg-clip-text text-transparent"> Implement</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto">
                Comprehensive business automation solutions that transform how your business operates.
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-20">
              {automationTypes.map((automation, index) => (
                <Card key={index} className="bg-slate-900/80 border-purple-500/20 hover:border-purple-500/40 transition-all duration-300">
                  <CardContent className="p-8">
                    <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg flex items-center justify-center mb-6">
                      <automation.icon className="w-6 h-6 text-white" />
                    </div>
                    <h3 className="text-xl font-bold text-white mb-4">{automation.type}</h3>
                    <p className="text-slate-300 mb-6">{automation.description}</p>
                    <ul className="space-y-2">
                      {automation.benefits.map((benefit, idx) => (
                        <li key={idx} className="flex items-center text-slate-400 text-sm">
                          <CheckCircle className="w-3 h-3 text-purple-400 mr-2 flex-shrink-0" />
                          {benefit}
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Features Section */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Business Automation
                <span className="bg-gradient-to-r from-purple-400 to-purple-600 bg-clip-text text-transparent"> Features</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto">
                Complete business automation services that streamline operations and drive efficiency.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-w-4xl mx-auto">
              {features.map((feature, index) => (
                <div key={index} className="flex items-center space-x-3">
                  <CheckCircle className="w-6 h-6 text-purple-400 flex-shrink-0" />
                  <span className="text-slate-300">{feature}</span>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Pricing Section */}
        <section className="py-20">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Business Automation
                <span className="bg-gradient-to-r from-purple-400 to-purple-600 bg-clip-text text-transparent"> Investment</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto">
                Choose the perfect automation package to transform your business operations and boost efficiency.
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
              {packages.map((pkg, index) => (
                <Card key={index} className={`bg-slate-900/80 border-purple-500/20 hover:border-purple-500/40 transition-all duration-300 relative ${pkg.popular ? 'ring-2 ring-purple-500' : ''}`}>
                  {pkg.popular && (
                    <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                      <span className="bg-purple-500 text-white px-4 py-2 rounded-full text-sm font-semibold">Most Popular</span>
                    </div>
                  )}
                  <CardContent className="p-8">
                    <h3 className="text-2xl font-bold text-white mb-2">{pkg.name}</h3>
                    <p className="text-slate-300 mb-6">{pkg.description}</p>
                    <div className="mb-6">
                      <span className="text-4xl font-bold text-purple-400">{pkg.price}</span>
                      <span className="text-slate-400">{pkg.period}</span>
                    </div>
                    <ul className="space-y-3 mb-8">
                      {pkg.features.map((feature, idx) => (
                        <li key={idx} className="flex items-center text-slate-300">
                          <CheckCircle className="w-4 h-4 text-purple-400 mr-3 flex-shrink-0" />
                          {feature}
                        </li>
                      ))}
                    </ul>
                    <Button asChild className={`w-full ${pkg.popular ? 'bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700' : 'bg-slate-700 hover:bg-slate-600'} text-white`}>
                      <Link to="/quote">Get Started</Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 bg-gradient-to-r from-purple-600 to-purple-500">
          <div className="container mx-auto px-6 text-center">
            <h2 className="text-4xl md:text-5xl font-bold mb-6">Ready to Automate Your Business?</h2>
            <p className="text-xl mb-8 max-w-3xl mx-auto">
              Join businesses saving ₹50L+ and achieving 70% time savings through strategic business automation. Transform your operations with AI-powered efficiency.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button asChild size="lg" className="bg-white text-purple-600 hover:bg-slate-100 text-lg px-8 py-4">
                <Link to="/quote">Get Automation Strategy</Link>
              </Button>
              <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-purple-600 text-lg px-8 py-4">
                <Link to="/contact">Book Automation Consultation</Link>
              </Button>
            </div>
          </div>
        </section>
      </main>
      
      <Footer />
    </div>
  );
};

export default BusinessAutomationServices;
