import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { ArrowRight, Search, TrendingUp, Users, CheckCircle, Building, Star, Crown, Target, Zap } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';

const SeoServicesDelhi = () => {
  const seoServices = [
    'Local SEO Services Delhi NCR',
    'International SEO Delhi',
    'E-commerce SEO Delhi',
    'Technical SEO Audit Delhi',
    'Enterprise SEO Solutions Delhi',
    'Mobile SEO Optimization Delhi',
    'Voice Search SEO Delhi',
    'Video SEO Services Delhi',
    'Image SEO Optimization Delhi',
    'Schema Markup Implementation Delhi',
    'Core Web Vitals Optimization Delhi',
    'Page Speed Optimization Delhi',
    'SEO Content Strategy Delhi',
    'Keyword Research & Analysis Delhi',
    'Competitor SEO Analysis Delhi',
    'SEO Link Building Delhi',
    'Local Citation Building Delhi',
    'Google My Business Optimization Delhi',
    'Multi-location SEO Delhi',
    'Franchise SEO Services Delhi'
  ];

  const delhiAreas = [
    'Central Delhi SEO Services',
    'South Delhi SEO Company',
    'North Delhi SEO Agency',
    'East Delhi SEO Services',
    'West Delhi SEO Company',
    'New Delhi SEO Services',
    'Connaught Place SEO',
    'Karol Bagh SEO Services',
    'Lajpat Nagar SEO Company',
    'Khan Market SEO Services',
    'Nehru Place SEO Agency',
    'Saket SEO Services',
    'Greater Kailash SEO',
    'Dwarka SEO Company',
    'Rohini SEO Services',
    'Janakpuri SEO Agency'
  ];

  const industries = [
    'Government Sector SEO Delhi',
    'Healthcare SEO Delhi',
    'Education SEO Delhi',
    'Real Estate SEO Delhi',
    'E-commerce SEO Delhi',
    'IT Services SEO Delhi',
    'Financial Services SEO Delhi',
    'Legal Services SEO Delhi',
    'Hospitality SEO Delhi',
    'Manufacturing SEO Delhi',
    'Retail SEO Delhi',
    'Automotive SEO Delhi',
    'Fashion & Lifestyle SEO Delhi',
    'Food & Restaurant SEO Delhi',
    'Travel & Tourism SEO Delhi',
    'Media & Entertainment SEO Delhi'
  ];

  const seoPackages = [
    {
      name: 'Local SEO Delhi Starter',
      price: '₹25,000',
      period: '/month',
      description: 'Perfect for small Delhi businesses targeting local customers',
      features: [
        'Google My Business Optimization',
        'Local Keyword Research (50 keywords)',
        'On-Page SEO (10 pages)',
        'Local Citation Building (25 citations)',
        'Monthly Local SEO Report',
        'Delhi Area Targeting'
      ]
    },
    {
      name: 'Delhi SEO Professional',
      price: '₹45,000',
      period: '/month',
      description: 'Comprehensive SEO for growing Delhi businesses',
      features: [
        'Everything in Starter',
        'Advanced Keyword Research (150 keywords)',
        'Technical SEO Audit & Fixes',
        'Content Marketing Strategy',
        'Link Building Campaign',
        'Competitor Analysis',
        'Multi-location SEO Setup'
      ],
      popular: true
    },
    {
      name: 'Enterprise SEO Delhi',
      price: '₹85,000',
      period: '/month',
      description: 'Advanced SEO for large Delhi enterprises',
      features: [
        'Everything in Professional',
        'Enterprise-level SEO Strategy',
        'Advanced Technical SEO',
        'International SEO Setup',
        'Custom SEO Dashboard',
        'Dedicated SEO Manager',
        'Priority Support'
      ]
    }
  ];

  const seoProcess = [
    {
      step: '1',
      title: 'Delhi Market SEO Audit',
      description: 'Comprehensive analysis of your website\'s SEO performance in Delhi market with competitor research',
      icon: Search
    },
    {
      step: '2',
      title: 'Delhi Keyword Strategy',
      description: 'Strategic keyword research targeting Delhi customers with local search intent analysis',
      icon: Target
    },
    {
      step: '3',
      title: 'Technical SEO Implementation',
      description: 'Advanced technical SEO optimization for better search engine crawling and indexing',
      icon: Zap
    },
    {
      step: '4',
      title: 'Content & Link Building',
      description: 'High-quality content creation and authoritative link building for Delhi market dominance',
      icon: TrendingUp
    }
  ];

  const stats = [
    {
      metric: '1500+',
      description: 'Delhi Websites Optimized',
      detail: 'Across all industries'
    },
    {
      metric: '650%',
      description: 'Average Organic Traffic Increase',
      detail: 'For Delhi businesses'
    },
    {
      metric: '₹35Cr+',
      description: 'Revenue Generated Through SEO',
      detail: 'For Delhi clients'
    },
    {
      metric: '92%',
      description: 'First Page Rankings Achieved',
      detail: 'For target keywords'
    }
  ];

  const achievements = [
    'Google Premier Partner for Delhi SEO',
    'Top SEO Company in Delhi NCR',
    'Certified SEO Specialists',
    'Delhi Local SEO Experts',
    'Enterprise SEO Solutions Provider',
    'International SEO Consultants'
  ];

  return (
    <div className="min-h-screen bg-slate-900 relative">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        {/* Hero Section */}
        <section className="pt-32 pb-20">
          <div className="container mx-auto px-6">
            <div className="max-w-5xl mx-auto text-center">
              <div className="inline-flex items-center space-x-2 bg-blue-500/20 border border-blue-500/30 rounded-full px-6 py-2 mb-8">
                <Search className="w-4 h-4 text-blue-400" />
                <span className="text-blue-400 font-medium">SEO Services Delhi • Search Engine Optimization Excellence</span>
              </div>
              
              <h1 className="text-5xl md:text-7xl font-bold mb-6 leading-tight">
                #1 SEO Services Company in
                <span className="bg-gradient-to-r from-blue-400 to-blue-600 bg-clip-text text-transparent"> Delhi NCR</span>
              </h1>
              
              <p className="text-xl text-slate-300 mb-12 max-w-4xl mx-auto">
                Premier SEO services in Delhi offering comprehensive search engine optimization solutions including local SEO, technical SEO, on-page SEO, off-page SEO, and enterprise SEO strategies. Our Delhi SEO company provides professional SEO services with keyword research, content optimization, link building, and SEO audits. Serving 1500+ Delhi businesses across all districts - from government sector enterprises in Central Delhi to tech startups in Gurgaon. Expert SEO solutions with proven ₹35Cr+ revenue generation and 650% average organic traffic increase for Delhi clients through strategic search engine optimization and digital marketing excellence.
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
                <Button asChild size="lg" className="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white text-lg px-8 py-4">
                  <Link to="/quote">Get Free Delhi SEO Audit</Link>
                </Button>
                <Button asChild size="lg" variant="outline" className="border-blue-500 text-blue-400 hover:bg-blue-500 hover:text-white text-lg px-8 py-4">
                  <Link to="/contact">Call Delhi SEO Experts: +91-8708577598</Link>
                </Button>
              </div>

              {/* Achievements */}
              <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                {achievements.map((achievement, index) => (
                  <div key={index} className="flex items-center justify-center space-x-2 bg-slate-800/50 rounded-lg p-3">
                    <CheckCircle className="w-4 h-4 text-blue-400 flex-shrink-0" />
                    <span className="text-slate-300">{achievement}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* Stats Section */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-6xl font-bold mb-6">
                Delhi SEO Services
                <span className="bg-gradient-to-r from-blue-400 to-blue-600 bg-clip-text text-transparent"> Success Metrics</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-4xl mx-auto mb-12">
                Measurable SEO results from Delhi businesses across all industries - from local businesses to enterprise corporations in India's capital region.
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-4 gap-8 mb-16">
              {stats.map((stat, index) => (
                <div key={index} className="text-center">
                  <div className="text-5xl font-bold text-blue-400 mb-2">{stat.metric}</div>
                  <div className="text-white font-semibold mb-1">{stat.description}</div>
                  <div className="text-slate-400">{stat.detail}</div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* SEO Process Section */}
        <section className="py-20">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-6xl font-bold mb-6">
                Our Delhi SEO
                <span className="bg-gradient-to-r from-blue-400 to-blue-600 bg-clip-text text-transparent"> Process</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-4xl mx-auto">
                Proven 4-step SEO methodology specifically designed for Delhi market dynamics and search behavior.
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-4 gap-8 mb-20">
              {seoProcess.map((process, index) => (
                <Card key={index} className="bg-slate-900/80 border-blue-500/20 hover:border-blue-500/40 transition-all duration-300">
                  <CardContent className="p-8 text-center">
                    <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-6">
                      <process.icon className="w-8 h-8 text-white" />
                    </div>
                    <div className="text-3xl font-bold text-blue-400 mb-4">Step {process.step}</div>
                    <h3 className="text-xl font-bold text-white mb-4">{process.title}</h3>
                    <p className="text-slate-300">{process.description}</p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* SEO Services Grid */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-6xl font-bold mb-6">
                Complete SEO Services
                <span className="bg-gradient-to-r from-blue-400 to-blue-600 bg-clip-text text-transparent"> Available in Delhi</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-4xl mx-auto">
                Comprehensive range of SEO services covering every aspect of search engine optimization for Delhi businesses.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {seoServices.map((service, index) => (
                <Card key={index} className="bg-slate-900/80 border-blue-500/20 hover:border-blue-500/40 transition-all duration-300">
                  <CardContent className="p-6 text-center">
                    <Search className="w-8 h-8 text-blue-400 mx-auto mb-4" />
                    <h3 className="text-white font-semibold mb-2 text-sm">{service}</h3>
                    <Button asChild size="sm" variant="outline" className="border-blue-500 text-blue-400 hover:bg-blue-500 hover:text-white text-xs">
                      <Link to="/quote">Get Quote</Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Pricing Section */}
        <section className="py-20">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-6xl font-bold mb-6">
                Delhi SEO Services
                <span className="bg-gradient-to-r from-blue-400 to-blue-600 bg-clip-text text-transparent"> Pricing</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-4xl mx-auto">
                Transparent SEO pricing packages designed for Delhi businesses of all sizes.
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
              {seoPackages.map((pkg, index) => (
                <Card key={index} className={`bg-slate-900/80 border-blue-500/20 hover:border-blue-500/40 transition-all duration-300 relative ${pkg.popular ? 'ring-2 ring-blue-500' : ''}`}>
                  {pkg.popular && (
                    <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                      <span className="bg-blue-500 text-white px-4 py-2 rounded-full text-sm font-semibold">Most Popular</span>
                    </div>
                  )}
                  <CardContent className="p-8">
                    <h3 className="text-2xl font-bold text-white mb-2">{pkg.name}</h3>
                    <p className="text-slate-300 mb-6">{pkg.description}</p>
                    <div className="mb-6">
                      <span className="text-4xl font-bold text-blue-400">{pkg.price}</span>
                      <span className="text-slate-400">{pkg.period}</span>
                    </div>
                    <ul className="space-y-3 mb-8">
                      {pkg.features.map((feature, idx) => (
                        <li key={idx} className="flex items-center text-slate-300">
                          <CheckCircle className="w-4 h-4 text-blue-400 mr-3 flex-shrink-0" />
                          {feature}
                        </li>
                      ))}
                    </ul>
                    <Button asChild className={`w-full ${pkg.popular ? 'bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700' : 'bg-slate-700 hover:bg-slate-600'} text-white`}>
                      <Link to="/quote">Get Started</Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 bg-gradient-to-r from-blue-600 to-blue-500">
          <div className="container mx-auto px-6 text-center">
            <h2 className="text-4xl md:text-6xl font-bold mb-6">Ready to Dominate Delhi Search Results?</h2>
            <p className="text-xl mb-8 max-w-4xl mx-auto">
              Join 1500+ Delhi businesses that trust GOD Digital Marketing for SEO success. Proven strategies that deliver 650% traffic increase and ₹35Cr+ revenue generation.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button asChild size="lg" className="bg-white text-blue-600 hover:bg-slate-100 text-lg px-8 py-4">
                <Link to="/quote">Get Free Delhi SEO Audit</Link>
              </Button>
              <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-blue-600 text-lg px-8 py-4">
                <Link to="/contact">Call SEO Experts: +91-8708577598</Link>
              </Button>
            </div>
          </div>
        </section>
      </main>
      
      <Footer />
    </div>
  );
};

export default SeoServicesDelhi;
