import React from 'react';
import { Link } from 'react-router-dom';
import { ArrowRight, Search, TrendingUp, Users, CheckCircle, Building, Star, Crown, Target, Zap } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';
import Sidebar from '@/components/Sidebar';

const SeoServicesJaipur = () => {
  const seoServices = [
    'SEO Services Jaipur',
    'Local SEO Jaipur',
    'Technical SEO Jaipur',
    'On-Page SEO Jaipur',
    'Off-Page SEO Jaipur',
    'E-commerce SEO Jaipur',
    'Enterprise SEO Jaipur',
    'SEO Audit Jaipur',
    'Keyword Research Jaipur',
    'Content Optimization Jaipur',
    'Link Building Jaipur',
    'Google Ranking Jaipur',
    'Tourism SEO Jaipur',
    'Heritage Industry SEO Jaipur',
    'Handicraft Business SEO Jaipur',
    'Jewelry SEO Jaipur',
    'Textile SEO Jaipur',
    'Hotel SEO Jaipur',
    'Restaurant SEO Jaipur',
    'Real Estate SEO Jaipur'
  ];

  const seoSpecializations = [
    {
      type: 'Tourism & Heritage SEO',
      description: 'Specialized SEO for Jaipur\'s tourism capital and heritage industry businesses',
      icon: Building,
      features: ['Tourism Business SEO', 'Heritage Site Optimization', 'Hotel & Resort SEO', 'Travel Agency SEO']
    },
    {
      type: 'Handicraft & Jewelry SEO',
      description: 'Expert SEO for Jaipur\'s famous handicraft and jewelry manufacturing sector',
      icon: Star,
      features: ['Jewelry Business SEO', 'Handicraft Export SEO', 'Artisan Business SEO', 'Craft Industry SEO']
    },
    {
      type: 'Textile & Fashion SEO',
      description: 'Professional SEO for Jaipur\'s textile and fashion industry leaders',
      icon: Crown,
      features: ['Textile Manufacturing SEO', 'Fashion Brand SEO', 'Garment Export SEO', 'Fabric Business SEO']
    },
    {
      type: 'Real Estate & Construction SEO',
      description: 'Comprehensive SEO for Jaipur\'s growing real estate and construction sector',
      icon: Target,
      features: ['Property Developer SEO', 'Construction Company SEO', 'Real Estate Agent SEO', 'Architecture Firm SEO']
    }
  ];

  const seoPackages = [
    {
      name: 'SEO Jaipur Starter',
      price: '₹25,000',
      period: '/month',
      description: 'Perfect for small Jaipur businesses and tourism operators',
      features: [
        'Local Jaipur SEO Optimization',
        'Google My Business Setup',
        '20 Target Keywords',
        'On-Page SEO (10 pages)',
        'Monthly Reporting',
        'Tourism Industry Focus'
      ]
    },
    {
      name: 'Jaipur SEO Professional',
      price: '₹45,000',
      period: '/month',
      description: 'Comprehensive SEO for growing Jaipur businesses',
      features: [
        'Advanced Local + National SEO',
        '50 Target Keywords',
        'Technical SEO Audit',
        'Content Marketing Strategy',
        'Link Building Campaign',
        'Heritage/Handicraft Specialization',
        'Bi-weekly Reporting'
      ],
      popular: true
    },
    {
      name: 'Enterprise SEO Jaipur',
      price: '₹85,000',
      period: '/month',
      description: 'Advanced SEO for large Jaipur enterprises and exporters',
      features: [
        'Enterprise-Level SEO Strategy',
        'Unlimited Keywords',
        'Advanced Technical SEO',
        'Multi-location Optimization',
        'International SEO',
        'Export Market Targeting',
        'Dedicated SEO Manager'
      ]
    }
  ];

  const stats = [
    {
      metric: '1200+',
      description: 'Jaipur Businesses Ranked',
      detail: 'Across all industries'
    },
    {
      metric: '580%',
      description: 'Average Traffic Increase',
      detail: 'For Jaipur clients'
    },
    {
      metric: '₹32Cr+',
      description: 'Revenue Generated',
      detail: 'Through SEO campaigns'
    },
    {
      metric: '94%',
      description: 'First Page Rankings',
      detail: 'Target keyword success'
    }
  ];

  const achievements = [
    'Top SEO Company in Jaipur',
    'Tourism Industry SEO Leaders',
    'Heritage Business SEO Experts',
    'Handicraft Industry SEO Specialists',
    'Jewelry Business SEO Champions',
    'Pink City Digital Marketing Pioneers'
  ];

  return (
    <div className="min-h-screen bg-slate-900 relative">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        <div className="container mx-auto px-6 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            {/* Main Content */}
            <div className="lg:col-span-3">
              {/* Hero Section */}
              <section className="pt-24 pb-16">
                <div className="max-w-4xl">
                  <div className="inline-flex items-center space-x-2 bg-pink-500/20 border border-pink-500/30 rounded-full px-6 py-2 mb-8">
                    <Search className="w-4 h-4 text-pink-400" />
                    <span className="text-pink-400 font-medium">SEO Services Jaipur • Pink City Digital Excellence</span>
                  </div>
                  
                  <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
                    #1 SEO Company in
                    <span className="bg-gradient-to-r from-pink-400 to-pink-600 bg-clip-text text-transparent"> Jaipur</span>
                  </h1>
                  
                  <p className="text-xl text-slate-300 mb-8">
                    Premier SEO services in Jaipur offering comprehensive search engine optimization solutions including local SEO, technical SEO, on-page optimization, off-page SEO, and enterprise SEO strategies. Our Jaipur SEO company provides professional SEO services with keyword research, content optimization, link building, SEO audits, and Google ranking optimization. Serving 1200+ Jaipur businesses across all areas - from tourism operators in Pink City to handicraft exporters in Bagru. Expert SEO solutions with proven ₹32Cr+ revenue generation and 580% average organic traffic increase for Jaipur clients in Rajasthan's heritage capital through strategic search engine optimization and digital marketing excellence.
                  </p>
                  
                  <div className="flex flex-col sm:flex-row gap-4 mb-8">
                    <Button asChild size="lg" className="bg-gradient-to-r from-pink-500 to-pink-600 hover:from-pink-600 hover:to-pink-700 text-white text-lg px-8 py-4">
                      <Link to="/quote">Get Free Jaipur SEO Audit</Link>
                    </Button>
                    <Button asChild size="lg" variant="outline" className="border-pink-500 text-pink-400 hover:bg-pink-500 hover:text-white text-lg px-8 py-4">
                      <Link to="/contact">Call Jaipur SEO Experts: +91-8708577598</Link>
                    </Button>
                  </div>

                  {/* Achievements */}
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                    {achievements.map((achievement, index) => (
                      <div key={index} className="flex items-center justify-center space-x-2 bg-slate-800/50 rounded-lg p-3">
                        <CheckCircle className="w-4 h-4 text-pink-400 flex-shrink-0" />
                        <span className="text-slate-300">{achievement}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </section>

              {/* Stats Section */}
              <section className="mb-16 bg-slate-800/50 rounded-2xl p-8">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Jaipur SEO
                    <span className="bg-gradient-to-r from-pink-400 to-pink-600 bg-clip-text text-transparent"> Success Metrics</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Measurable SEO results from Jaipur businesses across tourism, handicraft, jewelry, and heritage sectors.
                  </p>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
                  {stats.map((stat, index) => (
                    <div key={index} className="text-center">
                      <div className="text-4xl font-bold text-pink-400 mb-2">{stat.metric}</div>
                      <div className="text-white font-semibold mb-1">{stat.description}</div>
                      <div className="text-slate-400">{stat.detail}</div>
                    </div>
                  ))}
                </div>
              </section>

              {/* SEO Specializations Section */}
              <section className="mb-16">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    SEO Strategies We
                    <span className="bg-gradient-to-r from-pink-400 to-pink-600 bg-clip-text text-transparent"> Master</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Specialized SEO strategies designed for Jaipur's unique tourism, handicraft, jewelry, and heritage business landscape.
                  </p>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  {seoSpecializations.map((seo, index) => (
                    <Card key={index} className="bg-slate-900/80 border-pink-500/20 hover:border-pink-500/40 transition-all duration-300">
                      <CardContent className="p-8">
                        <div className="w-12 h-12 bg-gradient-to-r from-pink-500 to-pink-600 rounded-lg flex items-center justify-center mb-6">
                          <seo.icon className="w-6 h-6 text-white" />
                        </div>
                        <h3 className="text-xl font-bold text-white mb-4">{seo.type}</h3>
                        <p className="text-slate-300 mb-6">{seo.description}</p>
                        <ul className="space-y-2">
                          {seo.features.map((feature, idx) => (
                            <li key={idx} className="flex items-center text-slate-400 text-sm">
                              <CheckCircle className="w-3 h-3 text-pink-400 mr-2 flex-shrink-0" />
                              {feature}
                            </li>
                          ))}
                        </ul>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>

              {/* Pricing Section */}
              <section className="mb-16">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Jaipur SEO
                    <span className="bg-gradient-to-r from-pink-400 to-pink-600 bg-clip-text text-transparent"> Pricing</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Competitive SEO pricing designed for Jaipur's tourism, handicraft, and heritage business environment.
                  </p>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                  {seoPackages.map((pkg, index) => (
                    <Card key={index} className={`bg-slate-900/80 border-pink-500/20 hover:border-pink-500/40 transition-all duration-300 relative ${pkg.popular ? 'ring-2 ring-pink-500' : ''}`}>
                      {pkg.popular && (
                        <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                          <span className="bg-pink-500 text-white px-4 py-2 rounded-full text-sm font-semibold">Most Popular</span>
                        </div>
                      )}
                      <CardContent className="p-8">
                        <h3 className="text-2xl font-bold text-white mb-2">{pkg.name}</h3>
                        <p className="text-slate-300 mb-6">{pkg.description}</p>
                        <div className="mb-6">
                          <span className="text-4xl font-bold text-pink-400">{pkg.price}</span>
                          <span className="text-slate-400">{pkg.period}</span>
                        </div>
                        <ul className="space-y-3 mb-8">
                          {pkg.features.map((feature, idx) => (
                            <li key={idx} className="flex items-center text-slate-300">
                              <CheckCircle className="w-4 h-4 text-pink-400 mr-3 flex-shrink-0" />
                              {feature}
                            </li>
                          ))}
                        </ul>
                        <Button asChild className={`w-full ${pkg.popular ? 'bg-gradient-to-r from-pink-500 to-pink-600 hover:from-pink-600 hover:to-pink-700' : 'bg-slate-700 hover:bg-slate-600'} text-white`}>
                          <Link to="/quote">Get Started</Link>
                        </Button>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>

              {/* CTA Section */}
              <section className="bg-gradient-to-r from-pink-600 to-pink-500 rounded-2xl p-8 text-center">
                <h2 className="text-3xl md:text-5xl font-bold mb-6">Ready to Dominate Jaipur Search Results?</h2>
                <p className="text-xl mb-8">
                  Join 1200+ Jaipur businesses that trust GOD Digital Marketing for SEO success. Proven strategies that deliver 580% traffic increase and ₹32Cr+ revenue generation.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button asChild size="lg" className="bg-white text-pink-600 hover:bg-slate-100 text-lg px-8 py-4">
                    <Link to="/quote">Get Free SEO Audit</Link>
                  </Button>
                  <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-pink-600 text-lg px-8 py-4">
                    <Link to="/contact">Call SEO Experts: +91-8708577598</Link>
                  </Button>
                </div>
              </section>
            </div>

            {/* Sidebar */}
            <div className="lg:col-span-1">
              <Sidebar currentService="seo" currentLocation="jaipur" />
            </div>
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
};

export default SeoServicesJaipur;
