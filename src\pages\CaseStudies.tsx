
import React from 'react';
import { Link } from 'react-router-dom';
import { TrendingUp, Users, DollarSign, ArrowRight, Star, Globe, Target, Award } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';

const CaseStudies = () => {
  const caseStudies = [
    {
      title: 'UK Market Domination: Car Ovens & Camping Equipment',
      client: 'Easy Outdoor (UK)',
      industry: 'Outdoor Equipment & Car Accessories',
      country: 'United Kingdom',
      challenge: 'Zero online visibility for specialized car oven and Opus camper keywords in competitive UK market',
      solution: 'International SEO strategy with UK-specific keyword research, technical SEO optimization, and content marketing for car ovens and camping equipment',
      results: [
        { metric: '10M+', description: 'Monthly views achieved' },
        { metric: '#1', description: 'UK rankings for car oven keywords' },
        { metric: '1000s', description: 'Monthly visitors from zero' }
      ],
      image: 'https://images.unsplash.com/photo-1504851149312-7a075b496cc7?w=600&h=400&fit=crop',
      testimonial: "<PERSON><PERSON> took our car oven and Opus camper business from zero visibility to dominating UK search results. The international SEO expertise is unmatched - we now rank across multiple countries.",
      testimonialAuthor: 'James Mitchell, CEO Easy Outdoor'
    },
    {
      title: 'US Logistics Market Penetration',
      client: 'Bulkland (US)',
      industry: 'Logistics & Bulk Services',
      country: 'United States',
      challenge: 'Entering competitive US logistics market with no existing brand presence or search visibility',
      solution: 'Multi-state SEO campaign with US market analysis, local SEO optimization, and industry-specific content strategy',
      results: [
        { metric: '5M+', description: 'Monthly organic impressions' },
        { metric: '15+', description: 'US states with top rankings' },
        { metric: '400%', description: 'Lead generation increase' }
      ],
      image: 'https://images.unsplash.com/photo-1586528116311-ad8dd3c8310d?w=600&h=400&fit=crop',
      testimonial: "Working with Nitin was transformational for our US market expansion. The AI-powered content strategies and technical SEO expertise delivered results we never thought possible.",
      testimonialAuthor: 'Sarah Thompson, Marketing Director Bulkland'
    },
    {
      title: 'Dubai Flower Delivery Market Leadership',
      client: 'Shakespeare Flower Delivery (Dubai)',
      industry: 'Flower Delivery & Events',
      country: 'United Arab Emirates',
      challenge: 'Establishing market presence in Dubai\'s competitive flower delivery market with local and expat customers',
      solution: 'Dubai-focused local SEO with Arabic and English optimization, Google My Business mastery, and cultural content strategy',
      results: [
        { metric: '#1', description: 'Dubai flower delivery rankings' },
        { metric: '800%', description: 'Local search visibility increase' },
        { metric: '300%', description: 'Online order growth' }
      ],
      image: 'https://images.unsplash.com/photo-1563241527-3004b7be0ffd?w=600&h=400&fit=crop',
      testimonial: "The results speak for themselves - from zero to thousands of monthly visitors in Dubai market. Nitin understands international SEO like no other consultant I have worked with.",
      testimonialAuthor: 'Ahmed Al-Rashid, Owner Shakespeare Flowers'
    },
    {
      title: 'Multi-Country Real Estate Lead Generation',
      client: 'International Property Group',
      industry: 'Real Estate & Property Investment',
      country: 'India, Kuwait, South Africa',
      challenge: 'Generating qualified property leads across multiple international markets with different search behaviors',
      solution: 'Multi-country SEO strategy with market-specific landing pages, local property keyword optimization, and lead generation automation',
      results: [
        { metric: '3', description: 'Countries with market leadership' },
        { metric: '500%', description: 'Qualified lead increase' },
        { metric: '₹2Cr+', description: 'Property sales attributed to SEO' }
      ],
      image: 'https://images.unsplash.com/photo-1560518883-ce09059eeffa?w=600&h=400&fit=crop',
      testimonial: "Nitin's understanding of different markets and search behaviors across countries is exceptional. Our property leads increased 5x across India, Kuwait, and South Africa.",
      testimonialAuthor: 'Priya Sharma, Director International Property Group'
    }
  ];

  return (
    <div className="min-h-screen bg-slate-900 text-white relative overflow-hidden">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        {/* Hero Section */}
        <section className="pt-32 pb-20">
          <div className="container mx-auto px-6">
            <div className="max-w-4xl mx-auto text-center">
              <div className="inline-flex items-center space-x-2 bg-amber-500/20 border border-amber-500/30 rounded-full px-6 py-2 mb-8">
                <Globe className="w-4 h-4 text-amber-400" />
                <span className="text-amber-400 font-medium">International SEO Success Stories • 6 Countries</span>
              </div>

              <h1 className="text-5xl md:text-6xl font-bold mb-6 leading-tight">
                International SEO Results
                <span className="bg-gradient-to-r from-amber-400 to-amber-600 bg-clip-text text-transparent"> Across Global Markets</span>
              </h1>
              <p className="text-xl text-slate-300 mb-12 max-w-3xl mx-auto">
                Real businesses, real results across UK, US, Dubai, India, Kuwait & South Africa.
                7 years of building websites from zero to millions of monthly views in international markets.
              </p>
            </div>
          </div>
        </section>

        {/* Case Studies */}
        <section className="py-20">
          <div className="container mx-auto px-6">
            <div className="space-y-20">
              {caseStudies.map((study, index) => (
                <div key={index} className={`grid grid-cols-1 lg:grid-cols-2 gap-12 items-center ${index % 2 === 1 ? 'lg:grid-flow-col-dense' : ''}`}>
                  <div className={index % 2 === 1 ? 'lg:col-start-2' : ''}>
                    <img 
                      src={study.image} 
                      alt={study.title}
                      className="rounded-lg shadow-2xl w-full h-80 object-cover"
                    />
                  </div>
                  
                  <div className={index % 2 === 1 ? 'lg:col-start-1' : ''}>
                    <div className="mb-6">
                      <div className="flex flex-wrap gap-2 mb-4">
                        <span className="inline-block bg-amber-500/20 text-amber-400 px-4 py-2 rounded-full text-sm font-semibold">
                          {study.industry}
                        </span>
                        <span className="inline-flex items-center bg-blue-500/20 text-blue-400 px-4 py-2 rounded-full text-sm font-semibold">
                          <Globe className="w-3 h-3 mr-1" />
                          {study.country}
                        </span>
                      </div>
                      <h2 className="text-4xl font-bold text-white mb-4">{study.title}</h2>
                      <h3 className="text-2xl text-amber-400 mb-6">{study.client}</h3>
                    </div>
                    
                    <div className="mb-8">
                      <h4 className="text-xl font-semibold text-white mb-3">Challenge:</h4>
                      <p className="text-slate-300 mb-6">{study.challenge}</p>
                      
                      <h4 className="text-xl font-semibold text-white mb-3">Solution:</h4>
                      <p className="text-slate-300 mb-6">{study.solution}</p>
                    </div>
                    
                    <div className="grid grid-cols-1 sm:grid-cols-3 gap-6 mb-8">
                      {study.results.map((result, idx) => (
                        <Card key={idx} className="bg-slate-800/50 border-amber-500/20 text-center">
                          <CardContent className="p-6">
                            <div className="text-3xl font-bold text-amber-400 mb-2">{result.metric}</div>
                            <div className="text-slate-300 text-sm">{result.description}</div>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                    
                    <Card className="bg-slate-800/50 border-amber-500/20">
                      <CardContent className="p-6">
                        <div className="flex mb-4">
                          {[...Array(5)].map((_, i) => (
                            <Star key={i} className="w-5 h-5 text-amber-400 fill-current" />
                          ))}
                        </div>
                        <p className="text-slate-300 mb-4 italic">"{study.testimonial}"</p>
                        <p className="text-amber-400 font-semibold">- {study.testimonialAuthor}</p>
                      </CardContent>
                    </Card>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 bg-gradient-to-r from-amber-600 to-amber-500">
          <div className="container mx-auto px-6 text-center">
            <h2 className="text-4xl md:text-5xl font-bold mb-6">Ready to Dominate Your International Market?</h2>
            <p className="text-xl mb-8 max-w-3xl mx-auto">
              Join the elite businesses investing ₹60K-150K monthly for international SEO dominance.
              7 years of proven results across UK, US, Dubai, India, Kuwait & South Africa.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button asChild size="lg" className="bg-white text-amber-600 hover:bg-slate-100 text-lg px-8 py-4">
                <Link to="/quote" className="flex items-center">
                  Get International SEO Strategy
                  <ArrowRight className="ml-2 w-5 h-5" />
                </Link>
              </Button>
              <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-amber-600 text-lg px-8 py-4">
                <Link to="/contact">Book Premium Consultation</Link>
              </Button>
            </div>
          </div>
        </section>
      </main>
      
      <Footer />
    </div>
  );
};

export default CaseStudies;
