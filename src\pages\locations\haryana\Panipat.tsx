import React from 'react';
import { Link } from 'react-router-dom';
import { ArrowRight, MapPin, TrendingUp, Users, CheckCircle, Building, Star, Factory } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';

const Panipat = () => {
  const services = [
    'SEO Services Panipat',
    'Google Ads Management Panipat',
    'Social Media Marketing Panipat',
    'Local SEO Panipat',
    'E-commerce SEO Panipat',
    'Content Marketing Panipat',
    'Website Development Panipat',
    'Digital Marketing Consulting Panipat'
  ];

  const industries = [
    'Textile Manufacturing Panipat',
    'Handloom Industry Panipat',
    'Carpet Manufacturing Panipat',
    'Real Estate Panipat',
    'Agriculture Panipat',
    'Retail & E-commerce Panipat',
    'Education Panipat',
    'Healthcare Panipat'
  ];

  const areas = [
    'Model Town Digital Marketing',
    'Civil Lines SEO Services',
    'GT Road Digital Marketing',
    'Assandh SEO Services',
    'Samalkha Digital Marketing',
    'Bapoli SEO Services',
    'Israna Digital Marketing',
    'Madlauda SEO Services'
  ];

  const stats = [
    {
      metric: '80+',
      description: 'Panipat Businesses Served',
      detail: 'Across all sectors'
    },
    {
      metric: '380%',
      description: 'Average Traffic Increase',
      detail: 'For Panipat clients'
    },
    {
      metric: '₹25L+',
      description: 'Revenue Generated',
      detail: 'For Panipat businesses'
    }
  ];

  return (
    <div className="min-h-screen bg-slate-900 relative">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        {/* Hero Section */}
        <section className="pt-32 pb-20">
          <div className="container mx-auto px-6">
            <div className="max-w-4xl mx-auto text-center">
              <div className="inline-flex items-center space-x-2 bg-indigo-500/20 border border-indigo-500/30 rounded-full px-6 py-2 mb-8">
                <Factory className="w-4 h-4 text-indigo-400" />
                <span className="text-indigo-400 font-medium">Panipat Digital Marketing • Textile City of India</span>
              </div>
              
              <h1 className="text-5xl md:text-6xl font-bold mb-6 leading-tight">
                #1 Digital Marketing Company in
                <span className="bg-gradient-to-r from-indigo-400 to-indigo-600 bg-clip-text text-transparent"> Panipat</span>
              </h1>
              
              <p className="text-xl text-slate-300 mb-12 max-w-3xl mx-auto">
                Leading digital marketing agency in Panipat helping businesses dominate online. From textile manufacturing hubs to handloom centers, we've helped 80+ businesses grow with proven SEO, Google Ads, and digital marketing strategies across all Panipat areas.
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button asChild size="lg" className="bg-gradient-to-r from-indigo-500 to-indigo-600 hover:from-indigo-600 hover:to-indigo-700 text-white text-lg px-8 py-4">
                  <Link to="/quote">Get Free Panipat SEO Audit</Link>
                </Button>
                <Button asChild size="lg" variant="outline" className="border-indigo-500 text-indigo-400 hover:bg-indigo-500 hover:text-white text-lg px-8 py-4">
                  <Link to="/contact">Call Panipat Office</Link>
                </Button>
              </div>
            </div>
          </div>
        </section>

        {/* Stats Section */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Panipat Digital Marketing
                <span className="bg-gradient-to-r from-indigo-400 to-indigo-600 bg-clip-text text-transparent"> Success Stories</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto mb-12">
                Real results from Panipat businesses across industries - from traditional textile manufacturers to modern e-commerce businesses.
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
              {stats.map((stat, index) => (
                <div key={index} className="text-center">
                  <div className="text-5xl font-bold text-indigo-400 mb-2">{stat.metric}</div>
                  <div className="text-white font-semibold mb-1">{stat.description}</div>
                  <div className="text-slate-400">{stat.detail}</div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Services Section */}
        <section className="py-20">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Digital Marketing Services in
                <span className="bg-gradient-to-r from-indigo-400 to-indigo-600 bg-clip-text text-transparent"> Panipat</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto">
                Comprehensive digital marketing solutions for Panipat businesses looking to dominate online in the Textile City.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {services.map((service, index) => (
                <Card key={index} className="bg-slate-900/80 border-indigo-500/20 hover:border-indigo-500/40 transition-all duration-300">
                  <CardContent className="p-6 text-center">
                    <Building className="w-8 h-8 text-indigo-400 mx-auto mb-4" />
                    <h3 className="text-white font-semibold mb-2">{service}</h3>
                    <Button asChild size="sm" variant="outline" className="border-indigo-500 text-indigo-400 hover:bg-indigo-500 hover:text-white">
                      <Link to="/quote">Learn More</Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Industries Section */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Panipat Industries We
                <span className="bg-gradient-to-r from-indigo-400 to-indigo-600 bg-clip-text text-transparent"> Serve</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto">
                Specialized digital marketing strategies for Panipat's textile-focused industrial landscape.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {industries.map((industry, index) => (
                <Card key={index} className="bg-slate-900/80 border-indigo-500/20 hover:border-indigo-500/40 transition-all duration-300">
                  <CardContent className="p-6 text-center">
                    <Star className="w-8 h-8 text-indigo-400 mx-auto mb-4" />
                    <h3 className="text-white font-semibold mb-2">{industry}</h3>
                    <Button asChild size="sm" variant="outline" className="border-indigo-500 text-indigo-400 hover:bg-indigo-500 hover:text-white">
                      <Link to="/quote">Get Strategy</Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Areas Section */}
        <section className="py-20">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Panipat Areas We
                <span className="bg-gradient-to-r from-indigo-400 to-indigo-600 bg-clip-text text-transparent"> Cover</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto">
                Local digital marketing expertise across all major Panipat areas and textile hubs.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {areas.map((area, index) => (
                <Card key={index} className="bg-slate-900/80 border-indigo-500/20 hover:border-indigo-500/40 transition-all duration-300">
                  <CardContent className="p-6 text-center">
                    <MapPin className="w-8 h-8 text-indigo-400 mx-auto mb-4" />
                    <h3 className="text-white font-semibold mb-2">{area}</h3>
                    <Button asChild size="sm" variant="outline" className="border-indigo-500 text-indigo-400 hover:bg-indigo-500 hover:text-white">
                      <Link to="/contact">Contact Us</Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Why Choose Us Section */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Why Choose GOD Digital Marketing in
                <span className="bg-gradient-to-r from-indigo-400 to-indigo-600 bg-clip-text text-transparent"> Panipat?</span>
              </h2>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              <Card className="bg-slate-900/80 border-indigo-500/20">
                <CardContent className="p-8">
                  <TrendingUp className="w-12 h-12 text-indigo-400 mb-6" />
                  <h3 className="text-xl font-bold text-white mb-4">Textile Industry Expertise</h3>
                  <p className="text-slate-300">Deep understanding of Panipat's textile manufacturing ecosystem, handloom industry, and traditional business practices.</p>
                </CardContent>
              </Card>

              <Card className="bg-slate-900/80 border-indigo-500/20">
                <CardContent className="p-8">
                  <Users className="w-12 h-12 text-indigo-400 mb-6" />
                  <h3 className="text-xl font-bold text-white mb-4">Proven Panipat Results</h3>
                  <p className="text-slate-300">80+ successful Panipat campaigns with measurable ROI improvements and business growth across textile and other industries.</p>
                </CardContent>
              </Card>

              <Card className="bg-slate-900/80 border-indigo-500/20">
                <CardContent className="p-8">
                  <CheckCircle className="w-12 h-12 text-indigo-400 mb-6" />
                  <h3 className="text-xl font-bold text-white mb-4">Traditional & Modern Balance</h3>
                  <p className="text-slate-300">Expertise in marketing both traditional textile businesses and modern enterprises with cultural sensitivity.</p>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 bg-gradient-to-r from-indigo-600 to-indigo-500">
          <div className="container mx-auto px-6 text-center">
            <h2 className="text-4xl md:text-5xl font-bold mb-6">Ready to Dominate Panipat's Digital Market?</h2>
            <p className="text-xl mb-8 max-w-3xl mx-auto">
              Join 80+ Panipat businesses that trust GOD Digital Marketing for their online growth. From textile hubs to modern businesses, we deliver results.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button asChild size="lg" className="bg-white text-indigo-600 hover:bg-slate-100 text-lg px-8 py-4">
                <Link to="/quote">Get Free Panipat SEO Audit</Link>
              </Button>
              <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-indigo-600 text-lg px-8 py-4">
                <Link to="/contact">Call Panipat Office Now</Link>
              </Button>
            </div>
          </div>
        </section>
      </main>
      
      <Footer />
    </div>
  );
};

export default Panipat;
