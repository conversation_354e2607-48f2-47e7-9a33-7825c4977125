import React from 'react';
import { Link } from 'react-router-dom';
import { ArrowRight, Heart, TrendingUp, Users, CheckCircle, Shield, Star } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';

const Healthcare = () => {
  const services = [
    'Healthcare SEO',
    'Medical Practice Marketing',
    'Hospital Digital Marketing',
    'Healthcare PPC Management',
    'Medical Website Development',
    'Healthcare Content Marketing',
    'Patient Review Management',
    'Healthcare Social Media'
  ];

  const specialties = [
    'Hospitals & Clinics',
    'Dental Practices',
    'Dermatology Clinics',
    'Cardiology Centers',
    'Orthopedic Practices',
    'Pediatric Clinics',
    'Mental Health Services',
    'Diagnostic Centers'
  ];

  const challenges = [
    {
      challenge: 'Patient Trust',
      solution: 'Build credibility through patient testimonials, certifications, and professional content',
      icon: Shield
    },
    {
      challenge: 'Local Competition',
      solution: 'Dominate local search results for medical services in your area',
      icon: Heart
    },
    {
      challenge: 'Compliance',
      solution: 'HIPAA-compliant marketing strategies that protect patient privacy',
      icon: Star
    }
  ];

  const stats = [
    {
      metric: '400%',
      description: 'Increase in Patient Appointments',
      detail: 'For healthcare clients'
    },
    {
      metric: '150+',
      description: 'Healthcare Practices Served',
      detail: 'Across specialties'
    },
    {
      metric: '95%',
      description: 'Client Retention Rate',
      detail: 'In healthcare sector'
    }
  ];

  return (
    <div className="min-h-screen bg-slate-900 relative">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        {/* Hero Section */}
        <section className="pt-32 pb-20">
          <div className="container mx-auto px-6">
            <div className="max-w-4xl mx-auto text-center">
              <div className="inline-flex items-center space-x-2 bg-amber-500/20 border border-amber-500/30 rounded-full px-6 py-2 mb-8">
                <Heart className="w-4 h-4 text-amber-400" />
                <span className="text-amber-400 font-medium">Healthcare Digital Marketing • Patient Acquisition</span>
              </div>
              
              <h1 className="text-5xl md:text-6xl font-bold mb-6 leading-tight">
                Healthcare Digital Marketing That
                <span className="bg-gradient-to-r from-amber-400 to-amber-600 bg-clip-text text-transparent"> Attracts More Patients</span>
              </h1>
              
              <p className="text-xl text-slate-300 mb-12 max-w-3xl mx-auto">
                Specialized digital marketing for healthcare providers, hospitals, and medical practices. Build trust, attract more patients, and grow your practice with HIPAA-compliant marketing strategies.
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button asChild size="lg" className="bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700 text-white text-lg px-8 py-4">
                  <Link to="/quote">Get Healthcare Marketing Strategy</Link>
                </Button>
                <Button asChild size="lg" variant="outline" className="border-amber-500 text-amber-400 hover:bg-amber-500 hover:text-white text-lg px-8 py-4">
                  <Link to="/case-studies">View Healthcare Results</Link>
                </Button>
              </div>
            </div>
          </div>
        </section>

        {/* Stats Section */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Healthcare Marketing
                <span className="bg-gradient-to-r from-amber-400 to-amber-600 bg-clip-text text-transparent"> Success Stories</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto mb-12">
                Real results from healthcare providers across India - from small clinics to large hospital networks.
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
              {stats.map((stat, index) => (
                <div key={index} className="text-center">
                  <div className="text-5xl font-bold text-amber-400 mb-2">{stat.metric}</div>
                  <div className="text-white font-semibold mb-1">{stat.description}</div>
                  <div className="text-slate-400">{stat.detail}</div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Challenges & Solutions */}
        <section className="py-20">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Healthcare Marketing
                <span className="bg-gradient-to-r from-amber-400 to-amber-600 bg-clip-text text-transparent"> Challenges We Solve</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto">
                Understanding the unique challenges of healthcare marketing and providing compliant solutions.
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              {challenges.map((item, index) => (
                <Card key={index} className="bg-slate-900/80 border-amber-500/20 hover:border-amber-500/40 transition-all duration-300">
                  <CardContent className="p-8">
                    <div className="w-12 h-12 bg-gradient-to-r from-amber-500 to-amber-600 rounded-lg flex items-center justify-center mb-6">
                      <item.icon className="w-6 h-6 text-white" />
                    </div>
                    <h3 className="text-xl font-bold text-white mb-4">Challenge: {item.challenge}</h3>
                    <p className="text-slate-300">{item.solution}</p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Services Section */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Healthcare Digital Marketing
                <span className="bg-gradient-to-r from-amber-400 to-amber-600 bg-clip-text text-transparent"> Services</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto">
                Comprehensive digital marketing solutions designed specifically for healthcare providers.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {services.map((service, index) => (
                <Card key={index} className="bg-slate-900/80 border-amber-500/20 hover:border-amber-500/40 transition-all duration-300">
                  <CardContent className="p-6 text-center">
                    <Heart className="w-8 h-8 text-amber-400 mx-auto mb-4" />
                    <h3 className="text-white font-semibold mb-2">{service}</h3>
                    <Button asChild size="sm" variant="outline" className="border-amber-500 text-amber-400 hover:bg-amber-500 hover:text-white">
                      <Link to="/quote">Learn More</Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Specialties Section */}
        <section className="py-20">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Healthcare Specialties We
                <span className="bg-gradient-to-r from-amber-400 to-amber-600 bg-clip-text text-transparent"> Serve</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto">
                Specialized marketing strategies for different healthcare specialties and medical practices.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {specialties.map((specialty, index) => (
                <Card key={index} className="bg-slate-900/80 border-amber-500/20 hover:border-amber-500/40 transition-all duration-300">
                  <CardContent className="p-6 text-center">
                    <Star className="w-8 h-8 text-amber-400 mx-auto mb-4" />
                    <h3 className="text-white font-semibold mb-2">{specialty}</h3>
                    <Button asChild size="sm" variant="outline" className="border-amber-500 text-amber-400 hover:bg-amber-500 hover:text-white">
                      <Link to="/quote">Get Strategy</Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Why Choose Us Section */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Why Healthcare Providers Choose
                <span className="bg-gradient-to-r from-amber-400 to-amber-600 bg-clip-text text-transparent"> GOD Digital Marketing</span>
              </h2>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              <Card className="bg-slate-900/80 border-amber-500/20">
                <CardContent className="p-8">
                  <TrendingUp className="w-12 h-12 text-amber-400 mb-6" />
                  <h3 className="text-xl font-bold text-white mb-4">Healthcare Expertise</h3>
                  <p className="text-slate-300">Deep understanding of healthcare industry regulations, patient behavior, and medical marketing best practices.</p>
                </CardContent>
              </Card>

              <Card className="bg-slate-900/80 border-amber-500/20">
                <CardContent className="p-8">
                  <Users className="w-12 h-12 text-amber-400 mb-6" />
                  <h3 className="text-xl font-bold text-white mb-4">Patient-Focused Approach</h3>
                  <p className="text-slate-300">Marketing strategies that build trust, educate patients, and drive quality appointments for your practice.</p>
                </CardContent>
              </Card>

              <Card className="bg-slate-900/80 border-amber-500/20">
                <CardContent className="p-8">
                  <CheckCircle className="w-12 h-12 text-amber-400 mb-6" />
                  <h3 className="text-xl font-bold text-white mb-4">HIPAA Compliance</h3>
                  <p className="text-slate-300">All marketing activities are fully compliant with healthcare regulations and patient privacy requirements.</p>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 bg-gradient-to-r from-amber-600 to-amber-500">
          <div className="container mx-auto px-6 text-center">
            <h2 className="text-4xl md:text-5xl font-bold mb-6">Ready to Attract More Patients?</h2>
            <p className="text-xl mb-8 max-w-3xl mx-auto">
              Join 150+ healthcare providers who trust GOD Digital Marketing to grow their practice and attract quality patients.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button asChild size="lg" className="bg-white text-amber-600 hover:bg-slate-100 text-lg px-8 py-4">
                <Link to="/quote">Get Healthcare Marketing Strategy</Link>
              </Button>
              <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-amber-600 text-lg px-8 py-4">
                <Link to="/contact">Book Healthcare Consultation</Link>
              </Button>
            </div>
          </div>
        </section>
      </main>
      
      <Footer />
    </div>
  );
};

export default Healthcare;
