import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { <PERSON><PERSON><PERSON>, Bot, Zap, TrendingUp, CheckCircle, Star, Target } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';

const AiContentServices = () => {
  const features = [
    'AI Content Generation (50-100 pieces monthly)',
    'Multi-Language Content Creation',
    'SEO-Optimized Content Automation',
    'Quality Control & Human Review Systems',
    'Content Calendar Planning & Scheduling',
    'Cross-Platform Content Repurposing',
    'Industry-Specific Content Templates',
    'Brand Voice & Tone Consistency',
    'Content Performance Analytics',
    'Automated Content Distribution'
  ];

  const packages = [
    {
      name: 'AI Content Starter',
      price: '₹30,000',
      period: '/month',
      description: 'Perfect for businesses starting their AI content journey',
      features: [
        '30 AI-Generated Content Pieces',
        'Basic SEO Optimization',
        'Single Platform Focus',
        'Quality Review Process',
        'Monthly Performance Reports'
      ]
    },
    {
      name: 'AI Content Pro',
      price: '₹50,000',
      period: '/month',
      description: 'Comprehensive AI content creation for growing businesses',
      features: [
        'Everything in AI Content Starter',
        '60 AI-Generated Content Pieces',
        'Multi-Platform Optimization',
        'Advanced SEO Integration',
        'Content Calendar Management',
        'Brand Voice Training'
      ],
      popular: true
    },
    {
      name: 'AI Content Enterprise',
      price: '₹80,000',
      period: '/month',
      description: 'Enterprise AI content automation for scale',
      features: [
        'Everything in AI Content Pro',
        '100+ AI-Generated Content Pieces',
        'Multi-Language Content Creation',
        'Advanced Analytics & Insights',
        'Custom AI Model Training',
        'Dedicated Content Strategist'
      ]
    }
  ];

  const results = [
    {
      metric: '500%',
      description: 'Content output increase',
      timeframe: 'with AI automation'
    },
    {
      metric: '80%',
      description: 'Time savings',
      timeframe: 'on content creation'
    },
    {
      metric: '300%',
      description: 'Engagement improvement',
      timeframe: 'with optimized content'
    }
  ];

  const contentTypes = [
    {
      type: 'Blog Articles',
      description: 'SEO-optimized blog posts that drive organic traffic and establish thought leadership',
      icon: Target,
      features: ['Keyword-optimized', 'Industry-specific', 'Engaging headlines', 'CTA integration']
    },
    {
      type: 'Social Media Content',
      description: 'Platform-specific content that engages audiences and builds brand awareness',
      icon: Zap,
      features: ['Platform optimization', 'Visual content ideas', 'Hashtag research', 'Engagement hooks']
    },
    {
      type: 'Product Descriptions',
      description: 'Compelling product descriptions that convert browsers into buyers',
      icon: Star,
      features: ['Benefit-focused', 'SEO-friendly', 'Conversion-optimized', 'Brand-consistent']
    }
  ];

  return (
    <div className="min-h-screen bg-slate-900 relative">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        {/* Hero Section */}
        <section className="pt-32 pb-20">
          <div className="container mx-auto px-6">
            <div className="max-w-4xl mx-auto text-center">
              <div className="inline-flex items-center space-x-2 bg-amber-500/20 border border-amber-500/30 rounded-full px-6 py-2 mb-8">
                <Bot className="w-4 h-4 text-amber-400" />
                <span className="text-amber-400 font-medium">AI Content Creation • Scale Your Content Production</span>
              </div>
              
              <h1 className="text-5xl md:text-6xl font-bold mb-6 leading-tight">
                AI Content Creation That
                <span className="bg-gradient-to-r from-amber-400 to-amber-600 bg-clip-text text-transparent"> Scales Your Business</span>
              </h1>
              
              <p className="text-xl text-slate-300 mb-12 max-w-3xl mx-auto">
                Scale your content production with advanced AI automation while maintaining quality and relevance. Create 50-100 high-quality content pieces monthly that drive traffic, engagement, and conversions.
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button asChild size="lg" className="bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700 text-white text-lg px-8 py-4">
                  <Link to="/quote">Get AI Content Strategy</Link>
                </Button>
                <Button asChild size="lg" variant="outline" className="border-amber-500 text-amber-400 hover:bg-amber-500 hover:text-white text-lg px-8 py-4">
                  <Link to="/case-studies">View Content Results</Link>
                </Button>
              </div>
            </div>
          </div>
        </section>

        {/* Results Section */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                AI Content Creation
                <span className="bg-gradient-to-r from-amber-400 to-amber-600 bg-clip-text text-transparent"> Results</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto mb-12">
                Real results from businesses that scaled their content production with AI automation.
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
              {results.map((result, index) => (
                <div key={index} className="text-center">
                  <div className="text-5xl font-bold text-amber-400 mb-2">{result.metric}</div>
                  <div className="text-white font-semibold mb-1">{result.description}</div>
                  <div className="text-slate-400">{result.timeframe}</div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Content Types Section */}
        <section className="py-20">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                AI Content
                <span className="bg-gradient-to-r from-amber-400 to-amber-600 bg-clip-text text-transparent"> Types We Create</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto">
                From blog articles to social media content, our AI creates diverse content types optimized for your business goals.
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-20">
              {contentTypes.map((content, index) => (
                <Card key={index} className="bg-slate-900/80 border-amber-500/20 hover:border-amber-500/40 transition-all duration-300">
                  <CardContent className="p-8">
                    <div className="w-12 h-12 bg-gradient-to-r from-amber-500 to-amber-600 rounded-lg flex items-center justify-center mb-6">
                      <content.icon className="w-6 h-6 text-white" />
                    </div>
                    <h3 className="text-xl font-bold text-white mb-4">{content.type}</h3>
                    <p className="text-slate-300 mb-6">{content.description}</p>
                    <ul className="space-y-2">
                      {content.features.map((feature, idx) => (
                        <li key={idx} className="flex items-center text-slate-400 text-sm">
                          <CheckCircle className="w-3 h-3 text-amber-400 mr-2 flex-shrink-0" />
                          {feature}
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Features Section */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Advanced AI Content
                <span className="bg-gradient-to-r from-amber-400 to-amber-600 bg-clip-text text-transparent"> Features</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto">
                Cutting-edge AI technology that creates high-quality, engaging content at scale.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-w-4xl mx-auto">
              {features.map((feature, index) => (
                <div key={index} className="flex items-center space-x-3">
                  <CheckCircle className="w-6 h-6 text-amber-400 flex-shrink-0" />
                  <span className="text-slate-300">{feature}</span>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Pricing Section */}
        <section className="py-20">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                AI Content Investment
                <span className="bg-gradient-to-r from-amber-400 to-amber-600 bg-clip-text text-transparent"> Packages</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto">
                Choose the perfect AI content package to scale your content production and drive business growth.
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
              {packages.map((pkg, index) => (
                <Card key={index} className={`bg-slate-900/80 border-amber-500/20 hover:border-amber-500/40 transition-all duration-300 relative ${pkg.popular ? 'ring-2 ring-amber-500' : ''}`}>
                  {pkg.popular && (
                    <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                      <span className="bg-amber-500 text-white px-4 py-2 rounded-full text-sm font-semibold">Most Popular</span>
                    </div>
                  )}
                  <CardContent className="p-8">
                    <h3 className="text-2xl font-bold text-white mb-2">{pkg.name}</h3>
                    <p className="text-slate-300 mb-6">{pkg.description}</p>
                    <div className="mb-6">
                      <span className="text-4xl font-bold text-amber-400">{pkg.price}</span>
                      <span className="text-slate-400">{pkg.period}</span>
                    </div>
                    <ul className="space-y-3 mb-8">
                      {pkg.features.map((feature, idx) => (
                        <li key={idx} className="flex items-center text-slate-300">
                          <CheckCircle className="w-4 h-4 text-amber-400 mr-3 flex-shrink-0" />
                          {feature}
                        </li>
                      ))}
                    </ul>
                    <Button asChild className={`w-full ${pkg.popular ? 'bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700' : 'bg-slate-700 hover:bg-slate-600'} text-white`}>
                      <Link to="/quote">Get Started</Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 bg-gradient-to-r from-amber-600 to-amber-500">
          <div className="container mx-auto px-6 text-center">
            <h2 className="text-4xl md:text-5xl font-bold mb-6">Ready to Scale Your Content Production?</h2>
            <p className="text-xl mb-8 max-w-3xl mx-auto">
              Join businesses creating 50-100 high-quality content pieces monthly with AI automation. Scale your content while maintaining quality and relevance.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button asChild size="lg" className="bg-white text-amber-600 hover:bg-slate-100 text-lg px-8 py-4">
                <Link to="/quote">Get AI Content Strategy</Link>
              </Button>
              <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-amber-600 text-lg px-8 py-4">
                <Link to="/contact">Book AI Content Consultation</Link>
              </Button>
            </div>
          </div>
        </section>
      </main>
      
      <Footer />
    </div>
  );
};

export default AiContentServices;
