import React from 'react';
import { Link } from 'react-router-dom';
import { ArrowRight, Code, TrendingUp, Users, CheckCircle, Building, Star, Crown, Target, Zap } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';
import Sidebar from '@/components/Sidebar';

const WebDevelopmentThiruvananthapuram = () => {
  const webServices = [
    'Website Development Thiruvananthapuram',
    'Custom Web Development Thiruvananthapuram',
    'E-commerce Development Thiruvananthapuram',
    'Tourism Website Development Thiruvananthapuram',
    'Government Website Development Thiruvananthapuram',
    'Educational Website Development Thiruvananthapuram',
    'Healthcare Website Development Thiruvananthapuram',
    'IT Company Website Development Thiruvananthapuram',
    'Startup Website Development Thiruvananthapuram',
    'Corporate Website Development Thiruvananthapuram',
    'Responsive Web Design Thiruvananthapuram',
    'Mobile App Development Thiruvananthapuram',
    'Progressive Web Apps Thiruvananthapuram',
    'CMS Development Thiruvananthapuram',
    'WordPress Development Thiruvananthapuram',
    'React Development Thiruvananthapuram',
    'Angular Development Thiruvananthapuram',
    'Node.js Development Thiruvananthapuram',
    'PHP Development Thiruvananthapuram',
    'Python Development Thiruvananthapuram'
  ];

  const industryFocus = [
    {
      industry: 'IT & Technology Web Development',
      description: 'Advanced web development for Thiruvananthapuram\'s thriving IT sector and technology companies',
      icon: Building,
      features: ['Software Company Websites', 'SaaS Platform Development', 'Tech Startup Websites', 'IT Service Portals']
    },
    {
      industry: 'Government & Public Sector Web Development',
      description: 'Professional web development for Kerala government offices and public sector organizations',
      icon: Star,
      features: ['Government Portals', 'Public Service Websites', 'E-governance Platforms', 'Citizen Service Portals']
    },
    {
      industry: 'Tourism & Hospitality Web Development',
      description: 'Comprehensive web development for Kerala\'s tourism industry and hospitality businesses',
      icon: Crown,
      features: ['Tourism Websites', 'Hotel Booking Platforms', 'Travel Agency Websites', 'Resort Websites']
    },
    {
      industry: 'Education & Healthcare Web Development',
      description: 'Specialized web development for educational institutions and healthcare providers in Kerala',
      icon: Target,
      features: ['University Websites', 'School Portals', 'Hospital Websites', 'Medical Practice Websites']
    }
  ];

  const localWebFeatures = [
    'Thiruvananthapuram IT Hub Development',
    'Kerala Government Portal Expertise',
    'Tourism Website Specialization',
    'Educational Institution Portals',
    'Healthcare Website Development',
    'Startup Website Solutions',
    'E-commerce Platform Development',
    'Mobile-First Design Approach',
    'Progressive Web Applications',
    'Custom Software Development'
  ];

  const achievements = [
    '140+ Thiruvananthapuram Projects Delivered',
    '2,300% Average Business Growth',
    '₹38Cr+ Revenue Generated for Clients',
    '98% Project Success Rate',
    'Top Web Development Company in Kerala',
    'IT Sector Development Specialists'
  ];

  const technologyStack = [
    {
      category: 'Frontend Technologies',
      description: 'Modern frontend development using latest technologies and frameworks',
      technologies: ['React.js', 'Angular', 'Vue.js', 'TypeScript', 'HTML5/CSS3', 'Bootstrap']
    },
    {
      category: 'Backend Technologies',
      description: 'Robust backend development with scalable server-side solutions',
      technologies: ['Node.js', 'Python/Django', 'PHP/Laravel', 'Java/Spring', 'ASP.NET', 'Ruby on Rails']
    },
    {
      category: 'Database Solutions',
      description: 'Comprehensive database design and management solutions',
      technologies: ['MySQL', 'PostgreSQL', 'MongoDB', 'Redis', 'Firebase', 'AWS RDS']
    },
    {
      category: 'Cloud & DevOps',
      description: 'Modern cloud deployment and DevOps practices for scalable solutions',
      technologies: ['AWS', 'Google Cloud', 'Azure', 'Docker', 'Kubernetes', 'CI/CD Pipelines']
    }
  ];

  return (
    <div className="min-h-screen bg-slate-900 relative">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        <div className="container mx-auto px-6 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            <div className="lg:col-span-3">
              <section className="pt-24 pb-16">
                <div className="max-w-4xl">
                  <div className="inline-flex items-center space-x-2 bg-violet-500/20 border border-violet-500/30 rounded-full px-6 py-2 mb-8">
                    <Code className="w-4 h-4 text-violet-400" />
                    <span className="text-violet-400 font-medium">Web Development Thiruvananthapuram • Kerala's Digital Innovation Leaders</span>
                  </div>
                  
                  <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
                    #1 Web Development Company in
                    <span className="bg-gradient-to-r from-violet-400 to-violet-600 bg-clip-text text-transparent"> Thiruvananthapuram</span>
                  </h1>
                  
                  <p className="text-xl text-slate-300 mb-8">
                    Premier web development services in Thiruvananthapuram offering comprehensive website development and digital platform solutions for IT companies, government organizations, tourism businesses, and educational institutions. Our Thiruvananthapuram web development company provides professional web development services including custom web development, e-commerce development, government website development, tourism website development Thiruvananthapuram, and IT company website development. Serving 140+ Thiruvananthapuram projects with proven ₹38Cr+ revenue generation and 2,300% average business growth through strategic web development and digital platform excellence in Kerala's capital city.
                  </p>
                  
                  <div className="flex flex-col sm:flex-row gap-4 mb-8">
                    <Button asChild size="lg" className="bg-gradient-to-r from-violet-500 to-violet-600 hover:from-violet-600 hover:to-violet-700 text-white text-lg px-8 py-4">
                      <Link to="/quote">Get Free Web Development Quote Thiruvananthapuram</Link>
                    </Button>
                    <Button asChild size="lg" variant="outline" className="border-violet-500 text-violet-400 hover:bg-violet-500 hover:text-white text-lg px-8 py-4">
                      <Link to="/contact">Call Thiruvananthapuram Web Experts: +91-8708577598</Link>
                    </Button>
                  </div>

                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                    {achievements.map((achievement, index) => (
                      <div key={index} className="flex items-center justify-center space-x-2 bg-slate-800/50 rounded-lg p-3">
                        <CheckCircle className="w-4 h-4 text-violet-400 flex-shrink-0" />
                        <span className="text-slate-300">{achievement}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </section>

              <section className="mb-16">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Thiruvananthapuram Industry-Specific
                    <span className="bg-gradient-to-r from-violet-400 to-violet-600 bg-clip-text text-transparent"> Web Solutions</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Specialized web development strategies tailored for Thiruvananthapuram's key industries and sectors.
                  </p>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  {industryFocus.map((industry, index) => (
                    <Card key={index} className="bg-slate-900/80 border-violet-500/20 hover:border-violet-500/40 transition-all duration-300">
                      <CardContent className="p-8">
                        <div className="w-12 h-12 bg-gradient-to-r from-violet-500 to-violet-600 rounded-lg flex items-center justify-center mb-6">
                          <industry.icon className="w-6 h-6 text-white" />
                        </div>
                        <h3 className="text-xl font-bold text-white mb-4">{industry.industry}</h3>
                        <p className="text-slate-300 mb-6">{industry.description}</p>
                        <ul className="space-y-2">
                          {industry.features.map((feature, idx) => (
                            <li key={idx} className="flex items-center text-slate-400 text-sm">
                              <CheckCircle className="w-3 h-3 text-violet-400 mr-2 flex-shrink-0" />
                              {feature}
                            </li>
                          ))}
                        </ul>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>

              <section className="mb-16 bg-slate-800/30 rounded-2xl p-8">
                <h3 className="text-2xl font-bold text-white mb-6 text-center">
                  Comprehensive Web Development Services in Thiruvananthapuram
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  {webServices.map((service, index) => (
                    <div key={index} className="bg-slate-900/50 rounded-lg p-4 text-center">
                      <div className="text-violet-400 font-medium text-sm">{service}</div>
                    </div>
                  ))}
                </div>
              </section>

              <section className="mb-16 bg-slate-800/30 rounded-2xl p-8">
                <h3 className="text-2xl font-bold text-white mb-6 text-center">
                  Advanced Technology Stack
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                  {technologyStack.map((tech, index) => (
                    <div key={index} className="bg-slate-900/50 rounded-lg p-6">
                      <h4 className="text-violet-400 font-semibold mb-3">{tech.category}</h4>
                      <p className="text-slate-300 text-sm mb-4">{tech.description}</p>
                      <div className="space-y-1">
                        {tech.technologies.map((technology, idx) => (
                          <div key={idx} className="text-slate-400 text-xs">{technology}</div>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </section>

              <section className="mb-16 bg-slate-800/30 rounded-2xl p-8">
                <h3 className="text-2xl font-bold text-white mb-6 text-center">
                  Local Web Development Thiruvananthapuram Specialization
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                  {localWebFeatures.map((feature, index) => (
                    <div key={index} className="bg-slate-900/50 rounded-lg p-4 text-center">
                      <div className="text-violet-400 font-medium text-sm">{feature}</div>
                    </div>
                  ))}
                </div>
              </section>

              <section className="mb-16 bg-slate-800/30 rounded-2xl p-8">
                <h3 className="text-2xl font-bold text-white mb-6 text-center">
                  Why Choose Our Thiruvananthapuram Web Development Services?
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  <div className="text-center">
                    <div className="w-16 h-16 bg-gradient-to-r from-violet-500 to-violet-600 rounded-full flex items-center justify-center mx-auto mb-4">
                      <Building className="w-8 h-8 text-white" />
                    </div>
                    <h4 className="text-white font-semibold mb-2">IT Hub Expertise</h4>
                    <p className="text-slate-400 text-sm">Deep understanding of Thiruvananthapuram's IT landscape, government sector, and technology requirements for advanced web solutions.</p>
                  </div>
                  <div className="text-center">
                    <div className="w-16 h-16 bg-gradient-to-r from-violet-500 to-violet-600 rounded-full flex items-center justify-center mx-auto mb-4">
                      <TrendingUp className="w-8 h-8 text-white" />
                    </div>
                    <h4 className="text-white font-semibold mb-2">Proven Success</h4>
                    <p className="text-slate-400 text-sm">140+ successful web development projects in Thiruvananthapuram with 2,300% average business growth and ₹38Cr+ revenue generation.</p>
                  </div>
                  <div className="text-center">
                    <div className="w-16 h-16 bg-gradient-to-r from-violet-500 to-violet-600 rounded-full flex items-center justify-center mx-auto mb-4">
                      <Users className="w-8 h-8 text-white" />
                    </div>
                    <h4 className="text-white font-semibold mb-2">Multi-Industry Experience</h4>
                    <p className="text-slate-400 text-sm">Specialized expertise in IT, government, tourism, and education sectors prominent in Thiruvananthapuram market.</p>
                  </div>
                </div>
              </section>

              <section className="mb-16 bg-slate-800/30 rounded-2xl p-8">
                <h3 className="text-2xl font-bold text-white mb-6 text-center">
                  Complete Digital Marketing Solutions Thiruvananthapuram
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  <Link to="/services/seo/thiruvananthapuram" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-violet-400 font-semibold mb-2 group-hover:text-violet-300">SEO Services Thiruvananthapuram</h4>
                    <p className="text-slate-400 text-sm">Search engine optimization for Thiruvananthapuram businesses</p>
                  </Link>
                  <Link to="/services/ppc/thiruvananthapuram" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-violet-400 font-semibold mb-2 group-hover:text-violet-300">Google Ads Thiruvananthapuram</h4>
                    <p className="text-slate-400 text-sm">PPC advertising and Google Ads management for Thiruvananthapuram</p>
                  </Link>
                  <Link to="/services/social-media/thiruvananthapuram" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-violet-400 font-semibold mb-2 group-hover:text-violet-300">Social Media Marketing Thiruvananthapuram</h4>
                    <p className="text-slate-400 text-sm">Social media management for Thiruvananthapuram businesses</p>
                  </Link>
                  <Link to="/services/content/thiruvananthapuram" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-violet-400 font-semibold mb-2 group-hover:text-violet-300">Content Marketing Thiruvananthapuram</h4>
                    <p className="text-slate-400 text-sm">Content creation for Thiruvananthapuram businesses</p>
                  </Link>
                  <Link to="/services/email/thiruvananthapuram" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-violet-400 font-semibold mb-2 group-hover:text-violet-300">Email Marketing Thiruvananthapuram</h4>
                    <p className="text-slate-400 text-sm">Email marketing campaigns for Thiruvananthapuram businesses</p>
                  </Link>
                  <Link to="/portfolio" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-violet-400 font-semibold mb-2 group-hover:text-violet-300">Thiruvananthapuram Success Stories</h4>
                    <p className="text-slate-400 text-sm">See our proven results from Thiruvananthapuram clients</p>
                  </Link>
                </div>
              </section>

              <section className="bg-gradient-to-r from-violet-600 to-violet-500 rounded-2xl p-8 text-center">
                <h2 className="text-3xl md:text-5xl font-bold mb-6">Ready to Build Your Digital Presence in Thiruvananthapuram?</h2>
                <p className="text-xl mb-8">
                  Join 140+ successful Thiruvananthapuram businesses that trust GOD Digital Marketing for web development excellence. Proven solutions delivering 2,300% business growth and ₹38Cr+ revenue generation.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button asChild size="lg" className="bg-white text-violet-600 hover:bg-slate-100 text-lg px-8 py-4">
                    <Link to="/quote">Get Free Web Development Quote</Link>
                  </Button>
                  <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-violet-600 text-lg px-8 py-4">
                    <Link to="/contact">Call Thiruvananthapuram Web Experts: +91-8708577598</Link>
                  </Button>
                </div>
              </section>
            </div>

            <div className="lg:col-span-1">
              <Sidebar currentService="web-development" />
            </div>
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
};

export default WebDevelopmentThiruvananthapuram;
