import React, { useState, lazy, Suspense, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { Menu, X, ChevronDown, Globe, Building, Target } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { motion, AnimatePresence } from 'framer-motion';

// Lazy load the 3D logo for better performance
const Logo3DNav = lazy(() => import('@/components/Logo3DNav'));

// Fallback component for 3D logo loading
const LogoFallback = () => (
  <div className="w-12 h-12 bg-gradient-to-br from-amber-400 via-amber-500 to-amber-600 rounded-xl flex items-center justify-center font-black text-slate-900 text-xl shadow-lg shadow-amber-500/30 border border-amber-400/20 animate-pulse">
    G
  </div>
);

const Navigation = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [isServicesOpen, setIsServicesOpen] = useState(false);
  const [isLocationsOpen, setIsLocationsOpen] = useState(false);
  const [isIndustriesOpen, setIsIndustriesOpen] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const location = useLocation();

  const isActive = (path: string) => location.pathname === path;

  // Mobile detection and optimization
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);

    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Close mobile menu on route change
  useEffect(() => {
    setIsOpen(false);
    setIsServicesOpen(false);
    setIsLocationsOpen(false);
    setIsIndustriesOpen(false);
  }, [location.pathname]);

  // Prevent body scroll when mobile menu is open
  useEffect(() => {
    if (isOpen && isMobile) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, isMobile]);

  const serviceCategories = {
    'SEO Services': [
      { name: 'Local SEO', path: '/services/seo/local' },
      { name: 'International SEO', path: '/services/seo/international' },
      { name: 'E-commerce SEO', path: '/services/seo/ecommerce' },
      { name: 'Technical SEO', path: '/services/seo/technical' },
      { name: 'Enterprise SEO', path: '/services/seo/enterprise' },
      { name: 'SEO Delhi', path: '/services/seo/delhi' },
      { name: 'SEO Mumbai', path: '/services/seo/mumbai' },
      { name: 'SEO Bangalore', path: '/services/seo/bangalore' },
      { name: 'SEO Chennai', path: '/services/seo/chennai' },
      { name: 'SEO Sonipat', path: '/services/seo/sonipat' }
    ],
    'Paid Advertising': [
      { name: 'Google Ads', path: '/services/ppc' },
      { name: 'Facebook Ads', path: '/services/facebook-ads' },
      { name: 'LinkedIn Ads', path: '/services/linkedin-ads' },
      { name: 'YouTube Marketing', path: '/services/youtube-marketing' },
      { name: 'Google Ads Delhi', path: '/services/ppc/delhi' },
      { name: 'Google Ads Mumbai', path: '/services/ppc/mumbai' },
      { name: 'Google Ads Bangalore', path: '/services/ppc/bangalore' },
      { name: 'Google Ads Chennai', path: '/services/ppc/chennai' },
      { name: 'Google Ads Sonipat', path: '/services/ppc/sonipat' }
    ],
    'Social Media & Content': [
      { name: 'Social Media Marketing', path: '/services/social-media' },
      { name: 'Content Marketing', path: '/services/content' },
      { name: 'Social Media Delhi', path: '/services/social-media/delhi' },
      { name: 'Social Media Mumbai', path: '/services/social-media/mumbai' },
      { name: 'Social Media Bangalore', path: '/services/social-media/bangalore' },
      { name: 'Content Marketing Delhi', path: '/services/content/delhi' },
      { name: 'Content Marketing Mumbai', path: '/services/content/mumbai' },
      { name: 'Content Marketing Bangalore', path: '/services/content/bangalore' }
    ],
    'Email & Web Development': [
      { name: 'Email Marketing', path: '/services/email' },
      { name: 'Web Development', path: '/services/web-development' },
      { name: 'Email Marketing Delhi', path: '/services/email/delhi' },
      { name: 'Email Marketing Mumbai', path: '/services/email/mumbai' },
      { name: 'Web Development Delhi', path: '/services/web-development/delhi' }
    ],
    'Advanced Services': [
      { name: 'AI Automation', path: '/services/ai-automation' },
      { name: 'Business Automation', path: '/services/business-automation' },
      { name: 'Business Development', path: '/services/business-development' }
    ],
    'Industry-Specific SEO': [
      { name: 'E-commerce SEO', path: '/services/ecommerce-seo' },
      { name: 'Healthcare SEO', path: '/services/healthcare-seo' },
      { name: 'B2B SEO', path: '/services/b2b-seo' },
      { name: 'Local Business SEO', path: '/services/local-business-seo' },
      { name: 'Travel & Tourism SEO', path: '/services/travel-tourism-seo' },
      { name: 'Real Estate SEO', path: '/services/real-estate-seo' }
    ]
  };

  const topLocations = {
    'Metro Cities': [
      { name: 'Mumbai', path: '/locations/mumbai' },
      { name: 'Delhi', path: '/locations/delhi' },
      { name: 'Bangalore', path: '/locations/bangalore' },
      { name: 'Chennai', path: '/locations/chennai' },
      { name: 'Kolkata', path: '/locations/kolkata' },
      { name: 'Hyderabad', path: '/locations/hyderabad' }
    ],
    'Tier-2 Cities': [
      { name: 'Pune', path: '/locations/pune' },
      { name: 'Jaipur', path: '/locations/jaipur' },
      { name: 'Lucknow', path: '/locations/lucknow' },
      { name: 'Surat', path: '/locations/surat' },
      { name: 'Gurgaon', path: '/locations/haryana/gurgaon' },
      { name: 'Sonipat', path: '/locations/haryana/sonipat' }
    ],
    'States & Regions': [
      { name: 'Maharashtra', path: '/locations/maharashtra' },
      { name: 'Haryana', path: '/locations/haryana' },
      { name: 'Gujarat', path: '/locations/gujarat' },
      { name: 'Karnataka', path: '/locations/karnataka' }
    ],
    'Delhi Districts': [
      { name: 'Central Delhi', path: '/locations/delhi/central-delhi' },
      { name: 'South Delhi', path: '/locations/delhi/south-delhi' },
      { name: 'North Delhi', path: '/locations/delhi/north-delhi' },
      { name: 'East Delhi', path: '/locations/delhi/east-delhi' }
    ]
  };

  const keyIndustries = [
    { name: 'E-commerce SEO', path: '/services/ecommerce-seo' },
    { name: 'Healthcare SEO', path: '/services/healthcare-seo' },
    { name: 'B2B SEO', path: '/services/b2b-seo' },
    { name: 'Local Business SEO', path: '/services/local-business-seo' },
    { name: 'Travel & Tourism SEO', path: '/services/travel-tourism-seo' },
    { name: 'Real Estate SEO', path: '/services/real-estate-seo' }
  ];

  return (
    <nav className="bg-slate-900/95 backdrop-blur-sm border-b border-amber-500/20 sticky top-0 z-50">
      <div className="container mx-auto px-6">
        <div className="flex items-center justify-between h-20">
          {/* Enhanced 3D Logo */}
          <Link to="/" className="flex items-center space-x-3">
            <div className="relative">
              <Suspense fallback={<LogoFallback />}>
                <Logo3DNav />
              </Suspense>
              <div className="absolute -top-1 -right-1 w-4 h-4 bg-gradient-to-br from-emerald-400 to-emerald-500 rounded-full border-2 border-slate-900"></div>
            </div>
            <div>
              <div className="text-2xl font-black text-white tracking-tight">GOD</div>
              <div className="text-xs text-amber-400 font-semibold tracking-wider uppercase -mt-1">Digital Marketing</div>
            </div>
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden lg:flex items-center space-x-6">
            <Link
              to="/"
              className={`hover:text-amber-400 transition-colors font-medium ${isActive('/') ? 'text-amber-400' : 'text-white'}`}
            >
              Home
            </Link>

            {/* Services Mega Menu */}
            <div className="relative group">
              <button
                className={`flex items-center space-x-1 hover:text-amber-400 transition-colors font-medium ${location.pathname.startsWith('/services') ? 'text-amber-400' : 'text-white'}`}
                onMouseEnter={() => setIsServicesOpen(true)}
                onMouseLeave={() => setIsServicesOpen(false)}
              >
                <span>Services</span>
                <ChevronDown className="w-4 h-4" />
              </button>

              <div
                className={`absolute top-full left-0 w-96 bg-slate-800/95 backdrop-blur-sm border border-amber-500/20 rounded-lg shadow-xl transition-all duration-200 ${isServicesOpen ? 'opacity-100 visible translate-y-0' : 'opacity-0 invisible -translate-y-2'}`}
                onMouseEnter={() => setIsServicesOpen(true)}
                onMouseLeave={() => setIsServicesOpen(false)}
              >
                <div className="p-4">
                  <Link
                    to="/services"
                    className="block px-4 py-3 text-amber-400 hover:bg-slate-700/50 rounded font-semibold mb-2"
                  >
                    All Services
                  </Link>
                  <div className="grid grid-cols-1 gap-4">
                    {Object.entries(serviceCategories).map(([category, services]) => (
                      <div key={category}>
                        <h4 className="text-white font-semibold mb-2 px-2">{category}</h4>
                        <div className="space-y-1">
                          {services.map((service) => (
                            <Link
                              key={service.path}
                              to={service.path}
                              className="block px-3 py-1 text-slate-300 hover:text-amber-400 hover:bg-slate-700/50 rounded text-sm transition-colors"
                            >
                              {service.name}
                            </Link>
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>

            {/* Locations Mega Menu */}
            <div className="relative group">
              <button
                className={`flex items-center space-x-1 hover:text-amber-400 transition-colors font-medium ${location.pathname.startsWith('/locations') ? 'text-amber-400' : 'text-white'}`}
                onMouseEnter={() => setIsLocationsOpen(true)}
                onMouseLeave={() => setIsLocationsOpen(false)}
              >
                <Globe className="w-4 h-4" />
                <span>Locations</span>
                <ChevronDown className="w-4 h-4" />
              </button>

              <div
                className={`absolute top-full left-0 w-80 bg-slate-800/95 backdrop-blur-sm border border-amber-500/20 rounded-lg shadow-xl transition-all duration-200 ${isLocationsOpen ? 'opacity-100 visible translate-y-0' : 'opacity-0 invisible -translate-y-2'}`}
                onMouseEnter={() => setIsLocationsOpen(true)}
                onMouseLeave={() => setIsLocationsOpen(false)}
              >
                <div className="p-4">
                  <div className="grid grid-cols-1 gap-4">
                    {Object.entries(topLocations).map(([category, locations]) => (
                      <div key={category}>
                        <h4 className="text-white font-semibold mb-2 px-2">{category}</h4>
                        <div className="grid grid-cols-2 gap-1">
                          {locations.map((location) => (
                            <Link
                              key={location.path}
                              to={location.path}
                              className="block px-3 py-1 text-slate-300 hover:text-amber-400 hover:bg-slate-700/50 rounded text-sm transition-colors"
                            >
                              {location.name}
                            </Link>
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>

            {/* Industries Mega Menu */}
            <div className="relative group">
              <button
                className={`flex items-center space-x-1 hover:text-amber-400 transition-colors font-medium ${location.pathname.startsWith('/industries') ? 'text-amber-400' : 'text-white'}`}
                onMouseEnter={() => setIsIndustriesOpen(true)}
                onMouseLeave={() => setIsIndustriesOpen(false)}
              >
                <Building className="w-4 h-4" />
                <span>Industries</span>
                <ChevronDown className="w-4 h-4" />
              </button>

              <div
                className={`absolute top-full left-0 w-64 bg-slate-800/95 backdrop-blur-sm border border-amber-500/20 rounded-lg shadow-xl transition-all duration-200 ${isIndustriesOpen ? 'opacity-100 visible translate-y-0' : 'opacity-0 invisible -translate-y-2'}`}
                onMouseEnter={() => setIsIndustriesOpen(true)}
                onMouseLeave={() => setIsIndustriesOpen(false)}
              >
                <div className="p-2">
                  {keyIndustries.map((industry) => (
                    <Link
                      key={industry.path}
                      to={industry.path}
                      className="block px-4 py-2 text-slate-300 hover:text-amber-400 hover:bg-slate-700/50 rounded transition-colors"
                    >
                      {industry.name}
                    </Link>
                  ))}
                </div>
              </div>
            </div>

            <Link 
              to="/about" 
              className={`hover:text-amber-400 transition-colors font-medium ${isActive('/about') ? 'text-amber-400' : 'text-white'}`}
            >
              About
            </Link>
            <Link
              to="/portfolio"
              className={`hover:text-amber-400 transition-colors font-medium ${isActive('/portfolio') ? 'text-amber-400' : 'text-white'}`}
            >
              Portfolio
            </Link>
            <Link
              to="/case-studies"
              className={`hover:text-amber-400 transition-colors font-medium ${isActive('/case-studies') ? 'text-amber-400' : 'text-white'}`}
            >
              Case Studies
            </Link>
            <Link
              to="/blog"
              className={`hover:text-amber-400 transition-colors font-medium ${isActive('/blog') ? 'text-amber-400' : 'text-white'}`}
            >
              Blog
            </Link>
            <Link
              to="/resources/seo-tools"
              className={`hover:text-amber-400 transition-colors font-medium ${isActive('/resources/seo-tools') ? 'text-amber-400' : 'text-white'}`}
            >
              SEO Tools
            </Link>
            <Link 
              to="/contact" 
              className={`hover:text-amber-400 transition-colors font-medium ${isActive('/contact') ? 'text-amber-400' : 'text-white'}`}
            >
              Contact
            </Link>
          </div>

          {/* CTA Button */}
          <div className="hidden lg:block">
            <Button asChild className="bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700 text-white font-semibold">
              <Link to="/quote">Get International SEO Strategy</Link>
            </Button>
          </div>

          {/* Mobile Menu Toggle */}
          <button
            className="lg:hidden text-white"
            onClick={() => setIsOpen(!isOpen)}
          >
            {isOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
          </button>
        </div>

        {/* Mobile Navigation */}
        {isOpen && (
          <div className="lg:hidden py-4 border-t border-amber-500/20">
            <div className="flex flex-col space-y-4">
              <Link to="/" className={`font-medium ${isActive('/') ? 'text-amber-400' : 'text-white hover:text-amber-400'}`} onClick={() => setIsOpen(false)}>
                Home
              </Link>
              <Link to="/services" className={`font-medium ${isActive('/services') ? 'text-amber-400' : 'text-white hover:text-amber-400'}`} onClick={() => setIsOpen(false)}>
                Services
              </Link>
              <Link to="/services/seo/local" className={`font-medium pl-4 ${isActive('/services/seo/local') ? 'text-amber-400' : 'text-slate-300 hover:text-amber-400'}`} onClick={() => setIsOpen(false)}>
                Local SEO
              </Link>
              <Link to="/services/seo/international" className={`font-medium pl-4 ${isActive('/services/seo/international') ? 'text-amber-400' : 'text-slate-300 hover:text-amber-400'}`} onClick={() => setIsOpen(false)}>
                International SEO
              </Link>
              <Link to="/services/ai-content" className={`font-medium pl-4 ${isActive('/services/ai-content') ? 'text-amber-400' : 'text-slate-300 hover:text-amber-400'}`} onClick={() => setIsOpen(false)}>
                AI Content Creation
              </Link>
              <Link to="/services/ppc" className={`font-medium pl-4 ${isActive('/services/ppc') ? 'text-amber-400' : 'text-slate-300 hover:text-amber-400'}`} onClick={() => setIsOpen(false)}>
                Google Ads
              </Link>
              <Link to="/about" className={`font-medium ${isActive('/about') ? 'text-amber-400' : 'text-white hover:text-amber-400'}`} onClick={() => setIsOpen(false)}>
                About
              </Link>
              <Link to="/portfolio" className={`font-medium ${isActive('/portfolio') ? 'text-amber-400' : 'text-white hover:text-amber-400'}`} onClick={() => setIsOpen(false)}>
                Portfolio
              </Link>
              <Link to="/case-studies" className={`font-medium ${isActive('/case-studies') ? 'text-amber-400' : 'text-white hover:text-amber-400'}`} onClick={() => setIsOpen(false)}>
                Case Studies
              </Link>
              <Link to="/blog" className={`font-medium ${isActive('/blog') ? 'text-amber-400' : 'text-white hover:text-amber-400'}`} onClick={() => setIsOpen(false)}>
                Blog
              </Link>
              <Link to="/resources/seo-tools" className={`font-medium ${isActive('/resources/seo-tools') ? 'text-amber-400' : 'text-white hover:text-amber-400'}`} onClick={() => setIsOpen(false)}>
                SEO Tools
              </Link>
              <Link to="/contact" className={`font-medium ${isActive('/contact') ? 'text-amber-400' : 'text-white hover:text-amber-400'}`} onClick={() => setIsOpen(false)}>
                Contact
              </Link>
              <Button asChild className="bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700 text-white mt-4">
                <Link to="/quote" onClick={() => setIsOpen(false)}>Get Free Quote</Link>
              </Button>
            </div>
          </div>
        )}
      </div>
    </nav>
  );
};

export default Navigation;
