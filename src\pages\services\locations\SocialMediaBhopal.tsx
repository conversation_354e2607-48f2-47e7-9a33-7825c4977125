import React from 'react';
import { Link } from 'react-router-dom';
import { Share2, CheckCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';
import Sidebar from '@/components/Sidebar';

const SocialMediaBhopal = () => {
  const stats = [
    { metric: '1050+', description: 'Bhopal Brands Managed', detail: 'Across all platforms' },
    { metric: '270%', description: 'Average Engagement Growth', detail: 'For Bhopal clients' },
    { metric: '₹17Cr+', description: 'Revenue Generated', detail: 'Through social campaigns' },
    { metric: '90%', description: 'Brand Awareness Increase', detail: 'Measured growth' }
  ];

  const achievements = [
    'Top Social Media Agency in Bhopal',
    'Government Sector Social Leaders',
    'Education Industry Social Experts',
    'Healthcare Social Specialists',
    'IT Services Social Champions',
    'City of Lakes Digital Social Pioneers'
  ];

  return (
    <div className="min-h-screen bg-slate-900 relative">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        <div className="container mx-auto px-6 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            <div className="lg:col-span-3">
              <section className="pt-24 pb-16">
                <div className="max-w-4xl">
                  <div className="inline-flex items-center space-x-2 bg-purple-500/20 border border-purple-500/30 rounded-full px-6 py-2 mb-8">
                    <Share2 className="w-4 h-4 text-purple-400" />
                    <span className="text-purple-400 font-medium">Social Media Bhopal • City of Lakes Engagement Excellence</span>
                  </div>
                  
                  <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
                    #1 Social Media Marketing Company in
                    <span className="bg-gradient-to-r from-purple-400 to-purple-600 bg-clip-text text-transparent"> Bhopal</span>
                  </h1>
                  
                  <p className="text-xl text-slate-300 mb-8">
                    Comprehensive social media marketing services in Bhopal delivering professional social media management across Facebook, Instagram, LinkedIn, Twitter, and YouTube. Our Bhopal social media agency provides complete social media marketing solutions including content creation, social media advertising, community management, and social media strategy. Expert social media services with proven engagement growth, brand awareness, and lead generation through strategic social media campaigns and social media optimization for 1050+ Bhopal brands across government, education, healthcare, and IT sectors.
                  </p>
                  
                  <div className="flex flex-col sm:flex-row gap-4 mb-8">
                    <Button asChild size="lg" className="bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white text-lg px-8 py-4">
                      <Link to="/quote">Get Free Bhopal Social Media Audit</Link>
                    </Button>
                    <Button asChild size="lg" variant="outline" className="border-purple-500 text-purple-400 hover:bg-purple-500 hover:text-white text-lg px-8 py-4">
                      <Link to="/contact">Call Social Media Experts: +91-**********</Link>
                    </Button>
                  </div>

                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                    {achievements.map((achievement, index) => (
                      <div key={index} className="flex items-center justify-center space-x-2 bg-slate-800/50 rounded-lg p-3">
                        <CheckCircle className="w-4 h-4 text-purple-400 flex-shrink-0" />
                        <span className="text-slate-300">{achievement}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </section>

              <section className="mb-16 bg-slate-800/50 rounded-2xl p-8">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Bhopal Social Media
                    <span className="bg-gradient-to-r from-purple-400 to-purple-600 bg-clip-text text-transparent"> Success Metrics</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Measurable social media results from Bhopal businesses across government, education, healthcare, and IT sectors.
                  </p>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
                  {stats.map((stat, index) => (
                    <div key={index} className="text-center">
                      <div className="text-4xl font-bold text-purple-400 mb-2">{stat.metric}</div>
                      <div className="text-white font-semibold mb-1">{stat.description}</div>
                      <div className="text-slate-400">{stat.detail}</div>
                    </div>
                  ))}
                </div>
              </section>

              <section className="bg-gradient-to-r from-purple-600 to-purple-500 rounded-2xl p-8 text-center">
                <h2 className="text-3xl md:text-5xl font-bold mb-6">Ready to Dominate Bhopal Social Media?</h2>
                <p className="text-xl mb-8">
                  Join 1050+ Bhopal brands that trust GOD Digital Marketing for social media success. Proven strategies that deliver 270% engagement growth and ₹17Cr+ revenue generation.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button asChild size="lg" className="bg-white text-purple-600 hover:bg-slate-100 text-lg px-8 py-4">
                    <Link to="/quote">Get Free Social Media Audit</Link>
                  </Button>
                  <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-purple-600 text-lg px-8 py-4">
                    <Link to="/contact">Call Social Media Experts: +91-**********</Link>
                  </Button>
                </div>
              </section>
            </div>

            <div className="lg:col-span-1">
              <Sidebar currentService="social-media" currentLocation="bhopal" />
            </div>
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
};

export default SocialMediaBhopal;
