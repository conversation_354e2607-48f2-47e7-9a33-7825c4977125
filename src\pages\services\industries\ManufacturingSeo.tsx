import React from 'react';
import { Link } from 'react-router-dom';
import { Factory, Search, TrendingUp, Users, CheckCircle, Building, Star, Crown, Target, Zap, ArrowRight } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';
import Sidebar from '@/components/Sidebar';

const ManufacturingSeo = () => {
  const stats = [
    {
      metric: '950+',
      description: 'Manufacturing Companies Optimized',
      detail: 'Across all industrial sectors'
    },
    {
      metric: '3,400%',
      description: 'Average B2B Lead Growth',
      detail: 'For manufacturing clients'
    },
    {
      metric: '₹650Cr+',
      description: 'Manufacturing Revenue Generated',
      detail: 'Through SEO campaigns'
    },
    {
      metric: '96%',
      description: 'Industrial Keywords Ranking',
      detail: 'Top 10 positions achieved'
    }
  ];

  const achievements = [
    'Top Manufacturing SEO Company in India',
    'Industrial SEO Specialists',
    'B2B Manufacturing Experts',
    'Export Business SEO Leaders',
    'Supply Chain SEO Champions',
    'Industrial Automation SEO Pioneers'
  ];

  const manufacturingSpecializations = [
    {
      type: 'Heavy Industry & Steel SEO',
      description: 'Comprehensive SEO for steel plants, heavy machinery, and large-scale industrial operations',
      icon: Building,
      features: ['Steel Industry SEO', 'Heavy Machinery Optimization', 'Industrial Equipment SEO', 'Mining Industry SEO', 'Power Plant SEO', 'Infrastructure SEO']
    },
    {
      type: 'Automotive & Auto Parts SEO',
      description: 'Specialized SEO for automotive manufacturers, auto parts suppliers, and vehicle assembly plants',
      icon: Star,
      features: ['Auto Manufacturing SEO', 'Auto Parts Supplier SEO', 'Vehicle Assembly SEO', 'Automotive Export SEO', 'EV Manufacturing SEO', 'Auto Component SEO']
    },
    {
      type: 'Textile & Garment Manufacturing SEO',
      description: 'Strategic SEO for textile mills, garment manufacturers, and fashion industry suppliers',
      icon: Crown,
      features: ['Textile Mill SEO', 'Garment Export SEO', 'Fashion Manufacturing SEO', 'Fabric Supplier SEO', 'Apparel Industry SEO', 'Textile Export SEO']
    },
    {
      type: 'Chemical & Pharmaceutical Manufacturing SEO',
      description: 'Advanced SEO for chemical plants, pharmaceutical manufacturers, and specialty chemical companies',
      icon: Target,
      features: ['Chemical Industry SEO', 'Pharma Manufacturing SEO', 'Specialty Chemical SEO', 'API Manufacturing SEO', 'Chemical Export SEO', 'Industrial Chemical SEO']
    }
  ];

  const manufacturingSectors = [
    { name: 'Heavy Industry', clients: '220+', growth: '3,200%' },
    { name: 'Automotive', clients: '180+', growth: '3,600%' },
    { name: 'Textile & Garment', clients: '160+', growth: '2,800%' },
    { name: 'Chemical & Pharma', clients: '140+', growth: '3,800%' },
    { name: 'Electronics', clients: '120+', growth: '4,200%' },
    { name: 'Food Processing', clients: '130+', growth: '2,900%' }
  ];

  const caseStudies = [
    {
      client: 'Leading Steel Manufacturer',
      industry: 'Heavy Industry',
      challenge: 'Large steel plant needed to increase B2B visibility and attract global buyers',
      result: '4,800% B2B inquiry increase',
      metrics: ['1,200+ industrial keywords in top 3', '₹180Cr+ export revenue', '650% increase in international orders']
    },
    {
      client: 'Automotive Parts Supplier',
      industry: 'Automotive Manufacturing',
      challenge: 'Auto parts manufacturer needed to compete with global suppliers online',
      result: '3,600% supplier inquiry growth',
      metrics: ['850+ automotive keywords ranking', '₹125Cr+ parts revenue', '480% increase in OEM partnerships']
    },
    {
      client: 'Textile Export House',
      industry: 'Textile Manufacturing',
      challenge: 'Textile manufacturer needed to dominate global textile export market',
      result: '3,200% export inquiry increase',
      metrics: ['920+ textile keywords in top 5', '₹95Cr+ export revenue', '420% increase in international buyers']
    }
  ];

  const manufacturingSeoStrategies = [
    {
      strategy: 'B2B Manufacturing SEO',
      description: 'Optimize for business-to-business manufacturing searches and procurement',
      benefits: ['B2B lead generation', 'Supplier visibility', 'Export opportunities', 'Industry authority']
    },
    {
      strategy: 'Industrial Product SEO',
      description: 'Target specific industrial products and manufacturing capabilities',
      benefits: ['Product visibility', 'Technical expertise', 'Specification optimization', 'Buyer targeting']
    },
    {
      strategy: 'Export Manufacturing SEO',
      description: 'Focus on international markets and global manufacturing opportunities',
      benefits: ['Global visibility', 'Export growth', 'International buyers', 'Market expansion']
    },
    {
      strategy: 'Supply Chain SEO',
      description: 'Optimize for supply chain partnerships and vendor relationships',
      benefits: ['Supplier network', 'Partnership opportunities', 'Vendor visibility', 'Supply chain authority']
    }
  ];

  return (
    <div className="min-h-screen bg-slate-900 relative">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        <div className="container mx-auto px-6 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            <div className="lg:col-span-3">
              <section className="pt-24 pb-16">
                <div className="max-w-4xl">
                  <div className="inline-flex items-center space-x-2 bg-red-500/20 border border-red-500/30 rounded-full px-6 py-2 mb-8">
                    <Factory className="w-4 h-4 text-red-400" />
                    <span className="text-red-400 font-medium">Manufacturing SEO • Industrial Optimization Excellence</span>
                  </div>
                  
                  <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
                    #1 Manufacturing SEO Company in
                    <span className="bg-gradient-to-r from-red-400 to-red-600 bg-clip-text text-transparent"> India</span>
                  </h1>
                  
                  <p className="text-xl text-slate-300 mb-8">
                    Premier manufacturing SEO services offering comprehensive search engine optimization solutions for industrial companies, manufacturing plants, export businesses, and B2B suppliers. Our manufacturing SEO company provides professional SEO services with industrial SEO optimization, B2B manufacturing SEO, export business SEO, and supply chain SEO. Serving 950+ manufacturing companies across all industrial sectors with proven ₹650Cr+ revenue generation and 3,400% average B2B lead growth for manufacturing clients through strategic search engine optimization and industrial digital marketing excellence.
                  </p>
                  
                  <div className="flex flex-col sm:flex-row gap-4 mb-8">
                    <Button asChild size="lg" className="bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white text-lg px-8 py-4">
                      <Link to="/quote">Get Free Manufacturing SEO Audit</Link>
                    </Button>
                    <Button asChild size="lg" variant="outline" className="border-red-500 text-red-400 hover:bg-red-500 hover:text-white text-lg px-8 py-4">
                      <Link to="/contact">Call Manufacturing SEO Experts: +91-8708577598</Link>
                    </Button>
                  </div>

                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                    {achievements.map((achievement, index) => (
                      <div key={index} className="flex items-center justify-center space-x-2 bg-slate-800/50 rounded-lg p-3">
                        <CheckCircle className="w-4 h-4 text-red-400 flex-shrink-0" />
                        <span className="text-slate-300">{achievement}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </section>

              <section className="mb-16 bg-slate-800/50 rounded-2xl p-8">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Manufacturing SEO
                    <span className="bg-gradient-to-r from-red-400 to-red-600 bg-clip-text text-transparent"> Success Metrics</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Measurable manufacturing SEO results from companies across all industrial sectors and markets.
                  </p>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
                  {stats.map((stat, index) => (
                    <div key={index} className="text-center">
                      <div className="text-4xl font-bold text-red-400 mb-2">{stat.metric}</div>
                      <div className="text-white font-semibold mb-1">{stat.description}</div>
                      <div className="text-slate-400">{stat.detail}</div>
                    </div>
                  ))}
                </div>
              </section>

              <section className="mb-16">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Manufacturing SEO Strategies We
                    <span className="bg-gradient-to-r from-red-400 to-red-600 bg-clip-text text-transparent"> Master</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Specialized SEO strategies designed for manufacturing success across all industrial sectors and markets.
                  </p>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  {manufacturingSpecializations.map((spec, index) => (
                    <Card key={index} className="bg-slate-900/80 border-red-500/20 hover:border-red-500/40 transition-all duration-300">
                      <CardContent className="p-8">
                        <div className="w-12 h-12 bg-gradient-to-r from-red-500 to-red-600 rounded-lg flex items-center justify-center mb-6">
                          <spec.icon className="w-6 h-6 text-white" />
                        </div>
                        <h3 className="text-xl font-bold text-white mb-4">{spec.type}</h3>
                        <p className="text-slate-300 mb-6">{spec.description}</p>
                        <ul className="space-y-2">
                          {spec.features.map((feature, idx) => (
                            <li key={idx} className="flex items-center text-slate-400 text-sm">
                              <CheckCircle className="w-3 h-3 text-red-400 mr-2 flex-shrink-0" />
                              {feature}
                            </li>
                          ))}
                        </ul>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>

              {/* Manufacturing Sectors */}
              <section className="mb-16 bg-slate-800/30 rounded-2xl p-8">
                <h3 className="text-2xl font-bold text-white mb-6 text-center">
                  Manufacturing Sector Expertise
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-6">
                  {manufacturingSectors.map((sector, index) => (
                    <div key={index} className="text-center bg-slate-900/50 rounded-lg p-4">
                      <h4 className="text-red-400 font-semibold mb-2">{sector.name}</h4>
                      <div className="text-2xl font-bold text-white mb-1">{sector.clients}</div>
                      <div className="text-slate-400 text-sm mb-2">Companies Served</div>
                      <div className="text-green-400 font-semibold text-sm">{sector.growth} avg growth</div>
                    </div>
                  ))}
                </div>
              </section>

              {/* Manufacturing SEO Strategies */}
              <section className="mb-16 bg-slate-800/30 rounded-2xl p-8">
                <h3 className="text-2xl font-bold text-white mb-6 text-center">
                  Advanced Manufacturing SEO Strategies
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                  {manufacturingSeoStrategies.map((strategy, index) => (
                    <div key={index} className="bg-slate-900/50 rounded-lg p-6">
                      <h4 className="text-red-400 font-semibold mb-3">{strategy.strategy}</h4>
                      <p className="text-slate-300 text-sm mb-4">{strategy.description}</p>
                      <ul className="space-y-1">
                        {strategy.benefits.map((benefit, idx) => (
                          <li key={idx} className="text-slate-400 text-xs flex items-center">
                            <CheckCircle className="w-3 h-3 text-red-400 mr-2 flex-shrink-0" />
                            {benefit}
                          </li>
                        ))}
                      </ul>
                    </div>
                  ))}
                </div>
              </section>

              {/* Case Studies */}
              <section className="mb-16">
                <h3 className="text-2xl font-bold text-white mb-8 text-center">
                  Manufacturing SEO Success Stories
                </h3>
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                  {caseStudies.map((study, index) => (
                    <Card key={index} className="bg-slate-900/60 border-red-500/20 hover:border-red-500/40 transition-all duration-300">
                      <CardContent className="p-6">
                        <h4 className="text-red-400 font-semibold mb-2">{study.client}</h4>
                        <p className="text-slate-400 text-sm mb-3">{study.industry}</p>
                        <p className="text-slate-300 text-sm mb-4">{study.challenge}</p>
                        <div className="bg-slate-800/50 rounded p-3 mb-4">
                          <div className="text-green-400 font-semibold mb-2">{study.result}</div>
                          <ul className="space-y-1">
                            {study.metrics.map((metric, idx) => (
                              <li key={idx} className="text-slate-400 text-xs flex items-center">
                                <CheckCircle className="w-3 h-3 text-red-400 mr-2 flex-shrink-0" />
                                {metric}
                              </li>
                            ))}
                          </ul>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>

              {/* Internal Linking Section */}
              <section className="mb-16 bg-slate-800/30 rounded-2xl p-8">
                <h3 className="text-2xl font-bold text-white mb-6 text-center">
                  Complete Manufacturing Digital Marketing Solutions
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  <Link to="/services/ppc" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-red-400 font-semibold mb-2 group-hover:text-red-300">Manufacturing Google Ads</h4>
                    <p className="text-slate-400 text-sm">PPC advertising for industrial and manufacturing companies</p>
                  </Link>
                  <Link to="/services/social-media" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-red-400 font-semibold mb-2 group-hover:text-red-300">Manufacturing Social Media</h4>
                    <p className="text-slate-400 text-sm">B2B social media marketing for manufacturing companies</p>
                  </Link>
                  <Link to="/services/content" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-red-400 font-semibold mb-2 group-hover:text-red-300">Industrial Content Marketing</h4>
                    <p className="text-slate-400 text-sm">Technical content creation and industrial marketing</p>
                  </Link>
                  <Link to="/services/email" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-red-400 font-semibold mb-2 group-hover:text-red-300">Manufacturing Email Marketing</h4>
                    <p className="text-slate-400 text-sm">B2B email campaigns and industrial communication</p>
                  </Link>
                  <Link to="/services/web-development" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-red-400 font-semibold mb-2 group-hover:text-red-300">Industrial Website Development</h4>
                    <p className="text-slate-400 text-sm">Manufacturing website and B2B platform development</p>
                  </Link>
                  <Link to="/portfolio" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-red-400 font-semibold mb-2 group-hover:text-red-300">Manufacturing Success Stories</h4>
                    <p className="text-slate-400 text-sm">See proven results from our manufacturing clients</p>
                  </Link>
                </div>
              </section>

              <section className="bg-gradient-to-r from-red-600 to-red-500 rounded-2xl p-8 text-center">
                <h2 className="text-3xl md:text-5xl font-bold mb-6">Ready to Scale Your Manufacturing Business?</h2>
                <p className="text-xl mb-8">
                  Join 950+ manufacturing companies that trust GOD Digital Marketing for industrial SEO success. Proven strategies that deliver 3,400% B2B lead growth and ₹650Cr+ revenue generation.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button asChild size="lg" className="bg-white text-red-600 hover:bg-slate-100 text-lg px-8 py-4">
                    <Link to="/quote">Get Free Manufacturing SEO Audit</Link>
                  </Button>
                  <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-red-600 text-lg px-8 py-4">
                    <Link to="/contact">Call Manufacturing SEO Experts: +91-8708577598</Link>
                  </Button>
                </div>
              </section>
            </div>

            <div className="lg:col-span-1">
              <Sidebar currentService="seo" />
            </div>
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
};

export default ManufacturingSeo;
