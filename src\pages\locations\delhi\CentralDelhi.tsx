import React from 'react';
import { Link } from 'react-router-dom';
import { ArrowRight, MapPin, TrendingUp, Users, CheckCircle, Building, Star, Crown, Landmark } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';

const CentralDelhi = () => {
  const comprehensiveServices = [
    'Search Engine Optimization (SEO) Central Delhi',
    'Local SEO Services Central Delhi', 
    'International SEO Central Delhi',
    'E-commerce SEO Central Delhi',
    'Technical SEO Audit Central Delhi',
    'Enterprise SEO Solutions Central Delhi',
    'Google Ads Management Central Delhi',
    'Pay-Per-Click (PPC) Advertising Central Delhi',
    'Facebook Advertising Central Delhi',
    'Instagram Marketing Central Delhi',
    'LinkedIn Ads Management Central Delhi',
    'YouTube Marketing Services Central Delhi',
    'Social Media Marketing Central Delhi',
    'Social Media Management Central Delhi',
    'Content Marketing Strategy Central Delhi',
    'Content Creation Services Central Delhi',
    'AI Content Generation Central Delhi',
    'Copywriting Services Central Delhi',
    'Blog Writing Services Central Delhi',
    'Social Media Automation Central Delhi',
    'Marketing Automation Central Delhi',
    'Business Process Automation Central Delhi',
    'CRM Automation Central Delhi',
    'Email Marketing Automation Central Delhi',
    'Lead Generation Services Central Delhi',
    'Lead Nurturing Campaigns Central Delhi',
    'Conversion Rate Optimization Central Delhi',
    'Website Development Central Delhi',
    'Web Design Services Central Delhi',
    'E-commerce Development Central Delhi',
    'Mobile App Development Central Delhi',
    'Landing Page Optimization Central Delhi',
    'Email Marketing Central Delhi',
    'Newsletter Marketing Central Delhi',
    'Influencer Marketing Central Delhi',
    'Video Marketing Services Central Delhi',
    'Video Production Central Delhi',
    'Animation Services Central Delhi',
    'AI Chatbot Development Central Delhi',
    'Customer Support Automation Central Delhi',
    'CRM Solutions Central Delhi',
    'Sales Funnel Automation Central Delhi',
    'Marketing Analytics Central Delhi',
    'Digital Strategy Consulting Central Delhi',
    'Brand Strategy Central Delhi',
    'Online Reputation Management Central Delhi',
    'Crisis Management Central Delhi',
    'Public Relations (PR) Central Delhi',
    'Media Planning Central Delhi',
    'Programmatic Advertising Central Delhi'
  ];

  const centralDelhiAreas = [
    'Connaught Place Digital Marketing',
    'Karol Bagh SEO Services',
    'Paharganj Digital Marketing',
    'Rajendra Place SEO Services',
    'Patel Nagar Digital Marketing',
    'Daryaganj SEO Services',
    'Chandni Chowk Digital Marketing',
    'Red Fort Area SEO Services',
    'Jama Masjid Digital Marketing',
    'Kashmere Gate SEO Services',
    'Civil Lines Digital Marketing',
    'Kamla Nagar SEO Services'
  ];

  const industries = [
    'Government Sector Digital Marketing Central Delhi',
    'Tourism & Hospitality Central Delhi',
    'Retail & Shopping Central Delhi',
    'Financial Services Central Delhi',
    'Legal Services Central Delhi',
    'Healthcare Services Central Delhi',
    'Education & Training Central Delhi',
    'Media & Publishing Central Delhi',
    'Real Estate Central Delhi',
    'Consulting Services Central Delhi',
    'Import-Export Business Central Delhi',
    'Wholesale Trading Central Delhi'
  ];

  const serviceCategories = [
    {
      category: 'Government Sector SEO Central Delhi',
      services: ['Government Website SEO', 'Public Sector Digital Marketing', 'Compliance-Based SEO', 'Tender Portal Optimization', 'Citizen Service Promotion'],
      description: 'Specialized SEO and digital marketing for government departments, public sector enterprises, and civic organizations in Central Delhi',
      icon: Landmark
    },
    {
      category: 'Tourism & Heritage Marketing Central Delhi', 
      services: ['Heritage Site Promotion', 'Tourist Attraction Marketing', 'Hotel & Restaurant SEO', 'Travel Agency Marketing', 'Cultural Event Promotion'],
      description: 'Strategic digital marketing for tourism businesses, heritage sites, hotels, and cultural organizations in historic Central Delhi',
      icon: Crown
    },
    {
      category: 'Commercial Hub Marketing Central Delhi',
      services: ['Retail Store SEO', 'Shopping Center Marketing', 'Business Directory Optimization', 'Local Business Promotion', 'Commercial Property Marketing'],
      description: 'Comprehensive digital marketing for commercial establishments, retail businesses, and shopping centers in Central Delhi',
      icon: Building
    },
    {
      category: 'Professional Services Marketing Central Delhi',
      services: ['Legal Firm SEO', 'Financial Services Marketing', 'Consulting Business Promotion', 'Professional Directory Optimization', 'B2B Lead Generation'],
      description: 'Targeted digital marketing for professional services, legal firms, financial advisors, and consulting businesses in Central Delhi',
      icon: Star
    }
  ];

  const stats = [
    {
      metric: '500+',
      description: 'Central Delhi Businesses Served',
      detail: 'Government to commercial sectors'
    },
    {
      metric: '450%',
      description: 'Average Traffic Increase',
      detail: 'For Central Delhi clients'
    },
    {
      metric: '₹15Cr+',
      description: 'Revenue Generated',
      detail: 'For Central Delhi businesses'
    },
    {
      metric: '98%',
      description: 'Client Satisfaction Rate',
      detail: 'Consistent quality delivery'
    }
  ];

  const achievements = [
    'Top Digital Marketing Agency in Central Delhi',
    'Government Sector Marketing Specialist',
    'Heritage Tourism Marketing Expert',
    'Connaught Place Business Partner',
    'Karol Bagh Commercial Hub Specialist',
    'Chandni Chowk Digital Transformation Leader'
  ];

  return (
    <div className="min-h-screen bg-slate-900 relative">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        {/* Hero Section */}
        <section className="pt-32 pb-20">
          <div className="container mx-auto px-6">
            <div className="max-w-5xl mx-auto text-center">
              <div className="inline-flex items-center space-x-2 bg-amber-500/20 border border-amber-500/30 rounded-full px-6 py-2 mb-8">
                <Landmark className="w-4 h-4 text-amber-400" />
                <span className="text-amber-400 font-medium">Central Delhi Digital Marketing • Heart of India's Capital</span>
              </div>
              
              <h1 className="text-5xl md:text-7xl font-bold mb-6 leading-tight">
                Complete Digital Marketing Services in
                <span className="bg-gradient-to-r from-amber-400 to-amber-600 bg-clip-text text-transparent"> Central Delhi</span>
              </h1>
              
              <p className="text-xl text-slate-300 mb-12 max-w-4xl mx-auto">
                Premier digital marketing agency in Central Delhi offering comprehensive 50+ services from advanced SEO to AI-powered automation. Serving 500+ businesses from Connaught Place to Chandni Chowk - specializing in government sector, tourism, heritage sites, and commercial establishments. Expert solutions with proven ₹15Cr+ revenue generation in India's political and commercial heart.
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
                <Button asChild size="lg" className="bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700 text-white text-lg px-8 py-4">
                  <Link to="/quote">Get Free Central Delhi Digital Marketing Audit</Link>
                </Button>
                <Button asChild size="lg" variant="outline" className="border-amber-500 text-amber-400 hover:bg-amber-500 hover:text-white text-lg px-8 py-4">
                  <Link to="/contact">Call Central Delhi Office: +91-9999-XXXX-XX</Link>
                </Button>
              </div>

              {/* Achievements */}
              <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                {achievements.map((achievement, index) => (
                  <div key={index} className="flex items-center justify-center space-x-2 bg-slate-800/50 rounded-lg p-3">
                    <CheckCircle className="w-4 h-4 text-amber-400 flex-shrink-0" />
                    <span className="text-slate-300">{achievement}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* Stats Section */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-6xl font-bold mb-6">
                Central Delhi Digital Marketing
                <span className="bg-gradient-to-r from-amber-400 to-amber-600 bg-clip-text text-transparent"> Success Metrics</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-4xl mx-auto mb-12">
                Measurable results from Central Delhi businesses across all sectors - from government departments to heritage tourism and commercial enterprises in India's capital heart.
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-4 gap-8 mb-16">
              {stats.map((stat, index) => (
                <div key={index} className="text-center">
                  <div className="text-5xl font-bold text-amber-400 mb-2">{stat.metric}</div>
                  <div className="text-white font-semibold mb-1">{stat.description}</div>
                  <div className="text-slate-400">{stat.detail}</div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Service Categories Section */}
        <section className="py-20">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-6xl font-bold mb-6">
                Specialized Digital Marketing Services in
                <span className="bg-gradient-to-r from-amber-400 to-amber-600 bg-clip-text text-transparent"> Central Delhi</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-4xl mx-auto">
                Tailored digital marketing solutions for Central Delhi's unique business landscape - from government sector to heritage tourism and commercial hubs.
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-20">
              {serviceCategories.map((category, index) => (
                <Card key={index} className="bg-slate-900/80 border-amber-500/20 hover:border-amber-500/40 transition-all duration-300">
                  <CardContent className="p-8">
                    <div className="w-12 h-12 bg-gradient-to-r from-amber-500 to-amber-600 rounded-lg flex items-center justify-center mb-6">
                      <category.icon className="w-6 h-6 text-white" />
                    </div>
                    <h3 className="text-2xl font-bold text-white mb-4">{category.category}</h3>
                    <p className="text-slate-300 mb-6">{category.description}</p>
                    <div className="grid grid-cols-1 gap-2">
                      {category.services.map((service, idx) => (
                        <div key={idx} className="flex items-center text-slate-400 text-sm">
                          <CheckCircle className="w-3 h-3 text-amber-400 mr-2 flex-shrink-0" />
                          {service}
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>
      </main>
      
      <Footer />
    </div>
  );
};

export default CentralDelhi;
