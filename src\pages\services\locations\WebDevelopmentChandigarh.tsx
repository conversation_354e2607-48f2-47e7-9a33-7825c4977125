import React from 'react';
import { Link } from 'react-router-dom';
import { Code, CheckCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';
import Sidebar from '@/components/Sidebar';

const WebDevelopmentChandigarh = () => {
  const stats = [
    { metric: '950+', description: 'Websites Developed', detail: 'For Chandigarh businesses' },
    { metric: '99%', description: 'Client Satisfaction Rate', detail: 'Happy customers' },
    { metric: '₹35Cr+', description: 'Revenue Generated', detail: 'Through our websites' },
    { metric: '99.9%', description: 'Website Uptime', detail: 'Reliable hosting' }
  ];

  const achievements = [
    'Top Web Development Company in Chandigarh',
    'Government Sector Web Leaders',
    'Education Industry Web Experts',
    'IT Services Web Specialists',
    'Healthcare Web Champions',
    'Planned City Web Development Pioneers'
  ];

  return (
    <div className="min-h-screen bg-slate-900 relative">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        <div className="container mx-auto px-6 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            <div className="lg:col-span-3">
              <section className="pt-24 pb-16">
                <div className="max-w-4xl">
                  <div className="inline-flex items-center space-x-2 bg-cyan-500/20 border border-cyan-500/30 rounded-full px-6 py-2 mb-8">
                    <Code className="w-4 h-4 text-cyan-400" />
                    <span className="text-cyan-400 font-medium">Web Development Chandigarh • Planned City Digital Excellence</span>
                  </div>
                  
                  <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
                    #1 Web Development Company in
                    <span className="bg-gradient-to-r from-cyan-400 to-cyan-600 bg-clip-text text-transparent"> Chandigarh</span>
                  </h1>
                  
                  <p className="text-xl text-slate-300 mb-8">
                    Professional web development services in Chandigarh delivering comprehensive website design and web application development solutions. Our Chandigarh web development company provides complete web development services including responsive web design, e-commerce development, custom web applications, WordPress development, and mobile app development. Expert web development solutions with proven performance, user experience, and business growth through strategic web development and website optimization for 950+ Chandigarh businesses across government, education, IT, and healthcare sectors.
                  </p>
                  
                  <div className="flex flex-col sm:flex-row gap-4 mb-8">
                    <Button asChild size="lg" className="bg-gradient-to-r from-cyan-500 to-cyan-600 hover:from-cyan-600 hover:to-cyan-700 text-white text-lg px-8 py-4">
                      <Link to="/quote">Get Free Chandigarh Web Consultation</Link>
                    </Button>
                    <Button asChild size="lg" variant="outline" className="border-cyan-500 text-cyan-400 hover:bg-cyan-500 hover:text-white text-lg px-8 py-4">
                      <Link to="/contact">Call Web Experts: +91-**********</Link>
                    </Button>
                  </div>

                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                    {achievements.map((achievement, index) => (
                      <div key={index} className="flex items-center justify-center space-x-2 bg-slate-800/50 rounded-lg p-3">
                        <CheckCircle className="w-4 h-4 text-cyan-400 flex-shrink-0" />
                        <span className="text-slate-300">{achievement}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </section>

              <section className="mb-16 bg-slate-800/50 rounded-2xl p-8">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Chandigarh Web Development
                    <span className="bg-gradient-to-r from-cyan-400 to-cyan-600 bg-clip-text text-transparent"> Success Metrics</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Measurable web development results from Chandigarh businesses across government, education, IT, and healthcare sectors.
                  </p>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
                  {stats.map((stat, index) => (
                    <div key={index} className="text-center">
                      <div className="text-4xl font-bold text-cyan-400 mb-2">{stat.metric}</div>
                      <div className="text-white font-semibold mb-1">{stat.description}</div>
                      <div className="text-slate-400">{stat.detail}</div>
                    </div>
                  ))}
                </div>
              </section>

              {/* Internal Linking Section */}
              <section className="mb-16 bg-slate-800/30 rounded-2xl p-8">
                <h3 className="text-2xl font-bold text-white mb-6 text-center">
                  Complete Digital Marketing Solutions in Chandigarh
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  <Link to="/services/seo/chandigarh" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-cyan-400 font-semibold mb-2 group-hover:text-cyan-300">SEO Services Chandigarh</h4>
                    <p className="text-slate-400 text-sm">Search engine optimization for planned city businesses</p>
                  </Link>
                  <Link to="/services/ppc/chandigarh" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-cyan-400 font-semibold mb-2 group-hover:text-cyan-300">Google Ads Chandigarh</h4>
                    <p className="text-slate-400 text-sm">PPC advertising for government and education sectors</p>
                  </Link>
                  <Link to="/services/social-media/chandigarh" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-cyan-400 font-semibold mb-2 group-hover:text-cyan-300">Social Media Chandigarh</h4>
                    <p className="text-slate-400 text-sm">Social media marketing for planned city businesses</p>
                  </Link>
                  <Link to="/services/content/chandigarh" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-cyan-400 font-semibold mb-2 group-hover:text-cyan-300">Content Marketing Chandigarh</h4>
                    <p className="text-slate-400 text-sm">Content creation for IT and healthcare industries</p>
                  </Link>
                  <Link to="/services/email/chandigarh" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-cyan-400 font-semibold mb-2 group-hover:text-cyan-300">Email Marketing Chandigarh</h4>
                    <p className="text-slate-400 text-sm">Email campaigns for government and IT sectors</p>
                  </Link>
                  <Link to="/portfolio" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-cyan-400 font-semibold mb-2 group-hover:text-cyan-300">Our Success Stories</h4>
                    <p className="text-slate-400 text-sm">See web development success from Chandigarh</p>
                  </Link>
                </div>
              </section>

              <section className="bg-gradient-to-r from-cyan-600 to-cyan-500 rounded-2xl p-8 text-center">
                <h2 className="text-3xl md:text-5xl font-bold mb-6">Ready to Launch Your Chandigarh Website?</h2>
                <p className="text-xl mb-8">
                  Join 950+ Chandigarh businesses that trust GOD Digital Marketing for web development success. Proven solutions that deliver 99% satisfaction and ₹35Cr+ revenue generation.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button asChild size="lg" className="bg-white text-cyan-600 hover:bg-slate-100 text-lg px-8 py-4">
                    <Link to="/quote">Get Free Web Consultation</Link>
                  </Button>
                  <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-cyan-600 text-lg px-8 py-4">
                    <Link to="/contact">Call Web Experts: +91-**********</Link>
                  </Button>
                </div>
              </section>
            </div>

            <div className="lg:col-span-1">
              <Sidebar currentService="web-development" currentLocation="chandigarh" />
            </div>
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
};

export default WebDevelopmentChandigarh;
