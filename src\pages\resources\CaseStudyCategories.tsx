import React from 'react';
import { Link } from 'react-router-dom';
import { FileText, Search, TrendingUp, Users, CheckCircle, Building, Star, Crown, Target, Zap, ArrowRight, BarChart } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';
import Sidebar from '@/components/Sidebar';

const CaseStudyCategories = () => {
  const stats = [
    {
      metric: '150+',
      description: 'Detailed Case Studies',
      detail: 'Across all industries and services'
    },
    {
      metric: '₹500Cr+',
      description: 'Client Revenue Generated',
      detail: 'Documented results and ROI'
    },
    {
      metric: '2,500%',
      description: 'Average Growth Achieved',
      detail: 'Across all case studies'
    },
    {
      metric: '98%',
      description: 'Client Success Rate',
      detail: 'Meeting or exceeding goals'
    }
  ];

  const caseStudyCategories = [
    {
      category: 'SEO Success Stories',
      description: 'Comprehensive SEO case studies showing organic growth and ranking improvements',
      icon: Search,
      caseCount: '45+',
      avgGrowth: '1,850%',
      industries: ['E-commerce', 'Healthcare', 'B2B', 'Local Business', 'Real Estate', 'Travel'],
      featured: [
        { title: 'E-commerce Store: 2,400% Traffic Growth', result: '₹15Cr+ Revenue', timeframe: '6 months' },
        { title: 'Healthcare Practice: 1,800% Patient Growth', result: '₹8.5Cr+ Revenue', timeframe: '8 months' },
        { title: 'B2B Company: 2,200% Lead Generation', result: '₹25Cr+ Sales', timeframe: '12 months' }
      ]
    },
    {
      category: 'PPC & Paid Advertising',
      description: 'Google Ads, Facebook Ads, and paid advertising campaigns with exceptional ROAS',
      icon: Target,
      caseCount: '35+',
      avgGrowth: '1,450%',
      industries: ['E-commerce', 'Real Estate', 'Education', 'Healthcare', 'Finance', 'Travel'],
      featured: [
        { title: 'Fashion Brand: 2,800% ROAS', result: '₹18Cr+ Revenue', timeframe: '4 months' },
        { title: 'Real Estate: 1,650% Lead Growth', result: '₹85Cr+ Sales', timeframe: '6 months' },
        { title: 'SaaS Platform: 2,100% User Acquisition', result: '₹12Cr+ ARR', timeframe: '8 months' }
      ]
    },
    {
      category: 'Social Media Marketing',
      description: 'Social media campaigns driving engagement, followers, and conversions',
      icon: Users,
      caseCount: '30+',
      avgGrowth: '1,200%',
      industries: ['Fashion', 'Food & Beverage', 'Lifestyle', 'Technology', 'Healthcare', 'Education'],
      featured: [
        { title: 'Fashion Brand: 3,500% Engagement Growth', result: '₹22Cr+ Sales', timeframe: '6 months' },
        { title: 'Restaurant Chain: 2,200% Foot Traffic', result: '₹15Cr+ Revenue', timeframe: '5 months' },
        { title: 'Tech Startup: 1,800% Brand Awareness', result: '₹8Cr+ Funding', timeframe: '4 months' }
      ]
    },
    {
      category: 'E-commerce Growth',
      description: 'Online store optimization, conversion improvement, and e-commerce scaling',
      icon: Building,
      caseCount: '25+',
      avgGrowth: '2,100%',
      industries: ['Fashion', 'Electronics', 'Home & Garden', 'Beauty', 'Sports', 'Books'],
      featured: [
        { title: 'Electronics Store: 3,200% Sales Growth', result: '₹45Cr+ Revenue', timeframe: '8 months' },
        { title: 'Fashion Boutique: 2,600% Conversion Rate', result: '₹28Cr+ Sales', timeframe: '6 months' },
        { title: 'Beauty Brand: 1,900% Customer Acquisition', result: '₹18Cr+ Revenue', timeframe: '7 months' }
      ]
    },
    {
      category: 'Local Business Success',
      description: 'Local SEO, Google My Business optimization, and hyperlocal marketing wins',
      icon: Star,
      caseCount: '20+',
      avgGrowth: '1,650%',
      industries: ['Restaurants', 'Medical', 'Legal', 'Home Services', 'Automotive', 'Retail'],
      featured: [
        { title: 'Restaurant Chain: 2,400% Local Visibility', result: '₹12Cr+ Revenue', timeframe: '5 months' },
        { title: 'Medical Practice: 1,800% Patient Inquiries', result: '₹6.5Cr+ Revenue', timeframe: '6 months' },
        { title: 'Home Services: 2,100% Service Calls', result: '₹9Cr+ Revenue', timeframe: '4 months' }
      ]
    },
    {
      category: 'Business Transformation',
      description: 'Complete digital transformation and business automation success stories',
      icon: Crown,
      caseCount: '15+',
      avgGrowth: '3,200%',
      industries: ['Manufacturing', 'Professional Services', 'Technology', 'Healthcare', 'Finance', 'Education'],
      featured: [
        { title: 'Manufacturing Company: 4,500% Digital Growth', result: '₹125Cr+ Revenue', timeframe: '18 months' },
        { title: 'Professional Services: 2,800% Client Acquisition', result: '₹65Cr+ Revenue', timeframe: '12 months' },
        { title: 'Tech Startup: 3,500% Market Expansion', result: '₹85Cr+ Valuation', timeframe: '15 months' }
      ]
    }
  ];

  const industryResults = [
    { industry: 'E-commerce', cases: '35+', avgRevenue: '₹25Cr+', avgGrowth: '2,200%' },
    { industry: 'Healthcare', cases: '28+', avgRevenue: '₹18Cr+', avgGrowth: '1,850%' },
    { industry: 'Real Estate', cases: '22+', avgRevenue: '₹45Cr+', avgGrowth: '1,950%' },
    { industry: 'B2B Services', cases: '25+', avgRevenue: '₹35Cr+', avgGrowth: '1,750%' },
    { industry: 'Local Business', cases: '30+', avgRevenue: '₹12Cr+', avgGrowth: '1,650%' },
    { industry: 'Technology', cases: '18+', avgRevenue: '₹55Cr+', avgGrowth: '2,800%' }
  ];

  const successMetrics = [
    {
      metric: 'Revenue Growth',
      description: 'Average revenue increase across all case studies',
      value: '2,500%',
      icon: TrendingUp
    },
    {
      metric: 'Traffic Increase',
      description: 'Average website traffic growth achieved',
      value: '1,850%',
      icon: BarChart
    },
    {
      metric: 'Lead Generation',
      description: 'Average lead generation improvement',
      value: '2,200%',
      icon: Target
    },
    {
      metric: 'ROI Achievement',
      description: 'Average return on investment delivered',
      value: '1,450%',
      icon: Crown
    }
  ];

  return (
    <div className="min-h-screen bg-slate-900 relative">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        <div className="container mx-auto px-6 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            <div className="lg:col-span-3">
              <section className="pt-24 pb-16">
                <div className="max-w-4xl">
                  <div className="inline-flex items-center space-x-2 bg-amber-500/20 border border-amber-500/30 rounded-full px-6 py-2 mb-8">
                    <FileText className="w-4 h-4 text-amber-400" />
                    <span className="text-amber-400 font-medium">Case Studies • Proven Results & Success Stories</span>
                  </div>
                  
                  <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
                    Digital Marketing Case Studies by
                    <span className="bg-gradient-to-r from-amber-400 to-amber-600 bg-clip-text text-transparent"> GOD Digital Marketing</span>
                  </h1>
                  
                  <p className="text-xl text-slate-300 mb-8">
                    Comprehensive digital marketing case studies showcasing proven results across SEO, PPC, social media marketing, e-commerce optimization, and business transformation. Our case study collection features 150+ detailed success stories with documented ROI, revenue growth, and performance metrics. Explore real client results including ₹500Cr+ revenue generated, 2,500% average growth achieved, and 98% client success rate across all industries. Learn from proven strategies and implementations that delivered exceptional results for businesses of all sizes.
                  </p>
                  
                  <div className="flex flex-col sm:flex-row gap-4 mb-8">
                    <Button asChild size="lg" className="bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700 text-white text-lg px-8 py-4">
                      <Link to="#categories">Explore Case Studies</Link>
                    </Button>
                    <Button asChild size="lg" variant="outline" className="border-amber-500 text-amber-400 hover:bg-amber-500 hover:text-white text-lg px-8 py-4">
                      <Link to="/contact">Discuss Your Project: +91-8708577598</Link>
                    </Button>
                  </div>
                </div>
              </section>

              <section className="mb-16 bg-slate-800/50 rounded-2xl p-8">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Case Study
                    <span className="bg-gradient-to-r from-amber-400 to-amber-600 bg-clip-text text-transparent"> Performance Metrics</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Documented results and proven ROI from our comprehensive case study collection.
                  </p>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
                  {stats.map((stat, index) => (
                    <div key={index} className="text-center">
                      <div className="text-4xl font-bold text-amber-400 mb-2">{stat.metric}</div>
                      <div className="text-white font-semibold mb-1">{stat.description}</div>
                      <div className="text-slate-400">{stat.detail}</div>
                    </div>
                  ))}
                </div>
              </section>

              {/* Success Metrics */}
              <section className="mb-16">
                <h3 className="text-2xl font-bold text-white mb-8 text-center">
                  Average Success Metrics Across All Case Studies
                </h3>
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {successMetrics.map((metric, index) => (
                    <Card key={index} className="bg-slate-900/60 border-amber-500/20 hover:border-amber-500/40 transition-all duration-300">
                      <CardContent className="p-6">
                        <div className="flex items-center mb-4">
                          <div className="w-12 h-12 bg-gradient-to-r from-amber-500 to-amber-600 rounded-lg flex items-center justify-center mr-4">
                            <metric.icon className="w-6 h-6 text-white" />
                          </div>
                          <div>
                            <div className="text-2xl font-bold text-amber-400">{metric.value}</div>
                            <h4 className="text-white font-semibold">{metric.metric}</h4>
                          </div>
                        </div>
                        <p className="text-slate-300 text-sm">{metric.description}</p>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>

              <section id="categories" className="mb-16">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Case Study
                    <span className="bg-gradient-to-r from-amber-400 to-amber-600 bg-clip-text text-transparent"> Categories</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Explore our comprehensive collection of success stories organized by service category.
                  </p>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  {caseStudyCategories.map((category, index) => (
                    <Card key={index} className="bg-slate-900/80 border-amber-500/20 hover:border-amber-500/40 transition-all duration-300">
                      <CardContent className="p-8">
                        <div className="flex items-center justify-between mb-6">
                          <div className="w-12 h-12 bg-gradient-to-r from-amber-500 to-amber-600 rounded-lg flex items-center justify-center">
                            <category.icon className="w-6 h-6 text-white" />
                          </div>
                          <div className="text-right">
                            <div className="text-amber-400 font-semibold">{category.caseCount} cases</div>
                            <div className="text-green-400 text-sm">{category.avgGrowth} avg growth</div>
                          </div>
                        </div>
                        <h3 className="text-xl font-bold text-white mb-4">{category.category}</h3>
                        <p className="text-slate-300 mb-6">{category.description}</p>
                        
                        <div className="mb-6">
                          <h4 className="text-white font-medium mb-3">Industries Covered:</h4>
                          <div className="flex flex-wrap gap-2">
                            {category.industries.map((industry, idx) => (
                              <span key={idx} className="bg-slate-800 text-amber-400 text-xs px-2 py-1 rounded">
                                {industry}
                              </span>
                            ))}
                          </div>
                        </div>

                        <div>
                          <h4 className="text-white font-medium mb-3">Featured Case Studies:</h4>
                          <div className="space-y-3">
                            {category.featured.map((study, idx) => (
                              <div key={idx} className="bg-slate-800/50 rounded p-3">
                                <div className="text-white font-medium text-sm mb-1">{study.title}</div>
                                <div className="flex justify-between text-xs">
                                  <span className="text-green-400">{study.result}</span>
                                  <span className="text-slate-400">{study.timeframe}</span>
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>

              {/* Industry Results */}
              <section className="mb-16 bg-slate-800/30 rounded-2xl p-8">
                <h3 className="text-2xl font-bold text-white mb-6 text-center">
                  Results by Industry
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {industryResults.map((result, index) => (
                    <div key={index} className="bg-slate-900/50 rounded-lg p-6 text-center">
                      <h4 className="text-amber-400 font-semibold mb-3">{result.industry}</h4>
                      <div className="space-y-2">
                        <div>
                          <div className="text-2xl font-bold text-white">{result.cases}</div>
                          <div className="text-slate-400 text-sm">Case Studies</div>
                        </div>
                        <div>
                          <div className="text-lg font-semibold text-green-400">{result.avgRevenue}</div>
                          <div className="text-slate-400 text-sm">Avg Revenue</div>
                        </div>
                        <div>
                          <div className="text-lg font-semibold text-blue-400">{result.avgGrowth}</div>
                          <div className="text-slate-400 text-sm">Avg Growth</div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </section>

              {/* Internal Linking Section */}
              <section className="mb-16 bg-slate-800/30 rounded-2xl p-8">
                <h3 className="text-2xl font-bold text-white mb-6 text-center">
                  Our Digital Marketing Services
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  <Link to="/services/seo" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-amber-400 font-semibold mb-2 group-hover:text-amber-300">SEO Services</h4>
                    <p className="text-slate-400 text-sm">Professional SEO optimization with proven results</p>
                  </Link>
                  <Link to="/services/ppc" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-amber-400 font-semibold mb-2 group-hover:text-amber-300">PPC Advertising</h4>
                    <p className="text-slate-400 text-sm">Google Ads and paid advertising with exceptional ROAS</p>
                  </Link>
                  <Link to="/services/social-media" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-amber-400 font-semibold mb-2 group-hover:text-amber-300">Social Media Marketing</h4>
                    <p className="text-slate-400 text-sm">Social media campaigns driving engagement and conversions</p>
                  </Link>
                  <Link to="/services/ecommerce-seo" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-amber-400 font-semibold mb-2 group-hover:text-amber-300">E-commerce Marketing</h4>
                    <p className="text-slate-400 text-sm">Online store optimization and e-commerce growth</p>
                  </Link>
                  <Link to="/services/business-development" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-amber-400 font-semibold mb-2 group-hover:text-amber-300">Business Development</h4>
                    <p className="text-slate-400 text-sm">Complete business transformation and growth strategies</p>
                  </Link>
                  <Link to="/portfolio" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-amber-400 font-semibold mb-2 group-hover:text-amber-300">Portfolio</h4>
                    <p className="text-slate-400 text-sm">Complete portfolio of our successful projects</p>
                  </Link>
                </div>
              </section>

              <section className="bg-gradient-to-r from-amber-600 to-amber-500 rounded-2xl p-8 text-center">
                <h2 className="text-3xl md:text-5xl font-bold mb-6">Ready to Become Our Next Success Story?</h2>
                <p className="text-xl mb-8">
                  Join 150+ successful businesses that have achieved exceptional growth with our proven digital marketing strategies.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button asChild size="lg" className="bg-white text-amber-600 hover:bg-slate-100 text-lg px-8 py-4">
                    <Link to="/quote">Get Your Success Strategy</Link>
                  </Button>
                  <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-amber-600 text-lg px-8 py-4">
                    <Link to="/contact">Discuss Your Goals: +91-8708577598</Link>
                  </Button>
                </div>
              </section>
            </div>

            <div className="lg:col-span-1">
              <Sidebar currentService="case-studies" />
            </div>
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
};

export default CaseStudyCategories;
