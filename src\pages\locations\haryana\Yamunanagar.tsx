import React from 'react';
import { Link } from 'react-router-dom';
import { ArrowRight, MapPin, TrendingUp, Users, CheckCircle, Building, Star, Droplets } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';

const Yamunanagar = () => {
  const services = [
    'SEO Services Yamunanagar',
    'Google Ads Management Yamunanagar',
    'Social Media Marketing Yamunanagar',
    'Local SEO Yamunanagar',
    'E-commerce SEO Yamunanagar',
    'Content Marketing Yamunanagar',
    'Website Development Yamunanagar',
    'Digital Marketing Consulting Yamunanagar'
  ];

  const industries = [
    'Paper & Pulp Industry Yamunanagar',
    'Plywood Manufacturing Yamunanagar',
    'Sugar Industry Yamunanagar',
    'Agriculture Yamunanagar',
    'Real Estate Yamunanagar',
    'Healthcare Yamunanagar',
    'Education Yamunanagar',
    'Retail & E-commerce Yamunanagar'
  ];

  const areas = [
    'Jagadhri Digital Marketing',
    'Bilaspur SEO Services',
    'Chhachhrauli Digital Marketing',
    'Radaur SEO Services',
    'Mustafabad Digital Marketing',
    'Sadhaura SEO Services',
    'Yamuna Nagar City Digital Marketing',
    'Industrial Area SEO Services'
  ];

  const stats = [
    {
      metric: '75+',
      description: 'Yamunanagar Businesses Served',
      detail: 'Across all sectors'
    },
    {
      metric: '340%',
      description: 'Average Traffic Increase',
      detail: 'For Yamunanagar clients'
    },
    {
      metric: '₹22L+',
      description: 'Revenue Generated',
      detail: 'For Yamunanagar businesses'
    }
  ];

  return (
    <div className="min-h-screen bg-slate-900 relative">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        {/* Hero Section */}
        <section className="pt-32 pb-20">
          <div className="container mx-auto px-6">
            <div className="max-w-4xl mx-auto text-center">
              <div className="inline-flex items-center space-x-2 bg-teal-500/20 border border-teal-500/30 rounded-full px-6 py-2 mb-8">
                <Droplets className="w-4 h-4 text-teal-400" />
                <span className="text-teal-400 font-medium">Yamunanagar Digital Marketing • Paper & Plywood Hub</span>
              </div>
              
              <h1 className="text-5xl md:text-6xl font-bold mb-6 leading-tight">
                #1 Digital Marketing Company in
                <span className="bg-gradient-to-r from-teal-400 to-teal-600 bg-clip-text text-transparent"> Yamunanagar</span>
              </h1>
              
              <p className="text-xl text-slate-300 mb-12 max-w-3xl mx-auto">
                Leading digital marketing agency in Yamunanagar helping businesses dominate online. From paper mills to plywood manufacturers, we've helped 75+ businesses grow with proven SEO, Google Ads, and digital marketing strategies across all Yamunanagar areas.
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button asChild size="lg" className="bg-gradient-to-r from-teal-500 to-teal-600 hover:from-teal-600 hover:to-teal-700 text-white text-lg px-8 py-4">
                  <Link to="/quote">Get Free Yamunanagar SEO Audit</Link>
                </Button>
                <Button asChild size="lg" variant="outline" className="border-teal-500 text-teal-400 hover:bg-teal-500 hover:text-white text-lg px-8 py-4">
                  <Link to="/contact">Call Yamunanagar Office</Link>
                </Button>
              </div>
            </div>
          </div>
        </section>

        {/* Stats Section */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Yamunanagar Digital Marketing
                <span className="bg-gradient-to-r from-teal-400 to-teal-600 bg-clip-text text-transparent"> Success Stories</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto mb-12">
                Real results from Yamunanagar businesses across industries - from traditional paper mills to modern plywood manufacturers in this industrial hub.
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
              {stats.map((stat, index) => (
                <div key={index} className="text-center">
                  <div className="text-5xl font-bold text-teal-400 mb-2">{stat.metric}</div>
                  <div className="text-white font-semibold mb-1">{stat.description}</div>
                  <div className="text-slate-400">{stat.detail}</div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Services Section */}
        <section className="py-20">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Digital Marketing Services in
                <span className="bg-gradient-to-r from-teal-400 to-teal-600 bg-clip-text text-transparent"> Yamunanagar</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto">
                Comprehensive digital marketing solutions for Yamunanagar businesses looking to dominate online in the Paper & Plywood Hub.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {services.map((service, index) => (
                <Card key={index} className="bg-slate-900/80 border-teal-500/20 hover:border-teal-500/40 transition-all duration-300">
                  <CardContent className="p-6 text-center">
                    <Building className="w-8 h-8 text-teal-400 mx-auto mb-4" />
                    <h3 className="text-white font-semibold mb-2">{service}</h3>
                    <Button asChild size="sm" variant="outline" className="border-teal-500 text-teal-400 hover:bg-teal-500 hover:text-white">
                      <Link to="/quote">Learn More</Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Industries Section */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Yamunanagar Industries We
                <span className="bg-gradient-to-r from-teal-400 to-teal-600 bg-clip-text text-transparent"> Serve</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto">
                Specialized digital marketing strategies for Yamunanagar's paper, plywood, and manufacturing industries.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {industries.map((industry, index) => (
                <Card key={index} className="bg-slate-900/80 border-teal-500/20 hover:border-teal-500/40 transition-all duration-300">
                  <CardContent className="p-6 text-center">
                    <Star className="w-8 h-8 text-teal-400 mx-auto mb-4" />
                    <h3 className="text-white font-semibold mb-2">{industry}</h3>
                    <Button asChild size="sm" variant="outline" className="border-teal-500 text-teal-400 hover:bg-teal-500 hover:text-white">
                      <Link to="/quote">Get Strategy</Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Areas Section */}
        <section className="py-20">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Yamunanagar Areas We
                <span className="bg-gradient-to-r from-teal-400 to-teal-600 bg-clip-text text-transparent"> Cover</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto">
                Local digital marketing expertise across all major Yamunanagar areas including Jagadhri and industrial zones.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {areas.map((area, index) => (
                <Card key={index} className="bg-slate-900/80 border-teal-500/20 hover:border-teal-500/40 transition-all duration-300">
                  <CardContent className="p-6 text-center">
                    <MapPin className="w-8 h-8 text-teal-400 mx-auto mb-4" />
                    <h3 className="text-white font-semibold mb-2">{area}</h3>
                    <Button asChild size="sm" variant="outline" className="border-teal-500 text-teal-400 hover:bg-teal-500 hover:text-white">
                      <Link to="/contact">Contact Us</Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Why Choose Us Section */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Why Choose GOD Digital Marketing in
                <span className="bg-gradient-to-r from-teal-400 to-teal-600 bg-clip-text text-transparent"> Yamunanagar?</span>
              </h2>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              <Card className="bg-slate-900/80 border-teal-500/20">
                <CardContent className="p-8">
                  <TrendingUp className="w-12 h-12 text-teal-400 mb-6" />
                  <h3 className="text-xl font-bold text-white mb-4">Industrial Expertise</h3>
                  <p className="text-slate-300">Deep understanding of Yamunanagar's paper & pulp industry, plywood manufacturing, and industrial business dynamics.</p>
                </CardContent>
              </Card>

              <Card className="bg-slate-900/80 border-teal-500/20">
                <CardContent className="p-8">
                  <Users className="w-12 h-12 text-teal-400 mb-6" />
                  <h3 className="text-xl font-bold text-white mb-4">Proven Yamunanagar Results</h3>
                  <p className="text-slate-300">75+ successful Yamunanagar campaigns with measurable ROI improvements and business growth across manufacturing sectors.</p>
                </CardContent>
              </Card>

              <Card className="bg-slate-900/80 border-teal-500/20">
                <CardContent className="p-8">
                  <CheckCircle className="w-12 h-12 text-teal-400 mb-6" />
                  <h3 className="text-xl font-bold text-white mb-4">Manufacturing Focus</h3>
                  <p className="text-slate-300">Specialized expertise in B2B marketing for manufacturing industries with focus on lead generation and business growth.</p>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 bg-gradient-to-r from-teal-600 to-teal-500">
          <div className="container mx-auto px-6 text-center">
            <h2 className="text-4xl md:text-5xl font-bold mb-6">Ready to Dominate Yamunanagar's Digital Market?</h2>
            <p className="text-xl mb-8 max-w-3xl mx-auto">
              Join 75+ Yamunanagar businesses that trust GOD Digital Marketing for their online growth. From paper mills to plywood manufacturers, we deliver results.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button asChild size="lg" className="bg-white text-teal-600 hover:bg-slate-100 text-lg px-8 py-4">
                <Link to="/quote">Get Free Yamunanagar SEO Audit</Link>
              </Button>
              <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-teal-600 text-lg px-8 py-4">
                <Link to="/contact">Call Yamunanagar Office Now</Link>
              </Button>
            </div>
          </div>
        </section>
      </main>
      
      <Footer />
    </div>
  );
};

export default Yamunanagar;
