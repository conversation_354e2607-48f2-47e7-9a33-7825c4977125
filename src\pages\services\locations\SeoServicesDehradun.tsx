import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { ArrowRight, Search, TrendingUp, Users, CheckCircle, Building, Star, Crown, Target, Zap } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';
import Sidebar from '@/components/Sidebar';

const SeoServicesDehradun = () => {
  const seoServices = [
    'SEO Services Dehradun',
    'Local SEO Dehradun',
    'Google My Business Optimization Dehradun',
    'Website SEO Audit Dehradun',
    'Keyword Research Dehradun',
    'On-Page SEO Dehradun',
    'Off-Page SEO Dehradun',
    'Technical SEO Dehradun',
    'E-commerce SEO Dehradun',
    'Mobile SEO Dehradun',
    'Voice Search SEO Dehradun',
    'Schema Markup Dehradun',
    'Link Building Dehradun',
    'Content SEO Dehradun',
    'Local Citation Building Dehradun',
    'Education Sector SEO Dehradun',
    'Tourism Industry SEO Dehradun',
    'Healthcare SEO Dehradun',
    'Real Estate SEO Dehradun',
    'Manufacturing SEO Dehradun'
  ];

  const industryFocus = [
    {
      industry: 'Education & EdTech SEO',
      description: 'Specialized SEO for Dehradun\'s renowned educational institutions and emerging EdTech startups',
      icon: Building,
      features: ['University SEO', 'Online Course Optimization', 'Student Recruitment SEO', 'Educational Content Marketing']
    },
    {
      industry: 'Tourism & Hospitality SEO',
      description: 'Strategic SEO for Dehradun\'s thriving tourism industry and gateway to Uttarakhand hills',
      icon: Star,
      features: ['Hotel Booking SEO', 'Adventure Tourism SEO', 'Travel Package Optimization', 'Local Attraction SEO']
    },
    {
      industry: 'Healthcare & Wellness SEO',
      description: 'Comprehensive SEO for Dehradun\'s growing healthcare sector and wellness centers',
      icon: Crown,
      features: ['Hospital SEO', 'Clinic Optimization', 'Medical Tourism SEO', 'Ayurvedic Center SEO']
    },
    {
      industry: 'Manufacturing & Industrial SEO',
      description: 'B2B SEO solutions for Dehradun\'s manufacturing units and industrial establishments',
      icon: Target,
      features: ['Industrial SEO', 'B2B Lead Generation', 'Manufacturing Process SEO', 'Export Business SEO']
    }
  ];

  const localSeoFeatures = [
    'Dehradun Local Search Optimization',
    'Uttarakhand State SEO Coverage',
    'Hill Station Tourism SEO',
    'Educational Hub Optimization',
    'Local Business Directory Listings',
    'Dehradun Map Pack Optimization',
    'Regional Keyword Targeting',
    'Local Content Marketing',
    'Community Engagement SEO',
    'Seasonal Tourism SEO'
  ];

  const achievements = [
    '150+ Dehradun Businesses Optimized',
    '2,400% Average Traffic Growth',
    '₹25Cr+ Revenue Generated for Clients',
    '95% First Page Rankings Achieved',
    'Top SEO Company in Uttarakhand',
    'Education Sector SEO Specialists'
  ];

  return (
    <div className="min-h-screen bg-slate-900 relative">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        <div className="container mx-auto px-6 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            <div className="lg:col-span-3">
              <section className="pt-24 pb-16">
                <div className="max-w-4xl">
                  <div className="inline-flex items-center space-x-2 bg-emerald-500/20 border border-emerald-500/30 rounded-full px-6 py-2 mb-8">
                    <Search className="w-4 h-4 text-emerald-400" />
                    <span className="text-emerald-400 font-medium">SEO Services Dehradun • Uttarakhand's Digital Marketing Leaders</span>
                  </div>
                  
                  <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
                    #1 SEO Company in
                    <span className="bg-gradient-to-r from-emerald-400 to-emerald-600 bg-clip-text text-transparent"> Dehradun</span>
                  </h1>
                  
                  <p className="text-xl text-slate-300 mb-8">
                    Premier SEO services in Dehradun offering comprehensive search engine optimization solutions for educational institutions, tourism businesses, healthcare providers, and manufacturing companies. Our Dehradun SEO company provides professional SEO services including local SEO Dehradun, Google My Business optimization, education sector SEO, tourism industry SEO, and healthcare SEO. Serving 150+ Dehradun businesses with proven ₹25Cr+ revenue generation and 2,400% average traffic growth through strategic search engine optimization and digital marketing excellence in Uttarakhand's capital city.
                  </p>
                  
                  <div className="flex flex-col sm:flex-row gap-4 mb-8">
                    <Button asChild size="lg" className="bg-gradient-to-r from-emerald-500 to-emerald-600 hover:from-emerald-600 hover:to-emerald-700 text-white text-lg px-8 py-4">
                      <Link to="/quote">Get Free SEO Audit Dehradun</Link>
                    </Button>
                    <Button asChild size="lg" variant="outline" className="border-emerald-500 text-emerald-400 hover:bg-emerald-500 hover:text-white text-lg px-8 py-4">
                      <Link to="/contact">Call Dehradun SEO Experts: +91-**********</Link>
                    </Button>
                  </div>

                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                    {achievements.map((achievement, index) => (
                      <div key={index} className="flex items-center justify-center space-x-2 bg-slate-800/50 rounded-lg p-3">
                        <CheckCircle className="w-4 h-4 text-emerald-400 flex-shrink-0" />
                        <span className="text-slate-300">{achievement}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </section>

              <section className="mb-16">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Dehradun Industry-Specific
                    <span className="bg-gradient-to-r from-emerald-400 to-emerald-600 bg-clip-text text-transparent"> SEO Solutions</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Specialized SEO strategies tailored for Dehradun's key industries and business sectors.
                  </p>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  {industryFocus.map((industry, index) => (
                    <Card key={index} className="bg-slate-900/80 border-emerald-500/20 hover:border-emerald-500/40 transition-all duration-300">
                      <CardContent className="p-8">
                        <div className="w-12 h-12 bg-gradient-to-r from-emerald-500 to-emerald-600 rounded-lg flex items-center justify-center mb-6">
                          <industry.icon className="w-6 h-6 text-white" />
                        </div>
                        <h3 className="text-xl font-bold text-white mb-4">{industry.industry}</h3>
                        <p className="text-slate-300 mb-6">{industry.description}</p>
                        <ul className="space-y-2">
                          {industry.features.map((feature, idx) => (
                            <li key={idx} className="flex items-center text-slate-400 text-sm">
                              <CheckCircle className="w-3 h-3 text-emerald-400 mr-2 flex-shrink-0" />
                              {feature}
                            </li>
                          ))}
                        </ul>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>

              <section className="mb-16 bg-slate-800/30 rounded-2xl p-8">
                <h3 className="text-2xl font-bold text-white mb-6 text-center">
                  Comprehensive SEO Services in Dehradun
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  {seoServices.map((service, index) => (
                    <div key={index} className="bg-slate-900/50 rounded-lg p-4 text-center">
                      <div className="text-emerald-400 font-medium text-sm">{service}</div>
                    </div>
                  ))}
                </div>
              </section>

              <section className="mb-16 bg-slate-800/30 rounded-2xl p-8">
                <h3 className="text-2xl font-bold text-white mb-6 text-center">
                  Local SEO Dehradun Specialization
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                  {localSeoFeatures.map((feature, index) => (
                    <div key={index} className="bg-slate-900/50 rounded-lg p-4 text-center">
                      <div className="text-emerald-400 font-medium text-sm">{feature}</div>
                    </div>
                  ))}
                </div>
              </section>

              <section className="mb-16 bg-slate-800/30 rounded-2xl p-8">
                <h3 className="text-2xl font-bold text-white mb-6 text-center">
                  Why Choose Our Dehradun SEO Services?
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  <div className="text-center">
                    <div className="w-16 h-16 bg-gradient-to-r from-emerald-500 to-emerald-600 rounded-full flex items-center justify-center mx-auto mb-4">
                      <Building className="w-8 h-8 text-white" />
                    </div>
                    <h4 className="text-white font-semibold mb-2">Local Market Expertise</h4>
                    <p className="text-slate-400 text-sm">Deep understanding of Dehradun's business landscape, education sector, and tourism industry for targeted SEO strategies.</p>
                  </div>
                  <div className="text-center">
                    <div className="w-16 h-16 bg-gradient-to-r from-emerald-500 to-emerald-600 rounded-full flex items-center justify-center mx-auto mb-4">
                      <TrendingUp className="w-8 h-8 text-white" />
                    </div>
                    <h4 className="text-white font-semibold mb-2">Proven Results</h4>
                    <p className="text-slate-400 text-sm">150+ successful SEO campaigns in Dehradun with 2,400% average traffic growth and ₹25Cr+ revenue generation.</p>
                  </div>
                  <div className="text-center">
                    <div className="w-16 h-16 bg-gradient-to-r from-emerald-500 to-emerald-600 rounded-full flex items-center justify-center mx-auto mb-4">
                      <Users className="w-8 h-8 text-white" />
                    </div>
                    <h4 className="text-white font-semibold mb-2">Industry Specialization</h4>
                    <p className="text-slate-400 text-sm">Specialized expertise in education, tourism, healthcare, and manufacturing sectors dominant in Dehradun market.</p>
                  </div>
                </div>
              </section>

              <section className="mb-16 bg-slate-800/30 rounded-2xl p-8">
                <h3 className="text-2xl font-bold text-white mb-6 text-center">
                  Complete Digital Marketing Solutions Dehradun
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  <Link to="/services/ppc/dehradun" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-emerald-400 font-semibold mb-2 group-hover:text-emerald-300">Google Ads Dehradun</h4>
                    <p className="text-slate-400 text-sm">PPC advertising and Google Ads management for Dehradun businesses</p>
                  </Link>
                  <Link to="/services/social-media/dehradun" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-emerald-400 font-semibold mb-2 group-hover:text-emerald-300">Social Media Marketing Dehradun</h4>
                    <p className="text-slate-400 text-sm">Social media management and marketing for Dehradun brands</p>
                  </Link>
                  <Link to="/services/content/dehradun" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-emerald-400 font-semibold mb-2 group-hover:text-emerald-300">Content Marketing Dehradun</h4>
                    <p className="text-slate-400 text-sm">Content creation and marketing strategies for Dehradun businesses</p>
                  </Link>
                  <Link to="/services/web-development/dehradun" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-emerald-400 font-semibold mb-2 group-hover:text-emerald-300">Web Development Dehradun</h4>
                    <p className="text-slate-400 text-sm">Professional website development and design services</p>
                  </Link>
                  <Link to="/services/email/dehradun" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-emerald-400 font-semibold mb-2 group-hover:text-emerald-300">Email Marketing Dehradun</h4>
                    <p className="text-slate-400 text-sm">Email marketing campaigns and automation for Dehradun businesses</p>
                  </Link>
                  <Link to="/portfolio" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-emerald-400 font-semibold mb-2 group-hover:text-emerald-300">Dehradun Success Stories</h4>
                    <p className="text-slate-400 text-sm">See our proven results and case studies from Dehradun clients</p>
                  </Link>
                </div>
              </section>

              <section className="bg-gradient-to-r from-emerald-600 to-emerald-500 rounded-2xl p-8 text-center">
                <h2 className="text-3xl md:text-5xl font-bold mb-6">Ready to Dominate Dehradun Search Results?</h2>
                <p className="text-xl mb-8">
                  Join 150+ successful Dehradun businesses that trust GOD Digital Marketing for SEO excellence. Proven strategies delivering 2,400% traffic growth and ₹25Cr+ revenue generation.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button asChild size="lg" className="bg-white text-emerald-600 hover:bg-slate-100 text-lg px-8 py-4">
                    <Link to="/quote">Get Free SEO Audit</Link>
                  </Button>
                  <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-emerald-600 text-lg px-8 py-4">
                    <Link to="/contact">Call Dehradun SEO Experts: +91-**********</Link>
                  </Button>
                </div>
              </section>
            </div>

            <div className="lg:col-span-1">
              <Sidebar currentService="seo" />
            </div>
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
};

export default SeoServicesDehradun;
