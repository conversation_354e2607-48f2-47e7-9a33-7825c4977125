import React from 'react';
import { Link } from 'react-router-dom';
import { ArrowRight, MapPin, TrendingUp, Users, CheckCircle, Building, Star, Crown } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';

const Lucknow = () => {
  const services = [
    'SEO Services Lucknow',
    'Google Ads Management Lucknow',
    'Social Media Marketing Lucknow',
    'Local SEO Lucknow',
    'E-commerce SEO Lucknow',
    'Content Marketing Lucknow',
    'Website Development Lucknow',
    'Digital Marketing Consulting Lucknow'
  ];

  const industries = [
    'Government Services Lucknow',
    'Education Lucknow',
    'Healthcare Lucknow',
    'Real Estate Lucknow',
    'Manufacturing Lucknow',
    'IT Services Lucknow',
    'Retail & E-commerce Lucknow',
    'Hospitality Lucknow'
  ];

  const areas = [
    'Hazratganj Digital Marketing',
    'Gomti Nagar SEO Services',
    'Indira Nagar Digital Marketing',
    'Aliganj SEO Services',
    'Mahanagar Digital Marketing',
    'Alambagh SEO Services',
    'Aminabad Digital Marketing',
    'Chowk SEO Services',
    'Rajajipuram Digital Marketing',
    'Vikas Nagar SEO Services',
    'Jankipuram Digital Marketing',
    'Ashiyana SEO Services'
  ];

  const stats = [
    {
      metric: '180+',
      description: 'Lucknow Businesses Served',
      detail: 'Across all sectors'
    },
    {
      metric: '320%',
      description: 'Average Traffic Increase',
      detail: 'For Lucknow clients'
    },
    {
      metric: '₹30L+',
      description: 'Revenue Generated',
      detail: 'For Lucknow businesses'
    }
  ];

  return (
    <div className="min-h-screen bg-slate-900 relative">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        {/* Hero Section */}
        <section className="pt-32 pb-20">
          <div className="container mx-auto px-6">
            <div className="max-w-4xl mx-auto text-center">
              <div className="inline-flex items-center space-x-2 bg-green-500/20 border border-green-500/30 rounded-full px-6 py-2 mb-8">
                <Crown className="w-4 h-4 text-green-400" />
                <span className="text-green-400 font-medium">Lucknow Digital Marketing • City of Nawabs</span>
              </div>
              
              <h1 className="text-5xl md:text-6xl font-bold mb-6 leading-tight">
                #1 Digital Marketing Company in
                <span className="bg-gradient-to-r from-green-400 to-green-600 bg-clip-text text-transparent"> Lucknow</span>
              </h1>
              
              <p className="text-xl text-slate-300 mb-12 max-w-3xl mx-auto">
                Leading digital marketing agency in Lucknow helping businesses dominate online. From Hazratganj commercial hub to Gomti Nagar IT corridor, we've helped 180+ businesses grow with proven SEO, Google Ads, and digital marketing strategies across all Lucknow areas.
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button asChild size="lg" className="bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white text-lg px-8 py-4">
                  <Link to="/quote">Get Free Lucknow SEO Audit</Link>
                </Button>
                <Button asChild size="lg" variant="outline" className="border-green-500 text-green-400 hover:bg-green-500 hover:text-white text-lg px-8 py-4">
                  <Link to="/contact">Call Lucknow Office</Link>
                </Button>
              </div>
            </div>
          </div>
        </section>

        {/* Stats Section */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Lucknow Digital Marketing
                <span className="bg-gradient-to-r from-green-400 to-green-600 bg-clip-text text-transparent"> Success Stories</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto mb-12">
                Real results from Lucknow businesses across industries - from government sector enterprises to emerging tech companies in UP's capital.
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
              {stats.map((stat, index) => (
                <div key={index} className="text-center">
                  <div className="text-5xl font-bold text-green-400 mb-2">{stat.metric}</div>
                  <div className="text-white font-semibold mb-1">{stat.description}</div>
                  <div className="text-slate-400">{stat.detail}</div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Services Section */}
        <section className="py-20">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Digital Marketing Services in
                <span className="bg-gradient-to-r from-green-400 to-green-600 bg-clip-text text-transparent"> Lucknow</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto">
                Comprehensive digital marketing solutions for Lucknow businesses looking to dominate online across all areas of the City of Nawabs.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {services.map((service, index) => (
                <Card key={index} className="bg-slate-900/80 border-green-500/20 hover:border-green-500/40 transition-all duration-300">
                  <CardContent className="p-6 text-center">
                    <Building className="w-8 h-8 text-green-400 mx-auto mb-4" />
                    <h3 className="text-white font-semibold mb-2">{service}</h3>
                    <Button asChild size="sm" variant="outline" className="border-green-500 text-green-400 hover:bg-green-500 hover:text-white">
                      <Link to="/quote">Learn More</Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Industries Section */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Lucknow Industries We
                <span className="bg-gradient-to-r from-green-400 to-green-600 bg-clip-text text-transparent"> Serve</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto">
                Specialized digital marketing strategies for Lucknow's government-centric economy and emerging business sectors.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {industries.map((industry, index) => (
                <Card key={index} className="bg-slate-900/80 border-green-500/20 hover:border-green-500/40 transition-all duration-300">
                  <CardContent className="p-6 text-center">
                    <Star className="w-8 h-8 text-green-400 mx-auto mb-4" />
                    <h3 className="text-white font-semibold mb-2">{industry}</h3>
                    <Button asChild size="sm" variant="outline" className="border-green-500 text-green-400 hover:bg-green-500 hover:text-white">
                      <Link to="/quote">Get Strategy</Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Areas Section */}
        <section className="py-20">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Lucknow Areas We
                <span className="bg-gradient-to-r from-green-400 to-green-600 bg-clip-text text-transparent"> Cover</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto">
                Local digital marketing expertise across all major Lucknow areas from historic Hazratganj to modern Gomti Nagar.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {areas.map((area, index) => (
                <Card key={index} className="bg-slate-900/80 border-green-500/20 hover:border-green-500/40 transition-all duration-300">
                  <CardContent className="p-6 text-center">
                    <MapPin className="w-8 h-8 text-green-400 mx-auto mb-4" />
                    <h3 className="text-white font-semibold mb-2">{area}</h3>
                    <Button asChild size="sm" variant="outline" className="border-green-500 text-green-400 hover:bg-green-500 hover:text-white">
                      <Link to="/contact">Contact Us</Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Why Choose Us Section */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Why Choose GOD Digital Marketing in
                <span className="bg-gradient-to-r from-green-400 to-green-600 bg-clip-text text-transparent"> Lucknow?</span>
              </h2>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              <Card className="bg-slate-900/80 border-green-500/20">
                <CardContent className="p-8">
                  <TrendingUp className="w-12 h-12 text-green-400 mb-6" />
                  <h3 className="text-xl font-bold text-white mb-4">Local Lucknow Expertise</h3>
                  <p className="text-slate-300">Deep understanding of Lucknow's government sector dynamics, educational institutions, and emerging business landscape.</p>
                </CardContent>
              </Card>

              <Card className="bg-slate-900/80 border-green-500/20">
                <CardContent className="p-8">
                  <Users className="w-12 h-12 text-green-400 mb-6" />
                  <h3 className="text-xl font-bold text-white mb-4">Proven Lucknow Results</h3>
                  <p className="text-slate-300">180+ successful Lucknow campaigns with measurable ROI improvements and business growth across traditional and modern sectors.</p>
                </CardContent>
              </Card>

              <Card className="bg-slate-900/80 border-green-500/20">
                <CardContent className="p-8">
                  <CheckCircle className="w-12 h-12 text-green-400 mb-6" />
                  <h3 className="text-xl font-bold text-white mb-4">Cultural Understanding</h3>
                  <p className="text-slate-300">Unique expertise in navigating Lucknow's traditional business culture while implementing modern digital strategies.</p>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 bg-gradient-to-r from-green-600 to-green-500">
          <div className="container mx-auto px-6 text-center">
            <h2 className="text-4xl md:text-5xl font-bold mb-6">Ready to Dominate Lucknow's Digital Market?</h2>
            <p className="text-xl mb-8 max-w-3xl mx-auto">
              Join 180+ Lucknow businesses that trust GOD Digital Marketing for their online growth. From Hazratganj to Gomti Nagar, we deliver results.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button asChild size="lg" className="bg-white text-green-600 hover:bg-slate-100 text-lg px-8 py-4">
                <Link to="/quote">Get Free Lucknow SEO Audit</Link>
              </Button>
              <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-green-600 text-lg px-8 py-4">
                <Link to="/contact">Call Lucknow Office Now</Link>
              </Button>
            </div>
          </div>
        </section>
      </main>
      
      <Footer />
    </div>
  );
};

export default Lucknow;
