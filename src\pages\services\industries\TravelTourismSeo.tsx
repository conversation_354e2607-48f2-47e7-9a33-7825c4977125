import React from 'react';
import { Link } from 'react-router-dom';
import { Plane, Search, TrendingUp, Users, CheckCircle, Building, Star, Crown, Target, Zap, ArrowRight } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';
import Sidebar from '@/components/Sidebar';

const TravelTourismSeo = () => {
  const stats = [
    {
      metric: '1,800+',
      description: 'Travel Businesses Optimized',
      detail: 'Across all tourism sectors'
    },
    {
      metric: '2,200%',
      description: 'Average Booking Growth',
      detail: 'For travel clients'
    },
    {
      metric: '₹450Cr+',
      description: 'Travel Revenue Generated',
      detail: 'Through SEO campaigns'
    },
    {
      metric: '96%',
      description: 'Travel Keywords Ranking',
      detail: 'Top 10 positions achieved'
    }
  ];

  const achievements = [
    'Top Travel SEO Company in India',
    'Tourism Marketing Specialists',
    'Hotel SEO Optimization Experts',
    'Travel Agency SEO Leaders',
    'Destination Marketing Champions',
    'Travel Technology SEO Pioneers'
  ];

  const travelSpecializations = [
    {
      type: 'Hotel & Accommodation SEO',
      description: 'Comprehensive SEO for hotels, resorts, and accommodation providers',
      icon: Building,
      features: ['Hotel Booking Optimization', 'Room Page SEO', 'Local Hotel SEO', 'Review Management', 'Seasonal Content Strategy', 'OTA Optimization']
    },
    {
      type: 'Travel Agency SEO',
      description: 'Specialized SEO for travel agencies, tour operators, and travel booking platforms',
      icon: Star,
      features: ['Package Tour SEO', 'Destination Content', 'Travel Booking Optimization', 'Itinerary Page SEO', 'Travel Blog Content', 'Multi-destination SEO']
    },
    {
      type: 'Destination Marketing SEO',
      description: 'SEO strategies for tourism boards, destinations, and location-based travel businesses',
      icon: Crown,
      features: ['Destination Branding', 'Tourist Attraction SEO', 'Event & Festival Marketing', 'Cultural Tourism Content', 'Adventure Tourism SEO', 'Heritage Site Promotion']
    },
    {
      type: 'Travel Technology SEO',
      description: 'SEO for travel tech companies, booking platforms, and travel service providers',
      icon: Target,
      features: ['Travel App SEO', 'Booking Platform Optimization', 'Travel Comparison Sites', 'Travel API Documentation', 'SaaS Travel Solutions', 'Travel Tech Content']
    }
  ];

  const travelSectors = [
    { name: 'Hotels & Resorts', clients: '520+', growth: '2,400%' },
    { name: 'Travel Agencies', clients: '480+', growth: '2,200%' },
    { name: 'Tour Operators', clients: '380+', growth: '1,950%' },
    { name: 'Airlines & Transport', clients: '220+', growth: '1,800%' },
    { name: 'Adventure Tourism', clients: '180+', growth: '2,600%' },
    { name: 'Travel Technology', clients: '120+', growth: '2,100%' }
  ];

  const caseStudies = [
    {
      client: 'Luxury Resort Chain',
      industry: 'Hospitality & Hotels',
      challenge: 'Premium resort chain needed to compete with OTAs and increase direct bookings',
      result: '3,200% direct booking increase',
      metrics: ['680+ hospitality keywords in top 3', '₹25Cr+ direct booking revenue', '450% increase in resort inquiries']
    },
    {
      client: 'Adventure Travel Company',
      industry: 'Adventure Tourism',
      challenge: 'Specialized adventure tour operator needed to reach thrill-seeking travelers',
      result: '2,800% adventure booking growth',
      metrics: ['420+ adventure keywords ranking', '₹18Cr+ tour revenue', '380% increase in expedition bookings']
    },
    {
      client: 'Destination Tourism Board',
      industry: 'Destination Marketing',
      challenge: 'Regional tourism board needed to promote lesser-known destinations',
      result: '2,400% tourist inquiry increase',
      metrics: ['550+ destination keywords ranking', '₹35Cr+ tourism revenue impact', '320% increase in visitor arrivals']
    }
  ];

  const travelSeoStrategies = [
    {
      strategy: 'Seasonal SEO Optimization',
      description: 'Optimize for seasonal travel patterns and peak booking periods',
      benefits: ['Peak season visibility', 'Seasonal revenue boost', 'Travel trend alignment', 'Booking optimization']
    },
    {
      strategy: 'Visual Content SEO',
      description: 'Optimize travel imagery, videos, and visual content for search discovery',
      benefits: ['Visual search optimization', 'Image ranking', 'Social media integration', 'User engagement']
    },
    {
      strategy: 'Multi-language SEO',
      description: 'Target international travelers with multi-language SEO strategies',
      benefits: ['Global reach', 'International bookings', 'Cultural relevance', 'Market expansion']
    },
    {
      strategy: 'Mobile Travel SEO',
      description: 'Optimize for mobile travelers and on-the-go booking behavior',
      benefits: ['Mobile bookings', 'Travel app optimization', 'Location-based search', 'Real-time optimization']
    }
  ];

  return (
    <div className="min-h-screen bg-slate-900 relative">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        <div className="container mx-auto px-6 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            <div className="lg:col-span-3">
              <section className="pt-24 pb-16">
                <div className="max-w-4xl">
                  <div className="inline-flex items-center space-x-2 bg-cyan-500/20 border border-cyan-500/30 rounded-full px-6 py-2 mb-8">
                    <Plane className="w-4 h-4 text-cyan-400" />
                    <span className="text-cyan-400 font-medium">Travel & Tourism SEO • Destination Marketing Excellence</span>
                  </div>
                  
                  <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
                    #1 Travel & Tourism SEO Company in
                    <span className="bg-gradient-to-r from-cyan-400 to-cyan-600 bg-clip-text text-transparent"> India</span>
                  </h1>
                  
                  <p className="text-xl text-slate-300 mb-8">
                    Premier travel and tourism SEO services offering comprehensive search engine optimization solutions for travel businesses including hotel SEO, travel agency optimization, destination marketing, and tourism content strategy. Our travel SEO company provides professional SEO services with travel booking optimization, destination content marketing, seasonal SEO strategies, and travel conversion optimization. Serving 1,800+ travel businesses across all tourism sectors with proven ₹450Cr+ revenue generation and 2,200% average booking growth for travel clients through strategic search engine optimization and tourism digital marketing excellence.
                  </p>
                  
                  <div className="flex flex-col sm:flex-row gap-4 mb-8">
                    <Button asChild size="lg" className="bg-gradient-to-r from-cyan-500 to-cyan-600 hover:from-cyan-600 hover:to-cyan-700 text-white text-lg px-8 py-4">
                      <Link to="/quote">Get Free Travel SEO Audit</Link>
                    </Button>
                    <Button asChild size="lg" variant="outline" className="border-cyan-500 text-cyan-400 hover:bg-cyan-500 hover:text-white text-lg px-8 py-4">
                      <Link to="/contact">Call Travel SEO Experts: +91-8708577598</Link>
                    </Button>
                  </div>

                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                    {achievements.map((achievement, index) => (
                      <div key={index} className="flex items-center justify-center space-x-2 bg-slate-800/50 rounded-lg p-3">
                        <CheckCircle className="w-4 h-4 text-cyan-400 flex-shrink-0" />
                        <span className="text-slate-300">{achievement}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </section>

              <section className="mb-16 bg-slate-800/50 rounded-2xl p-8">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Travel & Tourism SEO
                    <span className="bg-gradient-to-r from-cyan-400 to-cyan-600 bg-clip-text text-transparent"> Success Metrics</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Measurable travel SEO results from businesses across all tourism sectors and travel industries.
                  </p>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
                  {stats.map((stat, index) => (
                    <div key={index} className="text-center">
                      <div className="text-4xl font-bold text-cyan-400 mb-2">{stat.metric}</div>
                      <div className="text-white font-semibold mb-1">{stat.description}</div>
                      <div className="text-slate-400">{stat.detail}</div>
                    </div>
                  ))}
                </div>
              </section>

              <section className="mb-16">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Travel SEO Strategies We
                    <span className="bg-gradient-to-r from-cyan-400 to-cyan-600 bg-clip-text text-transparent"> Master</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Specialized SEO strategies designed for travel and tourism success across all sectors and destinations.
                  </p>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  {travelSpecializations.map((spec, index) => (
                    <Card key={index} className="bg-slate-900/80 border-cyan-500/20 hover:border-cyan-500/40 transition-all duration-300">
                      <CardContent className="p-8">
                        <div className="w-12 h-12 bg-gradient-to-r from-cyan-500 to-cyan-600 rounded-lg flex items-center justify-center mb-6">
                          <spec.icon className="w-6 h-6 text-white" />
                        </div>
                        <h3 className="text-xl font-bold text-white mb-4">{spec.type}</h3>
                        <p className="text-slate-300 mb-6">{spec.description}</p>
                        <ul className="space-y-2">
                          {spec.features.map((feature, idx) => (
                            <li key={idx} className="flex items-center text-slate-400 text-sm">
                              <CheckCircle className="w-3 h-3 text-cyan-400 mr-2 flex-shrink-0" />
                              {feature}
                            </li>
                          ))}
                        </ul>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>

              {/* Travel Sectors */}
              <section className="mb-16 bg-slate-800/30 rounded-2xl p-8">
                <h3 className="text-2xl font-bold text-white mb-6 text-center">
                  Travel & Tourism Sector Expertise
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-6">
                  {travelSectors.map((sector, index) => (
                    <div key={index} className="text-center bg-slate-900/50 rounded-lg p-4">
                      <h4 className="text-cyan-400 font-semibold mb-2">{sector.name}</h4>
                      <div className="text-2xl font-bold text-white mb-1">{sector.clients}</div>
                      <div className="text-slate-400 text-sm mb-2">Businesses Served</div>
                      <div className="text-green-400 font-semibold text-sm">{sector.growth} avg growth</div>
                    </div>
                  ))}
                </div>
              </section>

              {/* Travel SEO Strategies */}
              <section className="mb-16 bg-slate-800/30 rounded-2xl p-8">
                <h3 className="text-2xl font-bold text-white mb-6 text-center">
                  Advanced Travel SEO Strategies
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                  {travelSeoStrategies.map((strategy, index) => (
                    <div key={index} className="bg-slate-900/50 rounded-lg p-6">
                      <h4 className="text-cyan-400 font-semibold mb-3">{strategy.strategy}</h4>
                      <p className="text-slate-300 text-sm mb-4">{strategy.description}</p>
                      <ul className="space-y-1">
                        {strategy.benefits.map((benefit, idx) => (
                          <li key={idx} className="text-slate-400 text-xs flex items-center">
                            <CheckCircle className="w-3 h-3 text-cyan-400 mr-2 flex-shrink-0" />
                            {benefit}
                          </li>
                        ))}
                      </ul>
                    </div>
                  ))}
                </div>
              </section>

              {/* Case Studies */}
              <section className="mb-16">
                <h3 className="text-2xl font-bold text-white mb-8 text-center">
                  Travel & Tourism SEO Success Stories
                </h3>
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                  {caseStudies.map((study, index) => (
                    <Card key={index} className="bg-slate-900/60 border-cyan-500/20 hover:border-cyan-500/40 transition-all duration-300">
                      <CardContent className="p-6">
                        <h4 className="text-cyan-400 font-semibold mb-2">{study.client}</h4>
                        <p className="text-slate-400 text-sm mb-3">{study.industry}</p>
                        <p className="text-slate-300 text-sm mb-4">{study.challenge}</p>
                        <div className="bg-slate-800/50 rounded p-3 mb-4">
                          <div className="text-green-400 font-semibold mb-2">{study.result}</div>
                          <ul className="space-y-1">
                            {study.metrics.map((metric, idx) => (
                              <li key={idx} className="text-slate-400 text-xs flex items-center">
                                <CheckCircle className="w-3 h-3 text-cyan-400 mr-2 flex-shrink-0" />
                                {metric}
                              </li>
                            ))}
                          </ul>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>

              {/* Internal Linking Section */}
              <section className="mb-16 bg-slate-800/30 rounded-2xl p-8">
                <h3 className="text-2xl font-bold text-white mb-6 text-center">
                  Complete Travel Digital Marketing Solutions
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  <Link to="/services/ppc" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-cyan-400 font-semibold mb-2 group-hover:text-cyan-300">Travel Google Ads</h4>
                    <p className="text-slate-400 text-sm">PPC advertising for travel bookings and destination promotion</p>
                  </Link>
                  <Link to="/services/social-media" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-cyan-400 font-semibold mb-2 group-hover:text-cyan-300">Travel Social Media</h4>
                    <p className="text-slate-400 text-sm">Visual storytelling and destination marketing on social platforms</p>
                  </Link>
                  <Link to="/services/content" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-cyan-400 font-semibold mb-2 group-hover:text-cyan-300">Travel Content Marketing</h4>
                    <p className="text-slate-400 text-sm">Destination guides, travel blogs, and tourism content</p>
                  </Link>
                  <Link to="/services/email" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-cyan-400 font-semibold mb-2 group-hover:text-cyan-300">Travel Email Marketing</h4>
                    <p className="text-slate-400 text-sm">Travel newsletters and booking confirmation campaigns</p>
                  </Link>
                  <Link to="/services/web-development" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-cyan-400 font-semibold mb-2 group-hover:text-cyan-300">Travel Website Development</h4>
                    <p className="text-slate-400 text-sm">Booking platforms and travel website development</p>
                  </Link>
                  <Link to="/portfolio" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-cyan-400 font-semibold mb-2 group-hover:text-cyan-300">Travel Success Stories</h4>
                    <p className="text-slate-400 text-sm">See proven results from our travel and tourism clients</p>
                  </Link>
                </div>
              </section>

              <section className="bg-gradient-to-r from-cyan-600 to-cyan-500 rounded-2xl p-8 text-center">
                <h2 className="text-3xl md:text-5xl font-bold mb-6">Ready to Boost Your Travel Bookings?</h2>
                <p className="text-xl mb-8">
                  Join 1,800+ travel businesses that trust GOD Digital Marketing for tourism SEO success. Proven strategies that deliver 2,200% booking growth and ₹450Cr+ revenue generation.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button asChild size="lg" className="bg-white text-cyan-600 hover:bg-slate-100 text-lg px-8 py-4">
                    <Link to="/quote">Get Free Travel SEO Audit</Link>
                  </Button>
                  <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-cyan-600 text-lg px-8 py-4">
                    <Link to="/contact">Call Travel SEO Experts: +91-8708577598</Link>
                  </Button>
                </div>
              </section>
            </div>

            <div className="lg:col-span-1">
              <Sidebar currentService="seo" />
            </div>
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
};

export default TravelTourismSeo;
