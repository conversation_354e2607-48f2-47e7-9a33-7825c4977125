import { useEffect } from 'react';

interface PerformanceMetrics {
  fcp: number; // First Contentful Paint
  lcp: number; // Largest Contentful Paint
  fid: number; // First Input Delay
  cls: number; // Cumulative Layout Shift
  ttfb: number; // Time to First Byte
}

const PerformanceMonitor = () => {
  useEffect(() => {
    // Only run in production
    if (process.env.NODE_ENV !== 'production') return;

    const metrics: Partial<PerformanceMetrics> = {};

    // Measure Core Web Vitals
    const measureWebVitals = () => {
      // First Contentful Paint
      const fcpEntry = performance.getEntriesByName('first-contentful-paint')[0];
      if (fcpEntry) {
        metrics.fcp = fcpEntry.startTime;
      }

      // Time to First Byte
      const navigationEntry = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      if (navigationEntry) {
        metrics.ttfb = navigationEntry.responseStart - navigationEntry.requestStart;
      }

      // Largest Contentful Paint
      if ('PerformanceObserver' in window) {
        try {
          const lcpObserver = new PerformanceObserver((list) => {
            const entries = list.getEntries();
            const lastEntry = entries[entries.length - 1];
            metrics.lcp = lastEntry.startTime;
          });
          lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });

          // First Input Delay
          const fidObserver = new PerformanceObserver((list) => {
            const entries = list.getEntries();
            entries.forEach((entry) => {
              metrics.fid = entry.processingStart - entry.startTime;
            });
          });
          fidObserver.observe({ entryTypes: ['first-input'] });

          // Cumulative Layout Shift
          const clsObserver = new PerformanceObserver((list) => {
            let clsValue = 0;
            const entries = list.getEntries();
            entries.forEach((entry: any) => {
              if (!entry.hadRecentInput) {
                clsValue += entry.value;
              }
            });
            metrics.cls = clsValue;
          });
          clsObserver.observe({ entryTypes: ['layout-shift'] });

        } catch (error) {
          console.warn('Performance monitoring not supported:', error);
        }
      }
    };

    // Log performance metrics after page load
    const logMetrics = () => {
      setTimeout(() => {
        console.group('🚀 Performance Metrics');
        console.log('First Contentful Paint:', metrics.fcp ? `${metrics.fcp.toFixed(2)}ms` : 'N/A');
        console.log('Largest Contentful Paint:', metrics.lcp ? `${metrics.lcp.toFixed(2)}ms` : 'N/A');
        console.log('First Input Delay:', metrics.fid ? `${metrics.fid.toFixed(2)}ms` : 'N/A');
        console.log('Cumulative Layout Shift:', metrics.cls ? metrics.cls.toFixed(4) : 'N/A');
        console.log('Time to First Byte:', metrics.ttfb ? `${metrics.ttfb.toFixed(2)}ms` : 'N/A');
        
        // Performance scoring
        const score = calculatePerformanceScore(metrics);
        console.log(`Overall Performance Score: ${score}/100`);
        console.groupEnd();
      }, 3000);
    };

    measureWebVitals();
    
    if (document.readyState === 'complete') {
      logMetrics();
    } else {
      window.addEventListener('load', logMetrics);
    }

    return () => {
      window.removeEventListener('load', logMetrics);
    };
  }, []);

  return null;
};

// Calculate performance score based on Core Web Vitals
const calculatePerformanceScore = (metrics: Partial<PerformanceMetrics>): number => {
  let score = 100;
  
  // FCP scoring (good: <1.8s, needs improvement: 1.8-3s, poor: >3s)
  if (metrics.fcp) {
    if (metrics.fcp > 3000) score -= 20;
    else if (metrics.fcp > 1800) score -= 10;
  }
  
  // LCP scoring (good: <2.5s, needs improvement: 2.5-4s, poor: >4s)
  if (metrics.lcp) {
    if (metrics.lcp > 4000) score -= 25;
    else if (metrics.lcp > 2500) score -= 15;
  }
  
  // FID scoring (good: <100ms, needs improvement: 100-300ms, poor: >300ms)
  if (metrics.fid) {
    if (metrics.fid > 300) score -= 20;
    else if (metrics.fid > 100) score -= 10;
  }
  
  // CLS scoring (good: <0.1, needs improvement: 0.1-0.25, poor: >0.25)
  if (metrics.cls) {
    if (metrics.cls > 0.25) score -= 20;
    else if (metrics.cls > 0.1) score -= 10;
  }
  
  // TTFB scoring (good: <200ms, needs improvement: 200-500ms, poor: >500ms)
  if (metrics.ttfb) {
    if (metrics.ttfb > 500) score -= 15;
    else if (metrics.ttfb > 200) score -= 5;
  }
  
  return Math.max(0, score);
};

export default PerformanceMonitor;
