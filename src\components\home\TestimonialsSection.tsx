
import React from 'react';
import { Star, Quote } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';

const TestimonialsSection = () => {
  const testimonials = [
    {
      name: '<PERSON>',
      title: 'CEO, Easy Outdoor (UK)',
      content: '<PERSON><PERSON> took our car oven and Opus camper business from zero visibility to dominating UK search results. The international SEO expertise is unmatched - we now rank across multiple countries.',
      rating: 5,
      image: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face'
    },
    {
      name: '<PERSON>',
      title: 'Owner, <PERSON> Flower Delivery (Dubai)',
      content: 'The results speak for themselves - from zero to thousands of monthly visitors in Dubai market. <PERSON><PERSON> understands international SEO like no other consultant I have worked with.',
      rating: 5,
      image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face'
    },
    {
      name: '<PERSON>',
      title: 'Marketing Director, <PERSON>ulkland (US)',
      content: 'Working with <PERSON><PERSON> was transformational for our US market expansion. The AI-powered content strategies and technical SEO expertise delivered results we never thought possible.',
      rating: 5,
      image: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face'
    }
  ];

  return (
    <section className="py-20 bg-slate-800/50 relative">
      <div className="container mx-auto px-6">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold mb-6">
            International Client
            <span className="bg-gradient-to-r from-amber-400 to-amber-600 bg-clip-text text-transparent"> Success Stories</span>
          </h2>
          <p className="text-xl text-slate-300 max-w-3xl mx-auto">
            Real results from real businesses across UK, US, Dubai and beyond. Premium clients who invested in international SEO dominance.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {testimonials.map((testimonial, index) => (
            <Card key={index} className="bg-slate-900/80 border-amber-500/20 hover:border-amber-500/40 transition-all duration-300">
              <CardContent className="p-8">
                <div className="flex items-center mb-6">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <Star key={i} className="w-5 h-5 text-amber-400 fill-current" />
                  ))}
                </div>
                
                <Quote className="w-8 h-8 text-amber-400 mb-4" />
                
                <p className="text-slate-300 mb-6 leading-relaxed">
                  "{testimonial.content}"
                </p>
                
                <div className="flex items-center">
                  <img 
                    src={testimonial.image} 
                    alt={testimonial.name}
                    className="w-12 h-12 rounded-full mr-4"
                  />
                  <div>
                    <div className="text-white font-semibold">{testimonial.name}</div>
                    <div className="text-slate-400 text-sm">{testimonial.title}</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
};

export default TestimonialsSection;
