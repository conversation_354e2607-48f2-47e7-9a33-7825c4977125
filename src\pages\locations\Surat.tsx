import React from 'react';
import { Link } from 'react-router-dom';
import { ArrowRight, MapPin, TrendingUp, Users, CheckCircle, Building, Star, Gem } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';

const Surat = () => {
  const services = [
    'SEO Services Surat',
    'Google Ads Management Surat',
    'Social Media Marketing Surat',
    'Local SEO Surat',
    'E-commerce SEO Surat',
    'Content Marketing Surat',
    'Website Development Surat',
    'Digital Marketing Consulting Surat'
  ];

  const industries = [
    'Diamond & Jewelry Surat',
    'Textile Manufacturing Surat',
    'Chemical Industry Surat',
    'Real Estate Surat',
    'Export-Import Surat',
    'Manufacturing Surat',
    'IT Services Surat',
    'Retail & E-commerce Surat'
  ];

  const areas = [
    'Adajan Digital Marketing',
    'Vesu SEO Services',
    'Citylight Digital Marketing',
    'Piplod SEO Services',
    'Rander Digital Marketing',
    'Udhna SEO Services',
    'Katargam Digital Marketing',
    'Varachha SEO Services',
    'Nanpura Digital Marketing',
    'Athwa SEO Services',
    'Magdalla Digital Marketing',
    'Dumas SEO Services'
  ];

  const stats = [
    {
      metric: '220+',
      description: 'Surat Businesses Served',
      detail: 'Across all sectors'
    },
    {
      metric: '420%',
      description: 'Average Traffic Increase',
      detail: 'For Surat clients'
    },
    {
      metric: '₹55L+',
      description: 'Revenue Generated',
      detail: 'For Surat businesses'
    }
  ];

  return (
    <div className="min-h-screen bg-slate-900 relative">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        {/* Hero Section */}
        <section className="pt-32 pb-20">
          <div className="container mx-auto px-6">
            <div className="max-w-4xl mx-auto text-center">
              <div className="inline-flex items-center space-x-2 bg-orange-500/20 border border-orange-500/30 rounded-full px-6 py-2 mb-8">
                <Gem className="w-4 h-4 text-orange-400" />
                <span className="text-orange-400 font-medium">Surat Digital Marketing • Diamond City of India</span>
              </div>
              
              <h1 className="text-5xl md:text-6xl font-bold mb-6 leading-tight">
                #1 Digital Marketing Company in
                <span className="bg-gradient-to-r from-orange-400 to-orange-600 bg-clip-text text-transparent"> Surat</span>
              </h1>
              
              <p className="text-xl text-slate-300 mb-12 max-w-3xl mx-auto">
                Leading digital marketing agency in Surat helping businesses dominate online. From diamond trading hubs to textile manufacturing centers, we've helped 220+ businesses grow with proven SEO, Google Ads, and digital marketing strategies across all Surat areas.
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button asChild size="lg" className="bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white text-lg px-8 py-4">
                  <Link to="/quote">Get Free Surat SEO Audit</Link>
                </Button>
                <Button asChild size="lg" variant="outline" className="border-orange-500 text-orange-400 hover:bg-orange-500 hover:text-white text-lg px-8 py-4">
                  <Link to="/contact">Call Surat Office</Link>
                </Button>
              </div>
            </div>
          </div>
        </section>

        {/* Stats Section */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Surat Digital Marketing
                <span className="bg-gradient-to-r from-orange-400 to-orange-600 bg-clip-text text-transparent"> Success Stories</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto mb-12">
                Real results from Surat businesses across industries - from diamond merchants to textile exporters in Gujarat's commercial capital.
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
              {stats.map((stat, index) => (
                <div key={index} className="text-center">
                  <div className="text-5xl font-bold text-orange-400 mb-2">{stat.metric}</div>
                  <div className="text-white font-semibold mb-1">{stat.description}</div>
                  <div className="text-slate-400">{stat.detail}</div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Services Section */}
        <section className="py-20">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Digital Marketing Services in
                <span className="bg-gradient-to-r from-orange-400 to-orange-600 bg-clip-text text-transparent"> Surat</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto">
                Comprehensive digital marketing solutions for Surat businesses looking to dominate online across all areas of the Diamond City.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {services.map((service, index) => (
                <Card key={index} className="bg-slate-900/80 border-orange-500/20 hover:border-orange-500/40 transition-all duration-300">
                  <CardContent className="p-6 text-center">
                    <Building className="w-8 h-8 text-orange-400 mx-auto mb-4" />
                    <h3 className="text-white font-semibold mb-2">{service}</h3>
                    <Button asChild size="sm" variant="outline" className="border-orange-500 text-orange-400 hover:bg-orange-500 hover:text-white">
                      <Link to="/quote">Learn More</Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Industries Section */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Surat Industries We
                <span className="bg-gradient-to-r from-orange-400 to-orange-600 bg-clip-text text-transparent"> Serve</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto">
                Specialized digital marketing strategies for Surat's diamond, textile, and manufacturing industries.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {industries.map((industry, index) => (
                <Card key={index} className="bg-slate-900/80 border-orange-500/20 hover:border-orange-500/40 transition-all duration-300">
                  <CardContent className="p-6 text-center">
                    <Star className="w-8 h-8 text-orange-400 mx-auto mb-4" />
                    <h3 className="text-white font-semibold mb-2">{industry}</h3>
                    <Button asChild size="sm" variant="outline" className="border-orange-500 text-orange-400 hover:bg-orange-500 hover:text-white">
                      <Link to="/quote">Get Strategy</Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Areas Section */}
        <section className="py-20">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Surat Areas We
                <span className="bg-gradient-to-r from-orange-400 to-orange-600 bg-clip-text text-transparent"> Cover</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto">
                Local digital marketing expertise across all major Surat areas from diamond markets to textile hubs.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {areas.map((area, index) => (
                <Card key={index} className="bg-slate-900/80 border-orange-500/20 hover:border-orange-500/40 transition-all duration-300">
                  <CardContent className="p-6 text-center">
                    <MapPin className="w-8 h-8 text-orange-400 mx-auto mb-4" />
                    <h3 className="text-white font-semibold mb-2">{area}</h3>
                    <Button asChild size="sm" variant="outline" className="border-orange-500 text-orange-400 hover:bg-orange-500 hover:text-white">
                      <Link to="/contact">Contact Us</Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Why Choose Us Section */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Why Choose GOD Digital Marketing in
                <span className="bg-gradient-to-r from-orange-400 to-orange-600 bg-clip-text text-transparent"> Surat?</span>
              </h2>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              <Card className="bg-slate-900/80 border-orange-500/20">
                <CardContent className="p-8">
                  <TrendingUp className="w-12 h-12 text-orange-400 mb-6" />
                  <h3 className="text-xl font-bold text-white mb-4">Local Surat Expertise</h3>
                  <p className="text-slate-300">Deep understanding of Surat's diamond trading, textile manufacturing, and export-import business dynamics.</p>
                </CardContent>
              </Card>

              <Card className="bg-slate-900/80 border-orange-500/20">
                <CardContent className="p-8">
                  <Users className="w-12 h-12 text-orange-400 mb-6" />
                  <h3 className="text-xl font-bold text-white mb-4">Proven Surat Results</h3>
                  <p className="text-slate-300">220+ successful Surat campaigns with measurable ROI improvements and business growth across traditional and modern industries.</p>
                </CardContent>
              </Card>

              <Card className="bg-slate-900/80 border-orange-500/20">
                <CardContent className="p-8">
                  <CheckCircle className="w-12 h-12 text-orange-400 mb-6" />
                  <h3 className="text-xl font-bold text-white mb-4">Export Market Focus</h3>
                  <p className="text-slate-300">Specialized expertise in international marketing for Surat's export-oriented diamond and textile businesses.</p>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 bg-gradient-to-r from-orange-600 to-orange-500">
          <div className="container mx-auto px-6 text-center">
            <h2 className="text-4xl md:text-5xl font-bold mb-6">Ready to Dominate Surat's Digital Market?</h2>
            <p className="text-xl mb-8 max-w-3xl mx-auto">
              Join 220+ Surat businesses that trust GOD Digital Marketing for their online growth. From diamond markets to textile hubs, we deliver results.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button asChild size="lg" className="bg-white text-orange-600 hover:bg-slate-100 text-lg px-8 py-4">
                <Link to="/quote">Get Free Surat SEO Audit</Link>
              </Button>
              <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-orange-600 text-lg px-8 py-4">
                <Link to="/contact">Call Surat Office Now</Link>
              </Button>
            </div>
          </div>
        </section>
      </main>
      
      <Footer />
    </div>
  );
};

export default Surat;
