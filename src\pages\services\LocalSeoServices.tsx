import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { ArrowRight, MapPin, Star, Users, TrendingUp, CheckCircle, Globe, Award } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';

const LocalSeoServices = () => {
  const features = [
    'Google My Business Optimization',
    'Local Citation Building & Management',
    'Review Generation & Management',
    'Local Keyword Research & Targeting',
    'Location-Based Content Strategy',
    'Local Link Building Campaigns',
    'NAP (Name, Address, Phone) Consistency',
    'Local Schema Markup Implementation',
    'Google Maps Optimization',
    'Local Competition Analysis'
  ];

  const packages = [
    {
      name: 'Local Starter',
      price: '₹25,000',
      period: '/month',
      description: 'Perfect for single-location businesses establishing local presence',
      features: [
        'Google My Business Setup & Optimization',
        'Local Keyword Research (25 keywords)',
        'Basic Citation Building (15 directories)',
        'Review Management Setup',
        'Monthly Performance Reports'
      ]
    },
    {
      name: 'Local Authority',
      price: '₹40,000',
      period: '/month',
      description: 'Comprehensive local SEO for businesses dominating their market',
      features: [
        'Everything in Local Starter',
        'Advanced Citation Building (50+ directories)',
        'Local Content Creation (4 articles/month)',
        'Review Generation Campaigns',
        'Local Link Building',
        'Competitor Analysis & Monitoring'
      ],
      popular: true
    },
    {
      name: 'Multi-Location Domination',
      price: '₹75,000',
      period: '/month',
      description: 'Enterprise local SEO for businesses with multiple locations',
      features: [
        'Everything in Local Authority',
        'Multi-Location Management',
        'Location-Specific Landing Pages',
        'Advanced Local Schema Implementation',
        'Franchise/Chain SEO Strategies',
        'Dedicated Account Manager'
      ]
    }
  ];

  const results = [
    {
      metric: '800%',
      description: 'Local visibility increase',
      timeframe: 'within 6 months'
    },
    {
      metric: '#1',
      description: 'Local pack rankings',
      timeframe: 'for target keywords'
    },
    {
      metric: '300%',
      description: 'Increase in local leads',
      timeframe: 'from Google My Business'
    }
  ];

  return (
    <div className="min-h-screen bg-slate-900 relative">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        {/* Hero Section */}
        <section className="pt-32 pb-20">
          <div className="container mx-auto px-6">
            <div className="max-w-4xl mx-auto text-center">
              <div className="inline-flex items-center space-x-2 bg-amber-500/20 border border-amber-500/30 rounded-full px-6 py-2 mb-8">
                <MapPin className="w-4 h-4 text-amber-400" />
                <span className="text-amber-400 font-medium">Local SEO Services • Dominate Your Local Market</span>
              </div>
              
              <h1 className="text-5xl md:text-6xl font-bold mb-6 leading-tight">
                Local SEO Services That
                <span className="bg-gradient-to-r from-amber-400 to-amber-600 bg-clip-text text-transparent"> Dominate Local Search</span>
              </h1>
              
              <p className="text-xl text-slate-300 mb-12 max-w-3xl mx-auto">
                Get found by local customers when they search for your services. Our proven local SEO strategies help businesses dominate Google Maps, local search results, and drive foot traffic to your location.
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button asChild size="lg" className="bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700 text-white text-lg px-8 py-4">
                  <Link to="/quote">Get Local SEO Audit</Link>
                </Button>
                <Button asChild size="lg" variant="outline" className="border-amber-500 text-amber-400 hover:bg-amber-500 hover:text-white text-lg px-8 py-4">
                  <Link to="/case-studies">View Local Results</Link>
                </Button>
              </div>
            </div>
          </div>
        </section>

        {/* Results Section */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Proven Local SEO
                <span className="bg-gradient-to-r from-amber-400 to-amber-600 bg-clip-text text-transparent"> Results</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto mb-12">
                Real results from real local businesses across India. Dominate your local market with our proven strategies.
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
              {results.map((result, index) => (
                <div key={index} className="text-center">
                  <div className="text-5xl font-bold text-amber-400 mb-2">{result.metric}</div>
                  <div className="text-white font-semibold mb-1">{result.description}</div>
                  <div className="text-slate-400">{result.timeframe}</div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Features Section */}
        <section className="py-20">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Comprehensive Local SEO
                <span className="bg-gradient-to-r from-amber-400 to-amber-600 bg-clip-text text-transparent"> Solutions</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto">
                Everything you need to dominate local search results and attract more local customers.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-w-4xl mx-auto">
              {features.map((feature, index) => (
                <div key={index} className="flex items-center space-x-3">
                  <CheckCircle className="w-6 h-6 text-amber-400 flex-shrink-0" />
                  <span className="text-slate-300">{feature}</span>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Pricing Section */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Local SEO Investment
                <span className="bg-gradient-to-r from-amber-400 to-amber-600 bg-clip-text text-transparent"> Packages</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto">
                Choose the perfect local SEO package to dominate your local market and attract more customers.
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
              {packages.map((pkg, index) => (
                <Card key={index} className={`bg-slate-900/80 border-amber-500/20 hover:border-amber-500/40 transition-all duration-300 relative ${pkg.popular ? 'ring-2 ring-amber-500' : ''}`}>
                  {pkg.popular && (
                    <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                      <span className="bg-amber-500 text-white px-4 py-2 rounded-full text-sm font-semibold">Most Popular</span>
                    </div>
                  )}
                  <CardContent className="p-8">
                    <h3 className="text-2xl font-bold text-white mb-2">{pkg.name}</h3>
                    <p className="text-slate-300 mb-6">{pkg.description}</p>
                    <div className="mb-6">
                      <span className="text-4xl font-bold text-amber-400">{pkg.price}</span>
                      <span className="text-slate-400">{pkg.period}</span>
                    </div>
                    <ul className="space-y-3 mb-8">
                      {pkg.features.map((feature, idx) => (
                        <li key={idx} className="flex items-center text-slate-300">
                          <CheckCircle className="w-4 h-4 text-amber-400 mr-3 flex-shrink-0" />
                          {feature}
                        </li>
                      ))}
                    </ul>
                    <Button asChild className={`w-full ${pkg.popular ? 'bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700' : 'bg-slate-700 hover:bg-slate-600'} text-white`}>
                      <Link to="/quote">Get Started</Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 bg-gradient-to-r from-amber-600 to-amber-500">
          <div className="container mx-auto px-6 text-center">
            <h2 className="text-4xl md:text-5xl font-bold mb-6">Ready to Dominate Local Search?</h2>
            <p className="text-xl mb-8 max-w-3xl mx-auto">
              Join hundreds of local businesses that trust GOD Digital Marketing to drive their local growth and attract more customers.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button asChild size="lg" className="bg-white text-amber-600 hover:bg-slate-100 text-lg px-8 py-4">
                <Link to="/quote">Get Local SEO Strategy</Link>
              </Button>
              <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-amber-600 text-lg px-8 py-4">
                <Link to="/contact">Book Local SEO Consultation</Link>
              </Button>
            </div>
          </div>
        </section>
      </main>
      
      <Footer />
    </div>
  );
};

export default LocalSeoServices;
