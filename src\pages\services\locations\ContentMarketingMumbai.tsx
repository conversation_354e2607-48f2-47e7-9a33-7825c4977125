import React from 'react';
import { Link } from 'react-router-dom';
import { ArrowRight, FileText, TrendingUp, Users, CheckCircle, Building, Star, Crown, Edit, Zap } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';

const ContentMarketingMumbai = () => {
  const contentServices = [
    'Content Marketing Strategy Mumbai',
    'Content Creation Services Mumbai',
    'AI Content Generation Mumbai',
    'Blog Writing Services Mumbai',
    'Copywriting Services Mumbai',
    'Technical Writing Mumbai',
    'Website Content Writing Mumbai',
    'SEO Content Writing Mumbai',
    'Social Media Content Mumbai',
    'Email Content Creation Mumbai',
    'Video Content Scripts Mumbai',
    'Infographic Content Mumbai',
    'White Paper Writing Mumbai',
    'Case Study Writing Mumbai',
    'Press Release Writing Mumbai',
    'Product Description Writing Mumbai',
    'Financial Content Writing Mumbai',
    'Entertainment Content Mumbai',
    'Content Localization Mumbai',
    'Content Performance Analytics Mumbai'
  ];

  const mumbaiAreas = [
    'South Mumbai Content Marketing',
    'Bandra Content Services',
    'Andheri Content Creation',
    'Powai Content Writing',
    'Worli Content Strategy',
    'Lower Parel Content Marketing',
    'BKC Content Services',
    'Malad Content Creation',
    'Goregaon Content Writing',
    'Thane Content Strategy',
    'Navi Mumbai Content Marketing',
    'Borivali Content Services'
  ];

  const contentTypes = [
    {
      type: 'Financial Content',
      description: 'Specialized content for Mumbai\'s financial district and banking sector',
      icon: Edit,
      features: ['Financial Reports', 'Investment Content', 'Banking Articles', 'Compliance Writing']
    },
    {
      type: 'Entertainment Content',
      description: 'Creative content for Mumbai\'s entertainment and media industry',
      icon: Star,
      features: ['Script Writing', 'Media Content', 'Celebrity Profiles', 'Event Coverage']
    },
    {
      type: 'Business Content',
      description: 'Professional content for Mumbai\'s corporate and business community',
      icon: FileText,
      features: ['Corporate Communications', 'Business Blogs', 'Annual Reports', 'Executive Content']
    },
    {
      type: 'AI-Powered Content',
      description: 'Advanced AI content generation for scale and efficiency',
      icon: Zap,
      features: ['AI Writing Tools', 'Content Automation', 'Bulk Content Creation', 'Quality Assurance']
    }
  ];

  const contentPackages = [
    {
      name: 'Content Marketing Mumbai Starter',
      price: '₹28,000',
      period: '/month',
      description: 'Perfect for small Mumbai businesses starting with content marketing',
      features: [
        '8 Blog Posts per Month',
        'SEO-Optimized Content',
        'Social Media Content (20 posts)',
        'Basic Graphics & Images',
        'Content Calendar',
        'Monthly Performance Report'
      ]
    },
    {
      name: 'Mumbai Content Marketing Professional',
      price: '₹50,000',
      period: '/month',
      description: 'Comprehensive content marketing for growing Mumbai businesses',
      features: [
        '16 Blog Posts per Month',
        'Website Content Updates',
        'Social Media Content (40 posts)',
        'Email Newsletter Content',
        'Video Script Writing',
        'Advanced SEO Content',
        'Content Strategy Consulting'
      ],
      popular: true
    },
    {
      name: 'Enterprise Content Marketing Mumbai',
      price: '₹90,000',
      period: '/month',
      description: 'Advanced content marketing for large Mumbai enterprises',
      features: [
        'Unlimited Content Creation',
        'AI-Powered Content Generation',
        'Multi-language Content',
        'Video Content Production',
        'White Papers & Case Studies',
        'Dedicated Content Manager',
        'Priority Support'
      ]
    }
  ];

  const stats = [
    {
      metric: '3000+',
      description: 'Content Pieces Created',
      detail: 'For Mumbai businesses'
    },
    {
      metric: '420%',
      description: 'Average Traffic Increase',
      detail: 'Through content marketing'
    },
    {
      metric: '₹30Cr+',
      description: 'Revenue Generated',
      detail: 'Through content-driven leads'
    },
    {
      metric: '88%',
      description: 'Lead Quality Improvement',
      detail: 'With targeted content'
    }
  ];

  const achievements = [
    'Top Content Marketing Agency in Mumbai',
    'Financial Content Specialists',
    'Entertainment Industry Experts',
    'AI Content Generation Leaders',
    'Multi-language Content Champions',
    'B2B Content Marketing Specialists'
  ];

  return (
    <div className="min-h-screen bg-slate-900 relative">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        {/* Hero Section */}
        <section className="pt-32 pb-20">
          <div className="container mx-auto px-6">
            <div className="max-w-5xl mx-auto text-center">
              <div className="inline-flex items-center space-x-2 bg-indigo-500/20 border border-indigo-500/30 rounded-full px-6 py-2 mb-8">
                <FileText className="w-4 h-4 text-indigo-400" />
                <span className="text-indigo-400 font-medium">Content Marketing Mumbai • Financial Capital Content Excellence</span>
              </div>
              
              <h1 className="text-5xl md:text-7xl font-bold mb-6 leading-tight">
                #1 Content Marketing Company in
                <span className="bg-gradient-to-r from-indigo-400 to-indigo-600 bg-clip-text text-transparent"> Mumbai</span>
              </h1>
              
              <p className="text-xl text-slate-300 mb-12 max-w-4xl mx-auto">
                Premier content marketing in Mumbai offering comprehensive content creation and strategy services from AI-powered content generation to financial sector content solutions. Serving 800+ Mumbai businesses across all areas - from financial services in BKC to entertainment industry in Andheri. Expert content marketing solutions with proven ₹30Cr+ revenue generation and 420% average traffic increase for Mumbai clients in India's commercial capital.
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
                <Button asChild size="lg" className="bg-gradient-to-r from-indigo-500 to-indigo-600 hover:from-indigo-600 hover:to-indigo-700 text-white text-lg px-8 py-4">
                  <Link to="/quote">Get Free Mumbai Content Audit</Link>
                </Button>
                <Button asChild size="lg" variant="outline" className="border-indigo-500 text-indigo-400 hover:bg-indigo-500 hover:text-white text-lg px-8 py-4">
                  <Link to="/contact">Call Content Experts: +91-8708577598</Link>
                </Button>
              </div>

              {/* Achievements */}
              <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                {achievements.map((achievement, index) => (
                  <div key={index} className="flex items-center justify-center space-x-2 bg-slate-800/50 rounded-lg p-3">
                    <CheckCircle className="w-4 h-4 text-indigo-400 flex-shrink-0" />
                    <span className="text-slate-300">{achievement}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* Stats Section */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-6xl font-bold mb-6">
                Mumbai Content Marketing
                <span className="bg-gradient-to-r from-indigo-400 to-indigo-600 bg-clip-text text-transparent"> Success Metrics</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-4xl mx-auto mb-12">
                Measurable content marketing results from Mumbai businesses across all industries - delivering exceptional traffic growth in India's financial capital.
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-4 gap-8 mb-16">
              {stats.map((stat, index) => (
                <div key={index} className="text-center">
                  <div className="text-5xl font-bold text-indigo-400 mb-2">{stat.metric}</div>
                  <div className="text-white font-semibold mb-1">{stat.description}</div>
                  <div className="text-slate-400">{stat.detail}</div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Content Types Section */}
        <section className="py-20">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-6xl font-bold mb-6">
                Content Types We
                <span className="bg-gradient-to-r from-indigo-400 to-indigo-600 bg-clip-text text-transparent"> Create</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-4xl mx-auto">
                Specialized content creation services for Mumbai's financial, entertainment, and business landscape.
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-4 gap-8 mb-20">
              {contentTypes.map((content, index) => (
                <Card key={index} className="bg-slate-900/80 border-indigo-500/20 hover:border-indigo-500/40 transition-all duration-300">
                  <CardContent className="p-8">
                    <div className="w-12 h-12 bg-gradient-to-r from-indigo-500 to-indigo-600 rounded-lg flex items-center justify-center mb-6">
                      <content.icon className="w-6 h-6 text-white" />
                    </div>
                    <h3 className="text-xl font-bold text-white mb-4">{content.type}</h3>
                    <p className="text-slate-300 mb-6">{content.description}</p>
                    <ul className="space-y-2">
                      {content.features.map((feature, idx) => (
                        <li key={idx} className="flex items-center text-slate-400 text-sm">
                          <CheckCircle className="w-3 h-3 text-indigo-400 mr-2 flex-shrink-0" />
                          {feature}
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Services Grid */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-6xl font-bold mb-6">
                Complete Content Marketing Services
                <span className="bg-gradient-to-r from-indigo-400 to-indigo-600 bg-clip-text text-transparent"> Available in Mumbai</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-4xl mx-auto">
                Comprehensive range of content marketing services covering every aspect of content creation and strategy for Mumbai businesses.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {contentServices.map((service, index) => (
                <Card key={index} className="bg-slate-900/80 border-indigo-500/20 hover:border-indigo-500/40 transition-all duration-300">
                  <CardContent className="p-6 text-center">
                    <FileText className="w-8 h-8 text-indigo-400 mx-auto mb-4" />
                    <h3 className="text-white font-semibold mb-2 text-sm">{service}</h3>
                    <Button asChild size="sm" variant="outline" className="border-indigo-500 text-indigo-400 hover:bg-indigo-500 hover:text-white text-xs">
                      <Link to="/quote">Get Quote</Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Pricing Section */}
        <section className="py-20">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-6xl font-bold mb-6">
                Mumbai Content Marketing
                <span className="bg-gradient-to-r from-indigo-400 to-indigo-600 bg-clip-text text-transparent"> Pricing</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-4xl mx-auto">
                Premium content marketing pricing designed for Mumbai's competitive business environment.
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
              {contentPackages.map((pkg, index) => (
                <Card key={index} className={`bg-slate-900/80 border-indigo-500/20 hover:border-indigo-500/40 transition-all duration-300 relative ${pkg.popular ? 'ring-2 ring-indigo-500' : ''}`}>
                  {pkg.popular && (
                    <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                      <span className="bg-indigo-500 text-white px-4 py-2 rounded-full text-sm font-semibold">Most Popular</span>
                    </div>
                  )}
                  <CardContent className="p-8">
                    <h3 className="text-2xl font-bold text-white mb-2">{pkg.name}</h3>
                    <p className="text-slate-300 mb-6">{pkg.description}</p>
                    <div className="mb-6">
                      <span className="text-4xl font-bold text-indigo-400">{pkg.price}</span>
                      <span className="text-slate-400">{pkg.period}</span>
                    </div>
                    <ul className="space-y-3 mb-8">
                      {pkg.features.map((feature, idx) => (
                        <li key={idx} className="flex items-center text-slate-300">
                          <CheckCircle className="w-4 h-4 text-indigo-400 mr-3 flex-shrink-0" />
                          {feature}
                        </li>
                      ))}
                    </ul>
                    <Button asChild className={`w-full ${pkg.popular ? 'bg-gradient-to-r from-indigo-500 to-indigo-600 hover:from-indigo-600 hover:to-indigo-700' : 'bg-slate-700 hover:bg-slate-600'} text-white`}>
                      <Link to="/quote">Get Started</Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 bg-gradient-to-r from-indigo-600 to-indigo-500">
          <div className="container mx-auto px-6 text-center">
            <h2 className="text-4xl md:text-6xl font-bold mb-6">Ready to Dominate Mumbai with Content?</h2>
            <p className="text-xl mb-8 max-w-4xl mx-auto">
              Join 800+ Mumbai businesses that trust GOD Digital Marketing for content marketing success. Proven strategies that deliver 420% traffic increase and ₹30Cr+ revenue generation.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button asChild size="lg" className="bg-white text-indigo-600 hover:bg-slate-100 text-lg px-8 py-4">
                <Link to="/quote">Get Free Content Audit</Link>
              </Button>
              <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-indigo-600 text-lg px-8 py-4">
                <Link to="/contact">Call Content Experts: +91-8708577598</Link>
              </Button>
            </div>
          </div>
        </section>
      </main>
      
      <Footer />
    </div>
  );
};

export default ContentMarketingMumbai;
