
import React from 'react';
import { Link } from 'react-router-dom';
import { Search, TrendingUp, Target, Award, CheckCircle, MapPin, Building2, Users2 } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';

const DelhiSeoServices = () => {
  return (
    <div className="min-h-screen bg-slate-900 text-white relative overflow-hidden">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        {/* Hero Section */}
        <section className="pt-32 pb-20">
          <div className="container mx-auto px-6">
            <div className="max-w-4xl mx-auto text-center">
              <div className="inline-flex items-center space-x-2 bg-amber-500/20 border border-amber-500/30 rounded-full px-6 py-2 mb-8">
                <MapPin className="w-4 h-4 text-amber-400" />
                <span className="text-amber-400 font-medium">Delhi SEO Services</span>
              </div>
              
              <h1 className="text-5xl md:text-6xl font-bold mb-6 leading-tight">
                Dominate Delhi's Search Results with
                <span className="bg-gradient-to-r from-amber-400 to-amber-600 bg-clip-text text-transparent"> Expert SEO Strategies</span>
              </h1>
              
              <p className="text-xl text-slate-300 mb-12 max-w-3xl mx-auto">
                In India's capital where business opportunities span from Connaught Place to Gurgaon, your online visibility determines success. Our Delhi-focused SEO strategies have helped businesses across NCR achieve top rankings, driving qualified traffic from Karol Bagh markets to corporate boardrooms in Connaught Place.
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button asChild size="lg" className="bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700 text-white text-lg px-8 py-4">
                  <Link to="/quote">Get Delhi SEO Audit</Link>
                </Button>
                <Button asChild size="lg" variant="outline" className="border-amber-500 text-amber-400 hover:bg-amber-500 hover:text-white text-lg px-8 py-4">
                  <Link to="/case-studies">Delhi Success Stories</Link>
                </Button>
              </div>
            </div>
          </div>
        </section>

        {/* Delhi Market Advantages */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <h2 className="text-4xl md:text-5xl font-bold mb-12 text-center">
              Why Delhi Businesses Need
              <span className="bg-gradient-to-r from-amber-400 to-amber-600 bg-clip-text text-transparent"> Specialized SEO</span>
            </h2>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
              <Card className="bg-slate-900/80 border-amber-500/20 hover:border-amber-500/40 transition-all duration-300">
                <CardContent className="p-8 text-center">
                  <Building2 className="w-12 h-12 text-amber-400 mx-auto mb-4" />
                  <h3 className="text-xl font-bold text-white mb-4">Government & Corporate Hub</h3>
                  <p className="text-slate-300">
                    Delhi's unique position as India's administrative capital creates distinct search patterns. From government contractors in Central Delhi to MNC headquarters in Gurgaon, we understand how Delhi searches differ.
                  </p>
                </CardContent>
              </Card>
              
              <Card className="bg-slate-900/80 border-amber-500/20 hover:border-amber-500/40 transition-all duration-300">
                <CardContent className="p-8 text-center">
                  <Users2 className="w-12 h-12 text-amber-400 mx-auto mb-4" />
                  <h3 className="text-xl font-bold text-white mb-4">Diverse Market Segments</h3>
                  <p className="text-slate-300">
                    From Chandni Chowk's traditional businesses to Cyber City's tech startups, Delhi's market diversity requires nuanced SEO strategies that speak to each segment's unique needs and search behaviors.
                  </p>
                </CardContent>
              </Card>
              
              <Card className="bg-slate-900/80 border-amber-500/20 hover:border-amber-500/40 transition-all duration-300">
                <CardContent className="p-8 text-center">
                  <Target className="w-12 h-12 text-amber-400 mx-auto mb-4" />
                  <h3 className="text-xl font-bold text-white mb-4">NCR Integration</h3>
                  <p className="text-slate-300">
                    Smart SEO strategies that capture traffic across the entire National Capital Region - from Noida's IT sector to Faridabad's manufacturing hub, ensuring comprehensive market coverage.
                  </p>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>

        {/* Delhi SEO Process */}
        <section className="py-20">
          <div className="container mx-auto px-6">
            <h2 className="text-4xl md:text-5xl font-bold mb-12 text-center">
              Our Delhi-Focused
              <span className="bg-gradient-to-r from-amber-400 to-amber-600 bg-clip-text text-transparent"> SEO Methodology</span>
            </h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              <div className="text-center">
                <div className="w-16 h-16 bg-gradient-to-r from-amber-500 to-amber-600 rounded-full flex items-center justify-center mx-auto mb-6">
                  <span className="text-xl font-bold text-white">1</span>
                </div>
                <h3 className="text-xl font-bold text-white mb-4">Delhi Market Research</h3>
                <p className="text-slate-300">
                  Comprehensive analysis of Delhi's search landscape, from high-volume commercial keywords in CP to long-tail local queries in Greater Kailash, understanding seasonal trends and local events impact.
                </p>
              </div>
              
              <div className="text-center">
                <div className="w-16 h-16 bg-gradient-to-r from-amber-500 to-amber-600 rounded-full flex items-center justify-center mx-auto mb-6">
                  <span className="text-xl font-bold text-white">2</span>
                </div>
                <h3 className="text-xl font-bold text-white mb-4">Local Competition Analysis</h3>
                <p className="text-slate-300">
                  Deep competitor research across Delhi's business districts - analyzing what works for Rajouri Garden retailers, Nehru Place tech vendors, and Lajpat Nagar service providers to identify ranking opportunities.
                </p>
              </div>
              
              <div className="text-center">
                <div className="w-16 h-16 bg-gradient-to-r from-amber-500 to-amber-600 rounded-full flex items-center justify-center mx-auto mb-6">
                  <span className="text-xl font-bold text-white">3</span>
                </div>
                <h3 className="text-xl font-bold text-white mb-4">Delhi-Centric Content</h3>
                <p className="text-slate-300">
                  Create content that resonates with Delhi audiences - incorporating local landmarks, addressing Delhi-specific problems, and using language patterns familiar to NCR residents for better engagement.
                </p>
              </div>
              
              <div className="text-center">
                <div className="w-16 h-16 bg-gradient-to-r from-amber-500 to-amber-600 rounded-full flex items-center justify-center mx-auto mb-6">
                  <span className="text-xl font-bold text-white">4</span>
                </div>
                <h3 className="text-xl font-bold text-white mb-4">Technical Excellence</h3>
                <p className="text-slate-300">
                  Advanced technical SEO optimized for Delhi's internet infrastructure, mobile usage patterns, and local search behaviors, ensuring fast loading across Delhi's varying connectivity speeds.
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* Delhi Success Case Study */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="max-w-4xl mx-auto text-center">
              <h2 className="text-4xl md:text-5xl font-bold mb-8">
                Delhi Success Story:
                <span className="bg-gradient-to-r from-amber-400 to-amber-600 bg-clip-text text-transparent"> Government Contractor's Growth</span>
              </h2>
              
              <div className="bg-slate-900/60 border border-amber-500/20 rounded-xl p-8 mb-8">
                <p className="text-lg text-slate-300 mb-6 italic">
                  "A Delhi-based government contracting firm specializing in infrastructure projects was invisible online despite handling crore-value contracts. They were losing opportunities to competitors who ranked higher for government tender-related searches."
                </p>
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-amber-400 mb-2">420%</div>
                    <div className="text-white">Increase in Organic Traffic</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-amber-400 mb-2">1st Page</div>
                    <div className="text-white">Rankings for 85+ Keywords</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-amber-400 mb-2">250%</div>
                    <div className="text-white">Increase in Lead Quality</div>
                  </div>
                </div>
                
                <p className="text-slate-300">
                  Through strategic optimization for government-related keywords, local Delhi citations, and authority building in the infrastructure sector, we positioned them as Delhi's go-to government contractor, resulting in consistent high-value project opportunities.
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* Delhi SEO FAQs */}
        <section className="py-20">
          <div className="container mx-auto px-6">
            <h2 className="text-4xl md:text-5xl font-bold mb-12 text-center">
              Delhi SEO
              <span className="bg-gradient-to-r from-amber-400 to-amber-600 bg-clip-text text-transparent"> Expert Answers</span>
            </h2>
            
            <div className="max-w-4xl mx-auto space-y-8">
              <div className="bg-slate-800/50 border border-slate-700 rounded-xl p-6">
                <h3 className="text-xl font-bold text-white mb-3">How is Delhi SEO different from other Indian cities?</h3>
                <p className="text-slate-300">
                  Delhi's search behavior is unique due to its diverse population and business landscape. Government-related searches are higher, B2B queries peak during weekdays, and local searches often include area-specific terms like "near CP" or "in South Ex." We tailor strategies for these Delhi-specific patterns.
                </p>
              </div>
              
              <div className="bg-slate-800/50 border border-slate-700 rounded-xl p-6">
                <h3 className="text-xl font-bold text-white mb-3">Which areas in Delhi have the highest search competition?</h3>
                <p className="text-slate-300">
                  Connaught Place, Karol Bagh, and Lajpat Nagar see the highest commercial search competition due to high business density. However, emerging areas like Dwarka and Rohini offer excellent opportunities for local SEO with lower competition and growing search volumes.
                </p>
              </div>
              
              <div className="bg-slate-800/50 border border-slate-700 rounded-xl p-6">
                <h3 className="text-xl font-bold text-white mb-3">How do you optimize for Delhi's multilingual searches?</h3>
                <p className="text-slate-300">
                  Delhi users search in Hindi, English, and Hinglish. We optimize for all three, including Hindi keyword research, bilingual content creation, and understanding code-mixing patterns. This comprehensive approach captures Delhi's diverse linguistic search behavior.
                </p>
              </div>
              
              <div className="bg-slate-800/50 border border-slate-700 rounded-xl p-6">
                <h3 className="text-xl font-bold text-white mb-3">What's the average timeline for SEO results in Delhi?</h3>
                <p className="text-slate-300">
                  Delhi's competitive market typically shows initial improvements in 3-4 months, with significant results in 6-8 months. Government and B2B sectors may take longer due to higher competition, while local service businesses often see faster results.
                </p>
              </div>
              
              <div className="bg-slate-800/50 border border-slate-700 rounded-xl p-6">
                <h3 className="text-xl font-bold text-white mb-3">Do you handle SEO for Delhi government contractors?</h3>
                <p className="text-slate-300">
                  Yes, we specialize in SEO for Delhi's government contracting sector. This includes tender-related keyword optimization, compliance with government advertising guidelines, and building authority in public sector domains while maintaining transparency and ethical practices.
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 bg-gradient-to-r from-amber-600 to-amber-500">
          <div className="container mx-auto px-6 text-center">
            <h2 className="text-4xl md:text-5xl font-bold mb-6">Ready to Dominate Delhi's Search Results?</h2>
            <p className="text-xl mb-8 max-w-3xl mx-auto">
              Join Delhi's leading businesses who trust our SEO expertise to drive growth in India's capital. Get your free Delhi market analysis and SEO strategy consultation.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button asChild size="lg" className="bg-white text-amber-600 hover:bg-slate-100 text-lg px-8 py-4">
                <Link to="/quote">Get Free Delhi SEO Audit</Link>
              </Button>
              <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-amber-600 text-lg px-8 py-4">
                <Link to="/contact">Book Delhi Consultation</Link>
              </Button>
            </div>
          </div>
        </section>
      </main>
      
      <Footer />
    </div>
  );
};

export default DelhiSeoServices;
