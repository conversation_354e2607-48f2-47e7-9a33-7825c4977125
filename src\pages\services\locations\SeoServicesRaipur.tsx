import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { ArrowRight, Search, TrendingUp, Users, CheckCircle, Building, Star, Crown, Target, Zap } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';
import Sidebar from '@/components/Sidebar';

const SeoServicesRaipur = () => {
  const seoServices = [
    'SEO Services Raipur',
    'Local SEO Raipur',
    'Google My Business Optimization Raipur',
    'Website SEO Audit Raipur',
    'Keyword Research Raipur',
    'On-Page SEO Raipur',
    'Off-Page SEO Raipur',
    'Technical SEO Raipur',
    'E-commerce SEO Raipur',
    'Mobile SEO Raipur',
    'Voice Search SEO Raipur',
    'Schema Markup Raipur',
    'Link Building Raipur',
    'Content SEO Raipur',
    'Local Citation Building Raipur',
    'Mining Industry SEO Raipur',
    'Steel Industry SEO Raipur',
    'Agriculture SEO Raipur',
    'Government SEO Raipur',
    'Healthcare SEO Raipur'
  ];

  const industryFocus = [
    {
      industry: 'Mining & Steel Industry SEO',
      description: 'Specialized SEO for Raipur\'s dominant mining and steel industry businesses',
      icon: Building,
      features: ['Mining Company SEO', 'Steel Plant SEO', 'Coal Industry SEO', 'Industrial Equipment SEO']
    },
    {
      industry: 'Agriculture & Agribusiness SEO',
      description: 'Strategic SEO for Chhattisgarh\'s agriculture sector and agribusiness companies',
      icon: Star,
      features: ['Agricultural SEO', 'Rice Industry SEO', 'Agribusiness SEO', 'Farm Equipment SEO']
    },
    {
      industry: 'Government & Public Sector SEO',
      description: 'Comprehensive SEO for Raipur\'s government offices and public sector organizations',
      icon: Crown,
      features: ['Government Portal SEO', 'Public Service SEO', 'Administrative SEO', 'Citizen Service SEO']
    },
    {
      industry: 'Healthcare & Education SEO',
      description: 'Targeted SEO for Raipur\'s healthcare providers and educational institutions',
      icon: Target,
      features: ['Hospital SEO', 'Medical Practice SEO', 'University SEO', 'School SEO']
    }
  ];

  const localSeoFeatures = [
    'Raipur Local Search Optimization',
    'Chhattisgarh State SEO Coverage',
    'Mining Industry Specialization',
    'Agricultural Business SEO',
    'Government Sector Optimization',
    'Raipur Map Pack Optimization',
    'Regional Keyword Targeting',
    'Local Business Directory Listings',
    'Industrial SEO Expertise',
    'Rural Market SEO'
  ];

  const achievements = [
    '130+ Raipur Businesses Optimized',
    '2,600% Average Traffic Growth',
    '₹26Cr+ Revenue Generated for Clients',
    '94% First Page Rankings Achieved',
    'Top SEO Company in Chhattisgarh',
    'Mining Industry SEO Specialists'
  ];

  const industrialSeoMetrics = [
    {
      sector: 'Mining & Steel Industry',
      description: 'B2B SEO for mining companies and steel manufacturers in Raipur',
      metrics: ['850+ industrial keywords ranking', '₹12Cr+ B2B revenue generated', '420% increase in business inquiries', '95% local market dominance']
    },
    {
      sector: 'Agriculture & Agribusiness',
      description: 'SEO for agricultural businesses and agribusiness companies',
      metrics: ['650+ agriculture keywords ranking', '₹8Cr+ agribusiness revenue', '380% increase in farmer connections', '88% regional market coverage']
    },
    {
      sector: 'Government & Public Services',
      description: 'SEO for government portals and public service organizations',
      metrics: ['450+ government keywords ranking', '₹4Cr+ public service value', '520% increase in citizen engagement', '92% service accessibility']
    },
    {
      sector: 'Healthcare & Education',
      description: 'SEO for healthcare providers and educational institutions',
      metrics: ['720+ healthcare/education keywords', '₹6Cr+ sector revenue generated', '350% increase in patient/student inquiries', '90% local healthcare dominance']
    }
  ];

  return (
    <div className="min-h-screen bg-slate-900 relative">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        <div className="container mx-auto px-6 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            <div className="lg:col-span-3">
              <section className="pt-24 pb-16">
                <div className="max-w-4xl">
                  <div className="inline-flex items-center space-x-2 bg-amber-500/20 border border-amber-500/30 rounded-full px-6 py-2 mb-8">
                    <Search className="w-4 h-4 text-amber-400" />
                    <span className="text-amber-400 font-medium">SEO Services Raipur • Chhattisgarh's Digital Marketing Leaders</span>
                  </div>
                  
                  <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
                    #1 SEO Company in
                    <span className="bg-gradient-to-r from-amber-400 to-amber-600 bg-clip-text text-transparent"> Raipur</span>
                  </h1>
                  
                  <p className="text-xl text-slate-300 mb-8">
                    Premier SEO services in Raipur offering comprehensive search engine optimization solutions for mining companies, steel industries, agricultural businesses, and government organizations. Our Raipur SEO company provides professional SEO services including local SEO Raipur, Google My Business optimization, mining industry SEO, agriculture sector SEO, and government SEO. Serving 130+ Raipur businesses with proven ₹26Cr+ revenue generation and 2,600% average traffic growth through strategic search engine optimization and digital marketing excellence in Chhattisgarh's capital city.
                  </p>
                  
                  <div className="flex flex-col sm:flex-row gap-4 mb-8">
                    <Button asChild size="lg" className="bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700 text-white text-lg px-8 py-4">
                      <Link to="/quote">Get Free SEO Audit Raipur</Link>
                    </Button>
                    <Button asChild size="lg" variant="outline" className="border-amber-500 text-amber-400 hover:bg-amber-500 hover:text-white text-lg px-8 py-4">
                      <Link to="/contact">Call Raipur SEO Experts: +91-8708577598</Link>
                    </Button>
                  </div>

                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                    {achievements.map((achievement, index) => (
                      <div key={index} className="flex items-center justify-center space-x-2 bg-slate-800/50 rounded-lg p-3">
                        <CheckCircle className="w-4 h-4 text-amber-400 flex-shrink-0" />
                        <span className="text-slate-300">{achievement}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </section>

              <section className="mb-16">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Raipur Industry-Specific
                    <span className="bg-gradient-to-r from-amber-400 to-amber-600 bg-clip-text text-transparent"> SEO Solutions</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Specialized SEO strategies tailored for Raipur's key industries and business sectors.
                  </p>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  {industryFocus.map((industry, index) => (
                    <Card key={index} className="bg-slate-900/80 border-amber-500/20 hover:border-amber-500/40 transition-all duration-300">
                      <CardContent className="p-8">
                        <div className="w-12 h-12 bg-gradient-to-r from-amber-500 to-amber-600 rounded-lg flex items-center justify-center mb-6">
                          <industry.icon className="w-6 h-6 text-white" />
                        </div>
                        <h3 className="text-xl font-bold text-white mb-4">{industry.industry}</h3>
                        <p className="text-slate-300 mb-6">{industry.description}</p>
                        <ul className="space-y-2">
                          {industry.features.map((feature, idx) => (
                            <li key={idx} className="flex items-center text-slate-400 text-sm">
                              <CheckCircle className="w-3 h-3 text-amber-400 mr-2 flex-shrink-0" />
                              {feature}
                            </li>
                          ))}
                        </ul>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>

              <section className="mb-16 bg-slate-800/30 rounded-2xl p-8">
                <h3 className="text-2xl font-bold text-white mb-6 text-center">
                  Comprehensive SEO Services in Raipur
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  {seoServices.map((service, index) => (
                    <div key={index} className="bg-slate-900/50 rounded-lg p-4 text-center">
                      <div className="text-amber-400 font-medium text-sm">{service}</div>
                    </div>
                  ))}
                </div>
              </section>

              <section className="mb-16 bg-slate-800/30 rounded-2xl p-8">
                <h3 className="text-2xl font-bold text-white mb-6 text-center">
                  Industry-Specific SEO Performance Metrics
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                  {industrialSeoMetrics.map((metric, index) => (
                    <div key={index} className="bg-slate-900/50 rounded-lg p-6">
                      <h4 className="text-amber-400 font-semibold mb-3">{metric.sector}</h4>
                      <p className="text-slate-300 text-sm mb-4">{metric.description}</p>
                      <div className="space-y-2">
                        {metric.metrics.map((data, idx) => (
                          <div key={idx} className="text-slate-400 text-xs">{data}</div>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </section>

              <section className="mb-16 bg-slate-800/30 rounded-2xl p-8">
                <h3 className="text-2xl font-bold text-white mb-6 text-center">
                  Local SEO Raipur Specialization
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                  {localSeoFeatures.map((feature, index) => (
                    <div key={index} className="bg-slate-900/50 rounded-lg p-4 text-center">
                      <div className="text-amber-400 font-medium text-sm">{feature}</div>
                    </div>
                  ))}
                </div>
              </section>

              <section className="mb-16 bg-slate-800/30 rounded-2xl p-8">
                <h3 className="text-2xl font-bold text-white mb-6 text-center">
                  Why Choose Our Raipur SEO Services?
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  <div className="text-center">
                    <div className="w-16 h-16 bg-gradient-to-r from-amber-500 to-amber-600 rounded-full flex items-center justify-center mx-auto mb-4">
                      <Building className="w-8 h-8 text-white" />
                    </div>
                    <h4 className="text-white font-semibold mb-2">Industrial Market Expertise</h4>
                    <p className="text-slate-400 text-sm">Deep understanding of Raipur's mining, steel, and agricultural industries for targeted B2B SEO strategies.</p>
                  </div>
                  <div className="text-center">
                    <div className="w-16 h-16 bg-gradient-to-r from-amber-500 to-amber-600 rounded-full flex items-center justify-center mx-auto mb-4">
                      <TrendingUp className="w-8 h-8 text-white" />
                    </div>
                    <h4 className="text-white font-semibold mb-2">Proven Results</h4>
                    <p className="text-slate-400 text-sm">130+ successful SEO campaigns in Raipur with 2,600% average traffic growth and ₹26Cr+ revenue generation.</p>
                  </div>
                  <div className="text-center">
                    <div className="w-16 h-16 bg-gradient-to-r from-amber-500 to-amber-600 rounded-full flex items-center justify-center mx-auto mb-4">
                      <Users className="w-8 h-8 text-white" />
                    </div>
                    <h4 className="text-white font-semibold mb-2">Multi-Sector Experience</h4>
                    <p className="text-slate-400 text-sm">Specialized expertise in mining, agriculture, government, and healthcare sectors prominent in Raipur market.</p>
                  </div>
                </div>
              </section>

              <section className="mb-16 bg-slate-800/30 rounded-2xl p-8">
                <h3 className="text-2xl font-bold text-white mb-6 text-center">
                  Complete Digital Marketing Solutions Raipur
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  <Link to="/services/ppc/raipur" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-amber-400 font-semibold mb-2 group-hover:text-amber-300">Google Ads Raipur</h4>
                    <p className="text-slate-400 text-sm">PPC advertising and Google Ads management for Raipur businesses</p>
                  </Link>
                  <Link to="/services/social-media/raipur" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-amber-400 font-semibold mb-2 group-hover:text-amber-300">Social Media Marketing Raipur</h4>
                    <p className="text-slate-400 text-sm">Social media management and marketing for Raipur brands</p>
                  </Link>
                  <Link to="/services/content/raipur" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-amber-400 font-semibold mb-2 group-hover:text-amber-300">Content Marketing Raipur</h4>
                    <p className="text-slate-400 text-sm">Content creation and marketing strategies for Raipur businesses</p>
                  </Link>
                  <Link to="/services/web-development/raipur" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-amber-400 font-semibold mb-2 group-hover:text-amber-300">Web Development Raipur</h4>
                    <p className="text-slate-400 text-sm">Professional website development and design services</p>
                  </Link>
                  <Link to="/services/email/raipur" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-amber-400 font-semibold mb-2 group-hover:text-amber-300">Email Marketing Raipur</h4>
                    <p className="text-slate-400 text-sm">Email marketing campaigns and automation for Raipur businesses</p>
                  </Link>
                  <Link to="/portfolio" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-amber-400 font-semibold mb-2 group-hover:text-amber-300">Raipur Success Stories</h4>
                    <p className="text-slate-400 text-sm">See our proven results and case studies from Raipur clients</p>
                  </Link>
                </div>
              </section>

              <section className="bg-gradient-to-r from-amber-600 to-amber-500 rounded-2xl p-8 text-center">
                <h2 className="text-3xl md:text-5xl font-bold mb-6">Ready to Dominate Raipur Search Results?</h2>
                <p className="text-xl mb-8">
                  Join 130+ successful Raipur businesses that trust GOD Digital Marketing for SEO excellence. Proven strategies delivering 2,600% traffic growth and ₹26Cr+ revenue generation.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button asChild size="lg" className="bg-white text-amber-600 hover:bg-slate-100 text-lg px-8 py-4">
                    <Link to="/quote">Get Free SEO Audit</Link>
                  </Button>
                  <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-amber-600 text-lg px-8 py-4">
                    <Link to="/contact">Call Raipur SEO Experts: +91-8708577598</Link>
                  </Button>
                </div>
              </section>
            </div>

            <div className="lg:col-span-1">
              <Sidebar currentService="seo" />
            </div>
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
};

export default SeoServicesRaipur;
