import React from 'react';
import { Link } from 'react-router-dom';
import { ArrowRight, MapPin, TrendingUp, Users, CheckCircle, Building, Star, Wheat } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';

const Karnal = () => {
  const services = [
    'SEO Services Karnal',
    'Google Ads Management Karnal',
    'Social Media Marketing Karnal',
    'Local SEO Karnal',
    'E-commerce SEO Karnal',
    'Content Marketing Karnal',
    'Website Development Karnal',
    'Digital Marketing Consulting Karnal'
  ];

  const industries = [
    'Agriculture & Rice Mills Karnal',
    'Basmati Rice Export Karnal',
    'Dairy Industry Karnal',
    'Food Processing Karnal',
    'Real Estate Karnal',
    'Healthcare Karnal',
    'Education Karnal',
    'Retail & E-commerce Karnal'
  ];

  const areas = [
    'Model Town Digital Marketing',
    'Sector 12 SEO Services',
    'Kunjpura Digital Marketing',
    'Assandh SEO Services',
    'Indri Digital Marketing',
    'Nilokheri SEO Services',
    'Taraori Digital Marketing',
    'Gharaunda SEO Services'
  ];

  const stats = [
    {
      metric: '100+',
      description: 'Karnal Businesses Served',
      detail: 'Across all sectors'
    },
    {
      metric: '410%',
      description: 'Average Traffic Increase',
      detail: 'For Karnal clients'
    },
    {
      metric: '₹38L+',
      description: 'Revenue Generated',
      detail: 'For Karnal businesses'
    }
  ];

  return (
    <div className="min-h-screen bg-slate-900 relative">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        {/* Hero Section */}
        <section className="pt-32 pb-20">
          <div className="container mx-auto px-6">
            <div className="max-w-4xl mx-auto text-center">
              <div className="inline-flex items-center space-x-2 bg-green-500/20 border border-green-500/30 rounded-full px-6 py-2 mb-8">
                <Wheat className="w-4 h-4 text-green-400" />
                <span className="text-green-400 font-medium">Karnal Digital Marketing • Rice Bowl of India</span>
              </div>
              
              <h1 className="text-5xl md:text-6xl font-bold mb-6 leading-tight">
                #1 Digital Marketing Company in
                <span className="bg-gradient-to-r from-green-400 to-green-600 bg-clip-text text-transparent"> Karnal</span>
              </h1>
              
              <p className="text-xl text-slate-300 mb-12 max-w-3xl mx-auto">
                Leading digital marketing agency in Karnal helping businesses dominate online. From basmati rice exporters to dairy industries, we've helped 100+ businesses grow with proven SEO, Google Ads, and digital marketing strategies across all Karnal areas.
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button asChild size="lg" className="bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white text-lg px-8 py-4">
                  <Link to="/quote">Get Free Karnal SEO Audit</Link>
                </Button>
                <Button asChild size="lg" variant="outline" className="border-green-500 text-green-400 hover:bg-green-500 hover:text-white text-lg px-8 py-4">
                  <Link to="/contact">Call Karnal Office</Link>
                </Button>
              </div>
            </div>
          </div>
        </section>

        {/* Stats Section */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Karnal Digital Marketing
                <span className="bg-gradient-to-r from-green-400 to-green-600 bg-clip-text text-transparent"> Success Stories</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto mb-12">
                Real results from Karnal businesses across industries - from basmati rice exporters to dairy processors in the Rice Bowl of India.
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
              {stats.map((stat, index) => (
                <div key={index} className="text-center">
                  <div className="text-5xl font-bold text-green-400 mb-2">{stat.metric}</div>
                  <div className="text-white font-semibold mb-1">{stat.description}</div>
                  <div className="text-slate-400">{stat.detail}</div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Services Section */}
        <section className="py-20">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Digital Marketing Services in
                <span className="bg-gradient-to-r from-green-400 to-green-600 bg-clip-text text-transparent"> Karnal</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto">
                Comprehensive digital marketing solutions for Karnal businesses looking to dominate online in the Rice Bowl of India.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {services.map((service, index) => (
                <Card key={index} className="bg-slate-900/80 border-green-500/20 hover:border-green-500/40 transition-all duration-300">
                  <CardContent className="p-6 text-center">
                    <Building className="w-8 h-8 text-green-400 mx-auto mb-4" />
                    <h3 className="text-white font-semibold mb-2">{service}</h3>
                    <Button asChild size="sm" variant="outline" className="border-green-500 text-green-400 hover:bg-green-500 hover:text-white">
                      <Link to="/quote">Learn More</Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Industries Section */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Karnal Industries We
                <span className="bg-gradient-to-r from-green-400 to-green-600 bg-clip-text text-transparent"> Serve</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto">
                Specialized digital marketing strategies for Karnal's agriculture, rice export, and food processing industries.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {industries.map((industry, index) => (
                <Card key={index} className="bg-slate-900/80 border-green-500/20 hover:border-green-500/40 transition-all duration-300">
                  <CardContent className="p-6 text-center">
                    <Star className="w-8 h-8 text-green-400 mx-auto mb-4" />
                    <h3 className="text-white font-semibold mb-2">{industry}</h3>
                    <Button asChild size="sm" variant="outline" className="border-green-500 text-green-400 hover:bg-green-500 hover:text-white">
                      <Link to="/quote">Get Strategy</Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Areas Section */}
        <section className="py-20">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Karnal Areas We
                <span className="bg-gradient-to-r from-green-400 to-green-600 bg-clip-text text-transparent"> Cover</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto">
                Local digital marketing expertise across all major Karnal areas including agricultural and industrial zones.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {areas.map((area, index) => (
                <Card key={index} className="bg-slate-900/80 border-green-500/20 hover:border-green-500/40 transition-all duration-300">
                  <CardContent className="p-6 text-center">
                    <MapPin className="w-8 h-8 text-green-400 mx-auto mb-4" />
                    <h3 className="text-white font-semibold mb-2">{area}</h3>
                    <Button asChild size="sm" variant="outline" className="border-green-500 text-green-400 hover:bg-green-500 hover:text-white">
                      <Link to="/contact">Contact Us</Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Why Choose Us Section */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Why Choose GOD Digital Marketing in
                <span className="bg-gradient-to-r from-green-400 to-green-600 bg-clip-text text-transparent"> Karnal?</span>
              </h2>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              <Card className="bg-slate-900/80 border-green-500/20">
                <CardContent className="p-8">
                  <TrendingUp className="w-12 h-12 text-green-400 mb-6" />
                  <h3 className="text-xl font-bold text-white mb-4">Agricultural Export Expertise</h3>
                  <p className="text-slate-300">Deep understanding of Karnal's basmati rice industry, agricultural exports, and food processing business dynamics.</p>
                </CardContent>
              </Card>

              <Card className="bg-slate-900/80 border-green-500/20">
                <CardContent className="p-8">
                  <Users className="w-12 h-12 text-green-400 mb-6" />
                  <h3 className="text-xl font-bold text-white mb-4">Proven Karnal Results</h3>
                  <p className="text-slate-300">100+ successful Karnal campaigns with measurable ROI improvements and business growth across agricultural and industrial sectors.</p>
                </CardContent>
              </Card>

              <Card className="bg-slate-900/80 border-green-500/20">
                <CardContent className="p-8">
                  <CheckCircle className="w-12 h-12 text-green-400 mb-6" />
                  <h3 className="text-xl font-bold text-white mb-4">Export Market Focus</h3>
                  <p className="text-slate-300">Specialized expertise in international marketing for rice exporters and agricultural businesses with global reach strategies.</p>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 bg-gradient-to-r from-green-600 to-green-500">
          <div className="container mx-auto px-6 text-center">
            <h2 className="text-4xl md:text-5xl font-bold mb-6">Ready to Dominate Karnal's Digital Market?</h2>
            <p className="text-xl mb-8 max-w-3xl mx-auto">
              Join 100+ Karnal businesses that trust GOD Digital Marketing for their online growth. From rice exports to dairy industry, we deliver results.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button asChild size="lg" className="bg-white text-green-600 hover:bg-slate-100 text-lg px-8 py-4">
                <Link to="/quote">Get Free Karnal SEO Audit</Link>
              </Button>
              <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-green-600 text-lg px-8 py-4">
                <Link to="/contact">Call Karnal Office Now</Link>
              </Button>
            </div>
          </div>
        </section>
      </main>
      
      <Footer />
    </div>
  );
};

export default Karnal;
