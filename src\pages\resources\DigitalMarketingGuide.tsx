import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { ArrowRight, BookOpen, TrendingUp, Users, CheckCircle, Search, Target, Star } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';

const DigitalMarketingGuide = () => {
  const guideTopics = [
    'Search Engine Optimization (SEO) Fundamentals',
    'Google Ads & PPC Campaign Management',
    'Social Media Marketing Strategies',
    'Content Marketing & Blogging',
    'Email Marketing Automation',
    'Conversion Rate Optimization',
    'Analytics & Performance Tracking',
    'Local SEO for Small Businesses',
    'E-commerce Digital Marketing',
    'International SEO Strategies'
  ];

  const chapters = [
    {
      title: 'SEO Mastery',
      description: 'Complete guide to dominating search engine rankings',
      icon: Search,
      topics: ['Keyword Research', 'On-Page SEO', 'Technical SEO', 'Link Building', 'Local SEO']
    },
    {
      title: 'Paid Advertising',
      description: 'Master Google Ads, Facebook Ads, and other paid channels',
      icon: Target,
      topics: ['Campaign Setup', 'Audience Targeting', 'Ad Creation', 'Optimization', 'ROI Tracking']
    },
    {
      title: 'Content Strategy',
      description: 'Create content that engages and converts your audience',
      icon: Star,
      topics: ['Content Planning', 'Blog Writing', 'Video Marketing', 'Social Content', 'Content Distribution']
    }
  ];

  const benefits = [
    'Learn from 7 years of international digital marketing experience',
    'Proven strategies used across 6 countries',
    'Step-by-step implementation guides',
    'Real case studies and examples',
    'Templates and checklists included',
    'Regular updates with latest trends',
    'Expert tips and insider secrets',
    'Actionable strategies for immediate results'
  ];

  return (
    <div className="min-h-screen bg-slate-900 relative">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        {/* Hero Section */}
        <section className="pt-32 pb-20">
          <div className="container mx-auto px-6">
            <div className="max-w-4xl mx-auto text-center">
              <div className="inline-flex items-center space-x-2 bg-blue-500/20 border border-blue-500/30 rounded-full px-6 py-2 mb-8">
                <BookOpen className="w-4 h-4 text-blue-400" />
                <span className="text-blue-400 font-medium">Digital Marketing Guide • Expert Knowledge</span>
              </div>
              
              <h1 className="text-5xl md:text-6xl font-bold mb-6 leading-tight">
                Complete Digital Marketing Guide
                <span className="bg-gradient-to-r from-blue-400 to-blue-600 bg-clip-text text-transparent"> From Industry Experts</span>
              </h1>
              
              <p className="text-xl text-slate-300 mb-12 max-w-3xl mx-auto">
                Master digital marketing with our comprehensive guide based on 7 years of international experience. Learn proven strategies that have generated ₹10Cr+ revenue across 6 countries and helped 1000+ businesses dominate online.
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button asChild size="lg" className="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white text-lg px-8 py-4">
                  <Link to="/quote">Get Free Digital Marketing Consultation</Link>
                </Button>
                <Button asChild size="lg" variant="outline" className="border-blue-500 text-blue-400 hover:bg-blue-500 hover:text-white text-lg px-8 py-4">
                  <Link to="/contact">Download Complete Guide</Link>
                </Button>
              </div>
            </div>
          </div>
        </section>

        {/* Guide Topics Section */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                What You'll Learn in Our
                <span className="bg-gradient-to-r from-blue-400 to-blue-600 bg-clip-text text-transparent"> Digital Marketing Guide</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto mb-12">
                Comprehensive coverage of all digital marketing channels with practical, actionable strategies.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-w-4xl mx-auto">
              {guideTopics.map((topic, index) => (
                <div key={index} className="flex items-center space-x-3">
                  <CheckCircle className="w-6 h-6 text-blue-400 flex-shrink-0" />
                  <span className="text-slate-300">{topic}</span>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Chapters Section */}
        <section className="py-20">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Guide Chapters &
                <span className="bg-gradient-to-r from-blue-400 to-blue-600 bg-clip-text text-transparent"> Learning Modules</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto">
                Structured learning modules that take you from beginner to expert in digital marketing.
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              {chapters.map((chapter, index) => (
                <Card key={index} className="bg-slate-900/80 border-blue-500/20 hover:border-blue-500/40 transition-all duration-300">
                  <CardContent className="p-8">
                    <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg flex items-center justify-center mb-6">
                      <chapter.icon className="w-6 h-6 text-white" />
                    </div>
                    <h3 className="text-xl font-bold text-white mb-4">{chapter.title}</h3>
                    <p className="text-slate-300 mb-6">{chapter.description}</p>
                    <ul className="space-y-2">
                      {chapter.topics.map((topic, idx) => (
                        <li key={idx} className="flex items-center text-slate-400 text-sm">
                          <CheckCircle className="w-3 h-3 text-blue-400 mr-2 flex-shrink-0" />
                          {topic}
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Benefits Section */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Why Our Digital Marketing Guide
                <span className="bg-gradient-to-r from-blue-400 to-blue-600 bg-clip-text text-transparent"> Stands Out</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto">
                Based on real-world experience and proven results from international digital marketing campaigns.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-w-4xl mx-auto">
              {benefits.map((benefit, index) => (
                <div key={index} className="flex items-center space-x-3">
                  <CheckCircle className="w-6 h-6 text-blue-400 flex-shrink-0" />
                  <span className="text-slate-300">{benefit}</span>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Expert Credentials Section */}
        <section className="py-20">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Learn from
                <span className="bg-gradient-to-r from-blue-400 to-blue-600 bg-clip-text text-transparent"> International Experts</span>
              </h2>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              <Card className="bg-slate-900/80 border-blue-500/20">
                <CardContent className="p-8">
                  <TrendingUp className="w-12 h-12 text-blue-400 mb-6" />
                  <h3 className="text-xl font-bold text-white mb-4">7 Years Experience</h3>
                  <p className="text-slate-300">International digital marketing experience across multiple industries and markets.</p>
                </CardContent>
              </Card>

              <Card className="bg-slate-900/80 border-blue-500/20">
                <CardContent className="p-8">
                  <Users className="w-12 h-12 text-blue-400 mb-6" />
                  <h3 className="text-xl font-bold text-white mb-4">6 Countries Served</h3>
                  <p className="text-slate-300">Proven strategies that work across different markets: UK, US, Dubai, India, Kuwait, South Africa.</p>
                </CardContent>
              </Card>

              <Card className="bg-slate-900/80 border-blue-500/20">
                <CardContent className="p-8">
                  <CheckCircle className="w-12 h-12 text-blue-400 mb-6" />
                  <h3 className="text-xl font-bold text-white mb-4">₹10Cr+ Revenue</h3>
                  <p className="text-slate-300">Generated over ₹10 crores in revenue for clients through strategic digital marketing campaigns.</p>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 bg-gradient-to-r from-blue-600 to-blue-500">
          <div className="container mx-auto px-6 text-center">
            <h2 className="text-4xl md:text-5xl font-bold mb-6">Ready to Master Digital Marketing?</h2>
            <p className="text-xl mb-8 max-w-3xl mx-auto">
              Get access to our comprehensive digital marketing guide and learn the strategies that have generated ₹10Cr+ revenue across 6 countries.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button asChild size="lg" className="bg-white text-blue-600 hover:bg-slate-100 text-lg px-8 py-4">
                <Link to="/contact">Download Complete Guide</Link>
              </Button>
              <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-blue-600 text-lg px-8 py-4">
                <Link to="/quote">Get Personal Consultation</Link>
              </Button>
            </div>
          </div>
        </section>
      </main>
      
      <Footer />
    </div>
  );
};

export default DigitalMarketingGuide;
