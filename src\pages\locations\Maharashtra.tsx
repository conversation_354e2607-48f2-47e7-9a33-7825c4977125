import React from 'react';
import { Link } from 'react-router-dom';
import { ArrowRight, MapPin, TrendingUp, Users, CheckCircle, Building, Star, Crown } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';

const Maharashtra = () => {
  const services = [
    'SEO Services Maharashtra',
    'Google Ads Management Maharashtra',
    'Social Media Marketing Maharashtra',
    'Local SEO Maharashtra',
    'E-commerce SEO Maharashtra',
    'Content Marketing Maharashtra',
    'Website Development Maharashtra',
    'Digital Marketing Consulting Maharashtra'
  ];

  const industries = [
    'Manufacturing Maharashtra',
    'IT Services Maharashtra',
    'Financial Services Maharashtra',
    'Real Estate Maharashtra',
    'Healthcare Maharashtra',
    'Education Maharashtra',
    'Automotive Maharashtra',
    'Entertainment Maharashtra'
  ];

  const cities = [
    'Mumbai Digital Marketing',
    'Pune SEO Services',
    'Nagpur Digital Marketing',
    'Nashik SEO Services',
    'Aurangabad Digital Marketing',
    'Solapur SEO Services',
    'Thane Digital Marketing',
    'Kolhapur SEO Services',
    'Sangli Digital Marketing',
    'Ahmednagar SEO Services',
    'Akola Digital Marketing',
    'Latur SEO Services'
  ];

  const stats = [
    {
      metric: '800+',
      description: 'Maharashtra Businesses Served',
      detail: 'Across all districts'
    },
    {
      metric: '450%',
      description: 'Average Traffic Increase',
      detail: 'For Maharashtra clients'
    },
    {
      metric: '₹5Cr+',
      description: 'Revenue Generated',
      detail: 'For Maharashtra businesses'
    }
  ];

  return (
    <div className="min-h-screen bg-slate-900 relative">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        {/* Hero Section */}
        <section className="pt-32 pb-20">
          <div className="container mx-auto px-6">
            <div className="max-w-4xl mx-auto text-center">
              <div className="inline-flex items-center space-x-2 bg-orange-500/20 border border-orange-500/30 rounded-full px-6 py-2 mb-8">
                <Crown className="w-4 h-4 text-orange-400" />
                <span className="text-orange-400 font-medium">Maharashtra Digital Marketing • Economic Powerhouse of India</span>
              </div>
              
              <h1 className="text-5xl md:text-6xl font-bold mb-6 leading-tight">
                #1 Digital Marketing Company in
                <span className="bg-gradient-to-r from-orange-400 to-orange-600 bg-clip-text text-transparent"> Maharashtra</span>
              </h1>
              
              <p className="text-xl text-slate-300 mb-12 max-w-3xl mx-auto">
                Leading digital marketing agency in Maharashtra helping businesses dominate online. From Mumbai's financial district to Pune's IT corridor, we've helped 800+ businesses grow with proven SEO, Google Ads, and digital marketing strategies across all Maharashtra districts.
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button asChild size="lg" className="bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white text-lg px-8 py-4">
                  <Link to="/quote">Get Free Maharashtra SEO Audit</Link>
                </Button>
                <Button asChild size="lg" variant="outline" className="border-orange-500 text-orange-400 hover:bg-orange-500 hover:text-white text-lg px-8 py-4">
                  <Link to="/contact">Call Maharashtra Office</Link>
                </Button>
              </div>
            </div>
          </div>
        </section>

        {/* Stats Section */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Maharashtra Digital Marketing
                <span className="bg-gradient-to-r from-orange-400 to-orange-600 bg-clip-text text-transparent"> Success Stories</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto mb-12">
                Real results from Maharashtra businesses across industries - from Mumbai's financial giants to Pune's tech startups across India's richest state.
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
              {stats.map((stat, index) => (
                <div key={index} className="text-center">
                  <div className="text-5xl font-bold text-orange-400 mb-2">{stat.metric}</div>
                  <div className="text-white font-semibold mb-1">{stat.description}</div>
                  <div className="text-slate-400">{stat.detail}</div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Services Section */}
        <section className="py-20">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Digital Marketing Services in
                <span className="bg-gradient-to-r from-orange-400 to-orange-600 bg-clip-text text-transparent"> Maharashtra</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto">
                Comprehensive digital marketing solutions for Maharashtra businesses looking to dominate online across India's economic powerhouse.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {services.map((service, index) => (
                <Card key={index} className="bg-slate-900/80 border-orange-500/20 hover:border-orange-500/40 transition-all duration-300">
                  <CardContent className="p-6 text-center">
                    <Building className="w-8 h-8 text-orange-400 mx-auto mb-4" />
                    <h3 className="text-white font-semibold mb-2">{service}</h3>
                    <Button asChild size="sm" variant="outline" className="border-orange-500 text-orange-400 hover:bg-orange-500 hover:text-white">
                      <Link to="/quote">Learn More</Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Industries Section */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Maharashtra Industries We
                <span className="bg-gradient-to-r from-orange-400 to-orange-600 bg-clip-text text-transparent"> Serve</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto">
                Specialized digital marketing strategies for Maharashtra's diverse industrial landscape and economic sectors.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {industries.map((industry, index) => (
                <Card key={index} className="bg-slate-900/80 border-orange-500/20 hover:border-orange-500/40 transition-all duration-300">
                  <CardContent className="p-6 text-center">
                    <Star className="w-8 h-8 text-orange-400 mx-auto mb-4" />
                    <h3 className="text-white font-semibold mb-2">{industry}</h3>
                    <Button asChild size="sm" variant="outline" className="border-orange-500 text-orange-400 hover:bg-orange-500 hover:text-white">
                      <Link to="/quote">Get Strategy</Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Cities Section */}
        <section className="py-20">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Maharashtra Cities We
                <span className="bg-gradient-to-r from-orange-400 to-orange-600 bg-clip-text text-transparent"> Cover</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto">
                Local digital marketing expertise across all major Maharashtra cities and districts.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {cities.map((city, index) => (
                <Card key={index} className="bg-slate-900/80 border-orange-500/20 hover:border-orange-500/40 transition-all duration-300">
                  <CardContent className="p-6 text-center">
                    <MapPin className="w-8 h-8 text-orange-400 mx-auto mb-4" />
                    <h3 className="text-white font-semibold mb-2">{city}</h3>
                    <Button asChild size="sm" variant="outline" className="border-orange-500 text-orange-400 hover:bg-orange-500 hover:text-white">
                      <Link to="/contact">Contact Us</Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Why Choose Us Section */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Why Choose GOD Digital Marketing in
                <span className="bg-gradient-to-r from-orange-400 to-orange-600 bg-clip-text text-transparent"> Maharashtra?</span>
              </h2>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              <Card className="bg-slate-900/80 border-orange-500/20">
                <CardContent className="p-8">
                  <TrendingUp className="w-12 h-12 text-orange-400 mb-6" />
                  <h3 className="text-xl font-bold text-white mb-4">Maharashtra Market Leadership</h3>
                  <p className="text-slate-300">Deep understanding of Maharashtra's diverse economy from Mumbai's financial sector to Pune's IT industry across all districts.</p>
                </CardContent>
              </Card>

              <Card className="bg-slate-900/80 border-orange-500/20">
                <CardContent className="p-8">
                  <Users className="w-12 h-12 text-orange-400 mb-6" />
                  <h3 className="text-xl font-bold text-white mb-4">Proven Maharashtra Results</h3>
                  <p className="text-slate-300">800+ successful Maharashtra campaigns with measurable ROI improvements and business growth across all major cities and industries.</p>
                </CardContent>
              </Card>

              <Card className="bg-slate-900/80 border-orange-500/20">
                <CardContent className="p-8">
                  <CheckCircle className="w-12 h-12 text-orange-400 mb-6" />
                  <h3 className="text-xl font-bold text-white mb-4">Economic Powerhouse Focus</h3>
                  <p className="text-slate-300">Specialized expertise in marketing for India's richest state with understanding of diverse business cultures and market dynamics.</p>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 bg-gradient-to-r from-orange-600 to-orange-500">
          <div className="container mx-auto px-6 text-center">
            <h2 className="text-4xl md:text-5xl font-bold mb-6">Ready to Dominate Maharashtra's Digital Market?</h2>
            <p className="text-xl mb-8 max-w-3xl mx-auto">
              Join 800+ Maharashtra businesses that trust GOD Digital Marketing for their online growth. From Mumbai to Pune, we deliver results across India's economic powerhouse.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button asChild size="lg" className="bg-white text-orange-600 hover:bg-slate-100 text-lg px-8 py-4">
                <Link to="/quote">Get Free Maharashtra SEO Audit</Link>
              </Button>
              <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-orange-600 text-lg px-8 py-4">
                <Link to="/contact">Call Maharashtra Office Now</Link>
              </Button>
            </div>
          </div>
        </section>
      </main>
      
      <Footer />
    </div>
  );
};

export default Maharashtra;
