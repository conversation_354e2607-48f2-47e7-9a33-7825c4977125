
import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { ArrowRight, Search, TrendingUp, Target, BarChart3, CheckCircle, Star, Globe, Bot, Award } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';

const SeoServices = () => {
  const features = [
    'Multi-Country SEO Campaigns (UK, US, Dubai, India, Kuwait, South Africa)',
    'AI-Powered Content Creation at Scale',
    'International Keyword Research & Market Analysis',
    'Cross-Continental Ranking Strategies',
    'Advanced SEO Tools Mastery',
    'Multi-Location Business Optimization',
    'Export Business SEO Strategies',
    'Real Estate Lead Generation Systems',
    'Different Search Behavior Analysis Across Markets',
    'Premium Monthly Reporting & Strategy Calls'
  ];

  const packages = [
    {
      name: 'Local Authority Builder',
      price: '₹40,000',
      period: '/month',
      description: 'Perfect for businesses establishing local market dominance',
      features: [
        'Local SEO Optimization',
        'Google My Business Management',
        'Review Generation System',
        'Local Keyword Research',
        'Citation Building',
        'Monthly Performance Reports'
      ]
    },
    {
      name: 'Lead Generation System',
      price: '₹80,000',
      period: '/month',
      description: 'Complete lead generation system with SEO + Ads + conversion optimization',
      features: [
        'Everything in Local Authority',
        'Google Ads Management',
        'Facebook Ads Campaigns',
        'Landing Page Optimization',
        'CRM Integration',
        'Lead Nurturing Automation',
        'Conversion Rate Optimization'
      ],
      popular: true
    },
    {
      name: 'International Market Domination',
      price: '₹120,000',
      period: '/month',
      description: 'Premium multi-country SEO with AI content and social automation',
      features: [
        'Everything in Lead Generation',
        'Multi-Country SEO Campaigns',
        'AI-Powered Content Creation',
        'Social Media Automation',
        'International Keyword Research',
        'Cross-Continental Link Building',
        'Weekly Strategy Calls',
        'Dedicated Account Manager'
      ]
    }
  ];

  const results = [
    {
      metric: '10M+',
      description: 'Monthly views generated',
      timeframe: 'from zero traffic'
    },
    {
      metric: '6',
      description: 'Countries dominated',
      timeframe: 'UK, US, Dubai, India, Kuwait, SA'
    },
    {
      metric: '7',
      description: 'Years international experience',
      timeframe: 'across diverse industries'
    }
  ];

  const caseStudies = [
    {
      company: 'Easy Outdoor (UK)',
      industry: 'Car Ovens & Opus Campers',
      result: 'Dominated UK search results for car oven and camping equipment keywords',
      traffic: 'Zero to thousands monthly',
      icon: Target
    },
    {
      company: 'Bulkland (US)',
      industry: 'Logistics & Bulk Services',
      result: 'Achieved first-page rankings across multiple US states',
      traffic: 'Massive traffic increase',
      icon: TrendingUp
    },
    {
      company: 'Shakespeare Flowers (Dubai)',
      industry: 'Flower Delivery',
      result: 'Dominated Dubai flower delivery market with local SEO',
      traffic: 'Market leader positioning',
      icon: Award
    }
  ];

  return (
    <div className="min-h-screen bg-slate-900 text-white relative overflow-hidden">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        {/* Hero Section */}
        <section className="pt-32 pb-20">
          <div className="container mx-auto px-6">
            <div className="max-w-4xl mx-auto text-center">
              <div className="inline-flex items-center space-x-2 bg-amber-500/20 border border-amber-500/30 rounded-full px-6 py-2 mb-8">
                <Globe className="w-4 h-4 text-amber-400" />
                <span className="text-amber-400 font-medium">Professional SEO Services • 7 Years • 6 Countries</span>
              </div>

              <h1 className="text-5xl md:text-6xl font-bold mb-6 leading-tight">
                Professional SEO Services That
                <span className="bg-gradient-to-r from-amber-400 to-amber-600 bg-clip-text text-transparent"> Drive Organic Growth</span>
              </h1>

              <p className="text-xl text-slate-300 mb-12 max-w-3xl mx-auto">
                Expert SEO services delivering comprehensive search engine optimization solutions. Our SEO company provides professional SEO services including on-page SEO, off-page SEO, technical SEO, local SEO, and enterprise SEO solutions. Proven SEO strategies generating 10+ million monthly views across UK, US, Dubai, India, Kuwait & South Africa. Complete SEO services for businesses seeking organic search visibility, keyword rankings, and sustainable online growth.
              </p>

              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button asChild size="lg" className="bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700 text-white text-lg px-8 py-4">
                  <Link to="/quote">Get Professional SEO Strategy</Link>
                </Button>
                <Button asChild size="lg" variant="outline" className="border-amber-500 text-amber-400 hover:bg-amber-500 hover:text-white text-lg px-8 py-4">
                  <Link to="/case-studies">View Global Results</Link>
                </Button>
              </div>
            </div>
          </div>
        </section>

        {/* Results Section */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Proven International
                <span className="bg-gradient-to-r from-amber-400 to-amber-600 bg-clip-text text-transparent"> SEO Results</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto mb-12">
                7 years of building digital empires across 6 countries. Real results from real businesses.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
              {results.map((result, index) => (
                <div key={index} className="text-center">
                  <div className="text-5xl font-bold text-amber-400 mb-2">{result.metric}</div>
                  <div className="text-white font-semibold mb-1">{result.description}</div>
                  <div className="text-slate-400">{result.timeframe}</div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Case Studies Section */}
        <section className="py-20">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                International Client
                <span className="bg-gradient-to-r from-amber-400 to-amber-600 bg-clip-text text-transparent"> Success Stories</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto">
                Real businesses, real results across multiple countries and industries.
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-20">
              {caseStudies.map((study, index) => (
                <Card key={index} className="bg-slate-900/80 border-amber-500/20 hover:border-amber-500/40 transition-all duration-300">
                  <CardContent className="p-8">
                    <div className="w-12 h-12 bg-gradient-to-r from-amber-500 to-amber-600 rounded-lg flex items-center justify-center mb-6">
                      <study.icon className="w-6 h-6 text-white" />
                    </div>
                    <h3 className="text-xl font-bold text-white mb-2">{study.company}</h3>
                    <p className="text-amber-400 text-sm mb-4">{study.industry}</p>
                    <p className="text-slate-300 mb-4">{study.result}</p>
                    <div className="text-amber-400 font-semibold">{study.traffic}</div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Features Section */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                International SEO
                <span className="bg-gradient-to-r from-amber-400 to-amber-600 bg-clip-text text-transparent"> Expertise</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto">
                Unlike local agencies, we understand how search works across different countries, cultures, and markets.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-w-4xl mx-auto">
              {features.map((feature, index) => (
                <div key={index} className="flex items-center space-x-3">
                  <CheckCircle className="w-6 h-6 text-amber-400 flex-shrink-0" />
                  <span className="text-slate-300">{feature}</span>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Pricing Section */}
        <section className="py-20">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Premium SEO Investment
                <span className="bg-gradient-to-r from-amber-400 to-amber-600 bg-clip-text text-transparent"> Packages</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto">
                Serious businesses invest seriously in international market dominance. Choose your level of commitment.
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
              {packages.map((pkg, index) => (
                <Card key={index} className={`bg-slate-900/80 border-amber-500/20 hover:border-amber-500/40 transition-all duration-300 relative ${pkg.popular ? 'ring-2 ring-amber-500' : ''}`}>
                  {pkg.popular && (
                    <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                      <span className="bg-amber-500 text-white px-4 py-2 rounded-full text-sm font-semibold">Most Popular</span>
                    </div>
                  )}
                  <CardContent className="p-8">
                    <h3 className="text-2xl font-bold text-white mb-2">{pkg.name}</h3>
                    <p className="text-slate-300 mb-6">{pkg.description}</p>
                    <div className="mb-6">
                      <span className="text-4xl font-bold text-amber-400">{pkg.price}</span>
                      <span className="text-slate-400">{pkg.period}</span>
                    </div>
                    <ul className="space-y-3 mb-8">
                      {pkg.features.map((feature, idx) => (
                        <li key={idx} className="flex items-center text-slate-300">
                          <CheckCircle className="w-4 h-4 text-amber-400 mr-3 flex-shrink-0" />
                          {feature}
                        </li>
                      ))}
                    </ul>
                    <Button asChild className={`w-full ${pkg.popular ? 'bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700' : 'bg-slate-700 hover:bg-slate-600'} text-white`}>
                      <Link to="/quote">Get Started</Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Process Section */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                International SEO
                <span className="bg-gradient-to-r from-amber-400 to-amber-600 bg-clip-text text-transparent"> Process</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto">
                Proven methodology that has dominated search results across 6 countries.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {[
                { step: '01', title: 'International Market Analysis', icon: Globe, desc: 'Deep dive into target countries, search behaviors, and competition' },
                { step: '02', title: 'Multi-Country Strategy', icon: Target, desc: 'Custom SEO strategy for each target market and region' },
                { step: '03', title: 'AI-Powered Implementation', icon: Bot, desc: 'Scale content and optimization using advanced AI tools' },
                { step: '04', title: 'Global Performance Tracking', icon: BarChart3, desc: 'Monitor rankings and traffic across all target markets' }
              ].map((item, index) => (
                <div key={index} className="text-center group">
                  <div className="w-20 h-20 bg-gradient-to-r from-amber-500 to-amber-600 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform">
                    <item.icon className="w-8 h-8 text-white" />
                  </div>
                  <div className="text-2xl font-bold text-amber-400 mb-2">{item.step}</div>
                  <h3 className="text-xl font-bold text-white mb-4">{item.title}</h3>
                  <p className="text-slate-300 text-sm">{item.desc}</p>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 bg-gradient-to-r from-amber-600 to-amber-500">
          <div className="container mx-auto px-6 text-center">
            <h2 className="text-4xl md:text-5xl font-bold mb-6">Ready to Dominate Global Markets?</h2>
            <p className="text-xl mb-8 max-w-3xl mx-auto">
              Join the elite businesses investing ₹40K-120K monthly for international SEO dominance.
              7 years of proven results across UK, US, Dubai, India, Kuwait & South Africa.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button asChild size="lg" className="bg-white text-amber-600 hover:bg-slate-100 text-lg px-8 py-4">
                <Link to="/quote">Get International SEO Strategy</Link>
              </Button>
              <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-amber-600 text-lg px-8 py-4">
                <Link to="/contact">Book Premium Consultation</Link>
              </Button>
            </div>
          </div>
        </section>
      </main>
      
      <Footer />
    </div>
  );
};

export default SeoServices;
