import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { ShoppingCart, Search, TrendingUp, Users, CheckCircle, Building, Star, Crown, Target, Zap, ArrowRight } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';
import Sidebar from '@/components/Sidebar';
import { Helmet } from 'react-helmet-async';

const EcommerceSeoServices = () => {
  // Advanced SEO Optimization Data - Based on Competitor Analysis
  const primaryKeyword = "ecommerce SEO services";
  const secondaryKeywords = [
    "Shopify SEO services",
    "WooCommerce SEO optimization",
    "online store SEO",
    "product page SEO",
    "ecommerce website optimization",
    "marketplace SEO services"
  ];

  const lsiKeywords = [
    "online store optimization",
    "product page optimization",
    "category page SEO",
    "ecommerce site structure",
    "shopping feed optimization",
    "product schema markup",
    "faceted navigation SEO",
    "duplicate content resolution",
    "mobile ecommerce SEO",
    "conversion rate optimization",
    "ecommerce link building",
    "product reviews SEO",
    "shopping cart optimization",
    "checkout page optimization",
    "ecommerce analytics"
  ];

  const entities = [
    "Shopify",
    "WooCommerce",
    "Magento",
    "BigCommerce",
    "Amazon",
    "Google Shopping",
    "Facebook Shop",
    "Instagram Shopping",
    "eBay",
    "Etsy"
  ];

  // Generate comprehensive keywords for meta tags
  const allKeywords = [
    primaryKeyword,
    ...secondaryKeywords,
    ...lsiKeywords,
    "best ecommerce SEO company India",
    "professional online store SEO",
    "ecommerce SEO experts",
    "product page optimization services",
    "shopping website SEO"
  ].join(", ");

  // Latest 2025 E-commerce SEO Facts
  const latest2025Facts = [
    "E-commerce sales expected to reach $8.1 trillion globally by 2025",
    "Mobile commerce accounts for 72.9% of e-commerce sales in 2025",
    "Voice commerce projected to reach $40 billion by 2025",
    "AI-powered product recommendations increase conversions by 35%",
    "Visual search drives 62% of millennial shopping decisions"
  ];

  const stats = [
    {
      metric: '2,400+',
      description: 'E-commerce Stores Optimized',
      detail: 'Across all platforms'
    },
    {
      metric: '4,800%',
      description: 'Average Sales Growth',
      detail: 'For e-commerce clients'
    },
    {
      metric: '₹1,250Cr+',
      description: 'E-commerce Revenue Generated',
      detail: 'Through SEO campaigns'
    },
    {
      metric: '96%',
      description: 'Product Keywords Ranking',
      detail: 'Top 10 positions achieved'
    }
  ];

  const achievements = [
    'Top E-commerce SEO Company in India',
    'Shopify SEO Specialists',
    'WooCommerce SEO Experts',
    'Magento SEO Leaders',
    'Amazon SEO Champions',
    'Marketplace SEO Pioneers'
  ];

  const ecommerceSpecializations = [
    {
      type: 'Product Page SEO',
      description: 'Advanced optimization for individual product pages and product catalogs',
      icon: Building,
      features: ['Product Title Optimization', 'Product Description SEO', 'Image Alt Text Optimization', 'Schema Markup Implementation', 'Product Review SEO', 'Related Product Optimization']
    },
    {
      type: 'Category & Collection SEO',
      description: 'Strategic optimization for category pages and product collections',
      icon: Star,
      features: ['Category Page SEO', 'Collection Page Optimization', 'Faceted Navigation SEO', 'Filter Page Optimization', 'Breadcrumb SEO', 'Category Description SEO']
    },
    {
      type: 'Technical E-commerce SEO',
      description: 'Comprehensive technical optimization for e-commerce platforms',
      icon: Crown,
      features: ['Site Speed Optimization', 'Mobile E-commerce SEO', 'Core Web Vitals', 'Crawl Budget Optimization', 'Duplicate Content Resolution', 'URL Structure Optimization']
    },
    {
      type: 'Marketplace & Platform SEO',
      description: 'Specialized SEO for various e-commerce platforms and marketplaces',
      icon: Target,
      features: ['Shopify SEO', 'WooCommerce SEO', 'Magento SEO', 'Amazon SEO', 'Flipkart SEO', 'Custom Platform SEO']
    }
  ];

  const ecommercePlatforms = [
    { name: 'Shopify Stores', clients: '680+', growth: '4,900%' },
    { name: 'WooCommerce', clients: '520+', growth: '4,600%' },
    { name: 'Magento', clients: '380+', growth: '4,200%' },
    { name: 'Custom Platforms', clients: '420+', growth: '4,800%' },
    { name: 'Amazon Stores', clients: '280+', growth: '5,200%' },
    { name: 'Multi-Platform', clients: '320+', growth: '4,400%' }
  ];

  const caseStudies = [
    {
      client: 'Leading Fashion E-commerce Store',
      industry: 'Fashion Retail',
      challenge: 'Fashion e-commerce store needed to compete with major online retailers',
      result: '5,200% organic sales increase',
      metrics: ['3,200+ product keywords in top 3', '₹480Cr+ e-commerce revenue', '920% increase in organic traffic']
    },
    {
      client: 'Electronics E-commerce Platform',
      industry: 'Electronics Retail',
      challenge: 'Electronics store needed to improve product visibility and conversion rates',
      result: '4,800% conversion rate improvement',
      metrics: ['2,800+ electronics keywords ranking', '₹385Cr+ electronics revenue', '680% increase in product page views']
    },
    {
      client: 'Multi-Category Marketplace',
      industry: 'Multi-Category E-commerce',
      challenge: 'Marketplace needed to optimize thousands of product pages across categories',
      result: '4,600% marketplace growth',
      metrics: ['4,500+ marketplace keywords in top 5', '₹285Cr+ marketplace revenue', '580% increase in seller registrations']
    }
  ];

  const ecommerceSeoStrategies = [
    {
      strategy: 'Product Discovery SEO',
      description: 'Optimize product pages for maximum search visibility and discovery',
      benefits: ['Product visibility', 'Search discovery', 'Category ranking', 'Brand awareness']
    },
    {
      strategy: 'Conversion-Focused SEO',
      description: 'SEO strategies designed to drive qualified traffic that converts',
      benefits: ['Higher conversion rates', 'Qualified traffic', 'Purchase intent targeting', 'Revenue optimization']
    },
    {
      strategy: 'Technical E-commerce SEO',
      description: 'Advanced technical optimization for e-commerce platform performance',
      benefits: ['Site speed optimization', 'Mobile performance', 'Crawl efficiency', 'User experience']
    },
    {
      strategy: 'Marketplace SEO',
      description: 'Specialized optimization for marketplace platforms and multi-vendor stores',
      benefits: ['Marketplace visibility', 'Vendor optimization', 'Platform compliance', 'Multi-channel presence']
    }
  ];

  return (
    <div className="min-h-screen bg-slate-900 relative">
      {/* Advanced SEO Meta Tags - Competitor Analysis Optimized */}
      <Helmet>
        {/* Primary Meta Tags - Target Keywords Integration */}
        <title>Best E-commerce SEO Services India | Shopify WooCommerce SEO | GOD Digital Marketing</title>
        <meta name="description" content="#1 E-commerce SEO services India. Expert Shopify SEO, WooCommerce optimization, product page SEO. 2,400+ online stores optimized, 4,800% sales growth, ₹1,250Cr+ revenue. Professional ecommerce website optimization by GOD Digital Marketing." />
        <meta name="keywords" content={allKeywords} />
        <link rel="canonical" href="https://goddigitalmarketing.com/services/seo/ecommerce" />

        {/* Advanced SEO Meta Tags */}
        <meta name="author" content="Nitin Tyagi" />
        <meta name="robots" content="index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1" />
        <meta name="googlebot" content="index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1" />

        {/* Open Graph Meta Tags */}
        <meta property="og:title" content="Best E-commerce SEO Services India | Shopify WooCommerce SEO" />
        <meta property="og:description" content="#1 E-commerce SEO services India. Expert Shopify SEO, WooCommerce optimization, product page SEO. 2,400+ online stores optimized, 4,800% sales growth." />
        <meta property="og:type" content="service" />
        <meta property="og:url" content="https://goddigitalmarketing.com/services/seo/ecommerce" />
        <meta property="og:image" content="https://goddigitalmarketing.com/ecommerce-seo-services.jpg" />

        {/* Schema.org Structured Data */}
        <script type="application/ld+json">
          {JSON.stringify({
            "@context": "https://schema.org",
            "@type": "Service",
            "name": "E-commerce SEO Services",
            "description": "#1 E-commerce SEO services India. Expert Shopify SEO, WooCommerce optimization, product page SEO.",
            "provider": {
              "@type": "Organization",
              "name": "GOD Digital Marketing",
              "founder": {
                "@type": "Person",
                "name": "Nitin Tyagi"
              }
            },
            "areaServed": "India",
            "hasOfferCatalog": {
              "@type": "OfferCatalog",
              "name": "E-commerce SEO Services",
              "itemListElement": [
                {
                  "@type": "Offer",
                  "itemOffered": {
                    "@type": "Service",
                    "name": "Shopify SEO Services"
                  }
                },
                {
                  "@type": "Offer",
                  "itemOffered": {
                    "@type": "Service",
                    "name": "WooCommerce SEO Optimization"
                  }
                },
                {
                  "@type": "Offer",
                  "itemOffered": {
                    "@type": "Service",
                    "name": "Product Page SEO"
                  }
                }
              ]
            },
            "aggregateRating": {
              "@type": "AggregateRating",
              "ratingValue": "4.9",
              "reviewCount": "2400+"
            }
          })}
        </script>

        {/* Breadcrumb Schema */}
        <script type="application/ld+json">
          {JSON.stringify({
            "@context": "https://schema.org",
            "@type": "BreadcrumbList",
            "itemListElement": [
              {
                "@type": "ListItem",
                "position": 1,
                "name": "Home",
                "item": "https://goddigitalmarketing.com"
              },
              {
                "@type": "ListItem",
                "position": 2,
                "name": "SEO Services",
                "item": "https://goddigitalmarketing.com/services/seo"
              },
              {
                "@type": "ListItem",
                "position": 3,
                "name": "E-commerce SEO Services",
                "item": "https://goddigitalmarketing.com/services/seo/ecommerce"
              }
            ]
          })}
        </script>
      </Helmet>

      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        <div className="container mx-auto px-6 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            <div className="lg:col-span-3">
              <section className="pt-24 pb-16">
                <div className="max-w-4xl">
                  <div className="inline-flex items-center space-x-2 bg-green-500/20 border border-green-500/30 rounded-full px-6 py-2 mb-8">
                    <ShoppingCart className="w-4 h-4 text-green-400" />
                    <span className="text-green-400 font-medium">E-commerce SEO • Online Store Optimization Excellence</span>
                  </div>
                  
                  <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
                    Best E-commerce SEO Services India | Shopify WooCommerce
                    <span className="bg-gradient-to-r from-green-400 to-green-600 bg-clip-text text-transparent"> SEO Experts</span>
                  </h1>
                  
                  <p className="text-xl text-slate-300 mb-8">
                    Premier e-commerce SEO services by GOD Digital Marketing offering comprehensive search engine optimization solutions for online stores, marketplaces, and e-commerce platforms. Our ecommerce website optimization includes expert Shopify SEO services, WooCommerce SEO optimization, product page optimization, category page SEO, and advanced product schema markup implementation. With 7+ years international SEO experience, we've optimized 2,400+ online stores achieving 4,800% average sales growth and ₹1,250Cr+ revenue generated. Professional ecommerce SEO strategies covering shopping feed optimization, faceted navigation SEO, mobile ecommerce optimization, and conversion rate optimization. Latest 2025 insight: E-commerce sales expected to reach $8.1 trillion globally, with mobile commerce accounting for 72.9% of sales.
                  </p>
                  
                  <div className="flex flex-col sm:flex-row gap-4 mb-8">
                    <Button asChild size="lg" className="bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white text-lg px-8 py-4">
                      <Link to="/quote">Get Free E-commerce SEO Audit</Link>
                    </Button>
                    <Button asChild size="lg" variant="outline" className="border-green-500 text-green-400 hover:bg-green-500 hover:text-white text-lg px-8 py-4">
                      <Link to="/contact">Call E-commerce SEO Experts: +91-**********</Link>
                    </Button>
                  </div>

                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                    {achievements.map((achievement, index) => (
                      <div key={index} className="flex items-center justify-center space-x-2 bg-slate-800/50 rounded-lg p-3">
                        <CheckCircle className="w-4 h-4 text-green-400 flex-shrink-0" />
                        <span className="text-slate-300">{achievement}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </section>

              <section className="mb-16 bg-slate-800/50 rounded-2xl p-8">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    E-commerce SEO Services
                    <span className="bg-gradient-to-r from-green-400 to-green-600 bg-clip-text text-transparent"> Success Metrics</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Measurable e-commerce SEO results from online stores across all platforms and categories.
                  </p>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
                  {stats.map((stat, index) => (
                    <div key={index} className="text-center">
                      <div className="text-4xl font-bold text-green-400 mb-2">{stat.metric}</div>
                      <div className="text-white font-semibold mb-1">{stat.description}</div>
                      <div className="text-slate-400">{stat.detail}</div>
                    </div>
                  ))}
                </div>
              </section>

              <section className="mb-16">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    E-commerce SEO Strategies We
                    <span className="bg-gradient-to-r from-green-400 to-green-600 bg-clip-text text-transparent"> Master</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Specialized SEO strategies designed for e-commerce success across all platforms and product categories.
                  </p>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  {ecommerceSpecializations.map((spec, index) => (
                    <Card key={index} className="bg-slate-900/80 border-green-500/20 hover:border-green-500/40 transition-all duration-300">
                      <CardContent className="p-8">
                        <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-green-600 rounded-lg flex items-center justify-center mb-6">
                          <spec.icon className="w-6 h-6 text-white" />
                        </div>
                        <h3 className="text-xl font-bold text-white mb-4">{spec.type}</h3>
                        <p className="text-slate-300 mb-6">{spec.description}</p>
                        <ul className="space-y-2">
                          {spec.features.map((feature, idx) => (
                            <li key={idx} className="flex items-center text-slate-400 text-sm">
                              <CheckCircle className="w-3 h-3 text-green-400 mr-2 flex-shrink-0" />
                              {feature}
                            </li>
                          ))}
                        </ul>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>

              {/* E-commerce Platforms */}
              <section className="mb-16 bg-slate-800/30 rounded-2xl p-8">
                <h3 className="text-2xl font-bold text-white mb-6 text-center">
                  E-commerce Platform Expertise
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-6">
                  {ecommercePlatforms.map((platform, index) => (
                    <div key={index} className="text-center bg-slate-900/50 rounded-lg p-4">
                      <h4 className="text-green-400 font-semibold mb-2">{platform.name}</h4>
                      <div className="text-2xl font-bold text-white mb-1">{platform.clients}</div>
                      <div className="text-slate-400 text-sm mb-2">Stores Optimized</div>
                      <div className="text-green-400 font-semibold text-sm">{platform.growth} avg growth</div>
                    </div>
                  ))}
                </div>
              </section>

              {/* E-commerce SEO Strategies */}
              <section className="mb-16 bg-slate-800/30 rounded-2xl p-8">
                <h3 className="text-2xl font-bold text-white mb-6 text-center">
                  Advanced E-commerce SEO Strategies
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                  {ecommerceSeoStrategies.map((strategy, index) => (
                    <div key={index} className="bg-slate-900/50 rounded-lg p-6">
                      <h4 className="text-green-400 font-semibold mb-3">{strategy.strategy}</h4>
                      <p className="text-slate-300 text-sm mb-4">{strategy.description}</p>
                      <ul className="space-y-1">
                        {strategy.benefits.map((benefit, idx) => (
                          <li key={idx} className="text-slate-400 text-xs flex items-center">
                            <CheckCircle className="w-3 h-3 text-green-400 mr-2 flex-shrink-0" />
                            {benefit}
                          </li>
                        ))}
                      </ul>
                    </div>
                  ))}
                </div>
              </section>

              {/* Case Studies */}
              <section className="mb-16">
                <h3 className="text-2xl font-bold text-white mb-8 text-center">
                  E-commerce SEO Success Stories
                </h3>
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                  {caseStudies.map((study, index) => (
                    <Card key={index} className="bg-slate-900/60 border-green-500/20 hover:border-green-500/40 transition-all duration-300">
                      <CardContent className="p-6">
                        <h4 className="text-green-400 font-semibold mb-2">{study.client}</h4>
                        <p className="text-slate-400 text-sm mb-3">{study.industry}</p>
                        <p className="text-slate-300 text-sm mb-4">{study.challenge}</p>
                        <div className="bg-slate-800/50 rounded p-3 mb-4">
                          <div className="text-green-400 font-semibold mb-2">{study.result}</div>
                          <ul className="space-y-1">
                            {study.metrics.map((metric, idx) => (
                              <li key={idx} className="text-slate-400 text-xs flex items-center">
                                <CheckCircle className="w-3 h-3 text-green-400 mr-2 flex-shrink-0" />
                                {metric}
                              </li>
                            ))}
                          </ul>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>

              {/* E-E-A-T Authority & Latest 2025 Facts Section */}
              <section className="mb-16 bg-slate-800/20 border-l-4 border-green-500 p-8">
                <h3 className="text-2xl font-bold text-white mb-6">
                  Latest 2025 E-commerce SEO Insights & Expertise
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                  <div>
                    <h4 className="text-lg font-semibold text-green-400 mb-4">2025 E-commerce Trends</h4>
                    <ul className="space-y-3">
                      {latest2025Facts.map((fact, index) => (
                        <li key={index} className="text-slate-300 text-sm flex items-start">
                          <CheckCircle className="w-4 h-4 text-green-400 mr-2 mt-0.5 flex-shrink-0" />
                          {fact}
                        </li>
                      ))}
                    </ul>
                  </div>
                  <div>
                    <h4 className="text-lg font-semibold text-green-400 mb-4">Our E-commerce SEO Expertise</h4>
                    <ul className="space-y-3">
                      <li className="text-slate-300 text-sm flex items-start">
                        <CheckCircle className="w-4 h-4 text-green-400 mr-2 mt-0.5 flex-shrink-0" />
                        7+ years international e-commerce SEO experience
                      </li>
                      <li className="text-slate-300 text-sm flex items-start">
                        <CheckCircle className="w-4 h-4 text-green-400 mr-2 mt-0.5 flex-shrink-0" />
                        Google certified e-commerce optimization specialist
                      </li>
                      <li className="text-slate-300 text-sm flex items-start">
                        <CheckCircle className="w-4 h-4 text-green-400 mr-2 mt-0.5 flex-shrink-0" />
                        Advanced Shopify and WooCommerce SEO certifications
                      </li>
                      <li className="text-slate-300 text-sm flex items-start">
                        <CheckCircle className="w-4 h-4 text-green-400 mr-2 mt-0.5 flex-shrink-0" />
                        2,400+ successful e-commerce store optimizations
                      </li>
                      <li className="text-slate-300 text-sm flex items-start">
                        <CheckCircle className="w-4 h-4 text-green-400 mr-2 mt-0.5 flex-shrink-0" />
                        Proven ₹1,250Cr+ revenue generation track record
                      </li>
                    </ul>
                  </div>
                </div>
              </section>

              {/* Internal Linking Section */}
              <section className="mb-16 bg-slate-800/30 rounded-2xl p-8">
                <h3 className="text-2xl font-bold text-white mb-6 text-center">
                  Complete E-commerce Digital Marketing Solutions
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  <Link to="/services/ppc" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-green-400 font-semibold mb-2 group-hover:text-green-300">E-commerce Google Ads</h4>
                    <p className="text-slate-400 text-sm">PPC advertising for online stores and e-commerce platforms</p>
                  </Link>
                  <Link to="/services/social-media" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-green-400 font-semibold mb-2 group-hover:text-green-300">E-commerce Social Media</h4>
                    <p className="text-slate-400 text-sm">Social media marketing for online retail brands</p>
                  </Link>
                  <Link to="/services/content" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-green-400 font-semibold mb-2 group-hover:text-green-300">E-commerce Content Marketing</h4>
                    <p className="text-slate-400 text-sm">Product content creation and e-commerce marketing</p>
                  </Link>
                  <Link to="/services/email" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-green-400 font-semibold mb-2 group-hover:text-green-300">E-commerce Email Marketing</h4>
                    <p className="text-slate-400 text-sm">Customer retention and e-commerce promotion campaigns</p>
                  </Link>
                  <Link to="/services/web-development" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-green-400 font-semibold mb-2 group-hover:text-green-300">E-commerce Website Development</h4>
                    <p className="text-slate-400 text-sm">Online store development and e-commerce platform optimization</p>
                  </Link>
                  <Link to="/portfolio" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-green-400 font-semibold mb-2 group-hover:text-green-300">E-commerce Success Stories</h4>
                    <p className="text-slate-400 text-sm">See proven results from our e-commerce clients</p>
                  </Link>
                </div>
              </section>

              <section className="bg-gradient-to-r from-green-600 to-green-500 rounded-2xl p-8 text-center">
                <h2 className="text-3xl md:text-5xl font-bold mb-6">Ready to Dominate E-commerce Search?</h2>
                <p className="text-xl mb-8">
                  Join 2,400+ e-commerce stores that trust GOD Digital Marketing for online retail SEO success. Proven strategies that deliver 4,800% sales growth and ₹1,250Cr+ revenue generation.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button asChild size="lg" className="bg-white text-green-600 hover:bg-slate-100 text-lg px-8 py-4">
                    <Link to="/quote">Get Free E-commerce SEO Audit</Link>
                  </Button>
                  <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-green-600 text-lg px-8 py-4">
                    <Link to="/contact">Call E-commerce SEO Experts: +91-**********</Link>
                  </Button>
                </div>
              </section>
            </div>

            <div className="lg:col-span-1">
              <Sidebar currentService="seo" />
            </div>
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
};

export default EcommerceSeoServices;
