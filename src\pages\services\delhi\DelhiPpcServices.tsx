
import React from 'react';
import { Link } from 'react-router-dom';
import { Target, TrendingUp, Users, CheckCircle, MapPin, Star, Award } from 'lucide-react';
import { Button } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';

const DelhiPpcServices = () => {
  return (
    <div className="min-h-screen bg-slate-900 text-white relative overflow-hidden">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        {/* Hero Section */}
        <section className="pt-32 pb-20">
          <div className="container mx-auto px-6">
            <div className="max-w-4xl mx-auto text-center">
              <div className="inline-flex items-center space-x-2 bg-amber-500/20 border border-amber-500/30 rounded-full px-6 py-2 mb-8">
                <MapPin className="w-4 h-4 text-amber-400" />
                <span className="text-amber-400 font-medium">Delhi PPC Services</span>
              </div>
              
              <h1 className="text-5xl md:text-6xl font-bold mb-6 leading-tight">
                #1 PPC Agency in 
                <span className="bg-gradient-to-r from-amber-400 to-amber-600 bg-clip-text text-transparent"> Delhi NCR</span>
              </h1>
              
              <p className="text-xl text-slate-300 mb-12 max-w-3xl mx-auto">
                Drive instant traffic and maximize ROI with Delhi's most trusted PPC management company. 
                Serving 500+ businesses across Connaught Place, Gurgaon, Noida, and South Delhi.
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
                <Button asChild size="lg" className="bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700 text-white text-lg px-8 py-4">
                  <Link to="/quote">Get Free PPC Audit</Link>
                </Button>
                <Button asChild size="lg" variant="outline" className="border-amber-500 text-amber-400 hover:bg-amber-500 hover:text-white text-lg px-8 py-4">
                  <Link to="/case-studies">View Delhi Success Stories</Link>
                </Button>
              </div>

              {/* Trust Indicators */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-16">
                <div className="text-center">
                  <div className="text-3xl font-bold text-amber-400 mb-2">500+</div>
                  <div className="text-slate-300">Delhi Businesses Served</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-amber-400 mb-2">₹50L+</div>
                  <div className="text-slate-300">Ad Spend Managed</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-amber-400 mb-2">350%</div>
                  <div className="text-slate-300">Average ROAS</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-amber-400 mb-2">98%</div>
                  <div className="text-slate-300">Client Retention</div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Why Choose Us for Delhi Market */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="max-w-6xl mx-auto">
              <h2 className="text-4xl font-bold text-center mb-6">Why Delhi Businesses Choose GOD Digital Marketing</h2>
              <p className="text-xl text-slate-300 text-center mb-16 max-w-3xl mx-auto">
                Deep understanding of Delhi's competitive market, local consumer behavior, and business landscape gives us the edge.
              </p>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div className="bg-slate-700/30 border border-amber-500/20 rounded-xl p-6">
                  <Award className="w-12 h-12 text-amber-400 mb-4" />
                  <h3 className="text-xl font-semibold mb-3">Delhi Market Expertise</h3>
                  <p className="text-slate-300">5+ years managing PPC campaigns for Delhi NCR businesses across Connaught Place, Karol Bagh, and Greater Noida.</p>
                </div>
                
                <div className="bg-slate-700/30 border border-amber-500/20 rounded-xl p-6">
                  <Target className="w-12 h-12 text-amber-400 mb-4" />
                  <h3 className="text-xl font-semibold mb-3">Local Targeting Mastery</h3>
                  <p className="text-slate-300">Precision targeting based on Delhi's unique demographics, traffic patterns, and seasonal business cycles.</p>
                </div>
                
                <div className="bg-slate-700/30 border border-amber-500/20 rounded-xl p-6">
                  <TrendingUp className="w-12 h-12 text-amber-400 mb-4" />
                  <h3 className="text-xl font-semibold mb-3">Proven Delhi Results</h3>
                  <p className="text-slate-300">Average 350% ROAS for Delhi clients with industry-leading conversion rates across all sectors.</p>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Services We Offer */}
        <section className="py-20">
          <div className="container mx-auto px-6">
            <div className="max-w-6xl mx-auto">
              <h2 className="text-4xl font-bold text-center mb-16">Comprehensive PPC Services for Delhi Businesses</h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <div className="bg-slate-800/50 border border-amber-500/20 rounded-xl p-6">
                  <h3 className="text-xl font-semibold mb-4 text-amber-400">Google Ads Management</h3>
                  <ul className="space-y-2 text-slate-300">
                    <li className="flex items-center"><CheckCircle className="w-4 h-4 text-green-400 mr-2" />Search Campaign Optimization</li>
                    <li className="flex items-center"><CheckCircle className="w-4 h-4 text-green-400 mr-2" />Display Network Advertising</li>
                    <li className="flex items-center"><CheckCircle className="w-4 h-4 text-green-400 mr-2" />Shopping Campaign Setup</li>
                    <li className="flex items-center"><CheckCircle className="w-4 h-4 text-green-400 mr-2" />YouTube Advertising</li>
                  </ul>
                </div>

                <div className="bg-slate-800/50 border border-amber-500/20 rounded-xl p-6">
                  <h3 className="text-xl font-semibold mb-4 text-amber-400">Facebook & Instagram Ads</h3>
                  <ul className="space-y-2 text-slate-300">
                    <li className="flex items-center"><CheckCircle className="w-4 h-4 text-green-400 mr-2" />Lead Generation Campaigns</li>
                    <li className="flex items-center"><CheckCircle className="w-4 h-4 text-green-400 mr-2" />Brand Awareness Campaigns</li>
                    <li className="flex items-center"><CheckCircle className="w-4 h-4 text-green-400 mr-2" />E-commerce Optimization</li>
                    <li className="flex items-center"><CheckCircle className="w-4 h-4 text-green-400 mr-2" />Retargeting Strategies</li>
                  </ul>
                </div>

                <div className="bg-slate-800/50 border border-amber-500/20 rounded-xl p-6">
                  <h3 className="text-xl font-semibold mb-4 text-amber-400">LinkedIn Advertising</h3>
                  <ul className="space-y-2 text-slate-300">
                    <li className="flex items-center"><CheckCircle className="w-4 h-4 text-green-400 mr-2" />B2B Lead Generation</li>
                    <li className="flex items-center"><CheckCircle className="w-4 h-4 text-green-400 mr-2" />Sponsored Content</li>
                    <li className="flex items-center"><CheckCircle className="w-4 h-4 text-green-400 mr-2" />Message Ads</li>
                    <li className="flex items-center"><CheckCircle className="w-4 h-4 text-green-400 mr-2" />Event Promotion</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Delhi Area Coverage */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="max-w-6xl mx-auto">
              <h2 className="text-4xl font-bold text-center mb-6">Serving All Major Delhi NCR Areas</h2>
              <p className="text-xl text-slate-300 text-center mb-16">
                Local expertise across Delhi's key business districts and residential areas.
              </p>
              
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <Link to="/delhi/connaught-place/ppc-services" className="bg-slate-700/30 border border-amber-500/20 rounded-lg p-4 text-center hover:bg-slate-700/50 transition-colors">
                  <div className="font-semibold text-amber-400">Connaught Place</div>
                  <div className="text-sm text-slate-300">Business Hub</div>
                </Link>
                <Link to="/delhi/gurgaon/ppc-services" className="bg-slate-700/30 border border-amber-500/20 rounded-lg p-4 text-center hover:bg-slate-700/50 transition-colors">
                  <div className="font-semibold text-amber-400">Gurgaon</div>
                  <div className="text-sm text-slate-300">Corporate Center</div>
                </Link>
                <Link to="/delhi/noida/ppc-services" className="bg-slate-700/30 border border-amber-500/20 rounded-lg p-4 text-center hover:bg-slate-700/50 transition-colors">
                  <div className="font-semibold text-amber-400">Noida</div>
                  <div className="text-sm text-slate-300">IT Hub</div>
                </Link>
                <Link to="/delhi/south-delhi/ppc-services" className="bg-slate-700/30 border border-amber-500/20 rounded-lg p-4 text-center hover:bg-slate-700/50 transition-colors">
                  <div className="font-semibold text-amber-400">South Delhi</div>
                  <div className="text-sm text-slate-300">Premium Market</div>
                </Link>
              </div>
            </div>
          </div>
        </section>

        {/* FAQ Section */}
        <section className="py-20">
          <div className="container mx-auto px-6">
            <div className="max-w-4xl mx-auto">
              <h2 className="text-4xl font-bold text-center mb-16">Frequently Asked Questions</h2>
              
              <div className="space-y-8">
                <div className="bg-slate-800/50 border border-amber-500/20 rounded-xl p-6">
                  <h3 className="text-xl font-semibold mb-3 text-amber-400">What makes your PPC services best for Delhi businesses?</h3>
                  <p className="text-slate-300">Our deep understanding of Delhi's market dynamics, local consumer behavior, and 5+ years of experience managing campaigns for 500+ Delhi businesses gives us unmatched expertise in driving results for the Delhi NCR market.</p>
                </div>

                <div className="bg-slate-800/50 border border-amber-500/20 rounded-xl p-6">
                  <h3 className="text-xl font-semibold mb-3 text-amber-400">How do you ensure ROI for Delhi PPC campaigns?</h3>
                  <p className="text-slate-300">We use advanced targeting based on Delhi's geographic nuances, optimize for local search patterns, and continuously refine campaigns based on real-time Delhi market data to maintain our average 350% ROAS.</p>
                </div>

                <div className="bg-slate-800/50 border border-amber-500/20 rounded-xl p-6">
                  <h3 className="text-xl font-semibold mb-3 text-amber-400">Do you provide PPC services across all Delhi NCR areas?</h3>
                  <p className="text-slate-300">Yes, we serve businesses across Delhi NCR including Connaught Place, Gurgaon, Noida, South Delhi, Karol Bagh, Dwarka, Rohini, and all major commercial and residential areas with localized campaign strategies.</p>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 bg-gradient-to-r from-amber-600 to-amber-500">
          <div className="container mx-auto px-6 text-center">
            <h2 className="text-4xl md:text-5xl font-bold mb-6">Ready to Dominate Delhi's Digital Market?</h2>
            <p className="text-xl mb-8 max-w-3xl mx-auto">
              Join 500+ successful Delhi businesses who trust GOD Digital Marketing for their PPC success.
            </p>
            <Button asChild size="lg" className="bg-white text-amber-600 hover:bg-slate-100 text-lg px-8 py-4">
              <Link to="/quote">Get Your Free Delhi PPC Strategy</Link>
            </Button>
          </div>
        </section>
      </main>
      
      <Footer />
    </div>
  );
};

export default DelhiPpcServices;
