import React from 'react';
import { Link } from 'react-router-dom';
import { Home, Search, TrendingUp, Users, CheckCircle, Building, Star, Crown, Target, Zap, ArrowRight } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';
import Sidebar from '@/components/Sidebar';

const RealEstateSeo = () => {
  const stats = [
    {
      metric: '2,500+',
      description: 'Real Estate Businesses Optimized',
      detail: 'Across all property sectors'
    },
    {
      metric: '1,850%',
      description: 'Average Lead Growth',
      detail: 'For real estate clients'
    },
    {
      metric: '₹680Cr+',
      description: 'Property Sales Generated',
      detail: 'Through SEO campaigns'
    },
    {
      metric: '95%',
      description: 'Property Keywords Ranking',
      detail: 'Top 10 positions achieved'
    }
  ];

  const achievements = [
    'Top Real Estate SEO Company in India',
    'Property Marketing Specialists',
    'Real Estate Agent SEO Experts',
    'Property Developer SEO Leaders',
    'Real Estate Portal SEO Champions',
    'Property Investment SEO Pioneers'
  ];

  const realEstateSpecializations = [
    {
      type: 'Real Estate Agent SEO',
      description: 'Comprehensive SEO for individual real estate agents and small agencies',
      icon: Building,
      features: ['Agent Profile Optimization', 'Local Property SEO', 'Listing Page Optimization', 'Neighborhood Content', 'Property Showcase SEO', 'Client Testimonial Pages']
    },
    {
      type: 'Property Developer SEO',
      description: 'Large-scale SEO solutions for property developers and construction companies',
      icon: Star,
      features: ['Project Marketing SEO', 'Development Portfolio', 'Property Launch Campaigns', 'Builder Brand SEO', 'Construction Progress Content', 'Investment Property SEO']
    },
    {
      type: 'Real Estate Portal SEO',
      description: 'SEO strategies for property portals, listing websites, and real estate platforms',
      icon: Crown,
      features: ['Property Listing SEO', 'Search Functionality Optimization', 'Location-Based SEO', 'Property Comparison Pages', 'Real Estate Market Data', 'User-Generated Content SEO']
    },
    {
      type: 'Commercial Real Estate SEO',
      description: 'Specialized SEO for commercial property, office spaces, and investment properties',
      icon: Target,
      features: ['Commercial Property SEO', 'Office Space Marketing', 'Investment Property Content', 'Commercial Lease SEO', 'Property Management SEO', 'Real Estate Investment Trust SEO']
    }
  ];

  const propertyTypes = [
    { name: 'Residential Properties', clients: '850+', growth: '2,100%' },
    { name: 'Commercial Real Estate', clients: '620+', growth: '1,850%' },
    { name: 'Luxury Properties', clients: '480+', growth: '2,400%' },
    { name: 'Investment Properties', clients: '380+', growth: '1,650%' },
    { name: 'Rental Properties', clients: '520+', growth: '1,750%' },
    { name: 'New Developments', clients: '320+', growth: '2,200%' }
  ];

  const caseStudies = [
    {
      client: 'Luxury Property Developer',
      industry: 'High-End Real Estate',
      challenge: 'Premium property developer needed to reach high-net-worth individuals',
      result: '3,500% luxury inquiry increase',
      metrics: ['720+ luxury property keywords in top 3', '₹85Cr+ property sales', '480% increase in premium inquiries']
    },
    {
      client: 'Real Estate Agency Network',
      industry: 'Residential Properties',
      challenge: 'Multi-location agency needed to compete with online property portals',
      result: '2,800% property lead growth',
      metrics: ['950+ property keywords ranking', '₹125Cr+ sales generated', '420% increase in property listings']
    },
    {
      client: 'Commercial Property Firm',
      industry: 'Commercial Real Estate',
      challenge: 'Commercial real estate firm needed to attract corporate clients',
      result: '2,200% commercial inquiry increase',
      metrics: ['580+ commercial keywords ranking', '₹180Cr+ commercial deals', '350% increase in corporate clients']
    }
  ];

  const realEstateSeoStrategies = [
    {
      strategy: 'Hyperlocal Property SEO',
      description: 'Target specific neighborhoods, localities, and micro-markets',
      benefits: ['Local market dominance', 'Neighborhood expertise', 'Location-specific leads', 'Community authority']
    },
    {
      strategy: 'Property Listing Optimization',
      description: 'Optimize individual property listings for maximum search visibility',
      benefits: ['Property discovery', 'Listing visibility', 'Buyer engagement', 'Faster sales cycles']
    },
    {
      strategy: 'Real Estate Content Marketing',
      description: 'Create valuable content around property markets, investment, and buying guides',
      benefits: ['Market authority', 'Buyer education', 'Trust building', 'Lead nurturing']
    },
    {
      strategy: 'Visual Property SEO',
      description: 'Optimize property images, virtual tours, and video content for search',
      benefits: ['Visual search ranking', 'Property showcase', 'Virtual tour optimization', 'Image SEO']
    }
  ];

  return (
    <div className="min-h-screen bg-slate-900 relative">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        <div className="container mx-auto px-6 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            <div className="lg:col-span-3">
              <section className="pt-24 pb-16">
                <div className="max-w-4xl">
                  <div className="inline-flex items-center space-x-2 bg-orange-500/20 border border-orange-500/30 rounded-full px-6 py-2 mb-8">
                    <Home className="w-4 h-4 text-orange-400" />
                    <span className="text-orange-400 font-medium">Real Estate SEO • Property Marketing Excellence</span>
                  </div>
                  
                  <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
                    #1 Real Estate SEO Company in
                    <span className="bg-gradient-to-r from-orange-400 to-orange-600 bg-clip-text text-transparent"> India</span>
                  </h1>
                  
                  <p className="text-xl text-slate-300 mb-8">
                    Premier real estate SEO services offering comprehensive search engine optimization solutions for property businesses including real estate agent SEO, property developer marketing, real estate portal optimization, and property listing SEO. Our real estate SEO company provides professional SEO services with property lead generation, local real estate SEO, property content marketing, and real estate conversion optimization. Serving 2,500+ real estate businesses across all property sectors with proven ₹680Cr+ property sales generation and 1,850% average lead growth for real estate clients through strategic search engine optimization and property digital marketing excellence.
                  </p>
                  
                  <div className="flex flex-col sm:flex-row gap-4 mb-8">
                    <Button asChild size="lg" className="bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white text-lg px-8 py-4">
                      <Link to="/quote">Get Free Real Estate SEO Audit</Link>
                    </Button>
                    <Button asChild size="lg" variant="outline" className="border-orange-500 text-orange-400 hover:bg-orange-500 hover:text-white text-lg px-8 py-4">
                      <Link to="/contact">Call Real Estate SEO Experts: +91-8708577598</Link>
                    </Button>
                  </div>

                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                    {achievements.map((achievement, index) => (
                      <div key={index} className="flex items-center justify-center space-x-2 bg-slate-800/50 rounded-lg p-3">
                        <CheckCircle className="w-4 h-4 text-orange-400 flex-shrink-0" />
                        <span className="text-slate-300">{achievement}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </section>

              <section className="mb-16 bg-slate-800/50 rounded-2xl p-8">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Real Estate SEO
                    <span className="bg-gradient-to-r from-orange-400 to-orange-600 bg-clip-text text-transparent"> Success Metrics</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Measurable real estate SEO results from property businesses across all real estate sectors and markets.
                  </p>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
                  {stats.map((stat, index) => (
                    <div key={index} className="text-center">
                      <div className="text-4xl font-bold text-orange-400 mb-2">{stat.metric}</div>
                      <div className="text-white font-semibold mb-1">{stat.description}</div>
                      <div className="text-slate-400">{stat.detail}</div>
                    </div>
                  ))}
                </div>
              </section>

              <section className="mb-16">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Real Estate SEO Strategies We
                    <span className="bg-gradient-to-r from-orange-400 to-orange-600 bg-clip-text text-transparent"> Master</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Specialized SEO strategies designed for real estate success across all property types and markets.
                  </p>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  {realEstateSpecializations.map((spec, index) => (
                    <Card key={index} className="bg-slate-900/80 border-orange-500/20 hover:border-orange-500/40 transition-all duration-300">
                      <CardContent className="p-8">
                        <div className="w-12 h-12 bg-gradient-to-r from-orange-500 to-orange-600 rounded-lg flex items-center justify-center mb-6">
                          <spec.icon className="w-6 h-6 text-white" />
                        </div>
                        <h3 className="text-xl font-bold text-white mb-4">{spec.type}</h3>
                        <p className="text-slate-300 mb-6">{spec.description}</p>
                        <ul className="space-y-2">
                          {spec.features.map((feature, idx) => (
                            <li key={idx} className="flex items-center text-slate-400 text-sm">
                              <CheckCircle className="w-3 h-3 text-orange-400 mr-2 flex-shrink-0" />
                              {feature}
                            </li>
                          ))}
                        </ul>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>

              {/* Property Types */}
              <section className="mb-16 bg-slate-800/30 rounded-2xl p-8">
                <h3 className="text-2xl font-bold text-white mb-6 text-center">
                  Property Type Expertise
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-6">
                  {propertyTypes.map((property, index) => (
                    <div key={index} className="text-center bg-slate-900/50 rounded-lg p-4">
                      <h4 className="text-orange-400 font-semibold mb-2">{property.name}</h4>
                      <div className="text-2xl font-bold text-white mb-1">{property.clients}</div>
                      <div className="text-slate-400 text-sm mb-2">Businesses Served</div>
                      <div className="text-green-400 font-semibold text-sm">{property.growth} avg growth</div>
                    </div>
                  ))}
                </div>
              </section>

              {/* Real Estate SEO Strategies */}
              <section className="mb-16 bg-slate-800/30 rounded-2xl p-8">
                <h3 className="text-2xl font-bold text-white mb-6 text-center">
                  Advanced Real Estate SEO Strategies
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                  {realEstateSeoStrategies.map((strategy, index) => (
                    <div key={index} className="bg-slate-900/50 rounded-lg p-6">
                      <h4 className="text-orange-400 font-semibold mb-3">{strategy.strategy}</h4>
                      <p className="text-slate-300 text-sm mb-4">{strategy.description}</p>
                      <ul className="space-y-1">
                        {strategy.benefits.map((benefit, idx) => (
                          <li key={idx} className="text-slate-400 text-xs flex items-center">
                            <CheckCircle className="w-3 h-3 text-orange-400 mr-2 flex-shrink-0" />
                            {benefit}
                          </li>
                        ))}
                      </ul>
                    </div>
                  ))}
                </div>
              </section>

              {/* Case Studies */}
              <section className="mb-16">
                <h3 className="text-2xl font-bold text-white mb-8 text-center">
                  Real Estate SEO Success Stories
                </h3>
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                  {caseStudies.map((study, index) => (
                    <Card key={index} className="bg-slate-900/60 border-orange-500/20 hover:border-orange-500/40 transition-all duration-300">
                      <CardContent className="p-6">
                        <h4 className="text-orange-400 font-semibold mb-2">{study.client}</h4>
                        <p className="text-slate-400 text-sm mb-3">{study.industry}</p>
                        <p className="text-slate-300 text-sm mb-4">{study.challenge}</p>
                        <div className="bg-slate-800/50 rounded p-3 mb-4">
                          <div className="text-green-400 font-semibold mb-2">{study.result}</div>
                          <ul className="space-y-1">
                            {study.metrics.map((metric, idx) => (
                              <li key={idx} className="text-slate-400 text-xs flex items-center">
                                <CheckCircle className="w-3 h-3 text-orange-400 mr-2 flex-shrink-0" />
                                {metric}
                              </li>
                            ))}
                          </ul>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>

              {/* Internal Linking Section */}
              <section className="mb-16 bg-slate-800/30 rounded-2xl p-8">
                <h3 className="text-2xl font-bold text-white mb-6 text-center">
                  Complete Real Estate Digital Marketing Solutions
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  <Link to="/services/ppc" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-orange-400 font-semibold mb-2 group-hover:text-orange-300">Real Estate Google Ads</h4>
                    <p className="text-slate-400 text-sm">PPC advertising for property listings and real estate leads</p>
                  </Link>
                  <Link to="/services/social-media" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-orange-400 font-semibold mb-2 group-hover:text-orange-300">Real Estate Social Media</h4>
                    <p className="text-slate-400 text-sm">Property showcasing and real estate brand building</p>
                  </Link>
                  <Link to="/services/content" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-orange-400 font-semibold mb-2 group-hover:text-orange-300">Real Estate Content Marketing</h4>
                    <p className="text-slate-400 text-sm">Property guides, market analysis, and buyer education</p>
                  </Link>
                  <Link to="/services/email" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-orange-400 font-semibold mb-2 group-hover:text-orange-300">Real Estate Email Marketing</h4>
                    <p className="text-slate-400 text-sm">Property newsletters and buyer nurturing campaigns</p>
                  </Link>
                  <Link to="/services/web-development" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-orange-400 font-semibold mb-2 group-hover:text-orange-300">Real Estate Website Development</h4>
                    <p className="text-slate-400 text-sm">Property portals and real estate website development</p>
                  </Link>
                  <Link to="/portfolio" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-orange-400 font-semibold mb-2 group-hover:text-orange-300">Real Estate Success Stories</h4>
                    <p className="text-slate-400 text-sm">See proven results from our real estate clients</p>
                  </Link>
                </div>
              </section>

              <section className="bg-gradient-to-r from-orange-600 to-orange-500 rounded-2xl p-8 text-center">
                <h2 className="text-3xl md:text-5xl font-bold mb-6">Ready to Generate More Property Leads?</h2>
                <p className="text-xl mb-8">
                  Join 2,500+ real estate businesses that trust GOD Digital Marketing for property SEO success. Proven strategies that deliver 1,850% lead growth and ₹680Cr+ sales generation.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button asChild size="lg" className="bg-white text-orange-600 hover:bg-slate-100 text-lg px-8 py-4">
                    <Link to="/quote">Get Free Real Estate SEO Audit</Link>
                  </Button>
                  <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-orange-600 text-lg px-8 py-4">
                    <Link to="/contact">Call Real Estate SEO Experts: +91-8708577598</Link>
                  </Button>
                </div>
              </section>
            </div>

            <div className="lg:col-span-1">
              <Sidebar currentService="seo" />
            </div>
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
};

export default RealEstateSeo;
