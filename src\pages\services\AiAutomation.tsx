import React from 'react';
import { Link } from 'react-router-dom';
import { Bo<PERSON>, Zap, TrendingUp, Users, CheckCircle, Building, Star, Crown, Target, ArrowRight, Cpu, Brain, Workflow } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';
import Sidebar from '@/components/Sidebar';

const AiAutomation = () => {
  const stats = [
    {
      metric: '850+',
      description: 'AI Automation Projects',
      detail: 'Successfully implemented'
    },
    {
      metric: '75%',
      description: 'Average Cost Reduction',
      detail: 'Through AI automation'
    },
    {
      metric: '₹120Cr+',
      description: 'Cost Savings Generated',
      detail: 'For our clients'
    },
    {
      metric: '95%',
      description: 'Process Efficiency Gain',
      detail: 'Average improvement'
    }
  ];

  const achievements = [
    'Top AI Automation Company in India',
    'ChatGPT Integration Specialists',
    'Business Process Automation Experts',
    'AI Chatbot Development Leaders',
    'Machine Learning Implementation Champions',
    'Intelligent Automation Pioneers'
  ];

  const aiServices = [
    {
      type: 'AI Chatbot Development',
      description: 'Intelligent chatbots for customer service, lead generation, and business automation',
      icon: Bot,
      features: ['Custom AI Chatbots', 'WhatsApp Bot Integration', 'Website Chat Automation', 'Lead Qualification Bots', 'Customer Support Automation', 'Multi-language Chat Support']
    },
    {
      type: 'Business Process Automation',
      description: 'AI-powered automation of repetitive business processes and workflows',
      icon: Workflow,
      features: ['Document Processing Automation', 'Email Automation Systems', 'Data Entry Automation', 'Invoice Processing AI', 'Inventory Management Automation', 'HR Process Automation']
    },
    {
      type: 'AI Content Generation',
      description: 'Automated content creation using advanced AI models and natural language processing',
      icon: Brain,
      features: ['AI Blog Writing', 'Social Media Content Automation', 'Product Description Generation', 'Email Campaign Automation', 'SEO Content Creation', 'Marketing Copy Generation']
    },
    {
      type: 'Predictive Analytics & AI',
      description: 'Machine learning models for business intelligence and predictive insights',
      icon: Cpu,
      features: ['Sales Forecasting AI', 'Customer Behavior Prediction', 'Market Trend Analysis', 'Risk Assessment Models', 'Demand Forecasting', 'Performance Optimization AI']
    }
  ];

  const automationSolutions = [
    {
      industry: 'E-commerce',
      solutions: ['Product Recommendation AI', 'Inventory Automation', 'Customer Service Bots', 'Price Optimization AI'],
      clients: '320+',
      savings: '₹45Cr+'
    },
    {
      industry: 'Healthcare',
      solutions: ['Patient Scheduling AI', 'Medical Record Automation', 'Appointment Bots', 'Billing Automation'],
      clients: '180+',
      savings: '₹28Cr+'
    },
    {
      industry: 'Manufacturing',
      solutions: ['Quality Control AI', 'Production Planning', 'Supply Chain Automation', 'Maintenance Prediction'],
      clients: '150+',
      savings: '₹35Cr+'
    },
    {
      industry: 'Finance',
      solutions: ['Fraud Detection AI', 'Risk Assessment', 'Document Processing', 'Customer Onboarding Automation'],
      clients: '120+',
      savings: '₹22Cr+'
    }
  ];

  const caseStudies = [
    {
      client: 'E-commerce Giant',
      industry: 'Online Retail',
      challenge: 'Manual customer service handling 10,000+ daily inquiries',
      solution: 'AI chatbot with natural language processing and automated response system',
      results: ['85% query resolution without human intervention', '60% reduction in response time', '₹2.5Cr annual cost savings', '95% customer satisfaction score']
    },
    {
      client: 'Manufacturing Company',
      industry: 'Industrial Manufacturing',
      challenge: 'Manual quality control processes causing delays and errors',
      solution: 'Computer vision AI for automated quality inspection and defect detection',
      results: ['99.2% defect detection accuracy', '70% faster inspection process', '₹1.8Cr cost reduction annually', '40% reduction in product returns']
    },
    {
      client: 'Healthcare Network',
      industry: 'Healthcare Services',
      challenge: 'Manual appointment scheduling and patient communication inefficiencies',
      solution: 'AI-powered appointment system with automated patient communication',
      results: ['90% appointment scheduling automation', '50% reduction in no-shows', '₹1.2Cr operational savings', '85% patient satisfaction improvement']
    }
  ];

  return (
    <div className="min-h-screen bg-slate-900 relative">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        <div className="container mx-auto px-6 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            <div className="lg:col-span-3">
              <section className="pt-24 pb-16">
                <div className="max-w-4xl">
                  <div className="inline-flex items-center space-x-2 bg-purple-500/20 border border-purple-500/30 rounded-full px-6 py-2 mb-8">
                    <Bot className="w-4 h-4 text-purple-400" />
                    <span className="text-purple-400 font-medium">AI Automation • Intelligent Business Solutions</span>
                  </div>
                  
                  <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
                    #1 AI Automation Company in
                    <span className="bg-gradient-to-r from-purple-400 to-purple-600 bg-clip-text text-transparent"> India</span>
                  </h1>
                  
                  <p className="text-xl text-slate-300 mb-8">
                    Premier AI automation services offering comprehensive artificial intelligence solutions including AI chatbot development, business process automation, machine learning implementation, and intelligent workflow optimization. Our AI automation company provides professional automation services with custom AI solutions, chatbot integration, process automation, predictive analytics, and AI-powered business intelligence. Serving 850+ businesses across all industries with proven ₹120Cr+ cost savings and 75% average efficiency improvement through strategic AI automation and intelligent business solutions.
                  </p>
                  
                  <div className="flex flex-col sm:flex-row gap-4 mb-8">
                    <Button asChild size="lg" className="bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white text-lg px-8 py-4">
                      <Link to="/quote">Get Free AI Automation Consultation</Link>
                    </Button>
                    <Button asChild size="lg" variant="outline" className="border-purple-500 text-purple-400 hover:bg-purple-500 hover:text-white text-lg px-8 py-4">
                      <Link to="/contact">Call AI Automation Experts: +91-8708577598</Link>
                    </Button>
                  </div>

                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                    {achievements.map((achievement, index) => (
                      <div key={index} className="flex items-center justify-center space-x-2 bg-slate-800/50 rounded-lg p-3">
                        <CheckCircle className="w-4 h-4 text-purple-400 flex-shrink-0" />
                        <span className="text-slate-300">{achievement}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </section>

              <section className="mb-16 bg-slate-800/50 rounded-2xl p-8">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    AI Automation
                    <span className="bg-gradient-to-r from-purple-400 to-purple-600 bg-clip-text text-transparent"> Success Metrics</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Measurable AI automation results from businesses across all industries and sectors.
                  </p>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
                  {stats.map((stat, index) => (
                    <div key={index} className="text-center">
                      <div className="text-4xl font-bold text-purple-400 mb-2">{stat.metric}</div>
                      <div className="text-white font-semibold mb-1">{stat.description}</div>
                      <div className="text-slate-400">{stat.detail}</div>
                    </div>
                  ))}
                </div>
              </section>

              <section className="mb-16">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    AI Automation Services We
                    <span className="bg-gradient-to-r from-purple-400 to-purple-600 bg-clip-text text-transparent"> Provide</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Comprehensive AI automation solutions designed to transform your business operations and drive efficiency.
                  </p>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  {aiServices.map((service, index) => (
                    <Card key={index} className="bg-slate-900/80 border-purple-500/20 hover:border-purple-500/40 transition-all duration-300">
                      <CardContent className="p-8">
                        <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg flex items-center justify-center mb-6">
                          <service.icon className="w-6 h-6 text-white" />
                        </div>
                        <h3 className="text-xl font-bold text-white mb-4">{service.type}</h3>
                        <p className="text-slate-300 mb-6">{service.description}</p>
                        <ul className="space-y-2">
                          {service.features.map((feature, idx) => (
                            <li key={idx} className="flex items-center text-slate-400 text-sm">
                              <CheckCircle className="w-3 h-3 text-purple-400 mr-2 flex-shrink-0" />
                              {feature}
                            </li>
                          ))}
                        </ul>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>

              {/* Industry Solutions */}
              <section className="mb-16 bg-slate-800/30 rounded-2xl p-8">
                <h3 className="text-2xl font-bold text-white mb-6 text-center">
                  Industry-Specific AI Automation Solutions
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                  {automationSolutions.map((solution, index) => (
                    <div key={index} className="bg-slate-900/50 rounded-lg p-6">
                      <h4 className="text-purple-400 font-semibold mb-3">{solution.industry}</h4>
                      <ul className="space-y-2 mb-4">
                        {solution.solutions.map((item, idx) => (
                          <li key={idx} className="text-slate-300 text-sm flex items-center">
                            <CheckCircle className="w-3 h-3 text-purple-400 mr-2 flex-shrink-0" />
                            {item}
                          </li>
                        ))}
                      </ul>
                      <div className="border-t border-slate-700 pt-3">
                        <div className="text-white font-semibold">{solution.clients}</div>
                        <div className="text-slate-400 text-sm mb-1">Clients Served</div>
                        <div className="text-green-400 font-semibold">{solution.savings}</div>
                        <div className="text-slate-400 text-sm">Cost Savings</div>
                      </div>
                    </div>
                  ))}
                </div>
              </section>

              {/* Case Studies */}
              <section className="mb-16">
                <h3 className="text-2xl font-bold text-white mb-8 text-center">
                  AI Automation Success Stories
                </h3>
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                  {caseStudies.map((study, index) => (
                    <Card key={index} className="bg-slate-900/60 border-purple-500/20 hover:border-purple-500/40 transition-all duration-300">
                      <CardContent className="p-6">
                        <h4 className="text-purple-400 font-semibold mb-2">{study.client}</h4>
                        <p className="text-slate-400 text-sm mb-3">{study.industry}</p>
                        <div className="mb-4">
                          <h5 className="text-white font-medium text-sm mb-1">Challenge:</h5>
                          <p className="text-slate-300 text-sm mb-3">{study.challenge}</p>
                          <h5 className="text-white font-medium text-sm mb-1">Solution:</h5>
                          <p className="text-slate-300 text-sm mb-3">{study.solution}</p>
                        </div>
                        <div className="bg-slate-800/50 rounded p-3">
                          <h5 className="text-green-400 font-semibold mb-2 text-sm">Results:</h5>
                          <ul className="space-y-1">
                            {study.results.map((result, idx) => (
                              <li key={idx} className="text-slate-400 text-xs flex items-center">
                                <CheckCircle className="w-3 h-3 text-purple-400 mr-2 flex-shrink-0" />
                                {result}
                              </li>
                            ))}
                          </ul>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>

              {/* Internal Linking Section */}
              <section className="mb-16 bg-slate-800/30 rounded-2xl p-8">
                <h3 className="text-2xl font-bold text-white mb-6 text-center">
                  Complete Digital Transformation Solutions
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  <Link to="/services/business-automation" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-purple-400 font-semibold mb-2 group-hover:text-purple-300">Business Automation</h4>
                    <p className="text-slate-400 text-sm">Complete business process automation and workflow optimization</p>
                  </Link>
                  <Link to="/services/web-development" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-purple-400 font-semibold mb-2 group-hover:text-purple-300">AI-Powered Web Development</h4>
                    <p className="text-slate-400 text-sm">Intelligent websites with AI integration and automation</p>
                  </Link>
                  <Link to="/services/seo" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-purple-400 font-semibold mb-2 group-hover:text-purple-300">AI-Enhanced SEO</h4>
                    <p className="text-slate-400 text-sm">SEO optimization powered by artificial intelligence</p>
                  </Link>
                  <Link to="/services/content" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-purple-400 font-semibold mb-2 group-hover:text-purple-300">AI Content Generation</h4>
                    <p className="text-slate-400 text-sm">Automated content creation and marketing automation</p>
                  </Link>
                  <Link to="/services/business-development" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-purple-400 font-semibold mb-2 group-hover:text-purple-300">AI Business Development</h4>
                    <p className="text-slate-400 text-sm">AI-powered business growth and development strategies</p>
                  </Link>
                  <Link to="/portfolio" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-purple-400 font-semibold mb-2 group-hover:text-purple-300">AI Automation Success Stories</h4>
                    <p className="text-slate-400 text-sm">See proven results from our AI automation projects</p>
                  </Link>
                </div>
              </section>

              <section className="bg-gradient-to-r from-purple-600 to-purple-500 rounded-2xl p-8 text-center">
                <h2 className="text-3xl md:text-5xl font-bold mb-6">Ready to Transform Your Business with AI?</h2>
                <p className="text-xl mb-8">
                  Join 850+ businesses that trust GOD Digital Marketing for AI automation success. Proven solutions that deliver 75% cost reduction and ₹120Cr+ savings.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button asChild size="lg" className="bg-white text-purple-600 hover:bg-slate-100 text-lg px-8 py-4">
                    <Link to="/quote">Get Free AI Automation Consultation</Link>
                  </Button>
                  <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-purple-600 text-lg px-8 py-4">
                    <Link to="/contact">Call AI Automation Experts: +91-8708577598</Link>
                  </Button>
                </div>
              </section>
            </div>

            <div className="lg:col-span-1">
              <Sidebar currentService="ai-automation" />
            </div>
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
};

export default AiAutomation;
