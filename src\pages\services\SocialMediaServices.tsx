
import React from 'react';
import { Link } from 'react-router-dom';
import { Share2, Users, TrendingUp, Heart, CheckCircle, Bot, Zap, Target, BarChart3, Globe, Award } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';

const SocialMediaServices = () => {
  const platforms = [
    {
      name: 'LinkedIn',
      icon: Users,
      focus: 'B2B Authority Building',
      automation: ['Daily thought leadership posts', 'Industry insights sharing', 'Professional network engagement', 'Lead generation automation'],
      seoImpact: 'Google recognizes LinkedIn authority signals for professional credibility'
    },
    {
      name: 'YouTube',
      icon: TrendingUp,
      focus: 'Educational Content Hub',
      automation: ['Weekly tutorial videos', 'Case study presentations', 'Industry trend analysis', 'SEO-optimized descriptions'],
      seoImpact: 'Video content boosts Google rankings and increases dwell time'
    },
    {
      name: 'Facebook',
      icon: Share2,
      focus: 'Community Engagement',
      automation: ['Business page management', 'Local community building', 'Event promotion', 'Customer testimonial sharing'],
      seoImpact: 'Social signals and brand mentions improve local SEO rankings'
    },
    {
      name: 'Instagram',
      icon: Heart,
      focus: 'Visual Brand Building',
      automation: ['Behind-scenes content', 'Success story highlights', 'Team culture posts', 'Client transformation showcases'],
      seoImpact: 'Visual content increases brand search volume and recognition'
    },
    {
      name: 'Podcast Platforms',
      icon: Award,
      focus: 'Industry Expertise',
      automation: ['Weekly industry podcasts', 'Expert interview series', 'Market trend discussions', 'Multi-platform distribution'],
      seoImpact: 'Podcast content creates authoritative backlinks and brand mentions'
    },
    {
      name: 'Twitter/X',
      icon: Zap,
      focus: 'Real-time Authority',
      automation: ['Industry news commentary', 'Quick tips sharing', 'Engagement with industry leaders', 'Trending topic participation'],
      seoImpact: 'Real-time content signals topical authority to Google'
    }
  ];

  const automationFeatures = [
    'AI Content Generation (50-100 pieces monthly)',
    'Cross-Platform Content Repurposing',
    'Automated Scheduling & Posting Systems',
    'Engagement Automation & Response Systems',
    'Brand Mention Building for Google Entity Recognition',
    'Multi-Platform SEO Amplification Strategy',
    'Analytics & Performance Tracking Automation',
    '24/7 Marketing While You Focus on Operations'
  ];

  const packages = [
    {
      name: 'Social Authority Builder',
      price: '₹30,000',
      period: '/month',
      description: 'Essential automation for consistent brand presence across key platforms',
      features: [
        'LinkedIn + Facebook + Instagram automation',
        '30 AI-generated posts monthly',
        'Basic engagement automation',
        'Monthly performance reports',
        'Cross-platform content repurposing'
      ]
    },
    {
      name: 'Digital Ecosystem Domination',
      price: '₹45,000',
      period: '/month',
      description: 'Complete multi-platform automation with video and podcast content',
      features: [
        'All platforms + YouTube + Podcasts',
        '60 AI-generated content pieces monthly',
        'Advanced engagement automation',
        'Weekly strategy optimization',
        'SEO amplification integration',
        'Brand mention building campaigns'
      ],
      popular: true
    },
    {
      name: 'International Authority System',
      price: '₹60,000',
      period: '/month',
      description: 'Premium automation for global market presence and thought leadership',
      features: [
        'Everything in Digital Ecosystem',
        '100+ content pieces monthly',
        'Multi-language content creation',
        'International market targeting',
        'Advanced analytics & insights',
        'Dedicated automation specialist',
        'Weekly strategy calls'
      ]
    }
  ];

  return (
    <div className="min-h-screen bg-slate-900 text-white relative overflow-hidden">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        <section className="pt-32 pb-20">
          <div className="container mx-auto px-6">
            <div className="max-w-4xl mx-auto text-center">
              <div className="inline-flex items-center space-x-2 bg-amber-500/20 border border-amber-500/30 rounded-full px-6 py-2 mb-8">
                <Bot className="w-4 h-4 text-amber-400" />
                <span className="text-amber-400 font-medium">Social Media Marketing Services • Digital Marketing Excellence</span>
              </div>

              <h1 className="text-5xl md:text-6xl font-bold mb-6 leading-tight">
                Professional Social Media Marketing
                <span className="bg-gradient-to-r from-amber-400 to-amber-600 bg-clip-text text-transparent"> Services</span>
              </h1>

              <p className="text-xl text-slate-300 mb-12 max-w-3xl mx-auto">
                Comprehensive social media marketing services delivering professional social media management across Facebook, Instagram, LinkedIn, Twitter, and YouTube. Our social media agency provides complete social media marketing solutions including content creation, social media advertising, community management, and social media strategy. Expert social media services with proven engagement growth, brand awareness, and lead generation through strategic social media campaigns and social media optimization.
              </p>

              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button asChild size="lg" className="bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700 text-white text-lg px-8 py-4">
                  <Link to="/quote">Get Social Media Strategy</Link>
                </Button>
                <Button asChild size="lg" variant="outline" className="border-amber-500 text-amber-400 hover:bg-amber-500 hover:text-white text-lg px-8 py-4">
                  <Link to="/case-studies">View Authority Results</Link>
                </Button>
              </div>
            </div>
          </div>
        </section>

        {/* Authority Building Strategy Section */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Complete Digital Authority
                <span className="bg-gradient-to-r from-amber-400 to-amber-600 bg-clip-text text-transparent"> Building System</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto">
                Multi-platform automation that creates Google authority signals and brand recognition across all digital channels, not just social media posting.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {platforms.map((platform, index) => (
                <Card key={index} className="bg-slate-900/80 border-amber-500/20 hover:border-amber-500/40 transition-all duration-300">
                  <CardContent className="p-6">
                    <div className="w-12 h-12 bg-gradient-to-r from-amber-500 to-amber-600 rounded-lg flex items-center justify-center mb-4">
                      <platform.icon className="w-6 h-6 text-white" />
                    </div>
                    <h3 className="text-xl font-bold text-white mb-2">{platform.name}</h3>
                    <p className="text-amber-400 font-semibold mb-4">{platform.focus}</p>

                    <div className="mb-4">
                      <h4 className="text-white font-semibold mb-2">Automation Features:</h4>
                      <ul className="space-y-1">
                        {platform.automation.map((feature, idx) => (
                          <li key={idx} className="flex items-start text-slate-300 text-sm">
                            <CheckCircle className="w-3 h-3 text-amber-400 mr-2 mt-1 flex-shrink-0" />
                            {feature}
                          </li>
                        ))}
                      </ul>
                    </div>

                    <div className="bg-amber-500/10 p-3 rounded-lg">
                      <p className="text-amber-400 text-sm font-medium">SEO Impact:</p>
                      <p className="text-slate-300 text-sm">{platform.seoImpact}</p>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Automation Features Section */}
        <section className="py-20">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Advanced AI Automation
                <span className="bg-gradient-to-r from-amber-400 to-amber-600 bg-clip-text text-transparent"> Technology</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto">
                Cutting-edge automation that replaces hiring social media managers while delivering superior results.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-w-4xl mx-auto">
              {automationFeatures.map((feature, index) => (
                <div key={index} className="flex items-center space-x-3">
                  <CheckCircle className="w-6 h-6 text-amber-400 flex-shrink-0" />
                  <span className="text-slate-300">{feature}</span>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Business Benefits Section */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Business Benefits &
                <span className="bg-gradient-to-r from-amber-400 to-amber-600 bg-clip-text text-transparent"> ROI Impact</span>
              </h2>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[
                {
                  icon: TrendingUp,
                  title: 'Improved Google Rankings',
                  description: 'Social signals and brand mentions boost SEO authority and search rankings'
                },
                {
                  icon: Globe,
                  title: 'International Market Presence',
                  description: 'Build consistent expert positioning across global markets and platforms'
                },
                {
                  icon: Target,
                  title: 'Lead Generation Automation',
                  description: 'Valuable content sharing generates qualified leads while you sleep'
                },
                {
                  icon: Award,
                  title: 'Thought Leadership',
                  description: 'Establish industry expertise through consistent, valuable content sharing'
                },
                {
                  icon: BarChart3,
                  title: 'Enhanced Brand Recognition',
                  description: 'Increased brand search volume and recognition across all channels'
                },
                {
                  icon: Zap,
                  title: '24/7 Marketing Automation',
                  description: 'Marketing works around the clock while you focus on business operations'
                }
              ].map((benefit, index) => (
                <Card key={index} className="bg-slate-900/80 border-amber-500/20 hover:border-amber-500/40 transition-all duration-300 text-center">
                  <CardContent className="p-6">
                    <div className="w-12 h-12 bg-gradient-to-r from-amber-500 to-amber-600 rounded-lg flex items-center justify-center mx-auto mb-4">
                      <benefit.icon className="w-6 h-6 text-white" />
                    </div>
                    <h3 className="text-xl font-bold text-white mb-3">{benefit.title}</h3>
                    <p className="text-slate-300">{benefit.description}</p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Pricing Section */}
        <section className="py-20">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Premium Automation
                <span className="bg-gradient-to-r from-amber-400 to-amber-600 bg-clip-text text-transparent"> Investment</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto">
                Replace hiring social media managers with superior AI automation that works 24/7.
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
              {packages.map((pkg, index) => (
                <Card key={index} className={`bg-slate-900/80 border-amber-500/20 hover:border-amber-500/40 transition-all duration-300 relative ${pkg.popular ? 'ring-2 ring-amber-500' : ''}`}>
                  {pkg.popular && (
                    <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                      <span className="bg-amber-500 text-white px-4 py-2 rounded-full text-sm font-semibold">Most Popular</span>
                    </div>
                  )}
                  <CardContent className="p-8">
                    <h3 className="text-2xl font-bold text-white mb-2">{pkg.name}</h3>
                    <p className="text-slate-300 mb-6">{pkg.description}</p>
                    <div className="mb-6">
                      <span className="text-4xl font-bold text-amber-400">{pkg.price}</span>
                      <span className="text-slate-400">{pkg.period}</span>
                    </div>
                    <ul className="space-y-3 mb-8">
                      {pkg.features.map((feature, idx) => (
                        <li key={idx} className="flex items-center text-slate-300">
                          <CheckCircle className="w-4 h-4 text-amber-400 mr-3 flex-shrink-0" />
                          {feature}
                        </li>
                      ))}
                    </ul>
                    <Button asChild className={`w-full ${pkg.popular ? 'bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700' : 'bg-slate-700 hover:bg-slate-600'} text-white`}>
                      <Link to="/quote">Get Started</Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Final CTA Section */}
        <section className="py-20 bg-gradient-to-r from-amber-600 to-amber-500">
          <div className="container mx-auto px-6 text-center">
            <h2 className="text-4xl md:text-5xl font-bold mb-6">Ready to Build Complete Digital Authority?</h2>
            <p className="text-xl mb-8 max-w-3xl mx-auto">
              Join businesses investing ₹30K-60K monthly in AI automation that builds Google authority signals and generates leads 24/7.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button asChild size="lg" className="bg-white text-amber-600 hover:bg-slate-100 text-lg px-8 py-4">
                <Link to="/quote">Get AI Automation Strategy</Link>
              </Button>
              <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-amber-600 text-lg px-8 py-4">
                <Link to="/contact">Book Authority Consultation</Link>
              </Button>
            </div>
          </div>
        </section>
      </main>
      
      <Footer />
    </div>
  );
};

export default SocialMediaServices;
