
import React, { useState } from 'react';
import { ArrowRight, CheckCircle, Calculator, Zap } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';
import { toast } from '@/hooks/use-toast';

const Quote = () => {
  const [step, setStep] = useState(1);
  const [formData, setFormData] = useState({
    // Business Information
    name: '',
    email: '',
    phone: '',
    company: '',
    website: '',
    industry: '',
    
    // Services
    services: [] as string[],
    
    // Project Details
    goals: [] as string[],
    timeline: '',
    budget: [5000],
    currentMarketing: '',
    competitors: '',
    targetAudience: '',
    
    // Additional Info
    additionalInfo: ''
  });

  const [estimatedCost, setEstimatedCost] = useState(0);

  const services = [
    { id: 'seo', name: 'SEO Services', price: 2500 },
    { id: 'ppc', name: 'PPC Advertising', price: 3000 },
    { id: 'social', name: 'Social Media Marketing', price: 2000 },
    { id: 'email', name: 'Email Marketing', price: 1500 },
    { id: 'content', name: 'Content Marketing', price: 2200 },
    { id: 'web-design', name: 'Web Design & Development', price: 5000 }
  ];

  const goals = [
    'Increase organic traffic',
    'Improve search rankings',
    'Generate more leads',
    'Boost online sales',
    'Build brand awareness',
    'Expand market reach',
    'Improve conversion rates',
    'Enhance customer retention'
  ];

  const calculateEstimate = () => {
    let baseCost = 0;
    formData.services.forEach(serviceId => {
      const service = services.find(s => s.id === serviceId);
      if (service) baseCost += service.price;
    });
    
    // Apply multipliers based on goals, timeline, and budget
    let multiplier = 1;
    if (formData.goals.length > 4) multiplier += 0.2;
    if (formData.timeline === 'asap') multiplier += 0.15;
    if (formData.budget[0] > 10000) multiplier += 0.1;
    
    setEstimatedCost(Math.round(baseCost * multiplier));
  };

  React.useEffect(() => {
    calculateEstimate();
  }, [formData.services, formData.goals, formData.timeline, formData.budget]);

  const handleServiceToggle = (serviceId: string) => {
    setFormData(prev => ({
      ...prev,
      services: prev.services.includes(serviceId)
        ? prev.services.filter(s => s !== serviceId)
        : [...prev.services, serviceId]
    }));
  };

  const handleGoalToggle = (goal: string) => {
    setFormData(prev => ({
      ...prev,
      goals: prev.goals.includes(goal)
        ? prev.goals.filter(g => g !== goal)
        : [...prev.goals, goal]
    }));
  };

  const handleNext = () => {
    if (step < 4) setStep(step + 1);
  };

  const handleBack = () => {
    if (step > 1) setStep(step - 1);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    toast({
      title: "Quote Request Submitted!",
      description: "Thank you for your request. Our team will contact you within 24 hours with a detailed proposal.",
    });
  };

  return (
    <div className="min-h-screen bg-slate-900 text-white relative overflow-hidden">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        {/* Hero Section */}
        <section className="pt-32 pb-12">
          <div className="container mx-auto px-6">
            <div className="max-w-4xl mx-auto text-center">
              <h1 className="text-5xl md:text-6xl font-bold mb-6 leading-tight">
                Get Your Digital Marketing
                <span className="bg-gradient-to-r from-amber-400 to-amber-600 bg-clip-text text-transparent"> Services Quote</span>
              </h1>
              <p className="text-xl text-slate-300 mb-8">
                Get custom pricing for professional digital marketing services including SEO services, Google Ads management, social media marketing, content marketing, email marketing, and web development. Our digital marketing agency provides transparent pricing for comprehensive digital marketing solutions tailored to your business goals and budget requirements.
              </p>
              
              {/* Progress Bar */}
              <div className="max-w-md mx-auto mb-8">
                <div className="flex justify-between mb-2">
                  {[1, 2, 3, 4].map((num) => (
                    <div key={num} className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-semibold ${
                      step >= num ? 'bg-amber-500 text-white' : 'bg-slate-700 text-slate-400'
                    }`}>
                      {num}
                    </div>
                  ))}
                </div>
                <div className="w-full bg-slate-700 rounded-full h-2">
                  <div 
                    className="bg-amber-500 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${(step / 4) * 100}%` }}
                  ></div>
                </div>
                <div className="flex justify-between text-sm text-slate-400 mt-2">
                  <span>Services</span>
                  <span>Goals</span>
                  <span>Details</span>
                  <span>Contact</span>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Quote Form */}
        <section className="pb-20">
          <div className="container mx-auto px-6">
            <div className="max-w-4xl mx-auto">
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                {/* Form */}
                <div className="lg:col-span-2">
                  <Card className="bg-slate-900/80 border-amber-500/20">
                    <CardContent className="p-8">
                      <form onSubmit={handleSubmit}>
                        {/* Step 1: Services */}
                        {step === 1 && (
                          <div>
                            <h2 className="text-3xl font-bold text-white mb-6">Select Your Services</h2>
                            <p className="text-slate-300 mb-8">Choose the digital marketing services you're interested in:</p>
                            
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
                              {services.map((service) => (
                                <div key={service.id} className="relative">
                                  <label className={`block p-4 border-2 rounded-lg cursor-pointer transition-all ${
                                    formData.services.includes(service.id)
                                      ? 'border-amber-500 bg-amber-500/10'
                                      : 'border-slate-600 hover:border-slate-500'
                                  }`}>
                                    <div className="flex items-center justify-between">
                                      <div className="flex items-center">
                                        <Checkbox
                                          checked={formData.services.includes(service.id)}
                                          onCheckedChange={() => handleServiceToggle(service.id)}
                                          className="mr-3"
                                        />
                                        <span className="text-white font-semibold">{service.name}</span>
                                      </div>
                                      <span className="text-amber-400 font-semibold">
                                        Starting at ${service.price.toLocaleString()}/mo
                                      </span>
                                    </div>
                                  </label>
                                </div>
                              ))}
                            </div>
                            
                            <div className="flex justify-end">
                              <Button 
                                onClick={handleNext} 
                                disabled={formData.services.length === 0}
                                className="bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700"
                              >
                                Next Step
                                <ArrowRight className="ml-2 w-4 h-4" />
                              </Button>
                            </div>
                          </div>
                        )}

                        {/* Step 2: Goals */}
                        {step === 2 && (
                          <div>
                            <h2 className="text-3xl font-bold text-white mb-6">What Are Your Goals?</h2>
                            <p className="text-slate-300 mb-8">Select all the goals you want to achieve:</p>
                            
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
                              {goals.map((goal) => (
                                <div key={goal} className="relative">
                                  <label className={`block p-4 border-2 rounded-lg cursor-pointer transition-all ${
                                    formData.goals.includes(goal)
                                      ? 'border-amber-500 bg-amber-500/10'
                                      : 'border-slate-600 hover:border-slate-500'
                                  }`}>
                                    <div className="flex items-center">
                                      <Checkbox
                                        checked={formData.goals.includes(goal)}
                                        onCheckedChange={() => handleGoalToggle(goal)}
                                        className="mr-3"
                                      />
                                      <span className="text-white">{goal}</span>
                                    </div>
                                  </label>
                                </div>
                              ))}
                            </div>
                            
                            <div className="flex justify-between">
                              <Button onClick={handleBack} variant="outline" className="border-slate-600 text-slate-300">
                                Back
                              </Button>
                              <Button 
                                onClick={handleNext}
                                disabled={formData.goals.length === 0}
                                className="bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700"
                              >
                                Next Step
                                <ArrowRight className="ml-2 w-4 h-4" />
                              </Button>
                            </div>
                          </div>
                        )}

                        {/* Step 3: Project Details */}
                        {step === 3 && (
                          <div>
                            <h2 className="text-3xl font-bold text-white mb-6">Project Details</h2>
                            
                            <div className="space-y-6 mb-8">
                              <div>
                                <label className="block text-sm font-medium text-slate-300 mb-2">Monthly Budget Range</label>
                                <div className="px-4">
                                  <Slider
                                    value={formData.budget}
                                    onValueChange={(value) => setFormData(prev => ({ ...prev, budget: value }))}
                                    max={50000}
                                    min={1000}
                                    step={500}
                                    className="mb-2"
                                  />
                                  <div className="flex justify-between text-sm text-slate-400">
                                    <span>$1,000</span>
                                    <span className="text-amber-400 font-semibold">
                                      ${formData.budget[0].toLocaleString()}
                                    </span>
                                    <span>$50,000+</span>
                                  </div>
                                </div>
                              </div>

                              <div>
                                <label className="block text-sm font-medium text-slate-300 mb-2">Project Timeline</label>
                                <Select value={formData.timeline} onValueChange={(value) => setFormData(prev => ({ ...prev, timeline: value }))}>
                                  <SelectTrigger className="bg-slate-800 border-slate-600 text-white">
                                    <SelectValue placeholder="When do you want to start?" />
                                  </SelectTrigger>
                                  <SelectContent className="bg-slate-800 border-slate-600">
                                    <SelectItem value="asap">ASAP</SelectItem>
                                    <SelectItem value="1-month">Within 1 month</SelectItem>
                                    <SelectItem value="2-3-months">2-3 months</SelectItem>
                                    <SelectItem value="later">More than 3 months</SelectItem>
                                  </SelectContent>
                                </Select>
                              </div>

                              <div>
                                <label className="block text-sm font-medium text-slate-300 mb-2">Current Marketing Efforts</label>
                                <Textarea
                                  value={formData.currentMarketing}
                                  onChange={(e) => setFormData(prev => ({ ...prev, currentMarketing: e.target.value }))}
                                  className="bg-slate-800 border-slate-600 text-white"
                                  placeholder="Tell us about your current marketing activities..."
                                />
                              </div>

                              <div>
                                <label className="block text-sm font-medium text-slate-300 mb-2">Target Audience</label>
                                <Input
                                  value={formData.targetAudience}
                                  onChange={(e) => setFormData(prev => ({ ...prev, targetAudience: e.target.value }))}
                                  className="bg-slate-800 border-slate-600 text-white"
                                  placeholder="Describe your ideal customers..."
                                />
                              </div>
                            </div>
                            
                            <div className="flex justify-between">
                              <Button onClick={handleBack} variant="outline" className="border-slate-600 text-slate-300">
                                Back
                              </Button>
                              <Button 
                                onClick={handleNext}
                                className="bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700"
                              >
                                Next Step
                                <ArrowRight className="ml-2 w-4 h-4" />
                              </Button>
                            </div>
                          </div>
                        )}

                        {/* Step 4: Contact Information */}
                        {step === 4 && (
                          <div>
                            <h2 className="text-3xl font-bold text-white mb-6">Contact Information</h2>
                            
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                              <div>
                                <label className="block text-sm font-medium text-slate-300 mb-2">Full Name *</label>
                                <Input
                                  value={formData.name}
                                  onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                                  className="bg-slate-800 border-slate-600 text-white"
                                  placeholder="John Doe"
                                  required
                                />
                              </div>
                              
                              <div>
                                <label className="block text-sm font-medium text-slate-300 mb-2">Email Address *</label>
                                <Input
                                  type="email"
                                  value={formData.email}
                                  onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                                  className="bg-slate-800 border-slate-600 text-white"
                                  placeholder="<EMAIL>"
                                  required
                                />
                              </div>
                              
                              <div>
                                <label className="block text-sm font-medium text-slate-300 mb-2">Phone Number</label>
                                <Input
                                  value={formData.phone}
                                  onChange={(e) => setFormData(prev => ({ ...prev, phone: e.target.value }))}
                                  className="bg-slate-800 border-slate-600 text-white"
                                  placeholder="+****************"
                                />
                              </div>
                              
                              <div>
                                <label className="block text-sm font-medium text-slate-300 mb-2">Company Name</label>
                                <Input
                                  value={formData.company}
                                  onChange={(e) => setFormData(prev => ({ ...prev, company: e.target.value }))}
                                  className="bg-slate-800 border-slate-600 text-white"
                                  placeholder="Your Company"
                                />
                              </div>
                              
                              <div>
                                <label className="block text-sm font-medium text-slate-300 mb-2">Website URL</label>
                                <Input
                                  value={formData.website}
                                  onChange={(e) => setFormData(prev => ({ ...prev, website: e.target.value }))}
                                  className="bg-slate-800 border-slate-600 text-white"
                                  placeholder="https://yourwebsite.com"
                                />
                              </div>
                              
                              <div>
                                <label className="block text-sm font-medium text-slate-300 mb-2">Industry</label>
                                <Select value={formData.industry} onValueChange={(value) => setFormData(prev => ({ ...prev, industry: value }))}>
                                  <SelectTrigger className="bg-slate-800 border-slate-600 text-white">
                                    <SelectValue placeholder="Select your industry" />
                                  </SelectTrigger>
                                  <SelectContent className="bg-slate-800 border-slate-600">
                                    <SelectItem value="technology">Technology</SelectItem>
                                    <SelectItem value="healthcare">Healthcare</SelectItem>
                                    <SelectItem value="finance">Finance</SelectItem>
                                    <SelectItem value="retail">Retail</SelectItem>
                                    <SelectItem value="manufacturing">Manufacturing</SelectItem>
                                    <SelectItem value="services">Professional Services</SelectItem>
                                    <SelectItem value="other">Other</SelectItem>
                                  </SelectContent>
                                </Select>
                              </div>
                            </div>

                            <div className="mb-8">
                              <label className="block text-sm font-medium text-slate-300 mb-2">Additional Information</label>
                              <Textarea
                                value={formData.additionalInfo}
                                onChange={(e) => setFormData(prev => ({ ...prev, additionalInfo: e.target.value }))}
                                className="bg-slate-800 border-slate-600 text-white"
                                placeholder="Any additional details about your project or specific requirements..."
                              />
                            </div>
                            
                            <div className="flex justify-between">
                              <Button onClick={handleBack} variant="outline" className="border-slate-600 text-slate-300">
                                Back
                              </Button>
                              <Button 
                                type="submit"
                                className="bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700"
                              >
                                Get My Quote
                                <Zap className="ml-2 w-4 h-4" />
                              </Button>
                            </div>
                          </div>
                        )}
                      </form>
                    </CardContent>
                  </Card>
                </div>

                {/* Quote Summary */}
                <div className="lg:col-span-1">
                  <Card className="bg-gradient-to-br from-amber-600 to-amber-700 border-none sticky top-8">
                    <CardContent className="p-8">
                      <div className="flex items-center mb-6">
                        <Calculator className="w-6 h-6 text-white mr-3" />
                        <h3 className="text-2xl font-bold text-white">Quote Summary</h3>
                      </div>
                      
                      {formData.services.length > 0 && (
                        <div className="mb-6">
                          <h4 className="text-white font-semibold mb-3">Selected Services:</h4>
                          <ul className="space-y-2">
                            {formData.services.map(serviceId => {
                              const service = services.find(s => s.id === serviceId);
                              return service ? (
                                <li key={serviceId} className="flex justify-between text-white/90">
                                  <span>{service.name}</span>
                                  <span>${service.price.toLocaleString()}</span>
                                </li>
                              ) : null;
                            })}
                          </ul>
                        </div>
                      )}
                      
                      {formData.goals.length > 0 && (
                        <div className="mb-6">
                          <h4 className="text-white font-semibold mb-3">Your Goals:</h4>
                          <ul className="space-y-1">
                            {formData.goals.map(goal => (
                              <li key={goal} className="flex items-center text-white/90">
                                <CheckCircle className="w-4 h-4 mr-2" />
                                <span className="text-sm">{goal}</span>
                              </li>
                            ))}
                          </ul>
                        </div>
                      )}
                      
                      {estimatedCost > 0 && (
                        <div className="border-t border-white/20 pt-6">
                          <div className="text-center">
                            <div className="text-sm text-white/80 mb-2">Estimated Monthly Investment:</div>
                            <div className="text-4xl font-bold text-white mb-2">
                              ${estimatedCost.toLocaleString()}
                            </div>
                            <div className="text-sm text-white/80">
                              *Final pricing may vary based on specific requirements
                            </div>
                          </div>
                        </div>
                      )}
                      
                      <div className="mt-6 p-4 bg-white/10 rounded-lg">
                        <h4 className="text-white font-semibold mb-2">What's Included:</h4>
                        <ul className="text-sm text-white/90 space-y-1">
                          <li>• Free strategy consultation</li>
                          <li>• Custom campaign setup</li>
                          <li>• Dedicated account manager</li>
                          <li>• Monthly performance reports</li>
                          <li>• 24/7 support</li>
                        </ul>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </div>
          </div>
        </section>
      </main>
      
      <Footer />
    </div>
  );
};

export default Quote;
