import React from 'react';
import { Link } from 'react-router-dom';
import { Shirt, Search, TrendingUp, Users, CheckCircle, Building, Star, Crown, Target, Zap, ArrowRight } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';
import Sidebar from '@/components/Sidebar';

const FashionRetailSeo = () => {
  const stats = [
    {
      metric: '1,400+',
      description: 'Fashion & Retail Brands Optimized',
      detail: 'Across all fashion sectors'
    },
    {
      metric: '4,500%',
      description: 'Average Sales Growth',
      detail: 'For fashion & retail clients'
    },
    {
      metric: '₹720Cr+',
      description: 'Fashion Revenue Generated',
      detail: 'Through SEO campaigns'
    },
    {
      metric: '96%',
      description: 'Fashion Keywords Ranking',
      detail: 'Top 10 positions achieved'
    }
  ];

  const achievements = [
    'Top Fashion & Retail SEO Company in India',
    'E-commerce Fashion SEO Specialists',
    'Luxury Brand SEO Experts',
    'Fashion Marketplace SEO Leaders',
    'Retail Chain SEO Champions',
    'Fashion Influencer SEO Pioneers'
  ];

  const fashionSpecializations = [
    {
      type: 'E-commerce Fashion SEO',
      description: 'Comprehensive SEO for online fashion stores, marketplaces, and e-commerce platforms',
      icon: Building,
      features: ['Fashion E-commerce SEO', 'Product Page Optimization', 'Category Page SEO', 'Fashion Marketplace SEO', 'Online Store SEO', 'Fashion App SEO']
    },
    {
      type: 'Luxury & Designer Brand SEO',
      description: 'Advanced SEO for luxury fashion brands, designer labels, and premium fashion houses',
      icon: Star,
      features: ['Luxury Brand SEO', 'Designer Label SEO', 'High-End Fashion SEO', 'Premium Brand SEO', 'Couture Fashion SEO', 'Luxury Retail SEO']
    },
    {
      type: 'Fashion Retail Chain SEO',
      description: 'Strategic SEO for fashion retail chains, multi-location stores, and franchise operations',
      icon: Crown,
      features: ['Retail Chain SEO', 'Multi-Location SEO', 'Fashion Franchise SEO', 'Store Locator SEO', 'Regional Fashion SEO', 'Retail Brand SEO']
    },
    {
      type: 'Fashion Manufacturing & Wholesale SEO',
      description: 'Specialized SEO for fashion manufacturers, wholesalers, and B2B fashion businesses',
      icon: Target,
      features: ['Fashion Manufacturing SEO', 'Wholesale Fashion SEO', 'B2B Fashion SEO', 'Garment Export SEO', 'Fashion Supplier SEO', 'Textile Business SEO']
    }
  ];

  const fashionSectors = [
    { name: 'E-commerce Fashion', clients: '420+', growth: '4,800%' },
    { name: 'Luxury Brands', clients: '280+', growth: '4,200%' },
    { name: 'Retail Chains', clients: '320+', growth: '3,900%' },
    { name: 'Fashion Manufacturing', clients: '180+', growth: '3,600%' },
    { name: 'Fashion Accessories', clients: '150+', growth: '4,100%' },
    { name: 'Fashion Marketplaces', clients: '120+', growth: '5,200%' }
  ];

  const caseStudies = [
    {
      client: 'Leading Fashion E-commerce Platform',
      industry: 'Online Fashion Retail',
      challenge: 'Fashion e-commerce platform needed to compete with global fashion giants',
      result: '5,200% online sales increase',
      metrics: ['2,200+ fashion keywords in top 3', '₹280Cr+ e-commerce revenue', '850% increase in online orders']
    },
    {
      client: 'Luxury Fashion Brand',
      industry: 'Premium Fashion Retail',
      challenge: 'Luxury brand needed to establish digital presence while maintaining exclusivity',
      result: '4,200% brand visibility growth',
      metrics: ['1,400+ luxury fashion keywords ranking', '₹185Cr+ luxury sales revenue', '620% increase in premium customers']
    },
    {
      client: 'Fashion Retail Chain',
      industry: 'Multi-Location Fashion Retail',
      challenge: 'Fashion chain needed to dominate local search across 100+ store locations',
      result: '3,900% store footfall increase',
      metrics: ['1,800+ retail keywords in top 5', '₹165Cr+ retail revenue', '480% increase in store visits']
    }
  ];

  const fashionSeoStrategies = [
    {
      strategy: 'Visual Fashion SEO',
      description: 'Optimize fashion imagery, product photos, and visual content for search',
      benefits: ['Image search visibility', 'Visual discovery', 'Product showcase', 'Fashion inspiration']
    },
    {
      strategy: 'Seasonal Fashion SEO',
      description: 'Target seasonal fashion trends, collections, and style preferences',
      benefits: ['Trend optimization', 'Seasonal visibility', 'Collection promotion', 'Style authority']
    },
    {
      strategy: 'Fashion Influencer SEO',
      description: 'Leverage fashion influencer content and collaborations for SEO',
      benefits: ['Influencer authority', 'Social proof', 'Trend validation', 'Style credibility']
    },
    {
      strategy: 'Local Fashion SEO',
      description: 'Target local fashion searches and nearby fashion store discovery',
      benefits: ['Local store visibility', 'Regional fashion trends', 'Store locator optimization', 'Local fashion authority']
    }
  ];

  return (
    <div className="min-h-screen bg-slate-900 relative">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        <div className="container mx-auto px-6 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            <div className="lg:col-span-3">
              <section className="pt-24 pb-16">
                <div className="max-w-4xl">
                  <div className="inline-flex items-center space-x-2 bg-pink-500/20 border border-pink-500/30 rounded-full px-6 py-2 mb-8">
                    <Shirt className="w-4 h-4 text-pink-400" />
                    <span className="text-pink-400 font-medium">Fashion & Retail SEO • Style Optimization Excellence</span>
                  </div>
                  
                  <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
                    #1 Fashion & Retail SEO Company in
                    <span className="bg-gradient-to-r from-pink-400 to-pink-600 bg-clip-text text-transparent"> India</span>
                  </h1>
                  
                  <p className="text-xl text-slate-300 mb-8">
                    Premier fashion & retail SEO services offering comprehensive search engine optimization solutions for fashion brands, e-commerce stores, luxury labels, and retail chains. Our fashion SEO company provides professional SEO services with e-commerce fashion SEO optimization, luxury brand SEO, fashion retail chain SEO, and fashion manufacturing SEO. Serving 1,400+ fashion & retail brands across all style sectors with proven ₹720Cr+ revenue generation and 4,500% average sales growth for fashion & retail clients through strategic search engine optimization and fashion digital marketing excellence.
                  </p>
                  
                  <div className="flex flex-col sm:flex-row gap-4 mb-8">
                    <Button asChild size="lg" className="bg-gradient-to-r from-pink-500 to-pink-600 hover:from-pink-600 hover:to-pink-700 text-white text-lg px-8 py-4">
                      <Link to="/quote">Get Free Fashion & Retail SEO Audit</Link>
                    </Button>
                    <Button asChild size="lg" variant="outline" className="border-pink-500 text-pink-400 hover:bg-pink-500 hover:text-white text-lg px-8 py-4">
                      <Link to="/contact">Call Fashion SEO Experts: +91-8708577598</Link>
                    </Button>
                  </div>

                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                    {achievements.map((achievement, index) => (
                      <div key={index} className="flex items-center justify-center space-x-2 bg-slate-800/50 rounded-lg p-3">
                        <CheckCircle className="w-4 h-4 text-pink-400 flex-shrink-0" />
                        <span className="text-slate-300">{achievement}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </section>

              <section className="mb-16 bg-slate-800/50 rounded-2xl p-8">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Fashion & Retail SEO
                    <span className="bg-gradient-to-r from-pink-400 to-pink-600 bg-clip-text text-transparent"> Success Metrics</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Measurable fashion & retail SEO results from brands across all style sectors and markets.
                  </p>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
                  {stats.map((stat, index) => (
                    <div key={index} className="text-center">
                      <div className="text-4xl font-bold text-pink-400 mb-2">{stat.metric}</div>
                      <div className="text-white font-semibold mb-1">{stat.description}</div>
                      <div className="text-slate-400">{stat.detail}</div>
                    </div>
                  ))}
                </div>
              </section>

              <section className="mb-16">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Fashion & Retail SEO Strategies We
                    <span className="bg-gradient-to-r from-pink-400 to-pink-600 bg-clip-text text-transparent"> Master</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Specialized SEO strategies designed for fashion & retail success across all style platforms and brands.
                  </p>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  {fashionSpecializations.map((spec, index) => (
                    <Card key={index} className="bg-slate-900/80 border-pink-500/20 hover:border-pink-500/40 transition-all duration-300">
                      <CardContent className="p-8">
                        <div className="w-12 h-12 bg-gradient-to-r from-pink-500 to-pink-600 rounded-lg flex items-center justify-center mb-6">
                          <spec.icon className="w-6 h-6 text-white" />
                        </div>
                        <h3 className="text-xl font-bold text-white mb-4">{spec.type}</h3>
                        <p className="text-slate-300 mb-6">{spec.description}</p>
                        <ul className="space-y-2">
                          {spec.features.map((feature, idx) => (
                            <li key={idx} className="flex items-center text-slate-400 text-sm">
                              <CheckCircle className="w-3 h-3 text-pink-400 mr-2 flex-shrink-0" />
                              {feature}
                            </li>
                          ))}
                        </ul>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>

              {/* Fashion Sectors */}
              <section className="mb-16 bg-slate-800/30 rounded-2xl p-8">
                <h3 className="text-2xl font-bold text-white mb-6 text-center">
                  Fashion Industry Sector Expertise
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-6">
                  {fashionSectors.map((sector, index) => (
                    <div key={index} className="text-center bg-slate-900/50 rounded-lg p-4">
                      <h4 className="text-pink-400 font-semibold mb-2">{sector.name}</h4>
                      <div className="text-2xl font-bold text-white mb-1">{sector.clients}</div>
                      <div className="text-slate-400 text-sm mb-2">Brands Served</div>
                      <div className="text-green-400 font-semibold text-sm">{sector.growth} avg growth</div>
                    </div>
                  ))}
                </div>
              </section>

              {/* Fashion SEO Strategies */}
              <section className="mb-16 bg-slate-800/30 rounded-2xl p-8">
                <h3 className="text-2xl font-bold text-white mb-6 text-center">
                  Advanced Fashion & Retail SEO Strategies
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                  {fashionSeoStrategies.map((strategy, index) => (
                    <div key={index} className="bg-slate-900/50 rounded-lg p-6">
                      <h4 className="text-pink-400 font-semibold mb-3">{strategy.strategy}</h4>
                      <p className="text-slate-300 text-sm mb-4">{strategy.description}</p>
                      <ul className="space-y-1">
                        {strategy.benefits.map((benefit, idx) => (
                          <li key={idx} className="text-slate-400 text-xs flex items-center">
                            <CheckCircle className="w-3 h-3 text-pink-400 mr-2 flex-shrink-0" />
                            {benefit}
                          </li>
                        ))}
                      </ul>
                    </div>
                  ))}
                </div>
              </section>

              {/* Case Studies */}
              <section className="mb-16">
                <h3 className="text-2xl font-bold text-white mb-8 text-center">
                  Fashion & Retail SEO Success Stories
                </h3>
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                  {caseStudies.map((study, index) => (
                    <Card key={index} className="bg-slate-900/60 border-pink-500/20 hover:border-pink-500/40 transition-all duration-300">
                      <CardContent className="p-6">
                        <h4 className="text-pink-400 font-semibold mb-2">{study.client}</h4>
                        <p className="text-slate-400 text-sm mb-3">{study.industry}</p>
                        <p className="text-slate-300 text-sm mb-4">{study.challenge}</p>
                        <div className="bg-slate-800/50 rounded p-3 mb-4">
                          <div className="text-green-400 font-semibold mb-2">{study.result}</div>
                          <ul className="space-y-1">
                            {study.metrics.map((metric, idx) => (
                              <li key={idx} className="text-slate-400 text-xs flex items-center">
                                <CheckCircle className="w-3 h-3 text-pink-400 mr-2 flex-shrink-0" />
                                {metric}
                              </li>
                            ))}
                          </ul>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>

              {/* Internal Linking Section */}
              <section className="mb-16 bg-slate-800/30 rounded-2xl p-8">
                <h3 className="text-2xl font-bold text-white mb-6 text-center">
                  Complete Fashion & Retail Digital Marketing Solutions
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  <Link to="/services/ppc" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-pink-400 font-semibold mb-2 group-hover:text-pink-300">Fashion & Retail Google Ads</h4>
                    <p className="text-slate-400 text-sm">PPC advertising for fashion brands and retail stores</p>
                  </Link>
                  <Link to="/services/social-media" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-pink-400 font-semibold mb-2 group-hover:text-pink-300">Fashion Social Media Marketing</h4>
                    <p className="text-slate-400 text-sm">Social media marketing for fashion and retail brands</p>
                  </Link>
                  <Link to="/services/content" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-pink-400 font-semibold mb-2 group-hover:text-pink-300">Fashion Content Marketing</h4>
                    <p className="text-slate-400 text-sm">Style content creation and fashion marketing</p>
                  </Link>
                  <Link to="/services/email" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-pink-400 font-semibold mb-2 group-hover:text-pink-300">Fashion Email Marketing</h4>
                    <p className="text-slate-400 text-sm">Fashion newsletters and customer retention campaigns</p>
                  </Link>
                  <Link to="/services/web-development" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-pink-400 font-semibold mb-2 group-hover:text-pink-300">Fashion E-commerce Development</h4>
                    <p className="text-slate-400 text-sm">Fashion e-commerce platforms and retail website development</p>
                  </Link>
                  <Link to="/portfolio" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-pink-400 font-semibold mb-2 group-hover:text-pink-300">Fashion & Retail Success Stories</h4>
                    <p className="text-slate-400 text-sm">See proven results from our fashion & retail clients</p>
                  </Link>
                </div>
              </section>

              <section className="bg-gradient-to-r from-pink-600 to-pink-500 rounded-2xl p-8 text-center">
                <h2 className="text-3xl md:text-5xl font-bold mb-6">Ready to Style Your Fashion Business Success?</h2>
                <p className="text-xl mb-8">
                  Join 1,400+ fashion brands that trust GOD Digital Marketing for style SEO success. Proven strategies that deliver 4,500% sales growth and ₹720Cr+ revenue generation.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button asChild size="lg" className="bg-white text-pink-600 hover:bg-slate-100 text-lg px-8 py-4">
                    <Link to="/quote">Get Free Fashion & Retail SEO Audit</Link>
                  </Button>
                  <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-pink-600 text-lg px-8 py-4">
                    <Link to="/contact">Call Fashion SEO Experts: +91-8708577598</Link>
                  </Button>
                </div>
              </section>
            </div>

            <div className="lg:col-span-1">
              <Sidebar currentService="seo" />
            </div>
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
};

export default FashionRetailSeo;
