import React from 'react';
import { Link } from 'react-router-dom';
import { MapPin, Search, TrendingUp, Users, CheckCircle, Building, Star, Crown, Target, Zap, ArrowRight } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';
import Sidebar from '@/components/Sidebar';

const LocalBusinessSeo = () => {
  const stats = [
    {
      metric: '4,200+',
      description: 'Local Businesses Optimized',
      detail: 'Across all service areas'
    },
    {
      metric: '1,650%',
      description: 'Average Local Traffic Growth',
      detail: 'For local business clients'
    },
    {
      metric: '₹320Cr+',
      description: 'Local Revenue Generated',
      detail: 'Through local SEO campaigns'
    },
    {
      metric: '97%',
      description: 'Local Keywords Ranking',
      detail: 'Top 3 local positions achieved'
    }
  ];

  const achievements = [
    'Top Local SEO Company in India',
    'Google My Business Specialists',
    'Local Citation Building Experts',
    'Multi-Location SEO Leaders',
    'Local Review Management Champions',
    'Hyperlocal Marketing Pioneers'
  ];

  const localSpecializations = [
    {
      type: 'Google My Business Optimization',
      description: 'Complete GMB optimization for maximum local visibility and customer engagement',
      icon: Building,
      features: ['GMB Profile Optimization', 'Local Keyword Optimization', 'Review Management', 'Local Photo Optimization', 'GMB Post Management', 'Local Q&A Optimization']
    },
    {
      type: 'Local Citation Building',
      description: 'Comprehensive local citation building across all major directories and platforms',
      icon: Star,
      features: ['Directory Submissions', 'NAP Consistency', 'Industry-Specific Citations', 'Local Business Listings', 'Citation Cleanup', 'Local Link Building']
    },
    {
      type: 'Multi-Location SEO',
      description: 'Advanced SEO strategies for businesses with multiple locations and service areas',
      icon: Crown,
      features: ['Location Page Optimization', 'Multi-Location Strategy', 'Local Schema Markup', 'Geo-Targeted Content', 'Location-Based Landing Pages', 'Regional SEO Management']
    },
    {
      type: 'Local Review Management',
      description: 'Professional review management and reputation building for local businesses',
      icon: Target,
      features: ['Review Generation Campaigns', 'Review Response Management', 'Reputation Monitoring', 'Review Platform Optimization', 'Customer Feedback Systems', 'Local Trust Building']
    }
  ];

  const localBusinessTypes = [
    { name: 'Restaurants & Food', clients: '980+', growth: '1,850%' },
    { name: 'Healthcare & Medical', clients: '720+', growth: '1,650%' },
    { name: 'Home Services', clients: '850+', growth: '1,920%' },
    { name: 'Retail & Shopping', clients: '680+', growth: '1,480%' },
    { name: 'Professional Services', clients: '520+', growth: '1,380%' },
    { name: 'Automotive Services', clients: '450+', growth: '1,580%' }
  ];

  const caseStudies = [
    {
      client: 'Local Restaurant Chain',
      industry: 'Food & Dining',
      challenge: 'Multi-location restaurant needed to dominate local search across 15 locations',
      result: '2,400% local visibility increase',
      metrics: ['850+ local keywords in top 3', '₹4.2Cr+ revenue generated', '380% increase in foot traffic']
    },
    {
      client: 'Medical Practice Group',
      industry: 'Healthcare Services',
      challenge: 'Healthcare practice needed to attract local patients across multiple specialties',
      result: '1,800% patient inquiry growth',
      metrics: ['620+ medical keywords ranking locally', '₹2.8Cr+ revenue boost', '450% increase in appointment bookings']
    },
    {
      client: 'Home Services Company',
      industry: 'Home Improvement',
      challenge: 'Local contractor needed to compete with large national chains',
      result: '2,100% local lead increase',
      metrics: ['480+ service keywords in top 3', '₹3.5Cr+ project revenue', '320% increase in service calls']
    }
  ];

  const localSeoStrategies = [
    {
      strategy: 'Hyperlocal Targeting',
      description: 'Target specific neighborhoods and micro-locations for maximum relevance',
      benefits: ['Higher conversion rates', 'Reduced competition', 'Better local relevance', 'Increased foot traffic']
    },
    {
      strategy: 'Local Content Marketing',
      description: 'Create location-specific content that resonates with local audiences',
      benefits: ['Local authority building', 'Community engagement', 'Local keyword ranking', 'Brand awareness']
    },
    {
      strategy: 'Voice Search Optimization',
      description: 'Optimize for voice search queries and local voice search patterns',
      benefits: ['Future-ready SEO', 'Mobile optimization', 'Conversational queries', 'Local discovery']
    },
    {
      strategy: 'Local Link Building',
      description: 'Build high-quality local links from community organizations and local businesses',
      benefits: ['Local authority', 'Community connections', 'Referral traffic', 'Local trust signals']
    }
  ];

  return (
    <div className="min-h-screen bg-slate-900 relative">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        <div className="container mx-auto px-6 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            <div className="lg:col-span-3">
              <section className="pt-24 pb-16">
                <div className="max-w-4xl">
                  <div className="inline-flex items-center space-x-2 bg-emerald-500/20 border border-emerald-500/30 rounded-full px-6 py-2 mb-8">
                    <MapPin className="w-4 h-4 text-emerald-400" />
                    <span className="text-emerald-400 font-medium">Local Business SEO • Hyperlocal Optimization Excellence</span>
                  </div>
                  
                  <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
                    #1 Local Business SEO Company in
                    <span className="bg-gradient-to-r from-emerald-400 to-emerald-600 bg-clip-text text-transparent"> India</span>
                  </h1>
                  
                  <p className="text-xl text-slate-300 mb-8">
                    Premier local business SEO services offering comprehensive search engine optimization solutions for local businesses including Google My Business optimization, local citation building, multi-location SEO, and hyperlocal marketing. Our local SEO company provides professional SEO services with local keyword optimization, review management, local link building, and local conversion optimization. Serving 4,200+ local businesses across all service areas with proven ₹320Cr+ revenue generation and 1,650% average local traffic growth for local business clients through strategic local search optimization and hyperlocal digital marketing excellence.
                  </p>
                  
                  <div className="flex flex-col sm:flex-row gap-4 mb-8">
                    <Button asChild size="lg" className="bg-gradient-to-r from-emerald-500 to-emerald-600 hover:from-emerald-600 hover:to-emerald-700 text-white text-lg px-8 py-4">
                      <Link to="/quote">Get Free Local SEO Audit</Link>
                    </Button>
                    <Button asChild size="lg" variant="outline" className="border-emerald-500 text-emerald-400 hover:bg-emerald-500 hover:text-white text-lg px-8 py-4">
                      <Link to="/contact">Call Local SEO Experts: +91-8708577598</Link>
                    </Button>
                  </div>

                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                    {achievements.map((achievement, index) => (
                      <div key={index} className="flex items-center justify-center space-x-2 bg-slate-800/50 rounded-lg p-3">
                        <CheckCircle className="w-4 h-4 text-emerald-400 flex-shrink-0" />
                        <span className="text-slate-300">{achievement}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </section>

              <section className="mb-16 bg-slate-800/50 rounded-2xl p-8">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Local Business SEO
                    <span className="bg-gradient-to-r from-emerald-400 to-emerald-600 bg-clip-text text-transparent"> Success Metrics</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Measurable local SEO results from businesses across all local service areas and industries.
                  </p>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
                  {stats.map((stat, index) => (
                    <div key={index} className="text-center">
                      <div className="text-4xl font-bold text-emerald-400 mb-2">{stat.metric}</div>
                      <div className="text-white font-semibold mb-1">{stat.description}</div>
                      <div className="text-slate-400">{stat.detail}</div>
                    </div>
                  ))}
                </div>
              </section>

              <section className="mb-16">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Local SEO Strategies We
                    <span className="bg-gradient-to-r from-emerald-400 to-emerald-600 bg-clip-text text-transparent"> Master</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Specialized local SEO strategies designed for local business success across all service areas and markets.
                  </p>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  {localSpecializations.map((spec, index) => (
                    <Card key={index} className="bg-slate-900/80 border-emerald-500/20 hover:border-emerald-500/40 transition-all duration-300">
                      <CardContent className="p-8">
                        <div className="w-12 h-12 bg-gradient-to-r from-emerald-500 to-emerald-600 rounded-lg flex items-center justify-center mb-6">
                          <spec.icon className="w-6 h-6 text-white" />
                        </div>
                        <h3 className="text-xl font-bold text-white mb-4">{spec.type}</h3>
                        <p className="text-slate-300 mb-6">{spec.description}</p>
                        <ul className="space-y-2">
                          {spec.features.map((feature, idx) => (
                            <li key={idx} className="flex items-center text-slate-400 text-sm">
                              <CheckCircle className="w-3 h-3 text-emerald-400 mr-2 flex-shrink-0" />
                              {feature}
                            </li>
                          ))}
                        </ul>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>

              {/* Local Business Types */}
              <section className="mb-16 bg-slate-800/30 rounded-2xl p-8">
                <h3 className="text-2xl font-bold text-white mb-6 text-center">
                  Local Business Type Expertise
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-6">
                  {localBusinessTypes.map((business, index) => (
                    <div key={index} className="text-center bg-slate-900/50 rounded-lg p-4">
                      <h4 className="text-emerald-400 font-semibold mb-2">{business.name}</h4>
                      <div className="text-2xl font-bold text-white mb-1">{business.clients}</div>
                      <div className="text-slate-400 text-sm mb-2">Businesses Served</div>
                      <div className="text-green-400 font-semibold text-sm">{business.growth} avg growth</div>
                    </div>
                  ))}
                </div>
              </section>

              {/* Local SEO Strategies */}
              <section className="mb-16 bg-slate-800/30 rounded-2xl p-8">
                <h3 className="text-2xl font-bold text-white mb-6 text-center">
                  Advanced Local SEO Strategies
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                  {localSeoStrategies.map((strategy, index) => (
                    <div key={index} className="bg-slate-900/50 rounded-lg p-6">
                      <h4 className="text-emerald-400 font-semibold mb-3">{strategy.strategy}</h4>
                      <p className="text-slate-300 text-sm mb-4">{strategy.description}</p>
                      <ul className="space-y-1">
                        {strategy.benefits.map((benefit, idx) => (
                          <li key={idx} className="text-slate-400 text-xs flex items-center">
                            <CheckCircle className="w-3 h-3 text-emerald-400 mr-2 flex-shrink-0" />
                            {benefit}
                          </li>
                        ))}
                      </ul>
                    </div>
                  ))}
                </div>
              </section>

              {/* Case Studies */}
              <section className="mb-16">
                <h3 className="text-2xl font-bold text-white mb-8 text-center">
                  Local Business SEO Success Stories
                </h3>
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                  {caseStudies.map((study, index) => (
                    <Card key={index} className="bg-slate-900/60 border-emerald-500/20 hover:border-emerald-500/40 transition-all duration-300">
                      <CardContent className="p-6">
                        <h4 className="text-emerald-400 font-semibold mb-2">{study.client}</h4>
                        <p className="text-slate-400 text-sm mb-3">{study.industry}</p>
                        <p className="text-slate-300 text-sm mb-4">{study.challenge}</p>
                        <div className="bg-slate-800/50 rounded p-3 mb-4">
                          <div className="text-green-400 font-semibold mb-2">{study.result}</div>
                          <ul className="space-y-1">
                            {study.metrics.map((metric, idx) => (
                              <li key={idx} className="text-slate-400 text-xs flex items-center">
                                <CheckCircle className="w-3 h-3 text-emerald-400 mr-2 flex-shrink-0" />
                                {metric}
                              </li>
                            ))}
                          </ul>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>

              {/* Internal Linking Section */}
              <section className="mb-16 bg-slate-800/30 rounded-2xl p-8">
                <h3 className="text-2xl font-bold text-white mb-6 text-center">
                  Complete Local Digital Marketing Solutions
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  <Link to="/services/ppc" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-emerald-400 font-semibold mb-2 group-hover:text-emerald-300">Local Google Ads</h4>
                    <p className="text-slate-400 text-sm">Location-targeted PPC advertising for local businesses</p>
                  </Link>
                  <Link to="/services/social-media" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-emerald-400 font-semibold mb-2 group-hover:text-emerald-300">Local Social Media</h4>
                    <p className="text-slate-400 text-sm">Community-focused social media marketing</p>
                  </Link>
                  <Link to="/services/content" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-emerald-400 font-semibold mb-2 group-hover:text-emerald-300">Local Content Marketing</h4>
                    <p className="text-slate-400 text-sm">Location-specific content and community engagement</p>
                  </Link>
                  <Link to="/services/email" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-emerald-400 font-semibold mb-2 group-hover:text-emerald-300">Local Email Marketing</h4>
                    <p className="text-slate-400 text-sm">Community newsletters and local customer retention</p>
                  </Link>
                  <Link to="/services/web-development" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-emerald-400 font-semibold mb-2 group-hover:text-emerald-300">Local Website Development</h4>
                    <p className="text-slate-400 text-sm">Location-optimized websites for local businesses</p>
                  </Link>
                  <Link to="/portfolio" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-emerald-400 font-semibold mb-2 group-hover:text-emerald-300">Local Success Stories</h4>
                    <p className="text-slate-400 text-sm">See proven results from our local business clients</p>
                  </Link>
                </div>
              </section>

              <section className="bg-gradient-to-r from-emerald-600 to-emerald-500 rounded-2xl p-8 text-center">
                <h2 className="text-3xl md:text-5xl font-bold mb-6">Ready to Dominate Local Search?</h2>
                <p className="text-xl mb-8">
                  Join 4,200+ local businesses that trust GOD Digital Marketing for local SEO success. Proven strategies that deliver 1,650% traffic growth and ₹320Cr+ revenue generation.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button asChild size="lg" className="bg-white text-emerald-600 hover:bg-slate-100 text-lg px-8 py-4">
                    <Link to="/quote">Get Free Local SEO Audit</Link>
                  </Button>
                  <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-emerald-600 text-lg px-8 py-4">
                    <Link to="/contact">Call Local SEO Experts: +91-8708577598</Link>
                  </Button>
                </div>
              </section>
            </div>

            <div className="lg:col-span-1">
              <Sidebar currentService="seo" />
            </div>
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
};

export default LocalBusinessSeo;
