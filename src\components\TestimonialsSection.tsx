import React from 'react';
import { Star, Quote, Building, MapPin, TrendingUp, Users, Award, Crown } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';

const TestimonialsSection = () => {
  const testimonials = [
    {
      id: 1,
      name: "<PERSON>",
      position: "Founder & CEO",
      company: "EzOutdoors",
      industry: "E-commerce",
      location: "Dubai, UAE",
      avatar: "/api/placeholder/60/60",
      rating: 5,
      text: "GOD Digital Marketing transformed our online presence completely. From zero visibility to dominating search results across UAE, Saudi Arabia, and Kuwait. Our revenue increased by 2,400% in just 6 months!",
      results: {
        metric: "2,400%",
        description: "Revenue Growth",
        timeframe: "6 months"
      },
      verified: true,
      featured: true
    },
    {
      id: 2,
      name: "Dr. <PERSON>",
      position: "Medical Director",
      company: "FH Wellness",
      industry: "Healthcare",
      location: "Dubai, UAE",
      avatar: "/api/placeholder/60/60",
      rating: 5,
      text: "Our corporate wellness programs are now in high demand. We've become the trusted wellness partner for major companies in Dubai. The SEO strategy was perfectly tailored for the healthcare industry.",
      results: {
        metric: "380%",
        description: "Lead Increase",
        timeframe: "5 months"
      },
      verified: true,
      featured: true
    },
    {
      id: 3,
      name: "<PERSON> & Raaj",
      position: "Co-Founders",
      company: "VR Dance World",
      industry: "Entertainment",
      location: "Dubai, UAE",
      avatar: "/api/placeholder/60/60",
      rating: 5,
      text: "We went from struggling to find students to having a waiting list. Our classes are now fully booked! The local SEO strategy was incredible - we're now the top dance academy in Dubai.",
      results: {
        metric: "450%",
        description: "Booking Increase",
        timeframe: "3 months"
      },
      verified: true,
      featured: false
    },
    {
      id: 4,
      name: "Raaj Patel",
      position: "Business Development Manager",
      company: "Smart Whip UAE",
      industry: "B2B Trading",
      location: "Dubai, UAE",
      avatar: "/api/placeholder/60/60",
      rating: 5,
      text: "Our B2B leads increased dramatically. We're now the go-to supplier for professional kitchens across Dubai. The industry-specific SEO approach was exactly what we needed.",
      results: {
        metric: "280%",
        description: "B2B Leads",
        timeframe: "4 months"
      },
      verified: true,
      featured: false
    },
    {
      id: 5,
      name: "Dr. Maria Rodriguez",
      position: "Medical Director",
      company: "Golden Rose Med Spa",
      industry: "Medical Aesthetics",
      location: "Dubai, UAE",
      avatar: "/api/placeholder/60/60",
      rating: 5,
      text: "We're now the premier destination for aesthetic treatments in Dubai. Our booking calendar is consistently full. The luxury market positioning was perfect for our brand.",
      results: {
        metric: "520%",
        description: "Bookings Growth",
        timeframe: "4 months"
      },
      verified: true,
      featured: true
    },
    {
      id: 6,
      name: "Marko Petrovic",
      position: "CEO",
      company: "Balkland Tours",
      industry: "Travel & Tourism",
      location: "Balkan Region",
      avatar: "/api/placeholder/60/60",
      rating: 5,
      text: "Our Balkan tours are now booked months in advance. We've become the leading tour operator for the region. The international SEO strategy brought us customers from 25+ countries.",
      results: {
        metric: "3,200%",
        description: "Traffic Growth",
        timeframe: "6 months"
      },
      verified: true,
      featured: true
    }
  ];

  const industryStats = [
    { industry: "E-commerce", clients: "2,800+", avgGrowth: "1,850%" },
    { industry: "Healthcare", clients: "1,200+", avgGrowth: "1,420%" },
    { industry: "B2B Services", clients: "3,500+", avgGrowth: "980%" },
    { industry: "Local Business", clients: "4,200+", avgGrowth: "1,200%" },
    { industry: "Travel & Tourism", clients: "850+", avgGrowth: "2,100%" },
    { industry: "Entertainment", clients: "650+", avgGrowth: "1,680%" }
  ];

  return (
    <section className="py-16 bg-slate-800/30">
      <div className="container mx-auto px-6">
        {/* Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center space-x-2 bg-amber-500/20 border border-amber-500/30 rounded-full px-6 py-2 mb-8">
            <Award className="w-4 h-4 text-amber-400" />
            <span className="text-amber-400 font-medium">Client Success Stories • Real Results</span>
          </div>
          
          <h2 className="text-3xl md:text-5xl font-bold mb-6">
            What Our Clients
            <span className="bg-gradient-to-r from-amber-400 to-amber-600 bg-clip-text text-transparent"> Say About Us</span>
          </h2>
          
          <p className="text-xl text-slate-300 max-w-3xl mx-auto">
            Don't just take our word for it. See how we've transformed businesses across industries with measurable results and long-term partnerships.
          </p>
        </div>

        {/* Featured Testimonials */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-16">
          {testimonials.filter(t => t.featured).slice(0, 4).map((testimonial) => (
            <Card key={testimonial.id} className="bg-slate-900/80 border-amber-500/20 hover:border-amber-500/40 transition-all duration-300">
              <CardContent className="p-8">
                {/* Header */}
                <div className="flex items-start justify-between mb-6">
                  <div className="flex items-center space-x-4">
                    <img 
                      src={testimonial.avatar} 
                      alt={testimonial.name}
                      className="w-12 h-12 rounded-full object-cover"
                    />
                    <div>
                      <div className="flex items-center space-x-2">
                        <h4 className="text-white font-semibold">{testimonial.name}</h4>
                        {testimonial.verified && (
                          <div className="bg-green-500/20 text-green-400 px-2 py-1 rounded text-xs flex items-center">
                            <Crown className="w-3 h-3 mr-1" />
                            Verified
                          </div>
                        )}
                      </div>
                      <p className="text-slate-400 text-sm">{testimonial.position}</p>
                      <div className="flex items-center space-x-2 text-xs text-slate-500">
                        <Building className="w-3 h-3" />
                        <span>{testimonial.company}</span>
                        <span>•</span>
                        <MapPin className="w-3 h-3" />
                        <span>{testimonial.location}</span>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-1">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <Star key={i} className="w-4 h-4 text-amber-400 fill-current" />
                    ))}
                  </div>
                </div>

                {/* Quote */}
                <div className="relative mb-6">
                  <Quote className="w-8 h-8 text-amber-400/30 absolute -top-2 -left-2" />
                  <p className="text-slate-300 leading-relaxed pl-6">{testimonial.text}</p>
                </div>

                {/* Results */}
                <div className="bg-slate-800/50 rounded-lg p-4 flex items-center justify-between">
                  <div>
                    <div className="text-2xl font-bold text-amber-400">{testimonial.results.metric}</div>
                    <div className="text-slate-400 text-sm">{testimonial.results.description}</div>
                  </div>
                  <div className="text-right">
                    <div className="text-slate-300 font-semibold">{testimonial.results.timeframe}</div>
                    <div className="text-slate-500 text-sm">Timeline</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Industry Stats */}
        <div className="bg-slate-900/50 rounded-2xl p-8 mb-16">
          <h3 className="text-2xl font-bold text-white mb-8 text-center">Success Across Industries</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-6">
            {industryStats.map((stat, index) => (
              <div key={index} className="text-center">
                <div className="text-xl font-bold text-amber-400 mb-1">{stat.clients}</div>
                <div className="text-slate-300 text-sm mb-2">{stat.industry}</div>
                <div className="text-green-400 font-semibold text-sm">{stat.avgGrowth} avg growth</div>
              </div>
            ))}
          </div>
        </div>

        {/* All Testimonials Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {testimonials.filter(t => !t.featured).map((testimonial) => (
            <Card key={testimonial.id} className="bg-slate-900/60 border-slate-700/50 hover:border-amber-500/30 transition-all duration-300">
              <CardContent className="p-6">
                <div className="flex items-center space-x-3 mb-4">
                  <img 
                    src={testimonial.avatar} 
                    alt={testimonial.name}
                    className="w-10 h-10 rounded-full object-cover"
                  />
                  <div>
                    <h4 className="text-white font-semibold text-sm">{testimonial.name}</h4>
                    <p className="text-slate-400 text-xs">{testimonial.company}</p>
                  </div>
                  <div className="flex items-center space-x-1 ml-auto">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <Star key={i} className="w-3 h-3 text-amber-400 fill-current" />
                    ))}
                  </div>
                </div>
                
                <p className="text-slate-300 text-sm leading-relaxed mb-4">{testimonial.text}</p>
                
                <div className="bg-slate-800/50 rounded p-3 text-center">
                  <div className="text-lg font-bold text-amber-400">{testimonial.results.metric}</div>
                  <div className="text-slate-400 text-xs">{testimonial.results.description}</div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
};

export default TestimonialsSection;
