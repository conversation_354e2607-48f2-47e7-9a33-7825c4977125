import React from 'react';
import { Link } from 'react-router-dom';
import { ArrowRight, Mail, TrendingUp, Users, CheckCircle, Building, Star, Crown, Target, Zap } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';
import Sidebar from '@/components/Sidebar';

const EmailMarketingPanaji = () => {
  const emailServices = [
    'Email Marketing Strategy Panaji',
    'Email Campaign Management Panaji',
    'Tourism Email Marketing Panaji',
    'Hotel Email Campaigns Panaji',
    'Resort Email Marketing Panaji',
    'Travel Email Automation Panaji',
    'Hospitality Email Marketing Panaji',
    'Beach Resort Email Campaigns Panaji',
    'Goa Tourism Email Marketing Panaji',
    'Event Email Marketing Panaji',
    'Wedding Email Campaigns Panaji',
    'Restaurant Email Marketing Panaji',
    'Nightlife Email Campaigns Panaji',
    'Adventure Tourism Email Marketing Panaji',
    'Cruise Email Marketing Panaji',
    'Casino Email Campaigns Panaji',
    'Spa & Wellness Email Marketing Panaji',
    'Local Business Email Marketing Panaji',
    'Seasonal Campaign Management Panaji',
    'Newsletter Marketing Panaji'
  ];

  const industryFocus = [
    {
      industry: 'Tourism & Hospitality Email Marketing',
      description: 'Specialized email campaigns for Panaji\'s thriving tourism industry and hospitality sector',
      icon: Building,
      features: ['Hotel Booking Campaigns', 'Resort Promotion Emails', 'Travel Package Marketing', 'Seasonal Tourism Campaigns']
    },
    {
      industry: 'Event & Wedding Email Marketing',
      description: 'Strategic email marketing for Goa\'s destination wedding and event management industry',
      icon: Star,
      features: ['Wedding Email Campaigns', 'Event Promotion Emails', 'Venue Marketing Campaigns', 'Destination Wedding Marketing']
    },
    {
      industry: 'Entertainment & Nightlife Email Marketing',
      description: 'Comprehensive email marketing for Panaji\'s entertainment venues and nightlife businesses',
      icon: Crown,
      features: ['Club Promotion Emails', 'Casino Marketing Campaigns', 'Entertainment Event Emails', 'Nightlife Newsletter Marketing']
    },
    {
      industry: 'Local Business Email Marketing',
      description: 'Targeted email campaigns for Panaji\'s local businesses and service providers',
      icon: Target,
      features: ['Restaurant Email Marketing', 'Retail Email Campaigns', 'Service Provider Marketing', 'Local Event Promotion']
    }
  ];

  const localEmailFeatures = [
    'Panaji Tourism Email Specialization',
    'Goa Hospitality Email Expertise',
    'Beach Resort Email Campaigns',
    'Destination Wedding Marketing',
    'Seasonal Tourism Email Automation',
    'Local Business Email Solutions',
    'Event Promotion Campaigns',
    'Hospitality Newsletter Marketing',
    'Travel Package Email Marketing',
    'Entertainment Venue Promotion'
  ];

  const achievements = [
    '160+ Panaji Businesses Served',
    '2,100% Average Email Engagement',
    '₹32Cr+ Revenue Generated for Clients',
    '93% Email Campaign Success Rate',
    'Top Email Marketing Agency in Goa',
    'Tourism Industry Email Specialists'
  ];

  const campaignTypes = [
    {
      type: 'Tourism Email Campaigns',
      description: 'Compelling email marketing for hotels, resorts, and tourism businesses in Panaji',
      metrics: ['28.5% Avg Open Rate', '8.2% Click Rate', '15% Booking Conversion', '₹2.8M Revenue Generated']
    },
    {
      type: 'Event Email Marketing',
      description: 'Strategic email campaigns for weddings, events, and celebrations in Goa',
      metrics: ['32.8% Avg Open Rate', '12.5% Click Rate', '22% Event Booking Rate', '₹1.9M Revenue Generated']
    },
    {
      type: 'Hospitality Email Automation',
      description: 'Automated email sequences for guest engagement and retention',
      metrics: ['25.2% Avg Open Rate', '6.8% Click Rate', '18% Repeat Booking Rate', '₹2.2M Revenue Generated']
    },
    {
      type: 'Local Business Email Marketing',
      description: 'Targeted email campaigns for local Panaji businesses and services',
      metrics: ['22.5% Avg Open Rate', '5.5% Click Rate', '12% Local Conversion Rate', '₹1.5M Revenue Generated']
    }
  ];

  return (
    <div className="min-h-screen bg-slate-900 relative">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        <div className="container mx-auto px-6 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            <div className="lg:col-span-3">
              <section className="pt-24 pb-16">
                <div className="max-w-4xl">
                  <div className="inline-flex items-center space-x-2 bg-teal-500/20 border border-teal-500/30 rounded-full px-6 py-2 mb-8">
                    <Mail className="w-4 h-4 text-teal-400" />
                    <span className="text-teal-400 font-medium">Email Marketing Panaji • Goa's Digital Communication Leaders</span>
                  </div>
                  
                  <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
                    #1 Email Marketing Company in
                    <span className="bg-gradient-to-r from-teal-400 to-teal-600 bg-clip-text text-transparent"> Panaji</span>
                  </h1>
                  
                  <p className="text-xl text-slate-300 mb-8">
                    Premier email marketing services in Panaji offering comprehensive email campaign management and digital communication solutions for tourism businesses, hospitality sector, event organizers, and local enterprises. Our Panaji email marketing agency provides professional email services including tourism email marketing, hotel email campaigns, event email marketing, hospitality email automation Panaji, and local business email marketing. Serving 160+ Panaji businesses with proven ₹32Cr+ revenue generation and 2,100% average email engagement through strategic email marketing and digital communication excellence in Goa's capital city.
                  </p>
                  
                  <div className="flex flex-col sm:flex-row gap-4 mb-8">
                    <Button asChild size="lg" className="bg-gradient-to-r from-teal-500 to-teal-600 hover:from-teal-600 hover:to-teal-700 text-white text-lg px-8 py-4">
                      <Link to="/quote">Get Free Email Audit Panaji</Link>
                    </Button>
                    <Button asChild size="lg" variant="outline" className="border-teal-500 text-teal-400 hover:bg-teal-500 hover:text-white text-lg px-8 py-4">
                      <Link to="/contact">Call Panaji Email Experts: +91-8708577598</Link>
                    </Button>
                  </div>

                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                    {achievements.map((achievement, index) => (
                      <div key={index} className="flex items-center justify-center space-x-2 bg-slate-800/50 rounded-lg p-3">
                        <CheckCircle className="w-4 h-4 text-teal-400 flex-shrink-0" />
                        <span className="text-slate-300">{achievement}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </section>

              <section className="mb-16">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Panaji Industry-Specific
                    <span className="bg-gradient-to-r from-teal-400 to-teal-600 bg-clip-text text-transparent"> Email Solutions</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Specialized email marketing strategies tailored for Panaji's key industries and business sectors.
                  </p>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  {industryFocus.map((industry, index) => (
                    <Card key={index} className="bg-slate-900/80 border-teal-500/20 hover:border-teal-500/40 transition-all duration-300">
                      <CardContent className="p-8">
                        <div className="w-12 h-12 bg-gradient-to-r from-teal-500 to-teal-600 rounded-lg flex items-center justify-center mb-6">
                          <industry.icon className="w-6 h-6 text-white" />
                        </div>
                        <h3 className="text-xl font-bold text-white mb-4">{industry.industry}</h3>
                        <p className="text-slate-300 mb-6">{industry.description}</p>
                        <ul className="space-y-2">
                          {industry.features.map((feature, idx) => (
                            <li key={idx} className="flex items-center text-slate-400 text-sm">
                              <CheckCircle className="w-3 h-3 text-teal-400 mr-2 flex-shrink-0" />
                              {feature}
                            </li>
                          ))}
                        </ul>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>

              <section className="mb-16 bg-slate-800/30 rounded-2xl p-8">
                <h3 className="text-2xl font-bold text-white mb-6 text-center">
                  Comprehensive Email Marketing Services in Panaji
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  {emailServices.map((service, index) => (
                    <div key={index} className="bg-slate-900/50 rounded-lg p-4 text-center">
                      <div className="text-teal-400 font-medium text-sm">{service}</div>
                    </div>
                  ))}
                </div>
              </section>

              <section className="mb-16 bg-slate-800/30 rounded-2xl p-8">
                <h3 className="text-2xl font-bold text-white mb-6 text-center">
                  Email Campaign Performance Metrics
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                  {campaignTypes.map((campaign, index) => (
                    <div key={index} className="bg-slate-900/50 rounded-lg p-6">
                      <h4 className="text-teal-400 font-semibold mb-3">{campaign.type}</h4>
                      <p className="text-slate-300 text-sm mb-4">{campaign.description}</p>
                      <div className="space-y-2">
                        {campaign.metrics.map((metric, idx) => (
                          <div key={idx} className="text-slate-400 text-xs">{metric}</div>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </section>

              <section className="mb-16 bg-slate-800/30 rounded-2xl p-8">
                <h3 className="text-2xl font-bold text-white mb-6 text-center">
                  Local Email Marketing Panaji Specialization
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                  {localEmailFeatures.map((feature, index) => (
                    <div key={index} className="bg-slate-900/50 rounded-lg p-4 text-center">
                      <div className="text-teal-400 font-medium text-sm">{feature}</div>
                    </div>
                  ))}
                </div>
              </section>

              <section className="mb-16 bg-slate-800/30 rounded-2xl p-8">
                <h3 className="text-2xl font-bold text-white mb-6 text-center">
                  Why Choose Our Panaji Email Marketing Services?
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  <div className="text-center">
                    <div className="w-16 h-16 bg-gradient-to-r from-teal-500 to-teal-600 rounded-full flex items-center justify-center mx-auto mb-4">
                      <Building className="w-8 h-8 text-white" />
                    </div>
                    <h4 className="text-white font-semibold mb-2">Tourism Industry Expertise</h4>
                    <p className="text-slate-400 text-sm">Deep understanding of Panaji's tourism landscape, hospitality sector, and seasonal business patterns for effective email campaigns.</p>
                  </div>
                  <div className="text-center">
                    <div className="w-16 h-16 bg-gradient-to-r from-teal-500 to-teal-600 rounded-full flex items-center justify-center mx-auto mb-4">
                      <TrendingUp className="w-8 h-8 text-white" />
                    </div>
                    <h4 className="text-white font-semibold mb-2">Proven Engagement</h4>
                    <p className="text-slate-400 text-sm">160+ successful email campaigns in Panaji with 2,100% average engagement growth and ₹32Cr+ revenue generation.</p>
                  </div>
                  <div className="text-center">
                    <div className="w-16 h-16 bg-gradient-to-r from-teal-500 to-teal-600 rounded-full flex items-center justify-center mx-auto mb-4">
                      <Users className="w-8 h-8 text-white" />
                    </div>
                    <h4 className="text-white font-semibold mb-2">Multi-Industry Experience</h4>
                    <p className="text-slate-400 text-sm">Specialized expertise in tourism, hospitality, events, and entertainment sectors prominent in Panaji market.</p>
                  </div>
                </div>
              </section>

              <section className="mb-16 bg-slate-800/30 rounded-2xl p-8">
                <h3 className="text-2xl font-bold text-white mb-6 text-center">
                  Complete Digital Marketing Solutions Panaji
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  <Link to="/services/seo/panaji" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-teal-400 font-semibold mb-2 group-hover:text-teal-300">SEO Services Panaji</h4>
                    <p className="text-slate-400 text-sm">Search engine optimization for Panaji tourism and businesses</p>
                  </Link>
                  <Link to="/services/ppc/panaji" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-teal-400 font-semibold mb-2 group-hover:text-teal-300">Google Ads Panaji</h4>
                    <p className="text-slate-400 text-sm">PPC advertising and Google Ads management for Panaji</p>
                  </Link>
                  <Link to="/services/social-media/panaji" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-teal-400 font-semibold mb-2 group-hover:text-teal-300">Social Media Marketing Panaji</h4>
                    <p className="text-slate-400 text-sm">Social media management for Panaji tourism and businesses</p>
                  </Link>
                  <Link to="/services/content/panaji" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-teal-400 font-semibold mb-2 group-hover:text-teal-300">Content Marketing Panaji</h4>
                    <p className="text-slate-400 text-sm">Content creation for Panaji tourism and hospitality</p>
                  </Link>
                  <Link to="/services/web-development/panaji" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-teal-400 font-semibold mb-2 group-hover:text-teal-300">Web Development Panaji</h4>
                    <p className="text-slate-400 text-sm">Professional website development for Panaji businesses</p>
                  </Link>
                  <Link to="/portfolio" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-teal-400 font-semibold mb-2 group-hover:text-teal-300">Panaji Success Stories</h4>
                    <p className="text-slate-400 text-sm">See our proven results from Panaji tourism and business clients</p>
                  </Link>
                </div>
              </section>

              <section className="bg-gradient-to-r from-teal-600 to-teal-500 rounded-2xl p-8 text-center">
                <h2 className="text-3xl md:text-5xl font-bold mb-6">Ready to Boost Your Panaji Email Engagement?</h2>
                <p className="text-xl mb-8">
                  Join 160+ successful Panaji businesses that trust GOD Digital Marketing for email excellence. Proven strategies delivering 2,100% engagement growth and ₹32Cr+ revenue generation.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button asChild size="lg" className="bg-white text-teal-600 hover:bg-slate-100 text-lg px-8 py-4">
                    <Link to="/quote">Get Free Email Audit</Link>
                  </Button>
                  <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-teal-600 text-lg px-8 py-4">
                    <Link to="/contact">Call Panaji Email Experts: +91-8708577598</Link>
                  </Button>
                </div>
              </section>
            </div>

            <div className="lg:col-span-1">
              <Sidebar currentService="email" />
            </div>
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
};

export default EmailMarketingPanaji;
