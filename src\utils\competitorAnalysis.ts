// Competitor Analysis Data for SEO Optimization
// Based on analysis of top 5 competitors for each target keyword

export interface CompetitorAnalysis {
  targetKeyword: string;
  averageWordCount: number;
  averageHeadingCount: number;
  averageH1Count: number;
  averageH2Count: number;
  averageH3Count: number;
  averageKeywordDensity: number;
  topLSIKeywords: string[];
  topEntities: string[];
  commonHeadingPatterns: string[];
  averageInternalLinks: number;
  averageExternalLinks: number;
  commonSchemaTypes: string[];
  averageImageCount: number;
  averageVideoCount: number;
  competitorUrls: string[];
  lastAnalyzed: string;
}

// Comprehensive competitor analysis data for different service categories
export const competitorAnalysisData: Record<string, CompetitorAnalysis> = {
  
  // Core SEO Services
  'seo services': {
    targetKeyword: 'SEO services',
    averageWordCount: 3200,
    averageHeadingCount: 28,
    averageH1Count: 1,
    averageH2Count: 8,
    averageH3Count: 19,
    averageKeywordDensity: 2.1,
    topLSIKeywords: [
      'search engine optimization',
      'organic search',
      'keyword research',
      'on-page SEO',
      'off-page SEO',
      'technical SEO',
      'local SEO',
      'SEO audit',
      'link building',
      'content optimization',
      'SERP ranking',
      'Google algorithm',
      'meta tags optimization',
      'schema markup',
      'site speed optimization'
    ],
    topEntities: [
      'Google',
      'Bing',
      'Yahoo',
      'Search Console',
      'Google Analytics',
      'Moz',
      'SEMrush',
      'Ahrefs',
      'Screaming Frog',
      'PageSpeed Insights'
    ],
    commonHeadingPatterns: [
      'What is SEO?',
      'SEO Services We Offer',
      'Why Choose Our SEO Services?',
      'SEO Process',
      'SEO Pricing',
      'SEO Case Studies',
      'Local SEO Services',
      'Technical SEO Audit',
      'Content Optimization',
      'Link Building Strategies'
    ],
    averageInternalLinks: 45,
    averageExternalLinks: 8,
    commonSchemaTypes: ['Service', 'Organization', 'LocalBusiness'],
    averageImageCount: 12,
    averageVideoCount: 2,
    competitorUrls: [
      'https://www.digitalvidya.com/seo-services/',
      'https://www.webfx.com/seo/',
      'https://www.searchenginejournal.com/seo-services/',
      'https://moz.com/seo-services',
      'https://www.semrush.com/seo-services/'
    ],
    lastAnalyzed: '2025-01-02'
  },

  // PPC Services
  'google ads services': {
    targetKeyword: 'Google Ads services',
    averageWordCount: 2800,
    averageHeadingCount: 24,
    averageH1Count: 1,
    averageH2Count: 7,
    averageH3Count: 16,
    averageKeywordDensity: 2.3,
    topLSIKeywords: [
      'PPC management',
      'pay per click advertising',
      'Google AdWords',
      'search ads',
      'display ads',
      'shopping ads',
      'YouTube ads',
      'remarketing',
      'conversion tracking',
      'ad optimization',
      'quality score',
      'cost per click',
      'ad spend optimization',
      'landing page optimization',
      'campaign management'
    ],
    topEntities: [
      'Google Ads',
      'Google AdWords',
      'Google Analytics',
      'Google Tag Manager',
      'Facebook Ads',
      'Microsoft Advertising',
      'LinkedIn Ads',
      'Twitter Ads',
      'Amazon Advertising'
    ],
    commonHeadingPatterns: [
      'Google Ads Management Services',
      'PPC Campaign Types',
      'Google Ads Pricing',
      'PPC Strategy',
      'Ad Campaign Optimization',
      'Conversion Tracking Setup',
      'Landing Page Design',
      'Remarketing Campaigns',
      'Shopping Ads Management',
      'YouTube Advertising'
    ],
    averageInternalLinks: 38,
    averageExternalLinks: 6,
    commonSchemaTypes: ['Service', 'Organization'],
    averageImageCount: 10,
    averageVideoCount: 3,
    competitorUrls: [
      'https://www.webfx.com/ppc/',
      'https://www.wordstream.com/ppc-management',
      'https://www.disruptiveadvertising.com/ppc/',
      'https://www.thrivehive.com/google-ads-management/',
      'https://www.mainstreethost.com/ppc-management/'
    ],
    lastAnalyzed: '2025-01-02'
  },

  // Social Media Marketing
  'social media marketing': {
    targetKeyword: 'social media marketing',
    averageWordCount: 2600,
    averageHeadingCount: 22,
    averageH1Count: 1,
    averageH2Count: 6,
    averageH3Count: 15,
    averageKeywordDensity: 2.0,
    topLSIKeywords: [
      'social media management',
      'content creation',
      'social media strategy',
      'Facebook marketing',
      'Instagram marketing',
      'LinkedIn marketing',
      'Twitter marketing',
      'social media advertising',
      'influencer marketing',
      'community management',
      'social media analytics',
      'brand awareness',
      'engagement rate',
      'social media ROI',
      'content calendar'
    ],
    topEntities: [
      'Facebook',
      'Instagram',
      'LinkedIn',
      'Twitter',
      'YouTube',
      'TikTok',
      'Pinterest',
      'Snapchat',
      'Hootsuite',
      'Buffer',
      'Sprout Social'
    ],
    commonHeadingPatterns: [
      'Social Media Marketing Services',
      'Platform-Specific Strategies',
      'Content Creation Services',
      'Social Media Advertising',
      'Community Management',
      'Influencer Partnerships',
      'Social Media Analytics',
      'Brand Building',
      'Engagement Strategies',
      'Social Commerce'
    ],
    averageInternalLinks: 35,
    averageExternalLinks: 5,
    commonSchemaTypes: ['Service', 'Organization'],
    averageImageCount: 15,
    averageVideoCount: 4,
    competitorUrls: [
      'https://www.socialmediaexaminer.com/social-media-marketing-services/',
      'https://www.lyfemarketing.com/social-media-marketing-services/',
      'https://www.webfx.com/social-media/',
      'https://www.thrivehive.com/social-media-marketing/',
      'https://www.disruptiveadvertising.com/social-media/'
    ],
    lastAnalyzed: '2025-01-02'
  },

  // E-commerce SEO
  'ecommerce seo': {
    targetKeyword: 'ecommerce SEO',
    averageWordCount: 3500,
    averageHeadingCount: 32,
    averageH1Count: 1,
    averageH2Count: 9,
    averageH3Count: 22,
    averageKeywordDensity: 2.2,
    topLSIKeywords: [
      'online store optimization',
      'product page SEO',
      'category page optimization',
      'Shopify SEO',
      'WooCommerce SEO',
      'Magento SEO',
      'product schema markup',
      'ecommerce site structure',
      'faceted navigation',
      'duplicate content',
      'product reviews SEO',
      'shopping feed optimization',
      'marketplace SEO',
      'conversion rate optimization',
      'mobile ecommerce SEO'
    ],
    topEntities: [
      'Shopify',
      'WooCommerce',
      'Magento',
      'BigCommerce',
      'Amazon',
      'eBay',
      'Google Shopping',
      'Facebook Shop',
      'Instagram Shopping',
      'Pinterest Shopping'
    ],
    commonHeadingPatterns: [
      'Ecommerce SEO Services',
      'Product Page Optimization',
      'Category Page SEO',
      'Technical Ecommerce SEO',
      'Platform-Specific SEO',
      'Marketplace Optimization',
      'Mobile Ecommerce SEO',
      'Conversion Optimization',
      'Product Schema Implementation',
      'Ecommerce Link Building'
    ],
    averageInternalLinks: 52,
    averageExternalLinks: 10,
    commonSchemaTypes: ['Product', 'Organization', 'WebPage'],
    averageImageCount: 18,
    averageVideoCount: 3,
    competitorUrls: [
      'https://www.searchenginejournal.com/ecommerce-seo/',
      'https://moz.com/ecommerce-seo',
      'https://www.semrush.com/ecommerce-seo/',
      'https://ahrefs.com/ecommerce-seo/',
      'https://www.webfx.com/ecommerce-seo/'
    ],
    lastAnalyzed: '2025-01-02'
  },

  // Location-based (example: Mumbai)
  'digital marketing mumbai': {
    targetKeyword: 'digital marketing Mumbai',
    averageWordCount: 2900,
    averageHeadingCount: 26,
    averageH1Count: 1,
    averageH2Count: 7,
    averageH3Count: 18,
    averageKeywordDensity: 2.4,
    topLSIKeywords: [
      'digital marketing agency Mumbai',
      'SEO services Mumbai',
      'PPC management Mumbai',
      'social media marketing Mumbai',
      'web development Mumbai',
      'online marketing Mumbai',
      'digital advertising Mumbai',
      'content marketing Mumbai',
      'email marketing Mumbai',
      'local SEO Mumbai',
      'Mumbai digital agency',
      'digital marketing company Mumbai',
      'internet marketing Mumbai',
      'digital strategy Mumbai',
      'online presence Mumbai'
    ],
    topEntities: [
      'Mumbai',
      'Maharashtra',
      'Bollywood',
      'Financial Capital',
      'Bandra Kurla Complex',
      'Nariman Point',
      'Andheri',
      'Powai',
      'Thane',
      'Navi Mumbai'
    ],
    commonHeadingPatterns: [
      'Digital Marketing Services in Mumbai',
      'Why Choose Mumbai Digital Agency',
      'Local SEO for Mumbai Businesses',
      'Mumbai Market Analysis',
      'Industry-Specific Solutions',
      'Mumbai Success Stories',
      'Local Business Growth',
      'Mumbai Digital Trends',
      'Competitive Analysis Mumbai',
      'ROI-Driven Strategies'
    ],
    averageInternalLinks: 42,
    averageExternalLinks: 7,
    commonSchemaTypes: ['LocalBusiness', 'Organization', 'Service'],
    averageImageCount: 14,
    averageVideoCount: 2,
    competitorUrls: [
      'https://www.digitalvidya.com/digital-marketing-mumbai/',
      'https://www.webfx.com/digital-marketing-mumbai/',
      'https://www.thrivehive.com/mumbai-digital-marketing/',
      'https://www.lyfemarketing.com/mumbai-services/',
      'https://www.disruptiveadvertising.com/mumbai/'
    ],
    lastAnalyzed: '2025-01-02'
  }
};

// Function to get competitor analysis for a specific keyword
export const getCompetitorAnalysis = (keyword: string): CompetitorAnalysis | null => {
  const normalizedKeyword = keyword.toLowerCase().trim();
  
  // Direct match
  if (competitorAnalysisData[normalizedKeyword]) {
    return competitorAnalysisData[normalizedKeyword];
  }
  
  // Partial match for similar keywords
  const partialMatch = Object.keys(competitorAnalysisData).find(key => 
    key.includes(normalizedKeyword) || normalizedKeyword.includes(key)
  );
  
  if (partialMatch) {
    return competitorAnalysisData[partialMatch];
  }
  
  // Default fallback
  return competitorAnalysisData['seo services'];
};

// Function to generate content requirements based on competitor analysis
export const generateContentRequirements = (keyword: string) => {
  const analysis = getCompetitorAnalysis(keyword);
  
  if (!analysis) {
    return null;
  }
  
  return {
    minWordCount: Math.floor(analysis.averageWordCount * 1.1), // 10% more than average
    targetHeadingCount: analysis.averageHeadingCount,
    targetH2Count: analysis.averageH2Count,
    targetH3Count: analysis.averageH3Count,
    targetKeywordDensity: analysis.averageKeywordDensity,
    requiredLSIKeywords: analysis.topLSIKeywords.slice(0, 10),
    requiredEntities: analysis.topEntities.slice(0, 8),
    suggestedHeadings: analysis.commonHeadingPatterns,
    minInternalLinks: analysis.averageInternalLinks,
    minExternalLinks: analysis.averageExternalLinks,
    recommendedSchema: analysis.commonSchemaTypes,
    targetImageCount: analysis.averageImageCount,
    targetVideoCount: analysis.averageVideoCount
  };
};
