import React, { useEffect } from "react";
import { useLocation, Link } from "react-router-dom";
import { Home, Search, ArrowLeft, Phone } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import Navigation from "@/components/Navigation";
import Footer from "@/components/Footer";
import MoneyBackground from "@/components/MoneyBackground";

const NotFound = () => {
  const location = useLocation();

  useEffect(() => {
    console.error(
      "404 Error: User attempted to access non-existent route:",
      location.pathname
    );
  }, [location.pathname]);

  const popularPages = [
    { name: "SEO Services", path: "/services/seo" },
    { name: "Google Ads", path: "/services/ppc" },
    { name: "Social Media Marketing", path: "/services/social-media" },
    { name: "Delhi Services", path: "/locations/delhi" },
    { name: "Mumbai Services", path: "/locations/mumbai" },
    { name: "Bangalore Services", path: "/locations/bangalore" },
    { name: "About Us", path: "/about" },
    { name: "Contact Us", path: "/contact" },
  ];

  return (
    <div className="min-h-screen bg-slate-900 relative">
      <MoneyBackground />
      <Navigation />

      <main className="relative z-10 pt-24">
        <div className="container mx-auto px-6 py-16">
          <div className="max-w-4xl mx-auto text-center">
            {/* 404 Hero */}
            <div className="mb-12">
              <h1 className="text-8xl md:text-9xl font-bold text-amber-400 mb-6">404</h1>
              <h2 className="text-3xl md:text-5xl font-bold text-white mb-6">
                Page Not Found
              </h2>
              <p className="text-xl text-slate-300 mb-8 max-w-2xl mx-auto">
                Sorry, the page you're looking for doesn't exist. It might have been moved, deleted, or you entered the wrong URL.
              </p>
              <p className="text-lg text-slate-400 mb-8">
                Requested URL: <code className="bg-slate-800 px-2 py-1 rounded text-amber-400">{location.pathname}</code>
              </p>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-16">
              <Button asChild size="lg" className="bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700 text-white text-lg px-8 py-4">
                <Link to="/">
                  <Home className="w-5 h-5 mr-2" />
                  Go Home
                </Link>
              </Button>
              <Button asChild size="lg" variant="outline" className="border-amber-500 text-amber-400 hover:bg-amber-500 hover:text-white text-lg px-8 py-4">
                <button onClick={() => window.history.back()}>
                  <ArrowLeft className="w-5 h-5 mr-2" />
                  Go Back
                </button>
              </Button>
              <Button asChild size="lg" variant="outline" className="border-slate-500 text-slate-300 hover:bg-slate-700 hover:text-white text-lg px-8 py-4">
                <Link to="/contact">
                  <Phone className="w-5 h-5 mr-2" />
                  Contact Support
                </Link>
              </Button>
            </div>

            {/* Popular Pages */}
            <div className="bg-slate-800/50 rounded-2xl p-8 backdrop-blur-sm border border-slate-700/50">
              <h3 className="text-2xl font-bold text-white mb-6 flex items-center justify-center">
                <Search className="w-6 h-6 mr-2 text-amber-400" />
                Popular Pages
              </h3>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                {popularPages.map((page, index) => (
                  <Link
                    key={index}
                    to={page.path}
                    className="bg-slate-700/50 hover:bg-slate-600/50 rounded-lg p-4 text-slate-300 hover:text-white transition-all duration-300 border border-slate-600/50 hover:border-amber-500/50"
                  >
                    {page.name}
                  </Link>
                ))}
              </div>
            </div>

            {/* Help Section */}
            <div className="mt-12 text-center">
              <p className="text-slate-400 mb-4">
                Need help finding what you're looking for?
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center text-sm">
                <span className="text-slate-500">
                  Call us: <a href="tel:+918708577598" className="text-amber-400 hover:text-amber-300">+91-8708577598</a>
                </span>
                <span className="text-slate-500">
                  Email: <a href="mailto:<EMAIL>" className="text-amber-400 hover:text-amber-300"><EMAIL></a>
                </span>
              </div>
            </div>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default NotFound;
