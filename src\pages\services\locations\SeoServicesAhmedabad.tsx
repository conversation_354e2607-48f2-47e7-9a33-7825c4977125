import React from 'react';
import { Link } from 'react-router-dom';
import { ArrowRight, Search, TrendingUp, Users, CheckCircle, Building, Star, Crown, Globe, Zap } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';
import Sidebar from '@/components/Sidebar';

const SeoServicesAhmedabad = () => {
  const seoServices = [
    'SEO Services Ahmedabad',
    'Local SEO Ahmedabad',
    'International SEO Ahmedabad',
    'E-commerce SEO Ahmedabad',
    'Technical SEO Ahmedabad',
    'Enterprise SEO Ahmedabad',
    'SEO Audit Ahmedabad',
    'Keyword Research Ahmedabad',
    'On-Page SEO Ahmedabad',
    'Off-Page SEO Ahmedabad',
    'Link Building Ahmedabad',
    'Content SEO Ahmedabad',
    'Mobile SEO Ahmedabad',
    'Voice Search SEO Ahmedabad',
    'Textile SEO Ahmedabad',
    'Chemical Industry SEO Ahmedabad',
    'Diamond & Jewelry SEO Ahmedabad',
    'Pharmaceutical SEO Ahmedabad',
    'Manufacturing SEO Ahmedabad',
    'Export Business SEO Ahmedabad'
  ];

  const seoSpecializations = [
    {
      type: 'Textile & Garment SEO',
      description: 'Specialized SEO for Ahmedabad\'s textile capital and garment manufacturing hub',
      icon: Building,
      features: ['Textile Industry SEO', 'Garment Export Marketing', 'Fashion B2B SEO', 'Fabric Supplier SEO']
    },
    {
      type: 'Chemical & Pharmaceutical SEO',
      description: 'Advanced SEO for Gujarat\'s chemical and pharmaceutical industry leaders',
      icon: Globe,
      features: ['Chemical Company SEO', 'Pharma Marketing', 'Industrial B2B SEO', 'Export Compliance SEO']
    },
    {
      type: 'Diamond & Jewelry SEO',
      description: 'Premium SEO for Ahmedabad\'s diamond cutting and jewelry manufacturing sector',
      icon: Star,
      features: ['Diamond Industry SEO', 'Jewelry Export SEO', 'Precious Metals SEO', 'Luxury Brand SEO']
    },
    {
      type: 'Engineering & Manufacturing SEO',
      description: 'Industrial SEO for Ahmedabad\'s engineering and manufacturing companies',
      icon: Zap,
      features: ['Engineering SEO', 'Manufacturing B2B', 'Industrial Equipment SEO', 'Export Marketing']
    }
  ];

  const seoPackages = [
    {
      name: 'SEO Ahmedabad Starter',
      price: '₹28,000',
      period: '/month',
      description: 'Perfect for small Ahmedabad businesses and textile traders',
      features: [
        'Local Ahmedabad SEO Optimization',
        'Google My Business Setup',
        '25 Target Keywords',
        'On-Page SEO (12 pages)',
        'Monthly Reporting',
        'Textile Industry Focus'
      ]
    },
    {
      name: 'Ahmedabad SEO Professional',
      price: '₹48,000',
      period: '/month',
      description: 'Comprehensive SEO for growing Ahmedabad businesses',
      features: [
        'Advanced Local + National SEO',
        '60 Target Keywords',
        'Technical SEO Audit',
        'Content Marketing Strategy',
        'Link Building Campaign',
        'Chemical/Pharma Specialization',
        'Bi-weekly Reporting'
      ],
      popular: true
    },
    {
      name: 'Enterprise SEO Ahmedabad',
      price: '₹95,000',
      period: '/month',
      description: 'Advanced SEO for large Ahmedabad enterprises and exporters',
      features: [
        'International SEO Strategy',
        'Unlimited Keywords',
        'Advanced Technical SEO',
        'Multi-language SEO',
        'Export Market SEO',
        'Dedicated SEO Manager',
        'Weekly Reporting'
      ]
    }
  ];

  const stats = [
    {
      metric: '2800+',
      description: 'Ahmedabad Businesses Ranked',
      detail: 'Across all industries'
    },
    {
      metric: '920%',
      description: 'Average Traffic Increase',
      detail: 'For Ahmedabad clients'
    },
    {
      metric: '₹52Cr+',
      description: 'Revenue Generated',
      detail: 'Through SEO campaigns'
    },
    {
      metric: '97%',
      description: 'Client Retention Rate',
      detail: 'Long-term partnerships'
    }
  ];

  const achievements = [
    'Top SEO Company in Ahmedabad',
    'Textile Industry SEO Leaders',
    'Chemical Sector SEO Experts',
    'Diamond Industry SEO Specialists',
    'Manufacturing SEO Champions',
    'Export Business SEO Pioneers'
  ];

  return (
    <div className="min-h-screen bg-slate-900 relative">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        <div className="container mx-auto px-6 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            {/* Main Content */}
            <div className="lg:col-span-3">
              {/* Hero Section */}
              <section className="pt-24 pb-16">
                <div className="max-w-4xl">
                  <div className="inline-flex items-center space-x-2 bg-orange-500/20 border border-orange-500/30 rounded-full px-6 py-2 mb-8">
                    <Search className="w-4 h-4 text-orange-400" />
                    <span className="text-orange-400 font-medium">SEO Services Ahmedabad • Gujarat's Commercial Capital Excellence</span>
                  </div>
                  
                  <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
                    #1 SEO Company in
                    <span className="bg-gradient-to-r from-orange-400 to-orange-600 bg-clip-text text-transparent"> Ahmedabad</span>
                  </h1>
                  
                  <p className="text-xl text-slate-300 mb-8">
                    Premier SEO services in Ahmedabad offering comprehensive search engine optimization for textile, chemical, diamond, and manufacturing businesses. Serving 2800+ Ahmedabad businesses across all areas - from textile hubs in Narol to chemical zones in Vatva. Expert SEO solutions with proven ₹52Cr+ revenue generation and 920% average traffic increase for Ahmedabad clients in Gujarat's commercial capital.
                  </p>
                  
                  <div className="flex flex-col sm:flex-row gap-4 mb-8">
                    <Button asChild size="lg" className="bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white text-lg px-8 py-4">
                      <Link to="/quote">Get Free Ahmedabad SEO Audit</Link>
                    </Button>
                    <Button asChild size="lg" variant="outline" className="border-orange-500 text-orange-400 hover:bg-orange-500 hover:text-white text-lg px-8 py-4">
                      <Link to="/contact">Call SEO Experts: +91-8708577598</Link>
                    </Button>
                  </div>

                  {/* Achievements */}
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                    {achievements.map((achievement, index) => (
                      <div key={index} className="flex items-center justify-center space-x-2 bg-slate-800/50 rounded-lg p-3">
                        <CheckCircle className="w-4 h-4 text-orange-400 flex-shrink-0" />
                        <span className="text-slate-300">{achievement}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </section>

              {/* Stats Section */}
              <section className="mb-16 bg-slate-800/50 rounded-2xl p-8">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Ahmedabad SEO
                    <span className="bg-gradient-to-r from-orange-400 to-orange-600 bg-clip-text text-transparent"> Success Metrics</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Measurable SEO results from Ahmedabad businesses across textile, chemical, diamond, and manufacturing sectors.
                  </p>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
                  {stats.map((stat, index) => (
                    <div key={index} className="text-center">
                      <div className="text-4xl font-bold text-orange-400 mb-2">{stat.metric}</div>
                      <div className="text-white font-semibold mb-1">{stat.description}</div>
                      <div className="text-slate-400">{stat.detail}</div>
                    </div>
                  ))}
                </div>
              </section>

              {/* SEO Specializations Section */}
              <section className="mb-16">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Ahmedabad SEO
                    <span className="bg-gradient-to-r from-orange-400 to-orange-600 bg-clip-text text-transparent"> Specializations</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Specialized SEO strategies designed for Ahmedabad's unique textile, chemical, diamond, and manufacturing landscape.
                  </p>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  {seoSpecializations.map((spec, index) => (
                    <Card key={index} className="bg-slate-900/80 border-orange-500/20 hover:border-orange-500/40 transition-all duration-300">
                      <CardContent className="p-8">
                        <div className="w-12 h-12 bg-gradient-to-r from-orange-500 to-orange-600 rounded-lg flex items-center justify-center mb-6">
                          <spec.icon className="w-6 h-6 text-white" />
                        </div>
                        <h3 className="text-xl font-bold text-white mb-4">{spec.type}</h3>
                        <p className="text-slate-300 mb-6">{spec.description}</p>
                        <ul className="space-y-2">
                          {spec.features.map((feature, idx) => (
                            <li key={idx} className="flex items-center text-slate-400 text-sm">
                              <CheckCircle className="w-3 h-3 text-orange-400 mr-2 flex-shrink-0" />
                              {feature}
                            </li>
                          ))}
                        </ul>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>

              {/* Pricing Section */}
              <section className="mb-16">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Ahmedabad SEO
                    <span className="bg-gradient-to-r from-orange-400 to-orange-600 bg-clip-text text-transparent"> Pricing</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Competitive SEO pricing designed for Ahmedabad's textile, chemical, and manufacturing business environment.
                  </p>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                  {seoPackages.map((pkg, index) => (
                    <Card key={index} className={`bg-slate-900/80 border-orange-500/20 hover:border-orange-500/40 transition-all duration-300 relative ${pkg.popular ? 'ring-2 ring-orange-500' : ''}`}>
                      {pkg.popular && (
                        <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                          <span className="bg-orange-500 text-white px-4 py-2 rounded-full text-sm font-semibold">Most Popular</span>
                        </div>
                      )}
                      <CardContent className="p-8">
                        <h3 className="text-2xl font-bold text-white mb-2">{pkg.name}</h3>
                        <p className="text-slate-300 mb-6">{pkg.description}</p>
                        <div className="mb-6">
                          <span className="text-4xl font-bold text-orange-400">{pkg.price}</span>
                          <span className="text-slate-400">{pkg.period}</span>
                        </div>
                        <ul className="space-y-3 mb-8">
                          {pkg.features.map((feature, idx) => (
                            <li key={idx} className="flex items-center text-slate-300">
                              <CheckCircle className="w-4 h-4 text-orange-400 mr-3 flex-shrink-0" />
                              {feature}
                            </li>
                          ))}
                        </ul>
                        <Button asChild className={`w-full ${pkg.popular ? 'bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700' : 'bg-slate-700 hover:bg-slate-600'} text-white`}>
                          <Link to="/quote">Get Started</Link>
                        </Button>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>

              {/* CTA Section */}
              <section className="bg-gradient-to-r from-orange-600 to-orange-500 rounded-2xl p-8 text-center">
                <h2 className="text-3xl md:text-5xl font-bold mb-6">Ready to Dominate Ahmedabad Search Results?</h2>
                <p className="text-xl mb-8">
                  Join 2800+ Ahmedabad businesses that trust GOD Digital Marketing for SEO success. Proven strategies that deliver 920% traffic increase and ₹52Cr+ revenue generation.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button asChild size="lg" className="bg-white text-orange-600 hover:bg-slate-100 text-lg px-8 py-4">
                    <Link to="/quote">Get Free SEO Audit</Link>
                  </Button>
                  <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-orange-600 text-lg px-8 py-4">
                    <Link to="/contact">Call SEO Experts: +91-8708577598</Link>
                  </Button>
                </div>
              </section>
            </div>

            {/* Sidebar */}
            <div className="lg:col-span-1">
              <Sidebar currentService="seo" currentLocation="ahmedabad" />
            </div>
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
};

export default SeoServicesAhmedabad;
