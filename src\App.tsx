
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";
import { useEffect } from "react";
import { HelmetProvider } from "react-helmet-async";
import PerformanceMonitor from "./components/PerformanceMonitor";
import RedirectHandler from "./components/RedirectHandler";
import "./styles/mobile-optimizations.css";
import Index from "./pages/Index";
import About from "./pages/About";
import Services from "./pages/Services";
import Contact from "./pages/Contact";
import Blog from "./pages/Blog";
import Quote from "./pages/Quote";
import Portfolio from "./pages/Portfolio";
import CaseStudies from "./pages/CaseStudies";
import NotFound from "./pages/NotFound";
import LogoDownload from "./pages/LogoDownload";
import RoutingTest from "./pages/RoutingTest";

// Service Pages
import SeoServices from "./pages/services/SeoServices";
import LocalSeoServices from "./pages/services/LocalSeoServices";
import InternationalSeoServices from "./pages/services/InternationalSeoServices";
import AiContentServices from "./pages/services/AiContentServices";
import PpcServices from "./pages/services/PpcServices";
import SocialMediaServices from "./pages/services/SocialMediaServices";
import EmailServices from "./pages/services/EmailServices";
import ContentServices from "./pages/services/ContentServices";
import WebDesignServices from "./pages/services/WebDesignServices";

// Location-based service pages
import DelhiSeoServices from "./pages/services/seo/DelhiSeoServices";
import MumbaiPpcServices from "./pages/services/ppc/MumbaiPpcServices";
import DelhiPpcServices from "./pages/services/delhi/DelhiPpcServices";
import DelhiSocialMediaServices from "./pages/services/delhi/DelhiSocialMediaServices";

// Area-specific pages
import ConnaughtPlaceDigitalMarketing from "./pages/services/delhi/areas/ConnaughtPlaceDigitalMarketing";

// Industry-specific pages
import BangaloreHealthcareDigitalMarketing from "./pages/services/industries/BangaloreHealthcareDigitalMarketing";
import Mumbai from "./pages/locations/Mumbai";
import Delhi from "./pages/locations/Delhi";
import CentralDelhi from "./pages/locations/delhi/CentralDelhi";
import SouthDelhi from "./pages/locations/delhi/SouthDelhi";
import Bangalore from "./pages/locations/Bangalore";
import Chennai from "./pages/locations/Chennai";
import Kolkata from "./pages/locations/Kolkata";
import Hyderabad from "./pages/locations/Hyderabad";
import Haryana from "./pages/locations/Haryana";
import Gurgaon from "./pages/locations/haryana/Gurgaon";
import Faridabad from "./pages/locations/haryana/Faridabad";
import RealEstate from "./pages/industries/RealEstate";
import Healthcare from "./pages/industries/Healthcare";
import Education from "./pages/industries/Education";
import Ecommerce from "./pages/industries/Ecommerce";
import Manufacturing from "./pages/industries/Manufacturing";
import FinancialServices from "./pages/industries/FinancialServices";
import SeoServicesGurgaon from "./pages/services/locations/SeoServicesGurgaon";
import Panipat from "./pages/locations/haryana/Panipat";
import Ambala from "./pages/locations/haryana/Ambala";
import Yamunanagar from "./pages/locations/haryana/Yamunanagar";
import Rohtak from "./pages/locations/haryana/Rohtak";
import Hisar from "./pages/locations/haryana/Hisar";
import Karnal from "./pages/locations/haryana/Karnal";
import Sonipat from "./pages/locations/haryana/Sonipat";
import Panchkula from "./pages/locations/haryana/Panchkula";
import Bhiwani from "./pages/locations/haryana/Bhiwani";
import Sirsa from "./pages/locations/haryana/Sirsa";
import DigitalMarketingGuide from "./pages/resources/DigitalMarketingGuide";
import SeoTools from "./pages/resources/SeoTools";
import BlogCategories from "./pages/resources/BlogCategories";
import CaseStudyCategories from "./pages/resources/CaseStudyCategories";
import FacebookAdsServices from "./pages/services/FacebookAdsServices";
import LinkedInAdsServices from "./pages/services/LinkedInAdsServices";
import YouTubeMarketingServices from "./pages/services/YouTubeMarketingServices";
import BusinessAutomationServices from "./pages/services/BusinessAutomationServices";
import MarketingAutomationServices from "./pages/services/MarketingAutomationServices";
import Pune from "./pages/locations/Pune";
import Jaipur from "./pages/locations/Jaipur";
import Lucknow from "./pages/locations/Lucknow";
import Surat from "./pages/locations/Surat";
import Maharashtra from "./pages/locations/Maharashtra";
import SeoServicesDelhi from "./pages/services/locations/SeoServicesDelhi";
import SeoServicesSonipat from "./pages/services/locations/SeoServicesSonipat";
import GoogleAdsDelhi from "./pages/services/locations/GoogleAdsDelhi";
import GoogleAdsSonipat from "./pages/services/locations/GoogleAdsSonipat";
import SocialMediaDelhi from "./pages/services/locations/SocialMediaDelhi";
import SocialMediaSonipat from "./pages/services/locations/SocialMediaSonipat";
import ContentMarketingDelhi from "./pages/services/locations/ContentMarketingDelhi";
import EmailMarketingDelhi from "./pages/services/locations/EmailMarketingDelhi";
import WebDevelopmentDelhi from "./pages/services/locations/WebDevelopmentDelhi";
import SeoServicesMumbai from "./pages/services/locations/SeoServicesMumbai";
import GoogleAdsMumbai from "./pages/services/locations/GoogleAdsMumbai";
import SocialMediaMumbai from "./pages/services/locations/SocialMediaMumbai";
import SeoServicesBangalore from "./pages/services/locations/SeoServicesBangalore";
import ContentMarketingMumbai from "./pages/services/locations/ContentMarketingMumbai";
import EmailMarketingMumbai from "./pages/services/locations/EmailMarketingMumbai";
import GoogleAdsBangalore from "./pages/services/locations/GoogleAdsBangalore";
import SocialMediaBangalore from "./pages/services/locations/SocialMediaBangalore";
import ContentMarketingBangalore from "./pages/services/locations/ContentMarketingBangalore";
import SeoServicesChennai from "./pages/services/locations/SeoServicesChennai";
import EmailMarketingBangalore from "./pages/services/locations/EmailMarketingBangalore";
import GoogleAdsChennai from "./pages/services/locations/GoogleAdsChennai";
import SocialMediaChennai from "./pages/services/locations/SocialMediaChennai";
import SeoServicesKolkata from "./pages/services/locations/SeoServicesKolkata";
import GoogleAdsKolkata from "./pages/services/locations/GoogleAdsKolkata";
import SocialMediaKolkata from "./pages/services/locations/SocialMediaKolkata";
import ContentMarketingKolkata from "./pages/services/locations/ContentMarketingKolkata";
import EmailMarketingKolkata from "./pages/services/locations/EmailMarketingKolkata";
import WebDevelopmentKolkata from "./pages/services/locations/WebDevelopmentKolkata";
import SeoServicesPune from "./pages/services/locations/SeoServicesPune";
import GoogleAdsPune from "./pages/services/locations/GoogleAdsPune";
import SocialMediaPune from "./pages/services/locations/SocialMediaPune";
import ContentMarketingPune from "./pages/services/locations/ContentMarketingPune";
import EmailMarketingPune from "./pages/services/locations/EmailMarketingPune";
import WebDevelopmentPune from "./pages/services/locations/WebDevelopmentPune";
import SeoServicesAhmedabad from "./pages/services/locations/SeoServicesAhmedabad";
import GoogleAdsAhmedabad from "./pages/services/locations/GoogleAdsAhmedabad";
import SocialMediaAhmedabad from "./pages/services/locations/SocialMediaAhmedabad";
import ContentMarketingAhmedabad from "./pages/services/locations/ContentMarketingAhmedabad";
import EmailMarketingAhmedabad from "./pages/services/locations/EmailMarketingAhmedabad";
import WebDevelopmentAhmedabad from "./pages/services/locations/WebDevelopmentAhmedabad";

// Priority 3 Service-Location Pages
import SeoServicesFaridabad from "./pages/services/locations/SeoServicesFaridabad";
import GoogleAdsGurgaon from "./pages/services/locations/GoogleAdsGurgaon";
import GoogleAdsFaridabad from "./pages/services/locations/GoogleAdsFaridabad";
import SocialMediaMarketingGurgaon from "./pages/services/locations/SocialMediaMarketingGurgaon";
import SocialMediaMarketingFaridabad from "./pages/services/locations/SocialMediaMarketingFaridabad";

// Priority 4 Additional Service-Location Pages
import SeoServicesHaryana from "./pages/services/locations/SeoServicesHaryana";
import SocialMediaMarketingDelhi from "./pages/services/locations/SocialMediaMarketingDelhi";
import SocialMediaMarketingMumbai from "./pages/services/locations/SocialMediaMarketingMumbai";

// Industry-specific pages
import AutomotiveIndustryDigitalMarketing from "./pages/industries/AutomotiveIndustryDigitalMarketing";
import AgricultureIndustryDigitalMarketing from "./pages/industries/AgricultureIndustryDigitalMarketing";
import TextileIndustryDigitalMarketing from "./pages/industries/TextileIndustryDigitalMarketing";
import ChemicalIndustryDigitalMarketing from "./pages/industries/ChemicalIndustryDigitalMarketing";
import PharmaceuticalIndustryDigitalMarketing from "./pages/industries/PharmaceuticalIndustryDigitalMarketing";
import RealEstateIndustryDigitalMarketing from "./pages/industries/RealEstateIndustryDigitalMarketing";
import ITIndustryDigitalMarketing from "./pages/industries/ITIndustryDigitalMarketing";
import SteelMetalIndustryDigitalMarketing from "./pages/industries/SteelMetalIndustryDigitalMarketing";
import HealthcareIndustryDigitalMarketing from "./pages/industries/HealthcareIndustryDigitalMarketing";
import RealEstateGurgaon from "./pages/industries/locations/RealEstateGurgaon";
import AiAutomationResources from "./pages/resources/AiAutomationResources";
import IndustryReportsResources from "./pages/resources/IndustryReportsResources";
import HealthcareGurgaon from "./pages/industries/locations/HealthcareGurgaon";
import SeoAuditTool from "./pages/tools/SeoAuditTool";
import Testimonials from "./pages/Testimonials";
import ManufacturingHaryana from "./pages/industries/locations/ManufacturingHaryana";
import PrivacyPolicy from "./pages/PrivacyPolicy";
import RealEstateFaridabad from "./pages/industries/locations/RealEstateFaridabad";
import TermsOfService from "./pages/TermsOfService";
import Awards from "./pages/Awards";
import Careers from "./pages/Careers";
import TechnologyDelhi from "./pages/industries/locations/TechnologyDelhi";
import EducationGurgaon from "./pages/industries/locations/EducationGurgaon";
import HealthcareMumbai from "./pages/industries/locations/HealthcareMumbai";

// Jaipur Service Pages
import SeoServicesJaipur from "./pages/services/locations/SeoServicesJaipur";
import GoogleAdsJaipur from "./pages/services/locations/GoogleAdsJaipur";
import SocialMediaJaipur from "./pages/services/locations/SocialMediaJaipur";
import ContentMarketingJaipur from "./pages/services/locations/ContentMarketingJaipur";
import EmailMarketingJaipur from "./pages/services/locations/EmailMarketingJaipur";
import WebDevelopmentJaipur from "./pages/services/locations/WebDevelopmentJaipur";

// Lucknow Service Pages
import SeoServicesLucknow from "./pages/services/locations/SeoServicesLucknow";
import GoogleAdsLucknow from "./pages/services/locations/GoogleAdsLucknow";
import SocialMediaLucknow from "./pages/services/locations/SocialMediaLucknow";
import ContentMarketingLucknow from "./pages/services/locations/ContentMarketingLucknow";
import EmailMarketingLucknow from "./pages/services/locations/EmailMarketingLucknow";
import WebDevelopmentLucknow from "./pages/services/locations/WebDevelopmentLucknow";

// Surat Service Pages
import SeoServicesSurat from "./pages/services/locations/SeoServicesSurat";
import GoogleAdsSurat from "./pages/services/locations/GoogleAdsSurat";
import SocialMediaSurat from "./pages/services/locations/SocialMediaSurat";
import ContentMarketingSurat from "./pages/services/locations/ContentMarketingSurat";
import EmailMarketingSurat from "./pages/services/locations/EmailMarketingSurat";
import WebDevelopmentSurat from "./pages/services/locations/WebDevelopmentSurat";

// Indore Service Pages
import SeoServicesIndore from "./pages/services/locations/SeoServicesIndore";
import GoogleAdsIndore from "./pages/services/locations/GoogleAdsIndore";
import SocialMediaIndore from "./pages/services/locations/SocialMediaIndore";
import ContentMarketingIndore from "./pages/services/locations/ContentMarketingIndore";
import EmailMarketingIndore from "./pages/services/locations/EmailMarketingIndore";
import WebDevelopmentIndore from "./pages/services/locations/WebDevelopmentIndore";

// Bhopal Service Pages
import SeoServicesBhopal from "./pages/services/locations/SeoServicesBhopal";
import GoogleAdsBhopal from "./pages/services/locations/GoogleAdsBhopal";
import SocialMediaBhopal from "./pages/services/locations/SocialMediaBhopal";
import ContentMarketingBhopal from "./pages/services/locations/ContentMarketingBhopal";
import EmailMarketingBhopal from "./pages/services/locations/EmailMarketingBhopal";
import WebDevelopmentBhopal from "./pages/services/locations/WebDevelopmentBhopal";

// Chandigarh Service Pages
import SeoServicesChandigarh from "./pages/services/locations/SeoServicesChandigarh";
import GoogleAdsChandigarh from "./pages/services/locations/GoogleAdsChandigarh";
import SocialMediaChandigarh from "./pages/services/locations/SocialMediaChandigarh";
import ContentMarketingChandigarh from "./pages/services/locations/ContentMarketingChandigarh";
import EmailMarketingChandigarh from "./pages/services/locations/EmailMarketingChandigarh";
import WebDevelopmentChandigarh from "./pages/services/locations/WebDevelopmentChandigarh";

// Industry-Specific Service Pages
import EcommerceSeo from "./pages/services/industries/EcommerceSeo";
import HealthcareSeo from "./pages/services/industries/HealthcareSeo";
import B2bSeo from "./pages/services/industries/B2bSeo";
import LocalBusinessSeo from "./pages/services/industries/LocalBusinessSeo";
import TravelTourismSeo from "./pages/services/industries/TravelTourismSeo";
import RealEstateSeo from "./pages/services/industries/RealEstateSeo";
import EducationSeo from "./pages/services/industries/EducationSeo";
import FinanceSeo from "./pages/services/industries/FinanceSeo";

// New State Capital Service Pages
import SeoServicesDehradun from "./pages/services/locations/SeoServicesDehradun";
import PpcServicesBhubaneswar from "./pages/services/locations/PpcServicesBhubaneswar";
import SocialMediaGandhinagar from "./pages/services/locations/SocialMediaGandhinagar";
import ContentMarketingShimla from "./pages/services/locations/ContentMarketingShimla";
import EmailMarketingPanaji from "./pages/services/locations/EmailMarketingPanaji";

// Additional Industry Pages
import ManufacturingSeo from "./pages/services/industries/ManufacturingSeo";
import TechnologySeo from "./pages/services/industries/TechnologySeo";
import FoodRestaurantSeo from "./pages/services/industries/FoodRestaurantSeo";
import FashionRetailSeo from "./pages/services/industries/FashionRetailSeo";
import AutomotiveSeo from "./pages/services/industries/AutomotiveSeo";
import LegalServicesSeo from "./pages/services/industries/LegalServicesSeo";

// Core Service Pages
import EcommerceSeoServices from "./pages/services/EcommerceSeoServices";
import TechnicalSeoServices from "./pages/services/TechnicalSeoServices";
import EnterpriseSeoServices from "./pages/services/EnterpriseSeoServices";

// Additional State Capital Pages
import WebDevelopmentThiruvananthapuram from "./pages/services/locations/WebDevelopmentThiruvananthapuram";
import SeoServicesRaipur from "./pages/services/locations/SeoServicesRaipur";
import PpcServicesRanchi from "./pages/services/locations/PpcServicesRanchi";
import SocialMediaGuwahati from "./pages/services/locations/SocialMediaGuwahati";
import ContentMarketingItanagar from "./pages/services/locations/ContentMarketingItanagar";
import EmailMarketingImphal from "./pages/services/locations/EmailMarketingImphal";

// Location Pages
import Gujarat from "./pages/locations/Gujarat";
import Karnataka from "./pages/locations/Karnataka";
import TamilNadu from "./pages/locations/TamilNadu";

// Advanced Service Pages
import AiAutomation from "./pages/services/AiAutomation";
import BusinessAutomation from "./pages/services/BusinessAutomation";
import BusinessDevelopment from "./pages/services/BusinessDevelopment";
import ContentMarketingChennai from "./pages/services/locations/ContentMarketingChennai";
import EmailMarketingChennai from "./pages/services/locations/EmailMarketingChennai";
import SeoServicesHyderabad from "./pages/services/locations/SeoServicesHyderabad";
import GoogleAdsHyderabad from "./pages/services/locations/GoogleAdsHyderabad";
import SocialMediaHyderabad from "./pages/services/locations/SocialMediaHyderabad";
import ContentMarketingHyderabad from "./pages/services/locations/ContentMarketingHyderabad";
import EmailMarketingHyderabad from "./pages/services/locations/EmailMarketingHyderabad";
import WebDevelopmentHyderabad from "./pages/services/locations/WebDevelopmentHyderabad";
import ComingSoon from "./pages/ComingSoon";
import Sitemap from "./pages/Sitemap";

const queryClient = new QueryClient();

const App = () => (
  <HelmetProvider>
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <PerformanceMonitor />
        <Toaster />
        <Sonner />
        <BrowserRouter>
          <RedirectHandler />
        <Routes>
          <Route path="/" element={<Index />} />
          <Route path="/about" element={<About />} />
          <Route path="/services" element={<Services />} />
          <Route path="/contact" element={<Contact />} />
          <Route path="/blog" element={<Blog />} />
          <Route path="/quote" element={<Quote />} />
          <Route path="/portfolio" element={<Portfolio />} />
          <Route path="/case-studies" element={<CaseStudies />} />
          <Route path="/logo" element={<LogoDownload />} />
          
          {/* Service Pages */}
          <Route path="/services/seo" element={<SeoServices />} />
          <Route path="/services/seo/local" element={<LocalSeoServices />} />
          <Route path="/services/seo/international" element={<InternationalSeoServices />} />
          <Route path="/services/seo/ecommerce" element={<EcommerceSeoServices />} />
          <Route path="/services/seo/technical" element={<TechnicalSeoServices />} />
          <Route path="/services/seo/enterprise" element={<EnterpriseSeoServices />} />
          <Route path="/services/ai-content" element={<AiContentServices />} />
          <Route path="/services/ppc" element={<PpcServices />} />
          <Route path="/services/facebook-ads" element={<FacebookAdsServices />} />
          <Route path="/services/linkedin-ads" element={<LinkedInAdsServices />} />
          <Route path="/services/youtube-marketing" element={<YouTubeMarketingServices />} />
          <Route path="/services/social-media" element={<SocialMediaServices />} />
          <Route path="/services/business-automation" element={<BusinessAutomationServices />} />
          <Route path="/services/marketing-automation" element={<MarketingAutomationServices />} />
          <Route path="/services/email" element={<EmailServices />} />
          <Route path="/services/content" element={<ContentServices />} />
          <Route path="/services/web-design" element={<WebDesignServices />} />
          
          {/* Location-based service pages */}
          <Route path="/services/seo/delhi" element={<DelhiSeoServices />} />
          <Route path="/services/ppc/mumbai" element={<MumbaiPpcServices />} />
          <Route path="/services/delhi/ppc" element={<DelhiPpcServices />} />
          <Route path="/services/delhi/social-media" element={<DelhiSocialMediaServices />} />
          
          {/* Area-specific pages */}
          <Route path="/services/delhi/connaught-place/digital-marketing" element={<ConnaughtPlaceDigitalMarketing />} />
          
          {/* Industry-specific pages */}
          <Route path="/services/industries/bangalore-healthcare-digital-marketing" element={<BangaloreHealthcareDigitalMarketing />} />

          {/* Location Pages */}
          <Route path="/locations/mumbai" element={<Mumbai />} />
          <Route path="/locations/delhi" element={<Delhi />} />
          <Route path="/locations/delhi/central-delhi" element={<CentralDelhi />} />
          <Route path="/locations/delhi/south-delhi" element={<SouthDelhi />} />
          <Route path="/locations/bangalore" element={<Bangalore />} />
          <Route path="/locations/chennai" element={<Chennai />} />
          <Route path="/locations/kolkata" element={<Kolkata />} />
          <Route path="/locations/hyderabad" element={<Hyderabad />} />
          <Route path="/locations/pune" element={<Pune />} />
          <Route path="/locations/jaipur" element={<Jaipur />} />
          <Route path="/locations/lucknow" element={<Lucknow />} />
          <Route path="/locations/surat" element={<Surat />} />
          <Route path="/locations/maharashtra" element={<Maharashtra />} />
          <Route path="/locations/gujarat" element={<Gujarat />} />
          <Route path="/locations/karnataka" element={<Karnataka />} />
          <Route path="/locations/tamil-nadu" element={<TamilNadu />} />
          <Route path="/locations/haryana" element={<Haryana />} />

          {/* Haryana Cities */}
          <Route path="/locations/haryana/gurgaon" element={<Gurgaon />} />
          <Route path="/locations/haryana/faridabad" element={<Faridabad />} />
          <Route path="/locations/haryana/panipat" element={<Panipat />} />
          <Route path="/locations/haryana/ambala" element={<Ambala />} />
          <Route path="/locations/haryana/yamunanagar" element={<Yamunanagar />} />
          <Route path="/locations/haryana/rohtak" element={<Rohtak />} />
          <Route path="/locations/haryana/hisar" element={<Hisar />} />
          <Route path="/locations/haryana/karnal" element={<Karnal />} />
          <Route path="/locations/haryana/sonipat" element={<Sonipat />} />
          <Route path="/locations/haryana/panchkula" element={<Panchkula />} />
          <Route path="/locations/haryana/bhiwani" element={<Bhiwani />} />
          <Route path="/locations/haryana/sirsa" element={<Sirsa />} />

          {/* Service-Location Combinations */}
          <Route path="/services/seo/gurgaon" element={<SeoServicesGurgaon />} />
          <Route path="/services/seo/faridabad" element={<SeoServicesFaridabad />} />
          <Route path="/services/seo/delhi" element={<SeoServicesDelhi />} />
          <Route path="/services/seo/sonipat" element={<SeoServicesSonipat />} />
          <Route path="/services/seo/mumbai" element={<SeoServicesMumbai />} />
          <Route path="/services/seo/bangalore" element={<SeoServicesBangalore />} />
          <Route path="/services/seo/chennai" element={<SeoServicesChennai />} />
          <Route path="/services/seo/kolkata" element={<SeoServicesKolkata />} />
          <Route path="/services/ppc/kolkata" element={<GoogleAdsKolkata />} />
          <Route path="/services/social-media/kolkata" element={<SocialMediaKolkata />} />
          <Route path="/services/content/kolkata" element={<ContentMarketingKolkata />} />
          <Route path="/services/email/kolkata" element={<EmailMarketingKolkata />} />
          <Route path="/services/web-development/kolkata" element={<WebDevelopmentKolkata />} />
          <Route path="/services/seo/hyderabad" element={<SeoServicesHyderabad />} />
          <Route path="/services/ppc/hyderabad" element={<GoogleAdsHyderabad />} />
          <Route path="/services/social-media/hyderabad" element={<SocialMediaHyderabad />} />
          <Route path="/services/content/hyderabad" element={<ContentMarketingHyderabad />} />
          <Route path="/services/email/hyderabad" element={<EmailMarketingHyderabad />} />
          <Route path="/services/web-development/hyderabad" element={<WebDevelopmentHyderabad />} />
          <Route path="/services/seo/pune" element={<SeoServicesPune />} />
          <Route path="/services/ppc/pune" element={<GoogleAdsPune />} />
          <Route path="/services/social-media/pune" element={<SocialMediaPune />} />
          <Route path="/services/content/pune" element={<ContentMarketingPune />} />
          <Route path="/services/email/pune" element={<EmailMarketingPune />} />
          <Route path="/services/web-development/pune" element={<WebDevelopmentPune />} />

          {/* Ahmedabad Services (6 complete) */}
          <Route path="/services/seo/ahmedabad" element={<SeoServicesAhmedabad />} />
          <Route path="/services/ppc/ahmedabad" element={<GoogleAdsAhmedabad />} />
          <Route path="/services/social-media/ahmedabad" element={<SocialMediaAhmedabad />} />
          <Route path="/services/content/ahmedabad" element={<ContentMarketingAhmedabad />} />
          <Route path="/services/email/ahmedabad" element={<EmailMarketingAhmedabad />} />
          <Route path="/services/web-development/ahmedabad" element={<WebDevelopmentAhmedabad />} />

          {/* Jaipur Services (6 complete) */}
          <Route path="/services/seo/jaipur" element={<SeoServicesJaipur />} />
          <Route path="/services/ppc/jaipur" element={<GoogleAdsJaipur />} />
          <Route path="/services/social-media/jaipur" element={<SocialMediaJaipur />} />
          <Route path="/services/content/jaipur" element={<ContentMarketingJaipur />} />
          <Route path="/services/email/jaipur" element={<EmailMarketingJaipur />} />
          <Route path="/services/web-development/jaipur" element={<WebDevelopmentJaipur />} />

          {/* Lucknow Services (6 complete) */}
          <Route path="/services/seo/lucknow" element={<SeoServicesLucknow />} />
          <Route path="/services/ppc/lucknow" element={<GoogleAdsLucknow />} />
          <Route path="/services/social-media/lucknow" element={<SocialMediaLucknow />} />
          <Route path="/services/content/lucknow" element={<ContentMarketingLucknow />} />
          <Route path="/services/email/lucknow" element={<EmailMarketingLucknow />} />
          <Route path="/services/web-development/lucknow" element={<WebDevelopmentLucknow />} />

          {/* Surat Services (6 complete) */}
          <Route path="/services/seo/surat" element={<SeoServicesSurat />} />
          <Route path="/services/ppc/surat" element={<GoogleAdsSurat />} />
          <Route path="/services/social-media/surat" element={<SocialMediaSurat />} />
          <Route path="/services/content/surat" element={<ContentMarketingSurat />} />
          <Route path="/services/email/surat" element={<EmailMarketingSurat />} />
          <Route path="/services/web-development/surat" element={<WebDevelopmentSurat />} />

          {/* Indore Services (6 complete) */}
          <Route path="/services/seo/indore" element={<SeoServicesIndore />} />
          <Route path="/services/ppc/indore" element={<GoogleAdsIndore />} />
          <Route path="/services/social-media/indore" element={<SocialMediaIndore />} />
          <Route path="/services/content/indore" element={<ContentMarketingIndore />} />
          <Route path="/services/email/indore" element={<EmailMarketingIndore />} />
          <Route path="/services/web-development/indore" element={<WebDevelopmentIndore />} />

          {/* Bhopal Services (6 complete) */}
          <Route path="/services/seo/bhopal" element={<SeoServicesBhopal />} />
          <Route path="/services/ppc/bhopal" element={<GoogleAdsBhopal />} />
          <Route path="/services/social-media/bhopal" element={<SocialMediaBhopal />} />
          <Route path="/services/content/bhopal" element={<ContentMarketingBhopal />} />
          <Route path="/services/email/bhopal" element={<EmailMarketingBhopal />} />
          <Route path="/services/web-development/bhopal" element={<WebDevelopmentBhopal />} />

          {/* Chandigarh Services (6 complete) */}
          <Route path="/services/seo/chandigarh" element={<SeoServicesChandigarh />} />
          <Route path="/services/ppc/chandigarh" element={<GoogleAdsChandigarh />} />
          <Route path="/services/social-media/chandigarh" element={<SocialMediaChandigarh />} />
          <Route path="/services/content/chandigarh" element={<ContentMarketingChandigarh />} />
          <Route path="/services/email/chandigarh" element={<EmailMarketingChandigarh />} />
          <Route path="/services/web-development/chandigarh" element={<WebDevelopmentChandigarh />} />

          {/* Industry-Specific Services */}
          <Route path="/services/ecommerce-seo" element={<EcommerceSeo />} />
          <Route path="/services/healthcare-seo" element={<HealthcareSeo />} />
          <Route path="/services/b2b-seo" element={<B2bSeo />} />
          <Route path="/services/local-business-seo" element={<LocalBusinessSeo />} />
          <Route path="/services/travel-tourism-seo" element={<TravelTourismSeo />} />
          <Route path="/services/real-estate-seo" element={<RealEstateSeo />} />
          <Route path="/services/education-seo" element={<EducationSeo />} />
          <Route path="/services/finance-seo" element={<FinanceSeo />} />
          <Route path="/services/manufacturing-seo" element={<ManufacturingSeo />} />
          <Route path="/services/technology-seo" element={<TechnologySeo />} />
          <Route path="/services/food-restaurant-seo" element={<FoodRestaurantSeo />} />
          <Route path="/services/fashion-retail-seo" element={<FashionRetailSeo />} />
          <Route path="/services/automotive-seo" element={<AutomotiveSeo />} />
          <Route path="/services/legal-services-seo" element={<LegalServicesSeo />} />

          {/* New State Capital Service Pages */}
          <Route path="/services/seo/dehradun" element={<SeoServicesDehradun />} />
          <Route path="/services/ppc/bhubaneswar" element={<PpcServicesBhubaneswar />} />
          <Route path="/services/social-media/gandhinagar" element={<SocialMediaGandhinagar />} />
          <Route path="/services/content/shimla" element={<ContentMarketingShimla />} />
          <Route path="/services/email/panaji" element={<EmailMarketingPanaji />} />
          <Route path="/services/web-development/thiruvananthapuram" element={<WebDevelopmentThiruvananthapuram />} />
          <Route path="/services/seo/raipur" element={<SeoServicesRaipur />} />
          <Route path="/services/ppc/ranchi" element={<PpcServicesRanchi />} />
          <Route path="/services/social-media/guwahati" element={<SocialMediaGuwahati />} />
          <Route path="/services/content/itanagar" element={<ContentMarketingItanagar />} />
          <Route path="/services/email/imphal" element={<EmailMarketingImphal />} />

          {/* Advanced Services */}
          <Route path="/services/ai-automation" element={<AiAutomation />} />
          <Route path="/services/business-automation" element={<BusinessAutomation />} />
          <Route path="/services/business-development" element={<BusinessDevelopment />} />

          <Route path="/services/seo/haryana" element={<SeoServicesHaryana />} />
          <Route path="/services/ppc/gurgaon" element={<GoogleAdsGurgaon />} />
          <Route path="/services/ppc/faridabad" element={<GoogleAdsFaridabad />} />
          <Route path="/services/ppc/delhi" element={<GoogleAdsDelhi />} />
          <Route path="/services/ppc/sonipat" element={<GoogleAdsSonipat />} />
          <Route path="/services/ppc/mumbai" element={<GoogleAdsMumbai />} />
          <Route path="/services/social-media/mumbai" element={<SocialMediaMumbai />} />
          <Route path="/services/content/mumbai" element={<ContentMarketingMumbai />} />
          <Route path="/services/email/mumbai" element={<EmailMarketingMumbai />} />
          <Route path="/services/ppc/bangalore" element={<GoogleAdsBangalore />} />
          <Route path="/services/ppc/chennai" element={<GoogleAdsChennai />} />
          <Route path="/services/social-media/bangalore" element={<SocialMediaBangalore />} />
          <Route path="/services/social-media/chennai" element={<SocialMediaChennai />} />
          <Route path="/services/content/chennai" element={<ContentMarketingChennai />} />
          <Route path="/services/email/chennai" element={<EmailMarketingChennai />} />
          <Route path="/services/content/bangalore" element={<ContentMarketingBangalore />} />
          <Route path="/services/email/bangalore" element={<EmailMarketingBangalore />} />
          <Route path="/services/social-media/delhi" element={<SocialMediaDelhi />} />
          <Route path="/services/social-media/sonipat" element={<SocialMediaSonipat />} />
          <Route path="/services/content/delhi" element={<ContentMarketingDelhi />} />
          <Route path="/services/email/delhi" element={<EmailMarketingDelhi />} />
          <Route path="/services/web-development/delhi" element={<WebDevelopmentDelhi />} />
          <Route path="/services/social-media/gurgaon" element={<SocialMediaMarketingGurgaon />} />
          <Route path="/services/social-media/faridabad" element={<SocialMediaMarketingFaridabad />} />
          <Route path="/services/social-media/delhi" element={<SocialMediaMarketingDelhi />} />
          <Route path="/services/social-media/mumbai" element={<SocialMediaMarketingMumbai />} />

          {/* Industry Pages */}
          <Route path="/industries/real-estate" element={<RealEstateIndustryDigitalMarketing />} />
          <Route path="/industries/healthcare" element={<HealthcareIndustryDigitalMarketing />} />
          <Route path="/industries/education" element={<Education />} />
          <Route path="/industries/ecommerce" element={<Ecommerce />} />
          <Route path="/industries/manufacturing" element={<Manufacturing />} />
          <Route path="/industries/financial-services" element={<FinancialServices />} />
          <Route path="/industries/automotive" element={<AutomotiveIndustryDigitalMarketing />} />
          <Route path="/industries/agriculture" element={<AgricultureIndustryDigitalMarketing />} />
          <Route path="/industries/textile" element={<TextileIndustryDigitalMarketing />} />
          <Route path="/industries/chemical" element={<ChemicalIndustryDigitalMarketing />} />
          <Route path="/industries/pharmaceutical" element={<PharmaceuticalIndustryDigitalMarketing />} />
          <Route path="/industries/it" element={<ITIndustryDigitalMarketing />} />
          <Route path="/industries/steel-metal-digital-marketing" element={<SteelMetalIndustryDigitalMarketing />} />

          {/* Industry-Location Combinations */}
          <Route path="/industries/real-estate-digital-marketing/gurgaon" element={<RealEstateGurgaon />} />
          <Route path="/industries/real-estate-digital-marketing/faridabad" element={<RealEstateFaridabad />} />
          <Route path="/industries/real-estate/delhi" element={<ComingSoon />} />
          <Route path="/industries/real-estate/mumbai" element={<ComingSoon />} />
          <Route path="/industries/healthcare-digital-marketing/gurgaon" element={<HealthcareGurgaon />} />
          <Route path="/industries/healthcare/faridabad" element={<ComingSoon />} />
          <Route path="/industries/healthcare/delhi" element={<ComingSoon />} />
          <Route path="/industries/healthcare/mumbai" element={<ComingSoon />} />
          <Route path="/industries/manufacturing/gurgaon" element={<ComingSoon />} />
          <Route path="/industries/manufacturing/faridabad" element={<ComingSoon />} />
          <Route path="/industries/manufacturing-digital-marketing/haryana" element={<ManufacturingHaryana />} />
          <Route path="/industries/technology-digital-marketing/delhi" element={<TechnologyDelhi />} />
          <Route path="/industries/education-digital-marketing/gurgaon" element={<EducationGurgaon />} />
          <Route path="/industries/healthcare-digital-marketing/mumbai" element={<HealthcareMumbai />} />
          <Route path="/industries/education/faridabad" element={<ComingSoon />} />
          <Route path="/industries/education/delhi" element={<ComingSoon />} />
          <Route path="/industries/automotive/gurgaon" element={<ComingSoon />} />
          <Route path="/industries/automotive/faridabad" element={<ComingSoon />} />
          <Route path="/industries/automotive/haryana" element={<ComingSoon />} />

          {/* Resource Pages */}
          <Route path="/resources/digital-marketing-guide" element={<DigitalMarketingGuide />} />
          <Route path="/resources/seo-tools" element={<SeoTools />} />
          <Route path="/resources/blog" element={<BlogCategories />} />
          <Route path="/resources/case-studies" element={<CaseStudyCategories />} />
          <Route path="/tools/seo-tools" element={<SeoTools />} />
          <Route path="/blog" element={<BlogCategories />} />
          <Route path="/case-studies" element={<CaseStudyCategories />} />
          <Route path="/resources/ai-automation-resources" element={<AiAutomationResources />} />
          <Route path="/resources/industry-reports-resources" element={<IndustryReportsResources />} />
          <Route path="/tools/seo-audit-tool" element={<SeoAuditTool />} />
          <Route path="/testimonials" element={<Testimonials />} />
          <Route path="/awards" element={<Awards />} />
          <Route path="/careers" element={<Careers />} />
          <Route path="/privacy-policy" element={<PrivacyPolicy />} />
          <Route path="/terms-of-service" element={<TermsOfService />} />
          <Route path="/sitemap" element={<Sitemap />} />

          <Route path="/about" element={<About />} />
          <Route path="/case-studies" element={<CaseStudies />} />
          <Route path="/blog" element={<Blog />} />
          <Route path="/contact" element={<Contact />} />
          <Route path="/quote" element={<Quote />} />
          <Route path="/routing-test" element={<RoutingTest />} />
          <Route path="*" element={<NotFound />} />
        </Routes>
      </BrowserRouter>
    </TooltipProvider>
  </QueryClientProvider>
  </HelmetProvider>
);

export default App;
