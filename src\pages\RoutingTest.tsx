import React from 'react';
import { Link } from 'react-router-dom';
import { ExternalLink, CheckCircle, AlertCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';

const RoutingTest = () => {
  const testRoutes = [
    // Main pages
    { name: 'Home', path: '/', type: 'main' },
    { name: 'About', path: '/about', type: 'main' },
    { name: 'Services', path: '/services', type: 'main' },
    { name: 'Contact', path: '/contact', type: 'main' },
    { name: 'Quote', path: '/quote', type: 'main' },
    
    // Service pages
    { name: 'SEO Services', path: '/services/seo', type: 'service' },
    { name: 'Google Ads', path: '/services/ppc', type: 'service' },
    { name: 'Social Media', path: '/services/social-media', type: 'service' },
    { name: 'Content Marketing', path: '/services/content', type: 'service' },
    { name: 'Email Marketing', path: '/services/email', type: 'service' },
    { name: 'Web Development', path: '/services/web-development', type: 'service' },
    
    // Location-specific services
    { name: 'SEO Delhi', path: '/services/seo/delhi', type: 'location' },
    { name: 'Google Ads Mumbai', path: '/services/ppc/mumbai', type: 'location' },
    { name: 'Social Media Bangalore', path: '/services/social-media/bangalore', type: 'location' },
    { name: 'SEO Pune', path: '/services/seo/pune', type: 'location' },
    { name: 'Content Marketing Chennai', path: '/services/content/chennai', type: 'location' },
    
    // Location pages
    { name: 'Delhi Services', path: '/locations/delhi', type: 'location' },
    { name: 'Mumbai Services', path: '/locations/mumbai', type: 'location' },
    { name: 'Bangalore Services', path: '/locations/bangalore', type: 'location' },
  ];

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'main': return 'bg-blue-500/20 text-blue-400 border-blue-500/30';
      case 'service': return 'bg-green-500/20 text-green-400 border-green-500/30';
      case 'location': return 'bg-purple-500/20 text-purple-400 border-purple-500/30';
      default: return 'bg-gray-500/20 text-gray-400 border-gray-500/30';
    }
  };

  const testDirectAccess = (path: string) => {
    window.open(window.location.origin + path, '_blank');
  };

  return (
    <div className="min-h-screen bg-slate-900">
      <Navigation />
      
      <main className="pt-24 pb-16">
        <div className="container mx-auto px-6">
          <div className="max-w-6xl mx-auto">
            {/* Header */}
            <div className="text-center mb-12">
              <h1 className="text-4xl md:text-6xl font-bold mb-6">
                Routing
                <span className="bg-gradient-to-r from-amber-400 to-amber-600 bg-clip-text text-transparent"> Test Page</span>
              </h1>
              <p className="text-xl text-slate-300 mb-8">
                Test all routes to ensure proper SPA routing and direct URL access
              </p>
              
              <div className="bg-slate-800/50 rounded-lg p-6 mb-8">
                <h2 className="text-lg font-semibold text-white mb-4">How to Test:</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-slate-300">
                  <div className="flex items-start space-x-2">
                    <CheckCircle className="w-4 h-4 text-green-400 mt-0.5 flex-shrink-0" />
                    <span><strong>Internal Navigation:</strong> Click links below to test React Router navigation</span>
                  </div>
                  <div className="flex items-start space-x-2">
                    <AlertCircle className="w-4 h-4 text-amber-400 mt-0.5 flex-shrink-0" />
                    <span><strong>Direct Access:</strong> Click "Test Direct" to open URL in new tab</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Route Categories */}
            <div className="space-y-8">
              {['main', 'service', 'location'].map((category) => (
                <div key={category} className="bg-slate-800/30 rounded-2xl p-8">
                  <h3 className="text-2xl font-bold text-white mb-6 capitalize">
                    {category} Pages
                  </h3>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {testRoutes
                      .filter(route => route.type === category)
                      .map((route, index) => (
                        <div
                          key={index}
                          className={`border rounded-lg p-4 ${getTypeColor(route.type)}`}
                        >
                          <div className="flex items-center justify-between mb-3">
                            <h4 className="font-semibold">{route.name}</h4>
                            <span className="text-xs px-2 py-1 rounded bg-slate-700/50">
                              {route.type}
                            </span>
                          </div>
                          
                          <p className="text-sm text-slate-400 mb-4 font-mono">
                            {route.path}
                          </p>
                          
                          <div className="flex space-x-2">
                            <Button asChild size="sm" className="flex-1">
                              <Link to={route.path}>
                                Navigate
                              </Link>
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => testDirectAccess(route.path)}
                              className="flex items-center space-x-1"
                            >
                              <ExternalLink className="w-3 h-3" />
                              <span>Test Direct</span>
                            </Button>
                          </div>
                        </div>
                      ))}
                  </div>
                </div>
              ))}
            </div>

            {/* Instructions */}
            <div className="mt-12 bg-slate-800/50 rounded-2xl p-8">
              <h3 className="text-2xl font-bold text-white mb-6">Routing Configuration Status</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 className="text-lg font-semibold text-green-400 mb-3">✅ Configured For:</h4>
                  <ul className="space-y-2 text-slate-300">
                    <li>• Netlify (_redirects file)</li>
                    <li>• Vercel (vercel.json)</li>
                    <li>• Apache (.htaccess)</li>
                    <li>• GitHub Pages (404.html)</li>
                    <li>• Vite Dev Server (historyApiFallback)</li>
                  </ul>
                </div>
                
                <div>
                  <h4 className="text-lg font-semibold text-amber-400 mb-3">⚠️ Expected Behavior:</h4>
                  <ul className="space-y-2 text-slate-300">
                    <li>• All internal navigation should work</li>
                    <li>• Direct URL access should work</li>
                    <li>• Page refresh should work</li>
                    <li>• 404 pages should redirect properly</li>
                    <li>• No 404 errors on valid routes</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
};

export default RoutingTest;
