import React from 'react';
import { Link } from 'react-router-dom';
import { Share2, CheckCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';
import Sidebar from '@/components/Sidebar';

const SocialMediaChandigarh = () => {
  const stats = [
    { metric: '1250+', description: 'Chandigarh Brands Managed', detail: 'Across all platforms' },
    { metric: '310%', description: 'Average Engagement Growth', detail: 'For Chandigarh clients' },
    { metric: '₹22Cr+', description: 'Revenue Generated', detail: 'Through social campaigns' },
    { metric: '92%', description: 'Brand Awareness Increase', detail: 'Measured growth' }
  ];

  const achievements = [
    'Top Social Media Agency in Chandigarh',
    'Government Sector Social Leaders',
    'Education Industry Social Experts',
    'IT Services Social Specialists',
    'Healthcare Social Champions',
    'Planned City Digital Social Pioneers'
  ];

  return (
    <div className="min-h-screen bg-slate-900 relative">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        <div className="container mx-auto px-6 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            <div className="lg:col-span-3">
              <section className="pt-24 pb-16">
                <div className="max-w-4xl">
                  <div className="inline-flex items-center space-x-2 bg-purple-500/20 border border-purple-500/30 rounded-full px-6 py-2 mb-8">
                    <Share2 className="w-4 h-4 text-purple-400" />
                    <span className="text-purple-400 font-medium">Social Media Chandigarh • Planned City Engagement Excellence</span>
                  </div>
                  
                  <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
                    #1 Social Media Marketing Company in
                    <span className="bg-gradient-to-r from-purple-400 to-purple-600 bg-clip-text text-transparent"> Chandigarh</span>
                  </h1>
                  
                  <p className="text-xl text-slate-300 mb-8">
                    Comprehensive social media marketing services in Chandigarh delivering professional social media management across Facebook, Instagram, LinkedIn, Twitter, and YouTube. Our Chandigarh social media agency provides complete social media marketing solutions including content creation, social media advertising, community management, and social media strategy. Expert social media services with proven engagement growth, brand awareness, and lead generation through strategic social media campaigns and social media optimization for 1250+ Chandigarh brands across government, education, IT, and healthcare sectors.
                  </p>
                  
                  <div className="flex flex-col sm:flex-row gap-4 mb-8">
                    <Button asChild size="lg" className="bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white text-lg px-8 py-4">
                      <Link to="/quote">Get Free Chandigarh Social Media Audit</Link>
                    </Button>
                    <Button asChild size="lg" variant="outline" className="border-purple-500 text-purple-400 hover:bg-purple-500 hover:text-white text-lg px-8 py-4">
                      <Link to="/contact">Call Social Media Experts: +91-**********</Link>
                    </Button>
                  </div>

                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                    {achievements.map((achievement, index) => (
                      <div key={index} className="flex items-center justify-center space-x-2 bg-slate-800/50 rounded-lg p-3">
                        <CheckCircle className="w-4 h-4 text-purple-400 flex-shrink-0" />
                        <span className="text-slate-300">{achievement}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </section>

              <section className="mb-16 bg-slate-800/50 rounded-2xl p-8">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Chandigarh Social Media
                    <span className="bg-gradient-to-r from-purple-400 to-purple-600 bg-clip-text text-transparent"> Success Metrics</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Measurable social media results from Chandigarh businesses across government, education, IT, and healthcare sectors.
                  </p>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
                  {stats.map((stat, index) => (
                    <div key={index} className="text-center">
                      <div className="text-4xl font-bold text-purple-400 mb-2">{stat.metric}</div>
                      <div className="text-white font-semibold mb-1">{stat.description}</div>
                      <div className="text-slate-400">{stat.detail}</div>
                    </div>
                  ))}
                </div>
              </section>

              {/* Internal Linking Section */}
              <section className="mb-16 bg-slate-800/30 rounded-2xl p-8">
                <h3 className="text-2xl font-bold text-white mb-6 text-center">
                  Complete Digital Marketing Solutions in Chandigarh
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  <Link to="/services/seo/chandigarh" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-purple-400 font-semibold mb-2 group-hover:text-purple-300">SEO Services Chandigarh</h4>
                    <p className="text-slate-400 text-sm">Search engine optimization for planned city businesses</p>
                  </Link>
                  <Link to="/services/ppc/chandigarh" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-purple-400 font-semibold mb-2 group-hover:text-purple-300">Google Ads Chandigarh</h4>
                    <p className="text-slate-400 text-sm">PPC advertising for government and education sectors</p>
                  </Link>
                  <Link to="/services/content/chandigarh" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-purple-400 font-semibold mb-2 group-hover:text-purple-300">Content Marketing Chandigarh</h4>
                    <p className="text-slate-400 text-sm">Content creation for IT and healthcare industries</p>
                  </Link>
                  <Link to="/services/email/chandigarh" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-purple-400 font-semibold mb-2 group-hover:text-purple-300">Email Marketing Chandigarh</h4>
                    <p className="text-slate-400 text-sm">Email campaigns for planned city businesses</p>
                  </Link>
                  <Link to="/services/web-development/chandigarh" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-purple-400 font-semibold mb-2 group-hover:text-purple-300">Web Development Chandigarh</h4>
                    <p className="text-slate-400 text-sm">Website development for government and IT sectors</p>
                  </Link>
                  <Link to="/portfolio" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-purple-400 font-semibold mb-2 group-hover:text-purple-300">Our Success Stories</h4>
                    <p className="text-slate-400 text-sm">See social media success from Chandigarh clients</p>
                  </Link>
                </div>
              </section>

              <section className="bg-gradient-to-r from-purple-600 to-purple-500 rounded-2xl p-8 text-center">
                <h2 className="text-3xl md:text-5xl font-bold mb-6">Ready to Dominate Chandigarh Social Media?</h2>
                <p className="text-xl mb-8">
                  Join 1250+ Chandigarh brands that trust GOD Digital Marketing for social media success. Proven strategies that deliver 310% engagement growth and ₹22Cr+ revenue generation.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button asChild size="lg" className="bg-white text-purple-600 hover:bg-slate-100 text-lg px-8 py-4">
                    <Link to="/quote">Get Free Social Media Audit</Link>
                  </Button>
                  <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-purple-600 text-lg px-8 py-4">
                    <Link to="/contact">Call Social Media Experts: +91-**********</Link>
                  </Button>
                </div>
              </section>
            </div>

            <div className="lg:col-span-1">
              <Sidebar currentService="social-media" currentLocation="chandigarh" />
            </div>
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
};

export default SocialMediaChandigarh;
