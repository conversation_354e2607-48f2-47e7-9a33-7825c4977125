import React from 'react';
import { Link } from 'react-router-dom';
import { ArrowRight, MapPin, TrendingUp, Users, CheckCircle, Building, Star, Crown } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';

const Jaipur = () => {
  const services = [
    'SEO Services Jaipur',
    'Google Ads Management Jaipur',
    'Social Media Marketing Jaipur',
    'Local SEO Jaipur',
    'E-commerce SEO Jaipur',
    'Content Marketing Jaipur',
    'Website Development Jaipur',
    'Digital Marketing Consulting Jaipur'
  ];

  const industries = [
    'Tourism & Hospitality Jaipur',
    'Handicrafts & Textiles Jaipur',
    'Jewelry & Gems Jaipur',
    'Real Estate Jaipur',
    'Healthcare Jaipur',
    'Education Jaipur',
    'Manufacturing Jaipur',
    'IT Services Jaipur'
  ];

  const areas = [
    'Malviya Nagar Digital Marketing',
    'Vaishali Nagar SEO Services',
    'Mansarovar Digital Marketing',
    'C-Scheme SEO Services',
    'Bani Park Digital Marketing',
    'Tonk Road SEO Services',
    'Ajmer Road Digital Marketing',
    'JLN Marg SEO Services',
    'Pink City Digital Marketing',
    'Sanganer SEO Services',
    'Bagru Digital Marketing',
    'Chomu SEO Services'
  ];

  const stats = [
    {
      metric: '200+',
      description: 'Jaipur Businesses Served',
      detail: 'Across all sectors'
    },
    {
      metric: '380%',
      description: 'Average Traffic Increase',
      detail: 'For Jaipur clients'
    },
    {
      metric: '₹35L+',
      description: 'Revenue Generated',
      detail: 'For Jaipur businesses'
    }
  ];

  return (
    <div className="min-h-screen bg-slate-900 relative">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        {/* Hero Section */}
        <section className="pt-32 pb-20">
          <div className="container mx-auto px-6">
            <div className="max-w-4xl mx-auto text-center">
              <div className="inline-flex items-center space-x-2 bg-pink-500/20 border border-pink-500/30 rounded-full px-6 py-2 mb-8">
                <Crown className="w-4 h-4 text-pink-400" />
                <span className="text-pink-400 font-medium">Jaipur Digital Marketing • Pink City Heritage</span>
              </div>
              
              <h1 className="text-5xl md:text-6xl font-bold mb-6 leading-tight">
                #1 Digital Marketing Company in
                <span className="bg-gradient-to-r from-pink-400 to-pink-600 bg-clip-text text-transparent"> Jaipur</span>
              </h1>
              
              <p className="text-xl text-slate-300 mb-12 max-w-3xl mx-auto">
                Leading digital marketing agency in Jaipur helping businesses dominate online. From Pink City heritage tourism to modern IT services, we've helped 200+ businesses grow with proven SEO, Google Ads, and digital marketing strategies across all Jaipur areas.
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button asChild size="lg" className="bg-gradient-to-r from-pink-500 to-pink-600 hover:from-pink-600 hover:to-pink-700 text-white text-lg px-8 py-4">
                  <Link to="/quote">Get Free Jaipur SEO Audit</Link>
                </Button>
                <Button asChild size="lg" variant="outline" className="border-pink-500 text-pink-400 hover:bg-pink-500 hover:text-white text-lg px-8 py-4">
                  <Link to="/contact">Call Jaipur Office</Link>
                </Button>
              </div>
            </div>
          </div>
        </section>

        {/* Stats Section */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Jaipur Digital Marketing
                <span className="bg-gradient-to-r from-pink-400 to-pink-600 bg-clip-text text-transparent"> Success Stories</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto mb-12">
                Real results from Jaipur businesses across industries - from heritage tourism companies to modern tech startups in Rajasthan's capital.
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
              {stats.map((stat, index) => (
                <div key={index} className="text-center">
                  <div className="text-5xl font-bold text-pink-400 mb-2">{stat.metric}</div>
                  <div className="text-white font-semibold mb-1">{stat.description}</div>
                  <div className="text-slate-400">{stat.detail}</div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Services Section */}
        <section className="py-20">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Digital Marketing Services in
                <span className="bg-gradient-to-r from-pink-400 to-pink-600 bg-clip-text text-transparent"> Jaipur</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto">
                Comprehensive digital marketing solutions for Jaipur businesses looking to dominate online across all areas of the Pink City.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {services.map((service, index) => (
                <Card key={index} className="bg-slate-900/80 border-pink-500/20 hover:border-pink-500/40 transition-all duration-300">
                  <CardContent className="p-6 text-center">
                    <Building className="w-8 h-8 text-pink-400 mx-auto mb-4" />
                    <h3 className="text-white font-semibold mb-2">{service}</h3>
                    <Button asChild size="sm" variant="outline" className="border-pink-500 text-pink-400 hover:bg-pink-500 hover:text-white">
                      <Link to="/quote">Learn More</Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Industries Section */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Jaipur Industries We
                <span className="bg-gradient-to-r from-pink-400 to-pink-600 bg-clip-text text-transparent"> Serve</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto">
                Specialized digital marketing strategies for Jaipur's unique blend of traditional crafts and modern business landscape.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {industries.map((industry, index) => (
                <Card key={index} className="bg-slate-900/80 border-pink-500/20 hover:border-pink-500/40 transition-all duration-300">
                  <CardContent className="p-6 text-center">
                    <Star className="w-8 h-8 text-pink-400 mx-auto mb-4" />
                    <h3 className="text-white font-semibold mb-2">{industry}</h3>
                    <Button asChild size="sm" variant="outline" className="border-pink-500 text-pink-400 hover:bg-pink-500 hover:text-white">
                      <Link to="/quote">Get Strategy</Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Areas Section */}
        <section className="py-20">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Jaipur Areas We
                <span className="bg-gradient-to-r from-pink-400 to-pink-600 bg-clip-text text-transparent"> Cover</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto">
                Local digital marketing expertise across all major Jaipur areas from heritage Pink City to modern business districts.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {areas.map((area, index) => (
                <Card key={index} className="bg-slate-900/80 border-pink-500/20 hover:border-pink-500/40 transition-all duration-300">
                  <CardContent className="p-6 text-center">
                    <MapPin className="w-8 h-8 text-pink-400 mx-auto mb-4" />
                    <h3 className="text-white font-semibold mb-2">{area}</h3>
                    <Button asChild size="sm" variant="outline" className="border-pink-500 text-pink-400 hover:bg-pink-500 hover:text-white">
                      <Link to="/contact">Contact Us</Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Why Choose Us Section */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Why Choose GOD Digital Marketing in
                <span className="bg-gradient-to-r from-pink-400 to-pink-600 bg-clip-text text-transparent"> Jaipur?</span>
              </h2>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              <Card className="bg-slate-900/80 border-pink-500/20">
                <CardContent className="p-8">
                  <TrendingUp className="w-12 h-12 text-pink-400 mb-6" />
                  <h3 className="text-xl font-bold text-white mb-4">Local Jaipur Expertise</h3>
                  <p className="text-slate-300">Deep understanding of Jaipur's tourism economy, traditional crafts market, and emerging business sectors across the Pink City.</p>
                </CardContent>
              </Card>

              <Card className="bg-slate-900/80 border-pink-500/20">
                <CardContent className="p-8">
                  <Users className="w-12 h-12 text-pink-400 mb-6" />
                  <h3 className="text-xl font-bold text-white mb-4">Proven Jaipur Results</h3>
                  <p className="text-slate-300">200+ successful Jaipur campaigns with measurable ROI improvements and business growth across traditional and modern industries.</p>
                </CardContent>
              </Card>

              <Card className="bg-slate-900/80 border-pink-500/20">
                <CardContent className="p-8">
                  <CheckCircle className="w-12 h-12 text-pink-400 mb-6" />
                  <h3 className="text-xl font-bold text-white mb-4">Heritage & Modern Balance</h3>
                  <p className="text-slate-300">Unique expertise in marketing both traditional Rajasthani businesses and modern enterprises with cultural sensitivity.</p>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 bg-gradient-to-r from-pink-600 to-pink-500">
          <div className="container mx-auto px-6 text-center">
            <h2 className="text-4xl md:text-5xl font-bold mb-6">Ready to Dominate Jaipur's Digital Market?</h2>
            <p className="text-xl mb-8 max-w-3xl mx-auto">
              Join 200+ Jaipur businesses that trust GOD Digital Marketing for their online growth. From Pink City heritage to modern business districts, we deliver results.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button asChild size="lg" className="bg-white text-pink-600 hover:bg-slate-100 text-lg px-8 py-4">
                <Link to="/quote">Get Free Jaipur SEO Audit</Link>
              </Button>
              <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-pink-600 text-lg px-8 py-4">
                <Link to="/contact">Call Jaipur Office Now</Link>
              </Button>
            </div>
          </div>
        </section>
      </main>
      
      <Footer />
    </div>
  );
};

export default Jaipur;
