import React from 'react';
import { Link } from 'react-router-dom';
import { ArrowRight, Facebook, TrendingUp, Users, CheckCircle, Target, Star, Zap } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';

const FacebookAdsServices = () => {
  const features = [
    'Facebook Ads Campaign Strategy & Setup',
    'Instagram Ads Management & Optimization',
    'Advanced Audience Targeting & Lookalikes',
    'Facebook Pixel Implementation & Tracking',
    'Creative Design & Video Ad Production',
    'A/B Testing & Performance Optimization',
    'Facebook Shop & Catalog Management',
    'Lead Generation & Conversion Campaigns',
    'Retargeting & Custom Audience Creation',
    'Facebook Analytics & ROI Reporting'
  ];

  const packages = [
    {
      name: 'Facebook Ads Starter',
      price: '₹25,000',
      period: '/month',
      description: 'Perfect for small businesses starting Facebook advertising',
      features: [
        'Facebook & Instagram Ads Setup',
        'Basic Audience Targeting',
        'Creative Design (5 ads)',
        'Campaign Optimization',
        'Monthly Performance Reports'
      ]
    },
    {
      name: 'Facebook Ads Pro',
      price: '₹45,000',
      period: '/month',
      description: 'Comprehensive Facebook advertising for growing businesses',
      features: [
        'Everything in Starter',
        'Advanced Audience Research',
        'Video Ad Creation',
        'Retargeting Campaigns',
        'A/B Testing & Optimization',
        'Weekly Performance Reviews'
      ],
      popular: true
    },
    {
      name: 'Facebook Ads Enterprise',
      price: '₹75,000',
      period: '/month',
      description: 'Enterprise Facebook advertising for large-scale campaigns',
      features: [
        'Everything in Pro',
        'Multi-Account Management',
        'Advanced Attribution Modeling',
        'Custom Audience Development',
        'Dedicated Facebook Ads Manager',
        'Daily Optimization & Reporting'
      ]
    }
  ];

  const results = [
    {
      metric: '400%',
      description: 'Average ROAS achieved',
      timeframe: 'for Facebook ad campaigns'
    },
    {
      metric: '60%',
      description: 'Reduction in cost per lead',
      timeframe: 'through optimization'
    },
    {
      metric: '₹2Cr+',
      description: 'Revenue generated',
      timeframe: 'through Facebook ads'
    }
  ];

  const adTypes = [
    {
      type: 'Lead Generation Ads',
      description: 'Capture high-quality leads directly within Facebook platform',
      icon: Target,
      benefits: ['Instant lead capture', 'Pre-filled forms', 'CRM integration', 'Lower cost per lead']
    },
    {
      type: 'E-commerce Ads',
      description: 'Drive sales and revenue with product catalog and shopping ads',
      icon: Star,
      benefits: ['Product catalog sync', 'Dynamic retargeting', 'Shopping campaigns', 'Revenue tracking']
    },
    {
      type: 'Brand Awareness Ads',
      description: 'Build brand recognition and reach new audiences effectively',
      icon: Zap,
      benefits: ['Massive reach', 'Brand recall lift', 'Video engagement', 'Audience insights']
    }
  ];

  return (
    <div className="min-h-screen bg-slate-900 relative">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        {/* Hero Section */}
        <section className="pt-32 pb-20">
          <div className="container mx-auto px-6">
            <div className="max-w-4xl mx-auto text-center">
              <div className="inline-flex items-center space-x-2 bg-blue-500/20 border border-blue-500/30 rounded-full px-6 py-2 mb-8">
                <Facebook className="w-4 h-4 text-blue-400" />
                <span className="text-blue-400 font-medium">Facebook Ads Services • Meta Advertising Experts</span>
              </div>
              
              <h1 className="text-5xl md:text-6xl font-bold mb-6 leading-tight">
                Facebook Ads Management That
                <span className="bg-gradient-to-r from-blue-400 to-blue-600 bg-clip-text text-transparent"> Drives Real Results</span>
              </h1>
              
              <p className="text-xl text-slate-300 mb-12 max-w-3xl mx-auto">
                Expert Facebook and Instagram advertising services that generate leads, drive sales, and grow your business. With 7 years of international experience, we've generated ₹2Cr+ revenue through strategic Facebook ad campaigns across multiple countries.
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button asChild size="lg" className="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white text-lg px-8 py-4">
                  <Link to="/quote">Get Facebook Ads Strategy</Link>
                </Button>
                <Button asChild size="lg" variant="outline" className="border-blue-500 text-blue-400 hover:bg-blue-500 hover:text-white text-lg px-8 py-4">
                  <Link to="/case-studies">View Facebook Ad Results</Link>
                </Button>
              </div>
            </div>
          </div>
        </section>

        {/* Results Section */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Facebook Ads
                <span className="bg-gradient-to-r from-blue-400 to-blue-600 bg-clip-text text-transparent"> Performance Results</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto mb-12">
                Real results from Facebook advertising campaigns across industries and international markets.
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
              {results.map((result, index) => (
                <div key={index} className="text-center">
                  <div className="text-5xl font-bold text-blue-400 mb-2">{result.metric}</div>
                  <div className="text-white font-semibold mb-1">{result.description}</div>
                  <div className="text-slate-400">{result.timeframe}</div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Ad Types Section */}
        <section className="py-20">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Facebook Ad Types We
                <span className="bg-gradient-to-r from-blue-400 to-blue-600 bg-clip-text text-transparent"> Specialize In</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto">
                Strategic Facebook advertising campaigns tailored to your specific business goals and objectives.
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-20">
              {adTypes.map((adType, index) => (
                <Card key={index} className="bg-slate-900/80 border-blue-500/20 hover:border-blue-500/40 transition-all duration-300">
                  <CardContent className="p-8">
                    <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg flex items-center justify-center mb-6">
                      <adType.icon className="w-6 h-6 text-white" />
                    </div>
                    <h3 className="text-xl font-bold text-white mb-4">{adType.type}</h3>
                    <p className="text-slate-300 mb-6">{adType.description}</p>
                    <ul className="space-y-2">
                      {adType.benefits.map((benefit, idx) => (
                        <li key={idx} className="flex items-center text-slate-400 text-sm">
                          <CheckCircle className="w-3 h-3 text-blue-400 mr-2 flex-shrink-0" />
                          {benefit}
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Features Section */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Facebook Ads Management
                <span className="bg-gradient-to-r from-blue-400 to-blue-600 bg-clip-text text-transparent"> Features</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto">
                Comprehensive Facebook advertising services that maximize your return on ad spend.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-w-4xl mx-auto">
              {features.map((feature, index) => (
                <div key={index} className="flex items-center space-x-3">
                  <CheckCircle className="w-6 h-6 text-blue-400 flex-shrink-0" />
                  <span className="text-slate-300">{feature}</span>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Pricing Section */}
        <section className="py-20">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Facebook Ads Management
                <span className="bg-gradient-to-r from-blue-400 to-blue-600 bg-clip-text text-transparent"> Packages</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto">
                Choose the perfect Facebook advertising package to scale your business and maximize ROI.
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
              {packages.map((pkg, index) => (
                <Card key={index} className={`bg-slate-900/80 border-blue-500/20 hover:border-blue-500/40 transition-all duration-300 relative ${pkg.popular ? 'ring-2 ring-blue-500' : ''}`}>
                  {pkg.popular && (
                    <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                      <span className="bg-blue-500 text-white px-4 py-2 rounded-full text-sm font-semibold">Most Popular</span>
                    </div>
                  )}
                  <CardContent className="p-8">
                    <h3 className="text-2xl font-bold text-white mb-2">{pkg.name}</h3>
                    <p className="text-slate-300 mb-6">{pkg.description}</p>
                    <div className="mb-6">
                      <span className="text-4xl font-bold text-blue-400">{pkg.price}</span>
                      <span className="text-slate-400">{pkg.period}</span>
                    </div>
                    <ul className="space-y-3 mb-8">
                      {pkg.features.map((feature, idx) => (
                        <li key={idx} className="flex items-center text-slate-300">
                          <CheckCircle className="w-4 h-4 text-blue-400 mr-3 flex-shrink-0" />
                          {feature}
                        </li>
                      ))}
                    </ul>
                    <Button asChild className={`w-full ${pkg.popular ? 'bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700' : 'bg-slate-700 hover:bg-slate-600'} text-white`}>
                      <Link to="/quote">Get Started</Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 bg-gradient-to-r from-blue-600 to-blue-500">
          <div className="container mx-auto px-6 text-center">
            <h2 className="text-4xl md:text-5xl font-bold mb-6">Ready to Scale with Facebook Ads?</h2>
            <p className="text-xl mb-8 max-w-3xl mx-auto">
              Join businesses generating ₹2Cr+ revenue through strategic Facebook advertising. Expert campaign management with proven 400% ROAS results.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button asChild size="lg" className="bg-white text-blue-600 hover:bg-slate-100 text-lg px-8 py-4">
                <Link to="/quote">Get Facebook Ads Strategy</Link>
              </Button>
              <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-blue-600 text-lg px-8 py-4">
                <Link to="/contact">Book Facebook Ads Consultation</Link>
              </Button>
            </div>
          </div>
        </section>
      </main>
      
      <Footer />
    </div>
  );
};

export default FacebookAdsServices;
