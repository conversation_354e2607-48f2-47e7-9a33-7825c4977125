import React from 'react';
import { Link } from 'react-router-dom';
import { ArrowRight, MapPin, TrendingUp, Users, CheckCircle, Building, Star, Wheat } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';

const Sirsa = () => {
  const allServices = [
    'SEO Services Sirsa',
    'Local SEO Sirsa', 
    'International SEO Sirsa',
    'E-commerce SEO Sirsa',
    'Technical SEO Sirsa',
    'Enterprise SEO Sirsa',
    'Google Ads Management Sirsa',
    'Facebook Ads Sirsa',
    'LinkedIn Ads Sirsa',
    'YouTube Marketing Sirsa',
    'Social Media Marketing Sirsa',
    'Content Marketing Sirsa',
    'AI Content Creation Sirsa',
    'Social Media Automation Sirsa',
    'Business Process Automation Sirsa',
    'Marketing Automation Sirsa',
    'Lead Generation Sirsa',
    'Website Development Sirsa',
    'Conversion Optimization Sirsa',
    'Email Marketing Sirsa',
    'Influencer Marketing Sirsa',
    'Video Marketing Sirsa',
    'AI Chatbot Development Sirsa',
    'CRM Solutions Sirsa',
    'Sales Funnel Automation Sirsa',
    'Digital Strategy Consulting Sirsa',
    'Online Reputation Management Sirsa'
  ];

  const industries = [
    'Agriculture & Farming Sirsa',
    'Cotton Industry Sirsa',
    'Food Processing Sirsa',
    'Dairy Industry Sirsa',
    'Manufacturing Sirsa',
    'Real Estate Sirsa',
    'Healthcare Sirsa',
    'Education Sirsa',
    'Retail & E-commerce Sirsa',
    'Export-Import Sirsa'
  ];

  const areas = [
    'Ellenabad Digital Marketing',
    'Dabwali SEO Services', 
    'Kalanwali Digital Marketing',
    'Odhan SEO Services',
    'Nathusari Chopta Digital Marketing',
    'Baragudha SEO Services',
    'Sirsa City Digital Marketing',
    'Industrial Area SEO Services',
    'Rania Digital Marketing',
    'Bhadra SEO Services'
  ];

  const serviceCategories = [
    {
      category: 'SEO Services Sirsa',
      services: ['Local SEO', 'International SEO', 'E-commerce SEO', 'Technical SEO', 'Enterprise SEO'],
      description: 'Complete SEO solutions for Sirsa agricultural and cotton industry businesses'
    },
    {
      category: 'Paid Advertising Sirsa', 
      services: ['Google Ads', 'Facebook Ads', 'LinkedIn Ads', 'YouTube Marketing'],
      description: 'Strategic paid advertising campaigns for Sirsa farming and food processing industries'
    },
    {
      category: 'AI Automation Sirsa',
      services: ['AI Content Creation', 'Social Media Automation', 'Business Process Automation', 'Marketing Automation'],
      description: 'Advanced AI-powered automation solutions for Sirsa agricultural sector'
    },
    {
      category: 'Digital Strategy Sirsa',
      services: ['Content Marketing', 'Social Media Marketing', 'Email Marketing', 'Digital Consulting'],
      description: 'Comprehensive digital strategy for Sirsa export and agricultural businesses'
    }
  ];

  const stats = [
    {
      metric: '55+',
      description: 'Sirsa Businesses Served',
      detail: 'Across all sectors'
    },
    {
      metric: '360%',
      description: 'Average Traffic Increase',
      detail: 'For Sirsa clients'
    },
    {
      metric: '₹20L+',
      description: 'Revenue Generated',
      detail: 'For Sirsa businesses'
    }
  ];

  return (
    <div className="min-h-screen bg-slate-900 relative">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        {/* Hero Section */}
        <section className="pt-32 pb-20">
          <div className="container mx-auto px-6">
            <div className="max-w-4xl mx-auto text-center">
              <div className="inline-flex items-center space-x-2 bg-amber-500/20 border border-amber-500/30 rounded-full px-6 py-2 mb-8">
                <Wheat className="w-4 h-4 text-amber-400" />
                <span className="text-amber-400 font-medium">Sirsa Digital Marketing • Agricultural Hub of Haryana</span>
              </div>
              
              <h1 className="text-5xl md:text-6xl font-bold mb-6 leading-tight">
                Complete Digital Marketing Services in
                <span className="bg-gradient-to-r from-amber-400 to-amber-600 bg-clip-text text-transparent"> Sirsa</span>
              </h1>
              
              <p className="text-xl text-slate-300 mb-12 max-w-3xl mx-auto">
                Leading digital marketing agency in Sirsa offering ALL 25+ services from SEO to AI automation. From agricultural enterprises to cotton industries, we've helped 55+ businesses grow with proven strategies across all Sirsa sectors and areas.
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button asChild size="lg" className="bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700 text-white text-lg px-8 py-4">
                  <Link to="/quote">Get Free Sirsa Digital Marketing Audit</Link>
                </Button>
                <Button asChild size="lg" variant="outline" className="border-amber-500 text-amber-400 hover:bg-amber-500 hover:text-white text-lg px-8 py-4">
                  <Link to="/contact">Call Sirsa Office</Link>
                </Button>
              </div>
            </div>
          </div>
        </section>

        {/* Stats Section */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Sirsa Digital Marketing
                <span className="bg-gradient-to-r from-amber-400 to-amber-600 bg-clip-text text-transparent"> Success Stories</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto mb-12">
                Real results from Sirsa businesses across all industries - from agricultural enterprises to cotton processors in this farming hub.
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
              {stats.map((stat, index) => (
                <div key={index} className="text-center">
                  <div className="text-5xl font-bold text-amber-400 mb-2">{stat.metric}</div>
                  <div className="text-white font-semibold mb-1">{stat.description}</div>
                  <div className="text-slate-400">{stat.detail}</div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Service Categories Section */}
        <section className="py-20">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Complete Digital Marketing Services in
                <span className="bg-gradient-to-r from-amber-400 to-amber-600 bg-clip-text text-transparent"> Sirsa</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto">
                All 25+ digital marketing services available for Sirsa businesses - from SEO to AI automation.
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-20">
              {serviceCategories.map((category, index) => (
                <Card key={index} className="bg-slate-900/80 border-amber-500/20 hover:border-amber-500/40 transition-all duration-300">
                  <CardContent className="p-8">
                    <h3 className="text-xl font-bold text-white mb-4">{category.category}</h3>
                    <p className="text-slate-300 mb-6">{category.description}</p>
                    <div className="grid grid-cols-2 gap-2">
                      {category.services.map((service, idx) => (
                        <div key={idx} className="flex items-center text-slate-400 text-sm">
                          <CheckCircle className="w-3 h-3 text-amber-400 mr-2 flex-shrink-0" />
                          {service}
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* All Services Grid */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                All Digital Marketing Services
                <span className="bg-gradient-to-r from-amber-400 to-amber-600 bg-clip-text text-transparent"> Available in Sirsa</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto">
                Complete range of 25+ digital marketing services for every business need in Sirsa.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {allServices.map((service, index) => (
                <Card key={index} className="bg-slate-900/80 border-amber-500/20 hover:border-amber-500/40 transition-all duration-300">
                  <CardContent className="p-4 text-center">
                    <h3 className="text-white font-semibold text-sm mb-2">{service}</h3>
                    <Button asChild size="sm" variant="outline" className="border-amber-500 text-amber-400 hover:bg-amber-500 hover:text-white text-xs">
                      <Link to="/quote">Get Quote</Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Industries Section */}
        <section className="py-20">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Sirsa Industries We
                <span className="bg-gradient-to-r from-amber-400 to-amber-600 bg-clip-text text-transparent"> Serve</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto">
                Specialized digital marketing strategies for all major Sirsa industries including agriculture, cotton, and food processing.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
              {industries.map((industry, index) => (
                <Card key={index} className="bg-slate-900/80 border-amber-500/20 hover:border-amber-500/40 transition-all duration-300">
                  <CardContent className="p-6 text-center">
                    <Star className="w-8 h-8 text-amber-400 mx-auto mb-4" />
                    <h3 className="text-white font-semibold mb-2 text-sm">{industry}</h3>
                    <Button asChild size="sm" variant="outline" className="border-amber-500 text-amber-400 hover:bg-amber-500 hover:text-white text-xs">
                      <Link to="/quote">Get Strategy</Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Areas Section */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Sirsa Areas We
                <span className="bg-gradient-to-r from-amber-400 to-amber-600 bg-clip-text text-transparent"> Cover</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto">
                Complete digital marketing services across all Sirsa areas and surrounding agricultural regions.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
              {areas.map((area, index) => (
                <Card key={index} className="bg-slate-900/80 border-amber-500/20 hover:border-amber-500/40 transition-all duration-300">
                  <CardContent className="p-6 text-center">
                    <MapPin className="w-8 h-8 text-amber-400 mx-auto mb-4" />
                    <h3 className="text-white font-semibold mb-2 text-sm">{area}</h3>
                    <Button asChild size="sm" variant="outline" className="border-amber-500 text-amber-400 hover:bg-amber-500 hover:text-white text-xs">
                      <Link to="/contact">Contact Us</Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Why Choose Us Section */}
        <section className="py-20">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Why Choose GOD Digital Marketing in
                <span className="bg-gradient-to-r from-amber-400 to-amber-600 bg-clip-text text-transparent"> Sirsa?</span>
              </h2>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              <Card className="bg-slate-900/80 border-amber-500/20">
                <CardContent className="p-8">
                  <TrendingUp className="w-12 h-12 text-amber-400 mb-6" />
                  <h3 className="text-xl font-bold text-white mb-4">Complete Service Portfolio</h3>
                  <p className="text-slate-300">All 25+ digital marketing services available in Sirsa - from basic SEO to advanced AI automation for agricultural and cotton industries.</p>
                </CardContent>
              </Card>

              <Card className="bg-slate-900/80 border-amber-500/20">
                <CardContent className="p-8">
                  <Users className="w-12 h-12 text-amber-400 mb-6" />
                  <h3 className="text-xl font-bold text-white mb-4">Proven Sirsa Results</h3>
                  <p className="text-slate-300">55+ successful Sirsa campaigns with measurable ROI improvements across agricultural, cotton, and food processing industries.</p>
                </CardContent>
              </Card>

              <Card className="bg-slate-900/80 border-amber-500/20">
                <CardContent className="p-8">
                  <CheckCircle className="w-12 h-12 text-amber-400 mb-6" />
                  <h3 className="text-xl font-bold text-white mb-4">Agricultural Hub Expertise</h3>
                  <p className="text-slate-300">Deep understanding of Sirsa's agricultural economy, cotton industry, and food processing business dynamics.</p>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 bg-gradient-to-r from-amber-600 to-amber-500">
          <div className="container mx-auto px-6 text-center">
            <h2 className="text-4xl md:text-5xl font-bold mb-6">Ready to Dominate Sirsa's Digital Market?</h2>
            <p className="text-xl mb-8 max-w-3xl mx-auto">
              Join 55+ Sirsa businesses that trust GOD Digital Marketing for complete digital solutions. All 25+ services available for your agricultural, cotton, or food processing business growth.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button asChild size="lg" className="bg-white text-amber-600 hover:bg-slate-100 text-lg px-8 py-4">
                <Link to="/quote">Get Free Digital Marketing Audit</Link>
              </Button>
              <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-amber-600 text-lg px-8 py-4">
                <Link to="/contact">Call Sirsa Office Now</Link>
              </Button>
            </div>
          </div>
        </section>
      </main>
      
      <Footer />
    </div>
  );
};

export default Sirsa;
