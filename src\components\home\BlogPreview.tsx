
import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { ArrowR<PERSON>, Clock, User } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';

const BlogPreview = () => {
  const posts = [
    {
      title: '10 SEO Strategies That Will Transform Your Rankings in 2024',
      excerpt: 'Discover the latest SEO techniques that are driving massive organic growth for our clients this year.',
      author: '<PERSON>',
      date: '5 min read',
      image: 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=400&h=250&fit=crop',
      category: 'SEO'
    },
    {
      title: 'The Ultimate Guide to PPC Campaign Optimization',
      excerpt: 'Learn how to maximize your ad spend ROI with advanced PPC optimization strategies and best practices.',
      author: '<PERSON>',
      date: '8 min read',
      image: 'https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=400&h=250&fit=crop',
      category: 'PPC'
    },
    {
      title: 'Social Media Marketing: Building Brand Authority in 2024',
      excerpt: 'Explore cutting-edge social media strategies that build authentic connections and drive conversions.',
      author: '<PERSON>',
      date: '6 min read',
      image: 'https://images.unsplash.com/photo-1611224923853-80b023f02d71?w=400&h=250&fit=crop',
      category: 'Social Media'
    }
  ];

  return (
    <section className="py-20 relative">
      <div className="container mx-auto px-6">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold mb-6">
            Latest Insights &
            <span className="bg-gradient-to-r from-amber-400 to-amber-600 bg-clip-text text-transparent"> Expert Tips</span>
          </h2>
          <p className="text-xl text-slate-300 max-w-3xl mx-auto mb-8">
            Stay ahead of the curve with our latest digital marketing insights, strategies, and industry trends.
          </p>
          <Button asChild variant="outline" className="border-amber-500 text-amber-400 hover:bg-amber-500 hover:text-white">
            <Link to="/blog">View All Articles</Link>
          </Button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {posts.map((post, index) => (
            <Card key={index} className="bg-slate-900/80 border-amber-500/20 hover:border-amber-500/40 transition-all duration-300 overflow-hidden group">
              <div className="relative overflow-hidden">
                <img 
                  src={post.image} 
                  alt={post.title}
                  className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                />
                <div className="absolute top-4 left-4">
                  <span className="bg-amber-500 text-white px-3 py-1 rounded-full text-sm font-medium">
                    {post.category}
                  </span>
                </div>
              </div>
              <CardContent className="p-6">
                <h3 className="text-xl font-bold text-white mb-3 group-hover:text-amber-400 transition-colors">
                  {post.title}
                </h3>
                <p className="text-slate-300 mb-4 line-clamp-3">
                  {post.excerpt}
                </p>
                <div className="flex items-center justify-between text-sm text-slate-400 mb-4">
                  <div className="flex items-center">
                    <User className="w-4 h-4 mr-1" />
                    {post.author}
                  </div>
                  <div className="flex items-center">
                    <Clock className="w-4 h-4 mr-1" />
                    {post.date}
                  </div>
                </div>
                <Button asChild variant="ghost" className="text-amber-400 hover:text-white hover:bg-amber-500 p-0">
                  <Link to="/blog" className="flex items-center">
                    Read More
                    <ArrowRight className="ml-2 w-4 h-4 group-hover:translate-x-1 transition-transform" />
                  </Link>
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
};

export default BlogPreview;
