import React from 'react';
import { Link } from 'react-router-dom';
import { ArrowRight, Share2, TrendingUp, Users, CheckCircle, Building, Star, Crown, Heart, Zap } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';
import Sidebar from '@/components/Sidebar';

const SocialMediaJaipur = () => {
  const socialMediaServices = [
    'Social Media Marketing Jaipur',
    'Facebook Marketing Jaipur',
    'Instagram Marketing Jaipur',
    'LinkedIn Marketing Jaipur',
    'Twitter Marketing Jaipur',
    'YouTube Marketing Jaipur',
    'Social Media Management Jaipur',
    'Content Creation Jaipur',
    'Influencer Marketing Jaipur',
    'Social Media Advertising Jaipur',
    'Community Management Jaipur',
    'Brand Building Jaipur',
    'Tourism Social Media Jaipur',
    'Heritage Business Social Media Jaipur',
    'Handicraft Social Media Jaipur',
    'Jewelry Social Media Jaipur',
    'Hotel Social Media Jaipur',
    'Restaurant Social Media Jaipur',
    'Real Estate Social Media Jaipur',
    'Fashion Social Media Jaipur'
  ];

  const socialMediaTypes = [
    {
      type: 'Tourism & Heritage Social Media',
      description: 'Engaging social media strategies for Jaipur\'s tourism capital and heritage industry businesses',
      icon: Building,
      features: ['Tourism Content Creation', 'Heritage Storytelling', 'Hotel & Resort Social Media', 'Travel Experience Sharing']
    },
    {
      type: 'Handicraft & Jewelry Social Media',
      description: 'Creative social media for Jaipur\'s famous handicraft and jewelry manufacturing sector',
      icon: Star,
      features: ['Artisan Showcases', 'Jewelry Visual Content', 'Craft Process Videos', 'Export Market Reach']
    },
    {
      type: 'Fashion & Textile Social Media',
      description: 'Trendy social media for Jaipur\'s textile and fashion industry leaders',
      icon: Crown,
      features: ['Fashion Brand Building', 'Textile Showcases', 'Designer Collections', 'Style Influencer Partnerships']
    },
    {
      type: 'Local Business Social Media',
      description: 'Community-focused social media for Jaipur\'s local businesses and services',
      icon: Heart,
      features: ['Local Community Engagement', 'Regional Content', 'Cultural Celebrations', 'Customer Stories']
    }
  ];

  const socialMediaPackages = [
    {
      name: 'Social Media Jaipur Starter',
      price: '₹18,000',
      period: '/month',
      description: 'Perfect for small Jaipur businesses and tourism operators',
      features: [
        '3 Social Media Platforms',
        '20 Posts per Month',
        'Basic Graphics & Content',
        'Community Management',
        'Monthly Analytics',
        'Tourism Industry Focus'
      ]
    },
    {
      name: 'Jaipur Social Media Professional',
      price: '₹32,000',
      period: '/month',
      description: 'Comprehensive social media for growing Jaipur businesses',
      features: [
        '5 Social Media Platforms',
        '40 Posts per Month',
        'Professional Content Creation',
        'Paid Social Campaigns',
        'Influencer Collaborations',
        'Heritage/Handicraft Specialization',
        'Bi-weekly Reporting'
      ],
      popular: true
    },
    {
      name: 'Enterprise Social Media Jaipur',
      price: '₹58,000',
      period: '/month',
      description: 'Advanced social media for large Jaipur enterprises and exporters',
      features: [
        'All Major Platforms',
        'Unlimited Content',
        'Video Content Production',
        'Advanced Analytics',
        'Crisis Management',
        'Export Market Strategy',
        'Dedicated Social Manager'
      ]
    }
  ];

  const stats = [
    {
      metric: '1100+',
      description: 'Jaipur Brands Managed',
      detail: 'Across all platforms'
    },
    {
      metric: '280%',
      description: 'Average Engagement Growth',
      detail: 'For Jaipur clients'
    },
    {
      metric: '₹18Cr+',
      description: 'Revenue Generated',
      detail: 'Through social campaigns'
    },
    {
      metric: '92%',
      description: 'Brand Awareness Increase',
      detail: 'Measured growth'
    }
  ];

  const achievements = [
    'Top Social Media Agency in Jaipur',
    'Tourism Industry Social Leaders',
    'Heritage Business Social Experts',
    'Handicraft Industry Social Specialists',
    'Jewelry Business Social Champions',
    'Pink City Digital Social Pioneers'
  ];

  return (
    <div className="min-h-screen bg-slate-900 relative">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        <div className="container mx-auto px-6 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            {/* Main Content */}
            <div className="lg:col-span-3">
              {/* Hero Section */}
              <section className="pt-24 pb-16">
                <div className="max-w-4xl">
                  <div className="inline-flex items-center space-x-2 bg-purple-500/20 border border-purple-500/30 rounded-full px-6 py-2 mb-8">
                    <Share2 className="w-4 h-4 text-purple-400" />
                    <span className="text-purple-400 font-medium">Social Media Jaipur • Pink City Engagement Excellence</span>
                  </div>
                  
                  <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
                    #1 Social Media Marketing Company in
                    <span className="bg-gradient-to-r from-purple-400 to-purple-600 bg-clip-text text-transparent"> Jaipur</span>
                  </h1>
                  
                  <p className="text-xl text-slate-300 mb-8">
                    Comprehensive social media marketing services in Jaipur delivering professional social media management across Facebook, Instagram, LinkedIn, Twitter, and YouTube. Our Jaipur social media agency provides complete social media marketing solutions including content creation, social media advertising, community management, and social media strategy. Expert social media services with proven engagement growth, brand awareness, and lead generation through strategic social media campaigns and social media optimization for 1100+ Jaipur brands across tourism, handicraft, jewelry, and heritage sectors.
                  </p>
                  
                  <div className="flex flex-col sm:flex-row gap-4 mb-8">
                    <Button asChild size="lg" className="bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white text-lg px-8 py-4">
                      <Link to="/quote">Get Free Jaipur Social Media Audit</Link>
                    </Button>
                    <Button asChild size="lg" variant="outline" className="border-purple-500 text-purple-400 hover:bg-purple-500 hover:text-white text-lg px-8 py-4">
                      <Link to="/contact">Call Social Media Experts: +91-8708577598</Link>
                    </Button>
                  </div>

                  {/* Achievements */}
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                    {achievements.map((achievement, index) => (
                      <div key={index} className="flex items-center justify-center space-x-2 bg-slate-800/50 rounded-lg p-3">
                        <CheckCircle className="w-4 h-4 text-purple-400 flex-shrink-0" />
                        <span className="text-slate-300">{achievement}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </section>

              {/* Stats Section */}
              <section className="mb-16 bg-slate-800/50 rounded-2xl p-8">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Jaipur Social Media
                    <span className="bg-gradient-to-r from-purple-400 to-purple-600 bg-clip-text text-transparent"> Success Metrics</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Measurable social media results from Jaipur businesses across tourism, handicraft, jewelry, and heritage sectors.
                  </p>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
                  {stats.map((stat, index) => (
                    <div key={index} className="text-center">
                      <div className="text-4xl font-bold text-purple-400 mb-2">{stat.metric}</div>
                      <div className="text-white font-semibold mb-1">{stat.description}</div>
                      <div className="text-slate-400">{stat.detail}</div>
                    </div>
                  ))}
                </div>
              </section>

              {/* Social Media Types Section */}
              <section className="mb-16">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Social Media Strategies We
                    <span className="bg-gradient-to-r from-purple-400 to-purple-600 bg-clip-text text-transparent"> Master</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Specialized social media strategies designed for Jaipur's unique tourism, handicraft, jewelry, and heritage business landscape.
                  </p>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  {socialMediaTypes.map((social, index) => (
                    <Card key={index} className="bg-slate-900/80 border-purple-500/20 hover:border-purple-500/40 transition-all duration-300">
                      <CardContent className="p-8">
                        <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg flex items-center justify-center mb-6">
                          <social.icon className="w-6 h-6 text-white" />
                        </div>
                        <h3 className="text-xl font-bold text-white mb-4">{social.type}</h3>
                        <p className="text-slate-300 mb-6">{social.description}</p>
                        <ul className="space-y-2">
                          {social.features.map((feature, idx) => (
                            <li key={idx} className="flex items-center text-slate-400 text-sm">
                              <CheckCircle className="w-3 h-3 text-purple-400 mr-2 flex-shrink-0" />
                              {feature}
                            </li>
                          ))}
                        </ul>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>

              {/* Pricing Section */}
              <section className="mb-16">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Jaipur Social Media
                    <span className="bg-gradient-to-r from-purple-400 to-purple-600 bg-clip-text text-transparent"> Pricing</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Competitive social media pricing designed for Jaipur's tourism, handicraft, and heritage business environment.
                  </p>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                  {socialMediaPackages.map((pkg, index) => (
                    <Card key={index} className={`bg-slate-900/80 border-purple-500/20 hover:border-purple-500/40 transition-all duration-300 relative ${pkg.popular ? 'ring-2 ring-purple-500' : ''}`}>
                      {pkg.popular && (
                        <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                          <span className="bg-purple-500 text-white px-4 py-2 rounded-full text-sm font-semibold">Most Popular</span>
                        </div>
                      )}
                      <CardContent className="p-8">
                        <h3 className="text-2xl font-bold text-white mb-2">{pkg.name}</h3>
                        <p className="text-slate-300 mb-6">{pkg.description}</p>
                        <div className="mb-6">
                          <span className="text-4xl font-bold text-purple-400">{pkg.price}</span>
                          <span className="text-slate-400">{pkg.period}</span>
                        </div>
                        <ul className="space-y-3 mb-8">
                          {pkg.features.map((feature, idx) => (
                            <li key={idx} className="flex items-center text-slate-300">
                              <CheckCircle className="w-4 h-4 text-purple-400 mr-3 flex-shrink-0" />
                              {feature}
                            </li>
                          ))}
                        </ul>
                        <Button asChild className={`w-full ${pkg.popular ? 'bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700' : 'bg-slate-700 hover:bg-slate-600'} text-white`}>
                          <Link to="/quote">Get Started</Link>
                        </Button>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>

              {/* CTA Section */}
              <section className="bg-gradient-to-r from-purple-600 to-purple-500 rounded-2xl p-8 text-center">
                <h2 className="text-3xl md:text-5xl font-bold mb-6">Ready to Dominate Jaipur Social Media?</h2>
                <p className="text-xl mb-8">
                  Join 1100+ Jaipur brands that trust GOD Digital Marketing for social media success. Proven strategies that deliver 280% engagement growth and ₹18Cr+ revenue generation.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button asChild size="lg" className="bg-white text-purple-600 hover:bg-slate-100 text-lg px-8 py-4">
                    <Link to="/quote">Get Free Social Media Audit</Link>
                  </Button>
                  <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-purple-600 text-lg px-8 py-4">
                    <Link to="/contact">Call Social Media Experts: +91-8708577598</Link>
                  </Button>
                </div>
              </section>
            </div>

            {/* Sidebar */}
            <div className="lg:col-span-1">
              <Sidebar currentService="social-media" currentLocation="jaipur" />
            </div>
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
};

export default SocialMediaJaipur;
