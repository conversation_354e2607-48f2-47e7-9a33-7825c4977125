
import React from 'react';
import { Button } from '@/components/ui/button';
import { Download, Share2 } from 'lucide-react';
import LogoStatic from '@/components/LogoStatic';

const LogoDownload = () => {
  const downloadLogo = (size: number, filename: string) => {
    const svg = document.querySelector('#logo-svg') as SVGElement;
    if (!svg) return;

    const canvas = document.createElement('canvas');
    canvas.width = size;
    canvas.height = size;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const svgData = new XMLSerializer().serializeToString(svg);
    const svgBlob = new Blob([svgData], { type: 'image/svg+xml;charset=utf-8' });
    const svgUrl = URL.createObjectURL(svgBlob);

    const img = new Image();
    img.onload = () => {
      ctx.clearRect(0, 0, size, size);
      ctx.drawImage(img, 0, 0, size, size);
      
      canvas.toBlob((blob) => {
        if (blob) {
          const url = URL.createObjectURL(blob);
          const a = document.createElement('a');
          a.href = url;
          a.download = filename;
          a.click();
          URL.revokeObjectURL(url);
        }
      }, 'image/png');
      
      URL.revokeObjectURL(svgUrl);
    };
    img.src = svgUrl;
  };

  return (
    <div className="min-h-screen bg-slate-900 text-white py-20">
      <div className="container mx-auto px-6">
        <div className="max-w-4xl mx-auto text-center">
          <h1 className="text-4xl font-bold mb-8">GOD Digital Marketing Logo</h1>
          <p className="text-slate-300 mb-12">
            Download your logo in various sizes for different platforms
          </p>

          {/* Logo Preview */}
          <div className="bg-slate-800 rounded-lg p-8 mb-12">
            <div id="logo-container" className="flex justify-center">
              <div id="logo-svg">
                <LogoStatic width={400} height={400} />
              </div>
            </div>
          </div>

          {/* Download Options */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
            <div className="bg-slate-800 rounded-lg p-6">
              <h3 className="text-xl font-semibold mb-4">Social Media</h3>
              <p className="text-slate-300 mb-4">500x500px - Perfect for profile pictures</p>
              <Button 
                onClick={() => downloadLogo(500, 'god-digital-marketing-logo-500x500.png')}
                className="w-full bg-amber-500 hover:bg-amber-600"
              >
                <Download className="w-4 h-4 mr-2" />
                Download 500x500
              </Button>
            </div>

            <div className="bg-slate-800 rounded-lg p-6">
              <h3 className="text-xl font-semibold mb-4">Website</h3>
              <p className="text-slate-300 mb-4">200x200px - For website headers</p>
              <Button 
                onClick={() => downloadLogo(200, 'god-digital-marketing-logo-200x200.png')}
                className="w-full bg-amber-500 hover:bg-amber-600"
              >
                <Download className="w-4 h-4 mr-2" />
                Download 200x200
              </Button>
            </div>

            <div className="bg-slate-800 rounded-lg p-6">
              <h3 className="text-xl font-semibold mb-4">High Resolution</h3>
              <p className="text-slate-300 mb-4">1000x1000px - For print & large displays</p>
              <Button 
                onClick={() => downloadLogo(1000, 'god-digital-marketing-logo-1000x1000.png')}
                className="w-full bg-amber-500 hover:bg-amber-600"
              >
                <Download className="w-4 h-4 mr-2" />
                Download 1000x1000
              </Button>
            </div>
          </div>

          {/* Usage Guidelines */}
          <div className="bg-slate-800 rounded-lg p-8 text-left">
            <h3 className="text-2xl font-semibold mb-6 text-center">Logo Usage Guidelines</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div>
                <h4 className="text-lg font-semibold text-amber-400 mb-3">Recommended Uses:</h4>
                <ul className="space-y-2 text-slate-300">
                  <li>• Social media profile pictures</li>
                  <li>• Business cards and letterheads</li>
                  <li>• Website headers and footers</li>
                  <li>• Marketing materials</li>
                  <li>• Email signatures</li>
                </ul>
              </div>
              <div>
                <h4 className="text-lg font-semibold text-amber-400 mb-3">File Information:</h4>
                <ul className="space-y-2 text-slate-300">
                  <li>• Format: PNG with transparency</li>
                  <li>• Background: Transparent</li>
                  <li>• Colors: Consistent with brand</li>
                  <li>• Resolution: High quality</li>
                  <li>• Ready for all platforms</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LogoDownload;
