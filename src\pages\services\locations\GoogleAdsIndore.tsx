import React from 'react';
import { Link } from 'react-router-dom';
import { Target, CheckCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';
import Sidebar from '@/components/Sidebar';

const GoogleAdsIndore = () => {
  const stats = [
    { metric: '1750+', description: 'Indore Ad Campaigns', detail: 'Successfully managed' },
    { metric: '510%', description: 'Average ROAS', detail: 'Return on ad spend' },
    { metric: '₹29Cr+', description: 'Revenue Generated', detail: 'Through Google Ads' },
    { metric: '88%', description: 'Lead Quality Score', detail: 'High-intent prospects' }
  ];

  const achievements = [
    'Top Google Ads Agency in Indore',
    'Manufacturing Industry PPC Leaders',
    'Pharmaceutical PPC Experts',
    'Automotive Industry Ad Specialists',
    'Food Processing Ad Champions',
    'Commercial Hub Digital Advertising Pioneers'
  ];

  return (
    <div className="min-h-screen bg-slate-900 relative">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        <div className="container mx-auto px-6 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            <div className="lg:col-span-3">
              <section className="pt-24 pb-16">
                <div className="max-w-4xl">
                  <div className="inline-flex items-center space-x-2 bg-red-500/20 border border-red-500/30 rounded-full px-6 py-2 mb-8">
                    <Target className="w-4 h-4 text-red-400" />
                    <span className="text-red-400 font-medium">Google Ads Indore • Commercial Hub PPC Excellence</span>
                  </div>
                  
                  <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
                    #1 Google Ads Company in
                    <span className="bg-gradient-to-r from-red-400 to-red-600 bg-clip-text text-transparent"> Indore</span>
                  </h1>
                  
                  <p className="text-xl text-slate-300 mb-8">
                    Premier Google Ads management in Indore offering comprehensive PPC advertising solutions including search ads, display campaigns, shopping ads, YouTube advertising, and remarketing campaigns. Our Indore Google Ads agency provides professional PPC services with keyword research, ad copywriting, bid management, conversion tracking, and campaign optimization. Serving 1750+ Indore businesses across all industries - from manufacturing companies in Pithampur to pharmaceutical firms in Sanwer Road. Expert Google Ads solutions with proven ₹29Cr+ revenue generation and 510% average ROAS for Indore clients in Madhya Pradesh's commercial hub through strategic pay-per-click advertising and digital marketing excellence.
                  </p>
                  
                  <div className="flex flex-col sm:flex-row gap-4 mb-8">
                    <Button asChild size="lg" className="bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white text-lg px-8 py-4">
                      <Link to="/quote">Get Free Indore Google Ads Audit</Link>
                    </Button>
                    <Button asChild size="lg" variant="outline" className="border-red-500 text-red-400 hover:bg-red-500 hover:text-white text-lg px-8 py-4">
                      <Link to="/contact">Call Indore PPC Experts: +91-8708577598</Link>
                    </Button>
                  </div>

                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                    {achievements.map((achievement, index) => (
                      <div key={index} className="flex items-center justify-center space-x-2 bg-slate-800/50 rounded-lg p-3">
                        <CheckCircle className="w-4 h-4 text-red-400 flex-shrink-0" />
                        <span className="text-slate-300">{achievement}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </section>

              <section className="mb-16 bg-slate-800/50 rounded-2xl p-8">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Indore Google Ads
                    <span className="bg-gradient-to-r from-red-400 to-red-600 bg-clip-text text-transparent"> Success Metrics</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Measurable Google Ads results from Indore businesses across manufacturing, pharmaceutical, automotive, and food processing sectors.
                  </p>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
                  {stats.map((stat, index) => (
                    <div key={index} className="text-center">
                      <div className="text-4xl font-bold text-red-400 mb-2">{stat.metric}</div>
                      <div className="text-white font-semibold mb-1">{stat.description}</div>
                      <div className="text-slate-400">{stat.detail}</div>
                    </div>
                  ))}
                </div>
              </section>

              <section className="bg-gradient-to-r from-red-600 to-red-500 rounded-2xl p-8 text-center">
                <h2 className="text-3xl md:text-5xl font-bold mb-6">Ready to Dominate Indore with Google Ads?</h2>
                <p className="text-xl mb-8">
                  Join 1750+ Indore businesses that trust GOD Digital Marketing for Google Ads success. Proven strategies that deliver 510% ROAS and ₹29Cr+ revenue generation.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button asChild size="lg" className="bg-white text-red-600 hover:bg-slate-100 text-lg px-8 py-4">
                    <Link to="/quote">Get Free Ads Audit</Link>
                  </Button>
                  <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-red-600 text-lg px-8 py-4">
                    <Link to="/contact">Call PPC Experts: +91-8708577598</Link>
                  </Button>
                </div>
              </section>
            </div>

            <div className="lg:col-span-1">
              <Sidebar currentService="ppc" currentLocation="indore" />
            </div>
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
};

export default GoogleAdsIndore;
