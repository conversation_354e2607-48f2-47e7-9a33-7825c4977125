# SPA Fallback for React Router
# This ensures all routes are handled by React Router instead of returning 404

# API routes (if any) - handle first
/api/*  /api/:splat  200

# Static assets - serve directly
/assets/*  /assets/:splat  200
/images/*  /images/:splat  200
/icons/*   /icons/:splat   200
/fonts/*   /fonts/:splat   200

# Service Worker
/sw.js  /sw.js  200

# Manifest and other PWA files
/manifest.json  /manifest.json  200
/favicon.ico    /favicon.ico    200

# All other routes - fallback to index.html for SPA routing
/*  /index.html  200
