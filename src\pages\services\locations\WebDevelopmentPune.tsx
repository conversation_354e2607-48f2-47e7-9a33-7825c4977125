import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { ArrowRight, Code, TrendingUp, Users, CheckCircle, Building, Star, Crown, Smartphone, Globe } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';
import Sidebar from '@/components/Sidebar';

const WebDevelopmentPune = () => {
  const webServices = [
    'Web Development Services Pune',
    'Custom Website Development Pune',
    'E-commerce Development Pune',
    'Educational Website Development Pune',
    'IT Company Website Development Pune',
    'Manufacturing Website Development Pune',
    'Startup Website Development Pune',
    'WordPress Development Pune',
    'PHP Development Pune',
    'React Development Pune',
    'Node.js Development Pune',
    'Database Development Pune',
    'API Development Pune',
    'Mobile App Development Pune',
    'UI/UX Design Pune',
    'Website Maintenance Pune',
    'B2B Website Development Pune',
    'Healthcare Website Development Pune',
    'Real Estate Website Development Pune',
    'Automotive Website Development Pune'
  ];

  const developmentTypes = [
    {
      type: 'Educational Web Solutions',
      description: 'Comprehensive web solutions for Pune\'s educational institutions and coaching centers',
      icon: Building,
      features: ['Student Portals', 'Learning Management Systems', 'Online Admission Systems', 'Educational Resources']
    },
    {
      type: 'IT & Software Solutions',
      description: 'Advanced web solutions for Pune\'s IT corridor and software companies',
      icon: Code,
      features: ['SaaS Platforms', 'Enterprise Applications', 'API Development', 'Cloud Solutions']
    },
    {
      type: 'Manufacturing Web Platforms',
      description: 'Industrial web solutions for Pune\'s manufacturing and automotive sector',
      icon: Globe,
      features: ['B2B Portals', 'Inventory Management', 'Supply Chain Systems', 'Product Catalogs']
    },
    {
      type: 'Startup Tech Solutions',
      description: 'Scalable web solutions for Pune\'s vibrant startup ecosystem',
      icon: Smartphone,
      features: ['MVP Development', 'Scalable Architecture', 'Mobile-First Design', 'Growth-Ready Platforms']
    }
  ];

  const webPackages = [
    {
      name: 'Web Development Pune Starter',
      price: '₹35,000',
      period: '/project',
      description: 'Perfect for small Pune businesses and startups',
      features: [
        'Custom Website Design',
        'Responsive Development',
        'Content Management System',
        'SEO Optimization',
        'Contact Forms',
        'Educational Sector Focus'
      ]
    },
    {
      name: 'Pune Web Development Professional',
      price: '₹65,000',
      period: '/project',
      description: 'Comprehensive web development for growing Pune businesses',
      features: [
        'Advanced Custom Development',
        'E-commerce Integration',
        'Database Development',
        'API Integration',
        'Performance Optimization',
        'Security Implementation',
        'IT Sector Specialization'
      ],
      popular: true
    },
    {
      name: 'Enterprise Web Development Pune',
      price: '₹1,20,000',
      period: '/project',
      description: 'Advanced web solutions for large Pune enterprises',
      features: [
        'Enterprise Architecture',
        'Custom Application Development',
        'Multi-platform Integration',
        'Advanced Security',
        'Scalable Infrastructure',
        'Integration Services',
        '12 Months Support'
      ]
    }
  ];

  const stats = [
    {
      metric: '280+',
      description: 'Websites Developed',
      detail: 'For Pune businesses'
    },
    {
      metric: '99.9%',
      description: 'Uptime Guarantee',
      detail: 'Reliable hosting solutions'
    },
    {
      metric: '₹18Cr+',
      description: 'Revenue Generated',
      detail: 'Through web solutions'
    },
    {
      metric: '95%',
      description: 'Client Satisfaction Rate',
      detail: 'Quality delivery'
    }
  ];

  const achievements = [
    'Top Web Development Company in Pune',
    'Educational Website Specialists',
    'IT Sector Web Experts',
    'Manufacturing Web Leaders',
    'Startup Development Champions',
    'Enterprise Solution Pioneers'
  ];

  return (
    <div className="min-h-screen bg-slate-900 relative">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        <div className="container mx-auto px-6 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            {/* Main Content */}
            <div className="lg:col-span-3">
              {/* Hero Section */}
              <section className="pt-24 pb-16">
                <div className="max-w-4xl">
                  <div className="inline-flex items-center space-x-2 bg-emerald-500/20 border border-emerald-500/30 rounded-full px-6 py-2 mb-8">
                    <Code className="w-4 h-4 text-emerald-400" />
                    <span className="text-emerald-400 font-medium">Web Development Pune • Oxford of the East Tech Excellence</span>
                  </div>
                  
                  <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
                    #1 Web Development Company in
                    <span className="bg-gradient-to-r from-emerald-400 to-emerald-600 bg-clip-text text-transparent"> Pune</span>
                  </h1>
                  
                  <p className="text-xl text-slate-300 mb-8">
                    Premier web development in Pune offering comprehensive website development and application solutions for educational institutions, IT companies, and manufacturing businesses. Serving 280+ Pune businesses across all areas - from educational hubs in Kothrud to IT corridors in Hinjewadi. Expert web development solutions with proven ₹18Cr+ revenue generation and 99.9% uptime guarantee for Pune clients in Maharashtra's Oxford of the East.
                  </p>
                  
                  <div className="flex flex-col sm:flex-row gap-4 mb-8">
                    <Button asChild size="lg" className="bg-gradient-to-r from-emerald-500 to-emerald-600 hover:from-emerald-600 hover:to-emerald-700 text-white text-lg px-8 py-4">
                      <Link to="/quote">Get Free Pune Web Development Quote</Link>
                    </Button>
                    <Button asChild size="lg" variant="outline" className="border-emerald-500 text-emerald-400 hover:bg-emerald-500 hover:text-white text-lg px-8 py-4">
                      <Link to="/contact">Call Web Developers: +91-8708577598</Link>
                    </Button>
                  </div>

                  {/* Achievements */}
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                    {achievements.map((achievement, index) => (
                      <div key={index} className="flex items-center justify-center space-x-2 bg-slate-800/50 rounded-lg p-3">
                        <CheckCircle className="w-4 h-4 text-emerald-400 flex-shrink-0" />
                        <span className="text-slate-300">{achievement}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </section>

              {/* Stats Section */}
              <section className="mb-16 bg-slate-800/50 rounded-2xl p-8">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Pune Web Development
                    <span className="bg-gradient-to-r from-emerald-400 to-emerald-600 bg-clip-text text-transparent"> Success Metrics</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Measurable web development results from Pune businesses across educational, IT, and manufacturing sectors.
                  </p>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
                  {stats.map((stat, index) => (
                    <div key={index} className="text-center">
                      <div className="text-4xl font-bold text-emerald-400 mb-2">{stat.metric}</div>
                      <div className="text-white font-semibold mb-1">{stat.description}</div>
                      <div className="text-slate-400">{stat.detail}</div>
                    </div>
                  ))}
                </div>
              </section>

              {/* Development Types Section */}
              <section className="mb-16">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Web Solutions We
                    <span className="bg-gradient-to-r from-emerald-400 to-emerald-600 bg-clip-text text-transparent"> Develop</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Specialized web development solutions designed for Pune's unique educational, IT, and industrial landscape.
                  </p>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  {developmentTypes.map((dev, index) => (
                    <Card key={index} className="bg-slate-900/80 border-emerald-500/20 hover:border-emerald-500/40 transition-all duration-300">
                      <CardContent className="p-8">
                        <div className="w-12 h-12 bg-gradient-to-r from-emerald-500 to-emerald-600 rounded-lg flex items-center justify-center mb-6">
                          <dev.icon className="w-6 h-6 text-white" />
                        </div>
                        <h3 className="text-xl font-bold text-white mb-4">{dev.type}</h3>
                        <p className="text-slate-300 mb-6">{dev.description}</p>
                        <ul className="space-y-2">
                          {dev.features.map((feature, idx) => (
                            <li key={idx} className="flex items-center text-slate-400 text-sm">
                              <CheckCircle className="w-3 h-3 text-emerald-400 mr-2 flex-shrink-0" />
                              {feature}
                            </li>
                          ))}
                        </ul>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>

              {/* Pricing Section */}
              <section className="mb-16">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Pune Web Development
                    <span className="bg-gradient-to-r from-emerald-400 to-emerald-600 bg-clip-text text-transparent"> Pricing</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Competitive web development pricing designed for Pune's educational and IT business environment.
                  </p>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                  {webPackages.map((pkg, index) => (
                    <Card key={index} className={`bg-slate-900/80 border-emerald-500/20 hover:border-emerald-500/40 transition-all duration-300 relative ${pkg.popular ? 'ring-2 ring-emerald-500' : ''}`}>
                      {pkg.popular && (
                        <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                          <span className="bg-emerald-500 text-white px-4 py-2 rounded-full text-sm font-semibold">Most Popular</span>
                        </div>
                      )}
                      <CardContent className="p-8">
                        <h3 className="text-2xl font-bold text-white mb-2">{pkg.name}</h3>
                        <p className="text-slate-300 mb-6">{pkg.description}</p>
                        <div className="mb-6">
                          <span className="text-4xl font-bold text-emerald-400">{pkg.price}</span>
                          <span className="text-slate-400">{pkg.period}</span>
                        </div>
                        <ul className="space-y-3 mb-8">
                          {pkg.features.map((feature, idx) => (
                            <li key={idx} className="flex items-center text-slate-300">
                              <CheckCircle className="w-4 h-4 text-emerald-400 mr-3 flex-shrink-0" />
                              {feature}
                            </li>
                          ))}
                        </ul>
                        <Button asChild className={`w-full ${pkg.popular ? 'bg-gradient-to-r from-emerald-500 to-emerald-600 hover:from-emerald-600 hover:to-emerald-700' : 'bg-slate-700 hover:bg-slate-600'} text-white`}>
                          <Link to="/quote">Get Started</Link>
                        </Button>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>

              {/* CTA Section */}
              <section className="bg-gradient-to-r from-emerald-600 to-emerald-500 rounded-2xl p-8 text-center">
                <h2 className="text-3xl md:text-5xl font-bold mb-6">Ready to Build Your Pune Web Presence?</h2>
                <p className="text-xl mb-8">
                  Join 280+ Pune businesses that trust GOD Digital Marketing for web development success. Proven solutions that deliver 99.9% uptime and ₹18Cr+ revenue generation.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button asChild size="lg" className="bg-white text-emerald-600 hover:bg-slate-100 text-lg px-8 py-4">
                    <Link to="/quote">Get Free Development Quote</Link>
                  </Button>
                  <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-emerald-600 text-lg px-8 py-4">
                    <Link to="/contact">Call Web Developers: +91-8708577598</Link>
                  </Button>
                </div>
              </section>
            </div>

            {/* Sidebar */}
            <div className="lg:col-span-1">
              <Sidebar currentService="web-development" currentLocation="pune" />
            </div>
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
};

export default WebDevelopmentPune;
