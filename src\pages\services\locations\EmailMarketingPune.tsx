import React from 'react';
import { Link } from 'react-router-dom';
import { ArrowRight, Mail, TrendingUp, Users, CheckCircle, Building, Star, Crown, Send, Zap } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';
import Sidebar from '@/components/Sidebar';

const EmailMarketingPune = () => {
  const emailServices = [
    'Email Marketing Strategy Pune',
    'Email Campaign Management Pune',
    'Email Automation Pune',
    'Newsletter Marketing Pune',
    'Drip Campaign Setup Pune',
    'Email Template Design Pune',
    'Email List Building Pune',
    'Email Segmentation Pune',
    'Personalized Email Marketing Pune',
    'Transactional Email Setup Pune',
    'Email A/B Testing Pune',
    'Email Analytics & Reporting Pune',
    'Email Deliverability Optimization Pune',
    'CRM Email Integration Pune',
    'Educational Email Marketing Pune',
    'IT Company Email Campaigns Pune',
    'Manufacturing Email Marketing Pune',
    'Startup Email Marketing Pune',
    'B2B Email Marketing Pune',
    'E-commerce Email Marketing Pune'
  ];

  const emailTypes = [
    {
      type: 'Educational Email Campaigns',
      description: 'Student and parent communication for Pune\'s educational institutions',
      icon: Building,
      features: ['Student Communications', 'Parent Updates', 'Course Promotions', 'Educational Newsletters']
    },
    {
      type: 'IT & Tech Email Marketing',
      description: 'B2B email campaigns for Pune\'s IT corridor and software companies',
      icon: Mail,
      features: ['Product Updates', 'Tech Newsletters', 'Lead Nurturing', 'Client Communications']
    },
    {
      type: 'Manufacturing Email Campaigns',
      description: 'Industrial email marketing for Pune\'s manufacturing and automotive sector',
      icon: Zap,
      features: ['B2B Communications', 'Product Catalogs', 'Trade Show Invites', 'Supply Chain Updates']
    },
    {
      type: 'Startup Growth Email Marketing',
      description: 'Growth-focused email campaigns for Pune\'s vibrant startup ecosystem',
      icon: Send,
      features: ['User Onboarding', 'Growth Campaigns', 'Investor Updates', 'Community Building']
    }
  ];

  const emailPackages = [
    {
      name: 'Email Marketing Pune Starter',
      price: '₹15,000',
      period: '/month',
      description: 'Perfect for small Pune businesses and startups',
      features: [
        'Up to 5,000 Subscribers',
        '4 Email Campaigns per Month',
        'Basic Email Templates',
        'List Management',
        'Basic Analytics',
        'Educational Sector Focus'
      ]
    },
    {
      name: 'Pune Email Marketing Professional',
      price: '₹28,000',
      period: '/month',
      description: 'Comprehensive email marketing for growing Pune businesses',
      features: [
        'Up to 25,000 Subscribers',
        'Unlimited Email Campaigns',
        'Custom Email Templates',
        'Advanced Automation',
        'A/B Testing',
        'Advanced Analytics',
        'IT Sector Specialization'
      ],
      popular: true
    },
    {
      name: 'Enterprise Email Marketing Pune',
      price: '₹52,000',
      period: '/month',
      description: 'Advanced email marketing for large Pune enterprises',
      features: [
        'Unlimited Subscribers',
        'Advanced Automation Workflows',
        'Custom Integrations',
        'Dedicated IP',
        'Advanced Segmentation',
        'Dedicated Account Manager',
        'Priority Support'
      ]
    }
  ];

  const stats = [
    {
      metric: '750K+',
      description: 'Emails Sent Monthly',
      detail: 'For Pune businesses'
    },
    {
      metric: '28%',
      description: 'Average Open Rate',
      detail: 'Above industry standard'
    },
    {
      metric: '₹16Cr+',
      description: 'Revenue Generated',
      detail: 'Through email campaigns'
    },
    {
      metric: '94%',
      description: 'Deliverability Rate',
      detail: 'Consistent inbox placement'
    }
  ];

  const achievements = [
    'Top Email Marketing Agency in Pune',
    'Educational Email Marketing Specialists',
    'IT Sector Email Experts',
    'Manufacturing Email Leaders',
    'Startup Email Marketing Champions',
    'B2B Email Marketing Pioneers'
  ];

  return (
    <div className="min-h-screen bg-slate-900 relative">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        <div className="container mx-auto px-6 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            {/* Main Content */}
            <div className="lg:col-span-3">
              {/* Hero Section */}
              <section className="pt-24 pb-16">
                <div className="max-w-4xl">
                  <div className="inline-flex items-center space-x-2 bg-cyan-500/20 border border-cyan-500/30 rounded-full px-6 py-2 mb-8">
                    <Mail className="w-4 h-4 text-cyan-400" />
                    <span className="text-cyan-400 font-medium">Email Marketing Pune • Oxford of the East Direct Communication</span>
                  </div>
                  
                  <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
                    #1 Email Marketing Company in
                    <span className="bg-gradient-to-r from-cyan-400 to-cyan-600 bg-clip-text text-transparent"> Pune</span>
                  </h1>
                  
                  <p className="text-xl text-slate-300 mb-8">
                    Premier email marketing in Pune offering comprehensive email campaign management and automation services for educational institutions, IT companies, and manufacturing businesses. Serving 750+ Pune businesses across all areas - from educational hubs in Kothrud to IT corridors in Hinjewadi. Expert email marketing solutions with proven ₹16Cr+ revenue generation and 750K+ emails sent monthly for Pune clients in Maharashtra's Oxford of the East.
                  </p>
                  
                  <div className="flex flex-col sm:flex-row gap-4 mb-8">
                    <Button asChild size="lg" className="bg-gradient-to-r from-cyan-500 to-cyan-600 hover:from-cyan-600 hover:to-cyan-700 text-white text-lg px-8 py-4">
                      <Link to="/quote">Get Free Pune Email Audit</Link>
                    </Button>
                    <Button asChild size="lg" variant="outline" className="border-cyan-500 text-cyan-400 hover:bg-cyan-500 hover:text-white text-lg px-8 py-4">
                      <Link to="/contact">Call Email Experts: +91-8708577598</Link>
                    </Button>
                  </div>

                  {/* Achievements */}
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                    {achievements.map((achievement, index) => (
                      <div key={index} className="flex items-center justify-center space-x-2 bg-slate-800/50 rounded-lg p-3">
                        <CheckCircle className="w-4 h-4 text-cyan-400 flex-shrink-0" />
                        <span className="text-slate-300">{achievement}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </section>

              {/* Stats Section */}
              <section className="mb-16 bg-slate-800/50 rounded-2xl p-8">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Pune Email Marketing
                    <span className="bg-gradient-to-r from-cyan-400 to-cyan-600 bg-clip-text text-transparent"> Success Metrics</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Measurable email marketing results from Pune businesses across educational, IT, and manufacturing sectors.
                  </p>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
                  {stats.map((stat, index) => (
                    <div key={index} className="text-center">
                      <div className="text-4xl font-bold text-cyan-400 mb-2">{stat.metric}</div>
                      <div className="text-white font-semibold mb-1">{stat.description}</div>
                      <div className="text-slate-400">{stat.detail}</div>
                    </div>
                  ))}
                </div>
              </section>

              {/* Email Types Section */}
              <section className="mb-16">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Email Campaign Types We
                    <span className="bg-gradient-to-r from-cyan-400 to-cyan-600 bg-clip-text text-transparent"> Master</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Specialized email marketing campaigns designed for Pune's unique educational, IT, and industrial landscape.
                  </p>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  {emailTypes.map((email, index) => (
                    <Card key={index} className="bg-slate-900/80 border-cyan-500/20 hover:border-cyan-500/40 transition-all duration-300">
                      <CardContent className="p-8">
                        <div className="w-12 h-12 bg-gradient-to-r from-cyan-500 to-cyan-600 rounded-lg flex items-center justify-center mb-6">
                          <email.icon className="w-6 h-6 text-white" />
                        </div>
                        <h3 className="text-xl font-bold text-white mb-4">{email.type}</h3>
                        <p className="text-slate-300 mb-6">{email.description}</p>
                        <ul className="space-y-2">
                          {email.features.map((feature, idx) => (
                            <li key={idx} className="flex items-center text-slate-400 text-sm">
                              <CheckCircle className="w-3 h-3 text-cyan-400 mr-2 flex-shrink-0" />
                              {feature}
                            </li>
                          ))}
                        </ul>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>

              {/* Pricing Section */}
              <section className="mb-16">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Pune Email Marketing
                    <span className="bg-gradient-to-r from-cyan-400 to-cyan-600 bg-clip-text text-transparent"> Pricing</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Competitive email marketing pricing designed for Pune's educational and IT business environment.
                  </p>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                  {emailPackages.map((pkg, index) => (
                    <Card key={index} className={`bg-slate-900/80 border-cyan-500/20 hover:border-cyan-500/40 transition-all duration-300 relative ${pkg.popular ? 'ring-2 ring-cyan-500' : ''}`}>
                      {pkg.popular && (
                        <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                          <span className="bg-cyan-500 text-white px-4 py-2 rounded-full text-sm font-semibold">Most Popular</span>
                        </div>
                      )}
                      <CardContent className="p-8">
                        <h3 className="text-2xl font-bold text-white mb-2">{pkg.name}</h3>
                        <p className="text-slate-300 mb-6">{pkg.description}</p>
                        <div className="mb-6">
                          <span className="text-4xl font-bold text-cyan-400">{pkg.price}</span>
                          <span className="text-slate-400">{pkg.period}</span>
                        </div>
                        <ul className="space-y-3 mb-8">
                          {pkg.features.map((feature, idx) => (
                            <li key={idx} className="flex items-center text-slate-300">
                              <CheckCircle className="w-4 h-4 text-cyan-400 mr-3 flex-shrink-0" />
                              {feature}
                            </li>
                          ))}
                        </ul>
                        <Button asChild className={`w-full ${pkg.popular ? 'bg-gradient-to-r from-cyan-500 to-cyan-600 hover:from-cyan-600 hover:to-cyan-700' : 'bg-slate-700 hover:bg-slate-600'} text-white`}>
                          <Link to="/quote">Get Started</Link>
                        </Button>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>

              {/* CTA Section */}
              <section className="bg-gradient-to-r from-cyan-600 to-cyan-500 rounded-2xl p-8 text-center">
                <h2 className="text-3xl md:text-5xl font-bold mb-6">Ready to Dominate Pune Inboxes?</h2>
                <p className="text-xl mb-8">
                  Join 750+ Pune businesses that trust GOD Digital Marketing for email marketing success. Proven strategies that deliver 28% open rates and ₹16Cr+ revenue generation.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button asChild size="lg" className="bg-white text-cyan-600 hover:bg-slate-100 text-lg px-8 py-4">
                    <Link to="/quote">Get Free Email Audit</Link>
                  </Button>
                  <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-cyan-600 text-lg px-8 py-4">
                    <Link to="/contact">Call Email Experts: +91-8708577598</Link>
                  </Button>
                </div>
              </section>
            </div>

            {/* Sidebar */}
            <div className="lg:col-span-1">
              <Sidebar currentService="email" currentLocation="pune" />
            </div>
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
};

export default EmailMarketingPune;
