import React from 'react';
import { Link } from 'react-router-dom';
import { Car, Search, TrendingUp, Users, CheckCircle, Building, Star, Crown, Target, Zap, ArrowRight } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';
import Sidebar from '@/components/Sidebar';

const AutomotiveSeo = () => {
  const stats = [
    {
      metric: '1,600+',
      description: 'Automotive Businesses Optimized',
      detail: 'Across all automotive sectors'
    },
    {
      metric: '3,900%',
      description: 'Average Sales Growth',
      detail: 'For automotive clients'
    },
    {
      metric: '₹950Cr+',
      description: 'Automotive Revenue Generated',
      detail: 'Through SEO campaigns'
    },
    {
      metric: '94%',
      description: 'Automotive Keywords Ranking',
      detail: 'Top 10 positions achieved'
    }
  ];

  const achievements = [
    'Top Automotive SEO Company in India',
    'Car Dealership SEO Specialists',
    'Auto Parts SEO Experts',
    'EV Industry SEO Leaders',
    'Auto Service SEO Champions',
    'Automotive Manufacturing SEO Pioneers'
  ];

  const automotiveSpecializations = [
    {
      type: 'Car Dealership & Sales SEO',
      description: 'Comprehensive SEO for car dealerships, auto sales centers, and vehicle showrooms',
      icon: Building,
      features: ['Car Dealership SEO', 'Vehicle Sales SEO', 'Auto Showroom SEO', 'Used Car Dealer SEO', 'Luxury Car SEO', 'Commercial Vehicle SEO']
    },
    {
      type: 'Auto Parts & Accessories SEO',
      description: 'Advanced SEO for auto parts suppliers, accessories retailers, and aftermarket businesses',
      icon: Star,
      features: ['Auto Parts SEO', 'Car Accessories SEO', 'Spare Parts SEO', 'Aftermarket Parts SEO', 'Auto Components SEO', 'Performance Parts SEO']
    },
    {
      type: 'Auto Service & Repair SEO',
      description: 'Strategic SEO for auto service centers, repair shops, and maintenance facilities',
      icon: Crown,
      features: ['Auto Service SEO', 'Car Repair Shop SEO', 'Auto Maintenance SEO', 'Tire Service SEO', 'Auto Body Shop SEO', 'Quick Lube SEO']
    },
    {
      type: 'EV & Future Mobility SEO',
      description: 'Specialized SEO for electric vehicles, EV charging, and future mobility solutions',
      icon: Target,
      features: ['Electric Vehicle SEO', 'EV Charging Station SEO', 'Hybrid Car SEO', 'EV Dealership SEO', 'Battery Technology SEO', 'Green Mobility SEO']
    }
  ];

  const automotiveSectors = [
    { name: 'Car Dealerships', clients: '480+', growth: '3,800%' },
    { name: 'Auto Parts', clients: '380+', growth: '4,200%' },
    { name: 'Auto Services', clients: '420+', growth: '3,600%' },
    { name: 'EV Industry', clients: '180+', growth: '4,800%' },
    { name: 'Auto Manufacturing', clients: '220+', growth: '3,400%' },
    { name: 'Auto Insurance', clients: '160+', growth: '3,900%' }
  ];

  const caseStudies = [
    {
      client: 'Leading Car Dealership Chain',
      industry: 'Automotive Sales',
      challenge: 'Multi-location car dealership needed to dominate local automotive searches',
      result: '4,200% vehicle sales increase',
      metrics: ['2,400+ automotive keywords in top 3', '₹380Cr+ vehicle sales revenue', '920% increase in showroom visits']
    },
    {
      client: 'Auto Parts E-commerce Platform',
      industry: 'Auto Parts Retail',
      challenge: 'Online auto parts retailer needed to compete with established automotive giants',
      result: '4,800% parts sales growth',
      metrics: ['1,800+ auto parts keywords ranking', '₹285Cr+ parts revenue', '680% increase in online orders']
    },
    {
      client: 'EV Charging Network',
      industry: 'Electric Vehicle Infrastructure',
      challenge: 'EV charging company needed to establish market presence in growing EV sector',
      result: '5,200% charging station visibility',
      metrics: ['1,200+ EV keywords in top 5', '₹165Cr+ charging revenue', '580% increase in charging sessions']
    }
  ];

  const automotiveSeoStrategies = [
    {
      strategy: 'Local Automotive SEO',
      description: 'Dominate local search for car dealerships and auto services in specific areas',
      benefits: ['Local dealership visibility', 'Service area dominance', 'Local customer acquisition', 'Geographic targeting']
    },
    {
      strategy: 'Vehicle-Specific SEO',
      description: 'Target specific car models, brands, and automotive categories',
      benefits: ['Model-specific ranking', 'Brand authority', 'Vehicle comparison optimization', 'Automotive expertise']
    },
    {
      strategy: 'Automotive E-commerce SEO',
      description: 'Optimize online auto parts stores and automotive e-commerce platforms',
      benefits: ['Product visibility', 'Parts catalog optimization', 'Online sales growth', 'Automotive marketplace ranking']
    },
    {
      strategy: 'EV & Green Mobility SEO',
      description: 'Target emerging electric vehicle and sustainable mobility searches',
      benefits: ['EV market leadership', 'Green technology authority', 'Future mobility positioning', 'Sustainability focus']
    }
  ];

  return (
    <div className="min-h-screen bg-slate-900 relative">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        <div className="container mx-auto px-6 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            <div className="lg:col-span-3">
              <section className="pt-24 pb-16">
                <div className="max-w-4xl">
                  <div className="inline-flex items-center space-x-2 bg-blue-500/20 border border-blue-500/30 rounded-full px-6 py-2 mb-8">
                    <Car className="w-4 h-4 text-blue-400" />
                    <span className="text-blue-400 font-medium">Automotive SEO • Vehicle Optimization Excellence</span>
                  </div>
                  
                  <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
                    #1 Automotive SEO Company in
                    <span className="bg-gradient-to-r from-blue-400 to-blue-600 bg-clip-text text-transparent"> India</span>
                  </h1>
                  
                  <p className="text-xl text-slate-300 mb-8">
                    Premier automotive SEO services offering comprehensive search engine optimization solutions for car dealerships, auto parts retailers, service centers, and EV companies. Our automotive SEO company provides professional SEO services with car dealership SEO optimization, auto parts SEO, auto service SEO, and electric vehicle SEO. Serving 1,600+ automotive businesses across all vehicle sectors with proven ₹950Cr+ revenue generation and 3,900% average sales growth for automotive clients through strategic search engine optimization and automotive digital marketing excellence.
                  </p>
                  
                  <div className="flex flex-col sm:flex-row gap-4 mb-8">
                    <Button asChild size="lg" className="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white text-lg px-8 py-4">
                      <Link to="/quote">Get Free Automotive SEO Audit</Link>
                    </Button>
                    <Button asChild size="lg" variant="outline" className="border-blue-500 text-blue-400 hover:bg-blue-500 hover:text-white text-lg px-8 py-4">
                      <Link to="/contact">Call Automotive SEO Experts: +91-8708577598</Link>
                    </Button>
                  </div>

                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                    {achievements.map((achievement, index) => (
                      <div key={index} className="flex items-center justify-center space-x-2 bg-slate-800/50 rounded-lg p-3">
                        <CheckCircle className="w-4 h-4 text-blue-400 flex-shrink-0" />
                        <span className="text-slate-300">{achievement}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </section>

              <section className="mb-16 bg-slate-800/50 rounded-2xl p-8">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Automotive SEO
                    <span className="bg-gradient-to-r from-blue-400 to-blue-600 bg-clip-text text-transparent"> Success Metrics</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Measurable automotive SEO results from businesses across all vehicle sectors and markets.
                  </p>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
                  {stats.map((stat, index) => (
                    <div key={index} className="text-center">
                      <div className="text-4xl font-bold text-blue-400 mb-2">{stat.metric}</div>
                      <div className="text-white font-semibold mb-1">{stat.description}</div>
                      <div className="text-slate-400">{stat.detail}</div>
                    </div>
                  ))}
                </div>
              </section>

              <section className="mb-16">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Automotive SEO Strategies We
                    <span className="bg-gradient-to-r from-blue-400 to-blue-600 bg-clip-text text-transparent"> Master</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Specialized SEO strategies designed for automotive success across all vehicle platforms and businesses.
                  </p>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  {automotiveSpecializations.map((spec, index) => (
                    <Card key={index} className="bg-slate-900/80 border-blue-500/20 hover:border-blue-500/40 transition-all duration-300">
                      <CardContent className="p-8">
                        <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg flex items-center justify-center mb-6">
                          <spec.icon className="w-6 h-6 text-white" />
                        </div>
                        <h3 className="text-xl font-bold text-white mb-4">{spec.type}</h3>
                        <p className="text-slate-300 mb-6">{spec.description}</p>
                        <ul className="space-y-2">
                          {spec.features.map((feature, idx) => (
                            <li key={idx} className="flex items-center text-slate-400 text-sm">
                              <CheckCircle className="w-3 h-3 text-blue-400 mr-2 flex-shrink-0" />
                              {feature}
                            </li>
                          ))}
                        </ul>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>

              {/* Automotive Sectors */}
              <section className="mb-16 bg-slate-800/30 rounded-2xl p-8">
                <h3 className="text-2xl font-bold text-white mb-6 text-center">
                  Automotive Industry Sector Expertise
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-6">
                  {automotiveSectors.map((sector, index) => (
                    <div key={index} className="text-center bg-slate-900/50 rounded-lg p-4">
                      <h4 className="text-blue-400 font-semibold mb-2">{sector.name}</h4>
                      <div className="text-2xl font-bold text-white mb-1">{sector.clients}</div>
                      <div className="text-slate-400 text-sm mb-2">Businesses Served</div>
                      <div className="text-green-400 font-semibold text-sm">{sector.growth} avg growth</div>
                    </div>
                  ))}
                </div>
              </section>

              {/* Automotive SEO Strategies */}
              <section className="mb-16 bg-slate-800/30 rounded-2xl p-8">
                <h3 className="text-2xl font-bold text-white mb-6 text-center">
                  Advanced Automotive SEO Strategies
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                  {automotiveSeoStrategies.map((strategy, index) => (
                    <div key={index} className="bg-slate-900/50 rounded-lg p-6">
                      <h4 className="text-blue-400 font-semibold mb-3">{strategy.strategy}</h4>
                      <p className="text-slate-300 text-sm mb-4">{strategy.description}</p>
                      <ul className="space-y-1">
                        {strategy.benefits.map((benefit, idx) => (
                          <li key={idx} className="text-slate-400 text-xs flex items-center">
                            <CheckCircle className="w-3 h-3 text-blue-400 mr-2 flex-shrink-0" />
                            {benefit}
                          </li>
                        ))}
                      </ul>
                    </div>
                  ))}
                </div>
              </section>

              {/* Case Studies */}
              <section className="mb-16">
                <h3 className="text-2xl font-bold text-white mb-8 text-center">
                  Automotive SEO Success Stories
                </h3>
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                  {caseStudies.map((study, index) => (
                    <Card key={index} className="bg-slate-900/60 border-blue-500/20 hover:border-blue-500/40 transition-all duration-300">
                      <CardContent className="p-6">
                        <h4 className="text-blue-400 font-semibold mb-2">{study.client}</h4>
                        <p className="text-slate-400 text-sm mb-3">{study.industry}</p>
                        <p className="text-slate-300 text-sm mb-4">{study.challenge}</p>
                        <div className="bg-slate-800/50 rounded p-3 mb-4">
                          <div className="text-green-400 font-semibold mb-2">{study.result}</div>
                          <ul className="space-y-1">
                            {study.metrics.map((metric, idx) => (
                              <li key={idx} className="text-slate-400 text-xs flex items-center">
                                <CheckCircle className="w-3 h-3 text-blue-400 mr-2 flex-shrink-0" />
                                {metric}
                              </li>
                            ))}
                          </ul>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>

              {/* Internal Linking Section */}
              <section className="mb-16 bg-slate-800/30 rounded-2xl p-8">
                <h3 className="text-2xl font-bold text-white mb-6 text-center">
                  Complete Automotive Digital Marketing Solutions
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  <Link to="/services/ppc" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-blue-400 font-semibold mb-2 group-hover:text-blue-300">Automotive Google Ads</h4>
                    <p className="text-slate-400 text-sm">PPC advertising for car dealerships and auto businesses</p>
                  </Link>
                  <Link to="/services/social-media" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-blue-400 font-semibold mb-2 group-hover:text-blue-300">Automotive Social Media</h4>
                    <p className="text-slate-400 text-sm">Social media marketing for automotive brands</p>
                  </Link>
                  <Link to="/services/content" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-blue-400 font-semibold mb-2 group-hover:text-blue-300">Automotive Content Marketing</h4>
                    <p className="text-slate-400 text-sm">Vehicle content creation and automotive marketing</p>
                  </Link>
                  <Link to="/services/email" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-blue-400 font-semibold mb-2 group-hover:text-blue-300">Automotive Email Marketing</h4>
                    <p className="text-slate-400 text-sm">Customer retention and automotive promotion campaigns</p>
                  </Link>
                  <Link to="/services/web-development" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-blue-400 font-semibold mb-2 group-hover:text-blue-300">Automotive Website Development</h4>
                    <p className="text-slate-400 text-sm">Car dealership websites and automotive platform development</p>
                  </Link>
                  <Link to="/portfolio" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-blue-400 font-semibold mb-2 group-hover:text-blue-300">Automotive Success Stories</h4>
                    <p className="text-slate-400 text-sm">See proven results from our automotive clients</p>
                  </Link>
                </div>
              </section>

              <section className="bg-gradient-to-r from-blue-600 to-blue-500 rounded-2xl p-8 text-center">
                <h2 className="text-3xl md:text-5xl font-bold mb-6">Ready to Drive Your Automotive Business Forward?</h2>
                <p className="text-xl mb-8">
                  Join 1,600+ automotive businesses that trust GOD Digital Marketing for vehicle SEO success. Proven strategies that deliver 3,900% sales growth and ₹950Cr+ revenue generation.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button asChild size="lg" className="bg-white text-blue-600 hover:bg-slate-100 text-lg px-8 py-4">
                    <Link to="/quote">Get Free Automotive SEO Audit</Link>
                  </Button>
                  <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-blue-600 text-lg px-8 py-4">
                    <Link to="/contact">Call Automotive SEO Experts: +91-8708577598</Link>
                  </Button>
                </div>
              </section>
            </div>

            <div className="lg:col-span-1">
              <Sidebar currentService="seo" />
            </div>
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
};

export default AutomotiveSeo;
