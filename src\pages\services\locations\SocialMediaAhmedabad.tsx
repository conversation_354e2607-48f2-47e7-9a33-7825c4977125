import React from 'react';
import { Link } from 'react-router-dom';
import { ArrowRight, Share2, TrendingUp, Users, CheckCircle, Building, Star, Crown, Heart, Zap } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';
import Sidebar from '@/components/Sidebar';

const SocialMediaAhmedabad = () => {
  const socialMediaServices = [
    'Social Media Marketing Ahmedabad',
    'Facebook Marketing Ahmedabad',
    'Instagram Marketing Ahmedabad',
    'LinkedIn Marketing Ahmedabad',
    'Twitter Marketing Ahmedabad',
    'YouTube Marketing Ahmedabad',
    'Social Media Management Ahmedabad',
    'Content Creation Ahmedabad',
    'Influencer Marketing Ahmedabad',
    'Social Media Advertising Ahmedabad',
    'Community Management Ahmedabad',
    'Brand Building Ahmedabad',
    'Textile Social Media Ahmedabad',
    'Chemical Industry Social Media Ahmedabad',
    'Diamond & Jewelry Social Media Ahmedabad',
    'Manufacturing Social Media Ahmedabad',
    'Export Business Social Media Ahmedabad',
    'B2B Social Media Ahmedabad',
    'Industrial Social Media Ahmedabad',
    'Pharmaceutical Social Media Ahmedabad'
  ];

  const socialMediaTypes = [
    {
      type: 'Textile & Garment Social Media',
      description: 'Engaging social media strategies for Ahmedabad\'s textile capital and garment exporters',
      icon: Building,
      features: ['Textile Industry Content', 'Fashion Showcase', 'B2B Networking', 'Export Market Reach']
    },
    {
      type: 'Chemical & Pharmaceutical Social Media',
      description: 'Professional social media for Gujarat\'s chemical and pharmaceutical industry leaders',
      icon: Share2,
      features: ['Industry Thought Leadership', 'B2B Content Strategy', 'Compliance-Safe Marketing', 'Professional Networks']
    },
    {
      type: 'Diamond & Jewelry Social Media',
      description: 'Premium social media strategies for Ahmedabad\'s diamond cutting and jewelry sector',
      icon: Star,
      features: ['Luxury Brand Building', 'Visual Storytelling', 'High-End Audience', 'Premium Content Creation']
    },
    {
      type: 'Manufacturing & Engineering Social Media',
      description: 'Industrial social media for Ahmedabad\'s engineering and manufacturing companies',
      icon: Zap,
      features: ['Industrial Content', 'B2B Lead Generation', 'Technical Showcases', 'Export Market Presence']
    }
  ];

  const socialMediaPackages = [
    {
      name: 'Social Media Ahmedabad Starter',
      price: '₹20,000',
      period: '/month',
      description: 'Perfect for small Ahmedabad businesses and textile traders',
      features: [
        '3 Social Media Platforms',
        '25 Posts per Month',
        'Basic Graphics & Content',
        'Community Management',
        'Monthly Analytics',
        'Textile Industry Focus'
      ]
    },
    {
      name: 'Ahmedabad Social Media Professional',
      price: '₹35,000',
      period: '/month',
      description: 'Comprehensive social media for growing Ahmedabad businesses',
      features: [
        '5 Social Media Platforms',
        '50 Posts per Month',
        'Professional Content Creation',
        'Paid Social Campaigns',
        'Influencer Collaborations',
        'Chemical/Pharma Specialization',
        'Bi-weekly Reporting'
      ],
      popular: true
    },
    {
      name: 'Enterprise Social Media Ahmedabad',
      price: '₹65,000',
      period: '/month',
      description: 'Advanced social media for large Ahmedabad enterprises and exporters',
      features: [
        'All Major Platforms',
        'Unlimited Content',
        'Video Content Production',
        'Advanced Analytics',
        'Crisis Management',
        'Export Market Strategy',
        'Dedicated Social Manager'
      ]
    }
  ];

  const stats = [
    {
      metric: '1400+',
      description: 'Ahmedabad Brands Managed',
      detail: 'Across all platforms'
    },
    {
      metric: '320%',
      description: 'Average Engagement Growth',
      detail: 'For Ahmedabad clients'
    },
    {
      metric: '₹22Cr+',
      description: 'Revenue Generated',
      detail: 'Through social campaigns'
    },
    {
      metric: '96%',
      description: 'Brand Awareness Increase',
      detail: 'Measured growth'
    }
  ];

  const achievements = [
    'Top Social Media Agency in Ahmedabad',
    'Textile Industry Social Leaders',
    'Chemical Sector Social Experts',
    'Diamond Industry Social Specialists',
    'Manufacturing Social Champions',
    'Export Business Social Pioneers'
  ];

  return (
    <div className="min-h-screen bg-slate-900 relative">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        <div className="container mx-auto px-6 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            {/* Main Content */}
            <div className="lg:col-span-3">
              {/* Hero Section */}
              <section className="pt-24 pb-16">
                <div className="max-w-4xl">
                  <div className="inline-flex items-center space-x-2 bg-purple-500/20 border border-purple-500/30 rounded-full px-6 py-2 mb-8">
                    <Share2 className="w-4 h-4 text-purple-400" />
                    <span className="text-purple-400 font-medium">Social Media Ahmedabad • Gujarat's Commercial Capital Engagement</span>
                  </div>
                  
                  <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
                    #1 Social Media Marketing Company in
                    <span className="bg-gradient-to-r from-purple-400 to-purple-600 bg-clip-text text-transparent"> Ahmedabad</span>
                  </h1>
                  
                  <p className="text-xl text-slate-300 mb-8">
                    Premier social media marketing in Ahmedabad offering comprehensive social media management for textile, chemical, diamond, and manufacturing businesses. Serving 1400+ Ahmedabad brands across all areas - from textile hubs in Narol to chemical zones in Vatva. Expert social media solutions with proven ₹22Cr+ revenue generation and 320% average engagement growth for Ahmedabad clients in Gujarat's commercial capital.
                  </p>
                  
                  <div className="flex flex-col sm:flex-row gap-4 mb-8">
                    <Button asChild size="lg" className="bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white text-lg px-8 py-4">
                      <Link to="/quote">Get Free Ahmedabad Social Media Audit</Link>
                    </Button>
                    <Button asChild size="lg" variant="outline" className="border-purple-500 text-purple-400 hover:bg-purple-500 hover:text-white text-lg px-8 py-4">
                      <Link to="/contact">Call Social Media Experts: +91-8708577598</Link>
                    </Button>
                  </div>

                  {/* Achievements */}
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                    {achievements.map((achievement, index) => (
                      <div key={index} className="flex items-center justify-center space-x-2 bg-slate-800/50 rounded-lg p-3">
                        <CheckCircle className="w-4 h-4 text-purple-400 flex-shrink-0" />
                        <span className="text-slate-300">{achievement}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </section>

              {/* Stats Section */}
              <section className="mb-16 bg-slate-800/50 rounded-2xl p-8">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Ahmedabad Social Media
                    <span className="bg-gradient-to-r from-purple-400 to-purple-600 bg-clip-text text-transparent"> Success Metrics</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Measurable social media results from Ahmedabad businesses across textile, chemical, diamond, and manufacturing sectors.
                  </p>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
                  {stats.map((stat, index) => (
                    <div key={index} className="text-center">
                      <div className="text-4xl font-bold text-purple-400 mb-2">{stat.metric}</div>
                      <div className="text-white font-semibold mb-1">{stat.description}</div>
                      <div className="text-slate-400">{stat.detail}</div>
                    </div>
                  ))}
                </div>
              </section>

              {/* Social Media Types Section */}
              <section className="mb-16">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Social Media Strategies We
                    <span className="bg-gradient-to-r from-purple-400 to-purple-600 bg-clip-text text-transparent"> Master</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Specialized social media strategies designed for Ahmedabad's unique textile, chemical, diamond, and manufacturing landscape.
                  </p>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  {socialMediaTypes.map((social, index) => (
                    <Card key={index} className="bg-slate-900/80 border-purple-500/20 hover:border-purple-500/40 transition-all duration-300">
                      <CardContent className="p-8">
                        <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg flex items-center justify-center mb-6">
                          <social.icon className="w-6 h-6 text-white" />
                        </div>
                        <h3 className="text-xl font-bold text-white mb-4">{social.type}</h3>
                        <p className="text-slate-300 mb-6">{social.description}</p>
                        <ul className="space-y-2">
                          {social.features.map((feature, idx) => (
                            <li key={idx} className="flex items-center text-slate-400 text-sm">
                              <CheckCircle className="w-3 h-3 text-purple-400 mr-2 flex-shrink-0" />
                              {feature}
                            </li>
                          ))}
                        </ul>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>

              {/* Pricing Section */}
              <section className="mb-16">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Ahmedabad Social Media
                    <span className="bg-gradient-to-r from-purple-400 to-purple-600 bg-clip-text text-transparent"> Pricing</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Competitive social media pricing designed for Ahmedabad's textile, chemical, and manufacturing business environment.
                  </p>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                  {socialMediaPackages.map((pkg, index) => (
                    <Card key={index} className={`bg-slate-900/80 border-purple-500/20 hover:border-purple-500/40 transition-all duration-300 relative ${pkg.popular ? 'ring-2 ring-purple-500' : ''}`}>
                      {pkg.popular && (
                        <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                          <span className="bg-purple-500 text-white px-4 py-2 rounded-full text-sm font-semibold">Most Popular</span>
                        </div>
                      )}
                      <CardContent className="p-8">
                        <h3 className="text-2xl font-bold text-white mb-2">{pkg.name}</h3>
                        <p className="text-slate-300 mb-6">{pkg.description}</p>
                        <div className="mb-6">
                          <span className="text-4xl font-bold text-purple-400">{pkg.price}</span>
                          <span className="text-slate-400">{pkg.period}</span>
                        </div>
                        <ul className="space-y-3 mb-8">
                          {pkg.features.map((feature, idx) => (
                            <li key={idx} className="flex items-center text-slate-300">
                              <CheckCircle className="w-4 h-4 text-purple-400 mr-3 flex-shrink-0" />
                              {feature}
                            </li>
                          ))}
                        </ul>
                        <Button asChild className={`w-full ${pkg.popular ? 'bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700' : 'bg-slate-700 hover:bg-slate-600'} text-white`}>
                          <Link to="/quote">Get Started</Link>
                        </Button>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>

              {/* CTA Section */}
              <section className="bg-gradient-to-r from-purple-600 to-purple-500 rounded-2xl p-8 text-center">
                <h2 className="text-3xl md:text-5xl font-bold mb-6">Ready to Dominate Ahmedabad Social Media?</h2>
                <p className="text-xl mb-8">
                  Join 1400+ Ahmedabad brands that trust GOD Digital Marketing for social media success. Proven strategies that deliver 320% engagement growth and ₹22Cr+ revenue generation.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button asChild size="lg" className="bg-white text-purple-600 hover:bg-slate-100 text-lg px-8 py-4">
                    <Link to="/quote">Get Free Social Media Audit</Link>
                  </Button>
                  <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-purple-600 text-lg px-8 py-4">
                    <Link to="/contact">Call Social Media Experts: +91-8708577598</Link>
                  </Button>
                </div>
              </section>
            </div>

            {/* Sidebar */}
            <div className="lg:col-span-1">
              <Sidebar currentService="social-media" currentLocation="ahmedabad" />
            </div>
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
};

export default SocialMediaAhmedabad;
