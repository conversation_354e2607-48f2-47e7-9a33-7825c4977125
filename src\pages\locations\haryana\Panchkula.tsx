import React from 'react';
import { Link } from 'react-router-dom';
import { ArrowRight, MapPin, TrendingUp, Users, CheckCircle, Building, Star, TreePine } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';

const Panchkula = () => {
  const allServices = [
    'SEO Services Panchkula',
    'Local SEO Panchkula', 
    'International SEO Panchkula',
    'E-commerce SEO Panchkula',
    'Technical SEO Panchkula',
    'Enterprise SEO Panchkula',
    'Google Ads Management Panchkula',
    'Facebook Ads Panchkula',
    'LinkedIn Ads Panchkula',
    'YouTube Marketing Panchkula',
    'Social Media Marketing Panchkula',
    'Content Marketing Panchkula',
    'AI Content Creation Panchkula',
    'Social Media Automation Panchkula',
    'Business Process Automation Panchkula',
    'Marketing Automation Panchkula',
    'Lead Generation Panchkula',
    'Website Development Panchkula',
    'Conversion Optimization Panchkula',
    'Email Marketing Panchkula',
    'Influencer Marketing Panchkula',
    'Video Marketing Panchkula',
    'AI Chatbot Development Panchkula',
    'CRM Solutions Panchkula',
    'Sales Funnel Automation Panchkula',
    'Digital Strategy Consulting Panchkula',
    'Online Reputation Management Panchkula'
  ];

  const industries = [
    'IT Services Panchkula',
    'Government Sector Panchkula',
    'Real Estate Panchkula',
    'Healthcare Panchkula',
    'Education Panchkula',
    'Hospitality Panchkula',
    'Retail & E-commerce Panchkula',
    'Financial Services Panchkula',
    'Manufacturing Panchkula',
    'Consulting Services Panchkula'
  ];

  const areas = [
    'Sector 5 Digital Marketing',
    'Sector 12A SEO Services', 
    'Sector 20 Digital Marketing',
    'Mansa Devi Complex SEO Services',
    'Industrial Area Phase 1 Digital Marketing',
    'Industrial Area Phase 2 SEO Services',
    'Kalka Digital Marketing',
    'Morni Hills SEO Services',
    'Raipur Rani Digital Marketing',
    'Barwala SEO Services'
  ];

  const serviceCategories = [
    {
      category: 'SEO Services Panchkula',
      services: ['Local SEO', 'International SEO', 'E-commerce SEO', 'Technical SEO', 'Enterprise SEO'],
      description: 'Complete SEO solutions for Panchkula businesses to dominate search rankings'
    },
    {
      category: 'Paid Advertising Panchkula', 
      services: ['Google Ads', 'Facebook Ads', 'LinkedIn Ads', 'YouTube Marketing'],
      description: 'Strategic paid advertising campaigns for maximum ROI in Panchkula market'
    },
    {
      category: 'AI Automation Panchkula',
      services: ['AI Content Creation', 'Social Media Automation', 'Business Process Automation', 'Marketing Automation'],
      description: 'Advanced AI-powered automation solutions for Panchkula enterprises'
    },
    {
      category: 'Digital Strategy Panchkula',
      services: ['Content Marketing', 'Social Media Marketing', 'Email Marketing', 'Digital Consulting'],
      description: 'Comprehensive digital strategy and marketing solutions for Panchkula businesses'
    }
  ];

  const stats = [
    {
      metric: '120+',
      description: 'Panchkula Businesses Served',
      detail: 'Across all sectors'
    },
    {
      metric: '450%',
      description: 'Average Traffic Increase',
      detail: 'For Panchkula clients'
    },
    {
      metric: '₹42L+',
      description: 'Revenue Generated',
      detail: 'For Panchkula businesses'
    }
  ];

  return (
    <div className="min-h-screen bg-slate-900 relative">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        {/* Hero Section */}
        <section className="pt-32 pb-20">
          <div className="container mx-auto px-6">
            <div className="max-w-4xl mx-auto text-center">
              <div className="inline-flex items-center space-x-2 bg-emerald-500/20 border border-emerald-500/30 rounded-full px-6 py-2 mb-8">
                <TreePine className="w-4 h-4 text-emerald-400" />
                <span className="text-emerald-400 font-medium">Panchkula Digital Marketing • Planned City Excellence</span>
              </div>
              
              <h1 className="text-5xl md:text-6xl font-bold mb-6 leading-tight">
                Complete Digital Marketing Services in
                <span className="bg-gradient-to-r from-emerald-400 to-emerald-600 bg-clip-text text-transparent"> Panchkula</span>
              </h1>
              
              <p className="text-xl text-slate-300 mb-12 max-w-3xl mx-auto">
                Leading digital marketing agency in Panchkula offering ALL 25+ services from SEO to AI automation. From IT companies to government sector enterprises, we've helped 120+ businesses grow with proven strategies across all Panchkula sectors and areas.
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button asChild size="lg" className="bg-gradient-to-r from-emerald-500 to-emerald-600 hover:from-emerald-600 hover:to-emerald-700 text-white text-lg px-8 py-4">
                  <Link to="/quote">Get Free Panchkula Digital Marketing Audit</Link>
                </Button>
                <Button asChild size="lg" variant="outline" className="border-emerald-500 text-emerald-400 hover:bg-emerald-500 hover:text-white text-lg px-8 py-4">
                  <Link to="/contact">Call Panchkula Office</Link>
                </Button>
              </div>
            </div>
          </div>
        </section>

        {/* Stats Section */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Panchkula Digital Marketing
                <span className="bg-gradient-to-r from-emerald-400 to-emerald-600 bg-clip-text text-transparent"> Success Stories</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto mb-12">
                Real results from Panchkula businesses across all industries - from IT companies to government sector enterprises in this planned city.
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
              {stats.map((stat, index) => (
                <div key={index} className="text-center">
                  <div className="text-5xl font-bold text-emerald-400 mb-2">{stat.metric}</div>
                  <div className="text-white font-semibold mb-1">{stat.description}</div>
                  <div className="text-slate-400">{stat.detail}</div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Service Categories Section */}
        <section className="py-20">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Complete Digital Marketing Services in
                <span className="bg-gradient-to-r from-emerald-400 to-emerald-600 bg-clip-text text-transparent"> Panchkula</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto">
                All 25+ digital marketing services available for Panchkula businesses - from SEO to AI automation.
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-20">
              {serviceCategories.map((category, index) => (
                <Card key={index} className="bg-slate-900/80 border-emerald-500/20 hover:border-emerald-500/40 transition-all duration-300">
                  <CardContent className="p-8">
                    <h3 className="text-xl font-bold text-white mb-4">{category.category}</h3>
                    <p className="text-slate-300 mb-6">{category.description}</p>
                    <div className="grid grid-cols-2 gap-2">
                      {category.services.map((service, idx) => (
                        <div key={idx} className="flex items-center text-slate-400 text-sm">
                          <CheckCircle className="w-3 h-3 text-emerald-400 mr-2 flex-shrink-0" />
                          {service}
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* All Services Grid */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                All Digital Marketing Services
                <span className="bg-gradient-to-r from-emerald-400 to-emerald-600 bg-clip-text text-transparent"> Available in Panchkula</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto">
                Complete range of 25+ digital marketing services for every business need in Panchkula.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {allServices.map((service, index) => (
                <Card key={index} className="bg-slate-900/80 border-emerald-500/20 hover:border-emerald-500/40 transition-all duration-300">
                  <CardContent className="p-4 text-center">
                    <h3 className="text-white font-semibold text-sm mb-2">{service}</h3>
                    <Button asChild size="sm" variant="outline" className="border-emerald-500 text-emerald-400 hover:bg-emerald-500 hover:text-white text-xs">
                      <Link to="/quote">Get Quote</Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Industries Section */}
        <section className="py-20">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Panchkula Industries We
                <span className="bg-gradient-to-r from-emerald-400 to-emerald-600 bg-clip-text text-transparent"> Serve</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto">
                Specialized digital marketing strategies for all major Panchkula industries and business sectors.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
              {industries.map((industry, index) => (
                <Card key={index} className="bg-slate-900/80 border-emerald-500/20 hover:border-emerald-500/40 transition-all duration-300">
                  <CardContent className="p-6 text-center">
                    <Star className="w-8 h-8 text-emerald-400 mx-auto mb-4" />
                    <h3 className="text-white font-semibold mb-2 text-sm">{industry}</h3>
                    <Button asChild size="sm" variant="outline" className="border-emerald-500 text-emerald-400 hover:bg-emerald-500 hover:text-white text-xs">
                      <Link to="/quote">Get Strategy</Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Areas Section */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Panchkula Areas We
                <span className="bg-gradient-to-r from-emerald-400 to-emerald-600 bg-clip-text text-transparent"> Cover</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto">
                Complete digital marketing services across all Panchkula sectors and surrounding areas.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
              {areas.map((area, index) => (
                <Card key={index} className="bg-slate-900/80 border-emerald-500/20 hover:border-emerald-500/40 transition-all duration-300">
                  <CardContent className="p-6 text-center">
                    <MapPin className="w-8 h-8 text-emerald-400 mx-auto mb-4" />
                    <h3 className="text-white font-semibold mb-2 text-sm">{area}</h3>
                    <Button asChild size="sm" variant="outline" className="border-emerald-500 text-emerald-400 hover:bg-emerald-500 hover:text-white text-xs">
                      <Link to="/contact">Contact Us</Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Why Choose Us Section */}
        <section className="py-20">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Why Choose GOD Digital Marketing in
                <span className="bg-gradient-to-r from-emerald-400 to-emerald-600 bg-clip-text text-transparent"> Panchkula?</span>
              </h2>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              <Card className="bg-slate-900/80 border-emerald-500/20">
                <CardContent className="p-8">
                  <TrendingUp className="w-12 h-12 text-emerald-400 mb-6" />
                  <h3 className="text-xl font-bold text-white mb-4">Complete Service Portfolio</h3>
                  <p className="text-slate-300">All 25+ digital marketing services available in Panchkula - from basic SEO to advanced AI automation solutions.</p>
                </CardContent>
              </Card>

              <Card className="bg-slate-900/80 border-emerald-500/20">
                <CardContent className="p-8">
                  <Users className="w-12 h-12 text-emerald-400 mb-6" />
                  <h3 className="text-xl font-bold text-white mb-4">Proven Panchkula Results</h3>
                  <p className="text-slate-300">120+ successful Panchkula campaigns with measurable ROI improvements across all industries and service categories.</p>
                </CardContent>
              </Card>

              <Card className="bg-slate-900/80 border-emerald-500/20">
                <CardContent className="p-8">
                  <CheckCircle className="w-12 h-12 text-emerald-400 mb-6" />
                  <h3 className="text-xl font-bold text-white mb-4">Planned City Expertise</h3>
                  <p className="text-slate-300">Deep understanding of Panchkula's planned city structure, IT sector growth, and government enterprise needs.</p>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 bg-gradient-to-r from-emerald-600 to-emerald-500">
          <div className="container mx-auto px-6 text-center">
            <h2 className="text-4xl md:text-5xl font-bold mb-6">Ready to Dominate Panchkula's Digital Market?</h2>
            <p className="text-xl mb-8 max-w-3xl mx-auto">
              Join 120+ Panchkula businesses that trust GOD Digital Marketing for complete digital solutions. All 25+ services available for your business growth.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button asChild size="lg" className="bg-white text-emerald-600 hover:bg-slate-100 text-lg px-8 py-4">
                <Link to="/quote">Get Free Digital Marketing Audit</Link>
              </Button>
              <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-emerald-600 text-lg px-8 py-4">
                <Link to="/contact">Call Panchkula Office Now</Link>
              </Button>
            </div>
          </div>
        </section>
      </main>
      
      <Footer />
    </div>
  );
};

export default Panchkula;
