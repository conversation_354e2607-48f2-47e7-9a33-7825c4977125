import React from 'react';
import { Link } from 'react-router-dom';
import { ArrowRight, Code, TrendingUp, Users, CheckCircle, Building, Star, Crown, Monitor, Zap } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';
import Sidebar from '@/components/Sidebar';

const WebDevelopmentJaipur = () => {
  const webServices = [
    'Web Development Services Jaipur',
    'Website Design Jaipur',
    'E-commerce Development Jaipur',
    'Custom Web Applications Jaipur',
    'Responsive Web Design Jaipur',
    'WordPress Development Jaipur',
    'React Development Jaipur',
    'Node.js Development Jaipur',
    'PHP Development Jaipur',
    'Mobile App Development Jaipur',
    'Tourism Website Development Jaipur',
    'Heritage Website Design Jaipur',
    'Handicraft E-commerce Jaipur',
    'Jewelry Website Development Jaipur',
    'Hotel Website Development Jaipur',
    'Restaurant Website Design Jaipur',
    'Real Estate Website Jaipur',
    'Fashion E-commerce Jaipur',
    'B2B Portal Development Jaipur',
    'CMS Development Jaipur'
  ];

  const webTypes = [
    {
      type: 'Tourism & Heritage Websites',
      description: 'Stunning websites for Jaipur\'s tourism capital and heritage industry businesses',
      icon: Building,
      features: ['Tourism Portals', 'Heritage Site Websites', 'Hotel Booking Systems', 'Travel Package Platforms']
    },
    {
      type: 'Handicraft & Jewelry E-commerce',
      description: 'Beautiful e-commerce solutions for Jaipur\'s famous handicraft and jewelry manufacturing sector',
      icon: Star,
      features: ['Craft E-commerce Stores', 'Jewelry Online Catalogs', 'Artisan Marketplaces', 'Export Portals']
    },
    {
      type: 'Fashion & Textile Platforms',
      description: 'Modern web platforms for Jaipur\'s textile and fashion industry leaders',
      icon: Crown,
      features: ['Fashion E-commerce', 'Textile B2B Portals', 'Designer Showcases', 'Style Platforms']
    },
    {
      type: 'Business & Commercial Websites',
      description: 'Professional websites for Jaipur\'s growing business and commercial sector',
      icon: Monitor,
      features: ['Corporate Websites', 'Business Portals', 'Service Platforms', 'Professional Sites']
    }
  ];

  const webPackages = [
    {
      name: 'Web Development Jaipur Starter',
      price: '₹35,000',
      period: 'one-time',
      description: 'Perfect for small Jaipur businesses and tourism operators',
      features: [
        '5-Page Responsive Website',
        'Mobile-Friendly Design',
        'Basic SEO Setup',
        'Contact Forms',
        '1 Year Free Hosting',
        'Tourism Industry Focus'
      ]
    },
    {
      name: 'Jaipur Web Development Professional',
      price: '₹75,000',
      period: 'one-time',
      description: 'Comprehensive web development for growing Jaipur businesses',
      features: [
        '10-Page Custom Website',
        'E-commerce Integration',
        'Advanced SEO Setup',
        'Payment Gateway Integration',
        'Admin Panel',
        'Heritage/Handicraft Specialization',
        '2 Years Free Hosting'
      ],
      popular: true
    },
    {
      name: 'Enterprise Web Development Jaipur',
      price: '₹1,50,000',
      period: 'one-time',
      description: 'Advanced web development for large Jaipur enterprises and exporters',
      features: [
        'Custom Web Application',
        'Advanced E-commerce Features',
        'Multi-language Support',
        'Advanced Analytics',
        'API Integrations',
        'Export Market Features',
        'Dedicated Developer Support'
      ]
    }
  ];

  const stats = [
    {
      metric: '750+',
      description: 'Websites Developed',
      detail: 'For Jaipur businesses'
    },
    {
      metric: '98%',
      description: 'Client Satisfaction Rate',
      detail: 'Happy customers'
    },
    {
      metric: '₹25Cr+',
      description: 'Revenue Generated',
      detail: 'Through our websites'
    },
    {
      metric: '99.9%',
      description: 'Website Uptime',
      detail: 'Reliable hosting'
    }
  ];

  const achievements = [
    'Top Web Development Company in Jaipur',
    'Tourism Industry Web Leaders',
    'Heritage Business Web Experts',
    'Handicraft Industry Web Specialists',
    'Jewelry Business Web Champions',
    'Pink City Web Development Pioneers'
  ];

  return (
    <div className="min-h-screen bg-slate-900 relative">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        <div className="container mx-auto px-6 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            {/* Main Content */}
            <div className="lg:col-span-3">
              {/* Hero Section */}
              <section className="pt-24 pb-16">
                <div className="max-w-4xl">
                  <div className="inline-flex items-center space-x-2 bg-cyan-500/20 border border-cyan-500/30 rounded-full px-6 py-2 mb-8">
                    <Code className="w-4 h-4 text-cyan-400" />
                    <span className="text-cyan-400 font-medium">Web Development Jaipur • Pink City Digital Excellence</span>
                  </div>
                  
                  <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
                    #1 Web Development Company in
                    <span className="bg-gradient-to-r from-cyan-400 to-cyan-600 bg-clip-text text-transparent"> Jaipur</span>
                  </h1>
                  
                  <p className="text-xl text-slate-300 mb-8">
                    Professional web development services in Jaipur delivering comprehensive website design and web application development solutions. Our Jaipur web development company provides complete web development services including responsive web design, e-commerce development, custom web applications, WordPress development, and mobile app development. Expert web development solutions with proven performance, user experience, and business growth through strategic web development and website optimization for 750+ Jaipur businesses across tourism, handicraft, jewelry, and heritage sectors.
                  </p>
                  
                  <div className="flex flex-col sm:flex-row gap-4 mb-8">
                    <Button asChild size="lg" className="bg-gradient-to-r from-cyan-500 to-cyan-600 hover:from-cyan-600 hover:to-cyan-700 text-white text-lg px-8 py-4">
                      <Link to="/quote">Get Free Jaipur Web Consultation</Link>
                    </Button>
                    <Button asChild size="lg" variant="outline" className="border-cyan-500 text-cyan-400 hover:bg-cyan-500 hover:text-white text-lg px-8 py-4">
                      <Link to="/contact">Call Web Experts: +91-8708577598</Link>
                    </Button>
                  </div>

                  {/* Achievements */}
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                    {achievements.map((achievement, index) => (
                      <div key={index} className="flex items-center justify-center space-x-2 bg-slate-800/50 rounded-lg p-3">
                        <CheckCircle className="w-4 h-4 text-cyan-400 flex-shrink-0" />
                        <span className="text-slate-300">{achievement}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </section>

              {/* Stats Section */}
              <section className="mb-16 bg-slate-800/50 rounded-2xl p-8">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Jaipur Web Development
                    <span className="bg-gradient-to-r from-cyan-400 to-cyan-600 bg-clip-text text-transparent"> Success Metrics</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Measurable web development results from Jaipur businesses across tourism, handicraft, jewelry, and heritage sectors.
                  </p>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
                  {stats.map((stat, index) => (
                    <div key={index} className="text-center">
                      <div className="text-4xl font-bold text-cyan-400 mb-2">{stat.metric}</div>
                      <div className="text-white font-semibold mb-1">{stat.description}</div>
                      <div className="text-slate-400">{stat.detail}</div>
                    </div>
                  ))}
                </div>
              </section>

              {/* Web Types Section */}
              <section className="mb-16">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Websites We
                    <span className="bg-gradient-to-r from-cyan-400 to-cyan-600 bg-clip-text text-transparent"> Develop</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Specialized web development services designed for Jaipur's unique tourism, handicraft, jewelry, and heritage business landscape.
                  </p>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  {webTypes.map((web, index) => (
                    <Card key={index} className="bg-slate-900/80 border-cyan-500/20 hover:border-cyan-500/40 transition-all duration-300">
                      <CardContent className="p-8">
                        <div className="w-12 h-12 bg-gradient-to-r from-cyan-500 to-cyan-600 rounded-lg flex items-center justify-center mb-6">
                          <web.icon className="w-6 h-6 text-white" />
                        </div>
                        <h3 className="text-xl font-bold text-white mb-4">{web.type}</h3>
                        <p className="text-slate-300 mb-6">{web.description}</p>
                        <ul className="space-y-2">
                          {web.features.map((feature, idx) => (
                            <li key={idx} className="flex items-center text-slate-400 text-sm">
                              <CheckCircle className="w-3 h-3 text-cyan-400 mr-2 flex-shrink-0" />
                              {feature}
                            </li>
                          ))}
                        </ul>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>

              {/* Pricing Section */}
              <section className="mb-16">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Jaipur Web Development
                    <span className="bg-gradient-to-r from-cyan-400 to-cyan-600 bg-clip-text text-transparent"> Pricing</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Competitive web development pricing designed for Jaipur's tourism, handicraft, and heritage business environment.
                  </p>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                  {webPackages.map((pkg, index) => (
                    <Card key={index} className={`bg-slate-900/80 border-cyan-500/20 hover:border-cyan-500/40 transition-all duration-300 relative ${pkg.popular ? 'ring-2 ring-cyan-500' : ''}`}>
                      {pkg.popular && (
                        <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                          <span className="bg-cyan-500 text-white px-4 py-2 rounded-full text-sm font-semibold">Most Popular</span>
                        </div>
                      )}
                      <CardContent className="p-8">
                        <h3 className="text-2xl font-bold text-white mb-2">{pkg.name}</h3>
                        <p className="text-slate-300 mb-6">{pkg.description}</p>
                        <div className="mb-6">
                          <span className="text-4xl font-bold text-cyan-400">{pkg.price}</span>
                          <span className="text-slate-400 text-sm ml-2">{pkg.period}</span>
                        </div>
                        <ul className="space-y-3 mb-8">
                          {pkg.features.map((feature, idx) => (
                            <li key={idx} className="flex items-center text-slate-300">
                              <CheckCircle className="w-4 h-4 text-cyan-400 mr-3 flex-shrink-0" />
                              {feature}
                            </li>
                          ))}
                        </ul>
                        <Button asChild className={`w-full ${pkg.popular ? 'bg-gradient-to-r from-cyan-500 to-cyan-600 hover:from-cyan-600 hover:to-cyan-700' : 'bg-slate-700 hover:bg-slate-600'} text-white`}>
                          <Link to="/quote">Get Started</Link>
                        </Button>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>

              {/* CTA Section */}
              <section className="bg-gradient-to-r from-cyan-600 to-cyan-500 rounded-2xl p-8 text-center">
                <h2 className="text-3xl md:text-5xl font-bold mb-6">Ready to Launch Your Jaipur Website?</h2>
                <p className="text-xl mb-8">
                  Join 750+ Jaipur businesses that trust GOD Digital Marketing for web development success. Proven solutions that deliver 98% satisfaction and ₹25Cr+ revenue generation.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button asChild size="lg" className="bg-white text-cyan-600 hover:bg-slate-100 text-lg px-8 py-4">
                    <Link to="/quote">Get Free Web Consultation</Link>
                  </Button>
                  <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-cyan-600 text-lg px-8 py-4">
                    <Link to="/contact">Call Web Experts: +91-8708577598</Link>
                  </Button>
                </div>
              </section>
            </div>

            {/* Sidebar */}
            <div className="lg:col-span-1">
              <Sidebar currentService="web-development" currentLocation="jaipur" />
            </div>
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
};

export default WebDevelopmentJaipur;
