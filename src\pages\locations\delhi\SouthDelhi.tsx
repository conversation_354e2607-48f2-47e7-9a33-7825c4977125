import React from 'react';
import { Link } from 'react-router-dom';
import { ArrowRight, MapPin, TrendingUp, Users, CheckCircle, Building, Star, Crown, Gem, Briefcase } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';

const SouthDelhi = () => {
  const comprehensiveServices = [
    'Search Engine Optimization (SEO) South Delhi',
    'Local SEO Services South Delhi', 
    'International SEO South Delhi',
    'E-commerce SEO South Delhi',
    'Technical SEO Audit South Delhi',
    'Enterprise SEO Solutions South Delhi',
    'Google Ads Management South Delhi',
    'Pay-Per-Click (PPC) Advertising South Delhi',
    'Facebook Advertising South Delhi',
    'Instagram Marketing South Delhi',
    'LinkedIn Ads Management South Delhi',
    'YouTube Marketing Services South Delhi',
    'Social Media Marketing South Delhi',
    'Social Media Management South Delhi',
    'Content Marketing Strategy South Delhi',
    'Content Creation Services South Delhi',
    'AI Content Generation South Delhi',
    'Copywriting Services South Delhi',
    'Blog Writing Services South Delhi',
    'Social Media Automation South Delhi',
    'Marketing Automation South Delhi',
    'Business Process Automation South Delhi',
    'CRM Automation South Delhi',
    'Email Marketing Automation South Delhi',
    'Lead Generation Services South Delhi',
    'Lead Nurturing Campaigns South Delhi',
    'Conversion Rate Optimization South Delhi',
    'Website Development South Delhi',
    'Web Design Services South Delhi',
    'E-commerce Development South Delhi',
    'Mobile App Development South Delhi',
    'Landing Page Optimization South Delhi',
    'Email Marketing South Delhi',
    'Newsletter Marketing South Delhi',
    'Influencer Marketing South Delhi',
    'Video Marketing Services South Delhi',
    'Video Production South Delhi',
    'Animation Services South Delhi',
    'AI Chatbot Development South Delhi',
    'Customer Support Automation South Delhi',
    'CRM Solutions South Delhi',
    'Sales Funnel Automation South Delhi',
    'Marketing Analytics South Delhi',
    'Digital Strategy Consulting South Delhi',
    'Brand Strategy South Delhi',
    'Online Reputation Management South Delhi',
    'Crisis Management South Delhi',
    'Public Relations (PR) South Delhi',
    'Media Planning South Delhi',
    'Programmatic Advertising South Delhi'
  ];

  const southDelhiAreas = [
    'Greater Kailash Digital Marketing',
    'Lajpat Nagar SEO Services',
    'Defence Colony Digital Marketing',
    'Khan Market SEO Services',
    'Saket Digital Marketing',
    'Vasant Kunj SEO Services',
    'Hauz Khas Digital Marketing',
    'Green Park SEO Services',
    'Nehru Place Digital Marketing',
    'Kalkaji SEO Services',
    'CR Park Digital Marketing',
    'Malviya Nagar SEO Services'
  ];

  const industries = [
    'Luxury Retail & Fashion South Delhi',
    'High-End Real Estate South Delhi',
    'Premium Healthcare Services South Delhi',
    'International Education South Delhi',
    'Financial Services & Banking South Delhi',
    'IT Services & Software South Delhi',
    'Consulting & Professional Services South Delhi',
    'Hospitality & Fine Dining South Delhi',
    'Art & Culture South Delhi',
    'Wellness & Lifestyle South Delhi',
    'Import-Export Business South Delhi',
    'Corporate Headquarters South Delhi'
  ];

  const serviceCategories = [
    {
      category: 'Luxury Brand Marketing South Delhi',
      services: ['Premium Brand SEO', 'Luxury E-commerce Marketing', 'High-End Fashion Marketing', 'Exclusive Product Promotion', 'Affluent Customer Targeting'],
      description: 'Specialized digital marketing for luxury brands, premium retailers, and high-end fashion businesses in affluent South Delhi markets',
      icon: Gem
    },
    {
      category: 'Corporate & Professional Services South Delhi', 
      services: ['Corporate SEO Solutions', 'B2B Lead Generation', 'Professional Services Marketing', 'Executive Branding', 'Enterprise Digital Strategy'],
      description: 'Strategic digital marketing for corporate headquarters, consulting firms, and professional services targeting business executives',
      icon: Briefcase
    },
    {
      category: 'Premium Real Estate Marketing South Delhi',
      services: ['Luxury Property SEO', 'High-Value Real Estate Marketing', 'Property Portal Optimization', 'Investor Targeting', 'Premium Development Promotion'],
      description: 'Comprehensive digital marketing for premium real estate developers, luxury property dealers, and high-value investment properties',
      icon: Building
    },
    {
      category: 'Lifestyle & Wellness Marketing South Delhi',
      services: ['Wellness Center SEO', 'Lifestyle Brand Marketing', 'Health & Fitness Promotion', 'Spa & Salon Marketing', 'Premium Service Promotion'],
      description: 'Targeted digital marketing for wellness centers, lifestyle brands, premium spas, and health-focused businesses in South Delhi',
      icon: Star
    }
  ];

  const stats = [
    {
      metric: '800+',
      description: 'South Delhi Businesses Served',
      detail: 'Premium to corporate sectors'
    },
    {
      metric: '520%',
      description: 'Average Traffic Increase',
      detail: 'For South Delhi clients'
    },
    {
      metric: '₹25Cr+',
      description: 'Revenue Generated',
      detail: 'For South Delhi businesses'
    },
    {
      metric: '97%',
      description: 'Client Retention Rate',
      detail: 'Long-term partnerships'
    }
  ];

  const achievements = [
    'Top Digital Marketing Agency in South Delhi',
    'Luxury Brand Marketing Specialist',
    'Premium Real Estate Marketing Expert',
    'Greater Kailash Business Partner',
    'Khan Market Commercial Specialist',
    'Saket Corporate Hub Leader'
  ];

  return (
    <div className="min-h-screen bg-slate-900 relative">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        {/* Hero Section */}
        <section className="pt-32 pb-20">
          <div className="container mx-auto px-6">
            <div className="max-w-5xl mx-auto text-center">
              <div className="inline-flex items-center space-x-2 bg-purple-500/20 border border-purple-500/30 rounded-full px-6 py-2 mb-8">
                <Crown className="w-4 h-4 text-purple-400" />
                <span className="text-purple-400 font-medium">South Delhi Digital Marketing • Premium Business District</span>
              </div>
              
              <h1 className="text-5xl md:text-7xl font-bold mb-6 leading-tight">
                Complete Digital Marketing Services in
                <span className="bg-gradient-to-r from-purple-400 to-purple-600 bg-clip-text text-transparent"> South Delhi</span>
              </h1>
              
              <p className="text-xl text-slate-300 mb-12 max-w-4xl mx-auto">
                Premier digital marketing agency in South Delhi offering comprehensive 50+ services from advanced SEO to AI-powered automation. Serving 800+ businesses from Greater Kailash to Saket - specializing in luxury brands, premium real estate, corporate headquarters, and high-end services. Expert solutions with proven ₹25Cr+ revenue generation in Delhi's most affluent district.
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
                <Button asChild size="lg" className="bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white text-lg px-8 py-4">
                  <Link to="/quote">Get Free South Delhi Digital Marketing Audit</Link>
                </Button>
                <Button asChild size="lg" variant="outline" className="border-purple-500 text-purple-400 hover:bg-purple-500 hover:text-white text-lg px-8 py-4">
                  <Link to="/contact">Call South Delhi Office: +91-9999-XXXX-XX</Link>
                </Button>
              </div>

              {/* Achievements */}
              <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                {achievements.map((achievement, index) => (
                  <div key={index} className="flex items-center justify-center space-x-2 bg-slate-800/50 rounded-lg p-3">
                    <CheckCircle className="w-4 h-4 text-purple-400 flex-shrink-0" />
                    <span className="text-slate-300">{achievement}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* Stats Section */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-6xl font-bold mb-6">
                South Delhi Digital Marketing
                <span className="bg-gradient-to-r from-purple-400 to-purple-600 bg-clip-text text-transparent"> Success Metrics</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-4xl mx-auto mb-12">
                Measurable results from South Delhi businesses across all premium sectors - from luxury brands to corporate headquarters in Delhi's most affluent district.
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-4 gap-8 mb-16">
              {stats.map((stat, index) => (
                <div key={index} className="text-center">
                  <div className="text-5xl font-bold text-purple-400 mb-2">{stat.metric}</div>
                  <div className="text-white font-semibold mb-1">{stat.description}</div>
                  <div className="text-slate-400">{stat.detail}</div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Service Categories Section */}
        <section className="py-20">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-6xl font-bold mb-6">
                Specialized Digital Marketing Services in
                <span className="bg-gradient-to-r from-purple-400 to-purple-600 bg-clip-text text-transparent"> South Delhi</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-4xl mx-auto">
                Tailored digital marketing solutions for South Delhi's premium business landscape - from luxury brands to corporate headquarters and high-end services.
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-20">
              {serviceCategories.map((category, index) => (
                <Card key={index} className="bg-slate-900/80 border-purple-500/20 hover:border-purple-500/40 transition-all duration-300">
                  <CardContent className="p-8">
                    <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg flex items-center justify-center mb-6">
                      <category.icon className="w-6 h-6 text-white" />
                    </div>
                    <h3 className="text-2xl font-bold text-white mb-4">{category.category}</h3>
                    <p className="text-slate-300 mb-6">{category.description}</p>
                    <div className="grid grid-cols-1 gap-2">
                      {category.services.map((service, idx) => (
                        <div key={idx} className="flex items-center text-slate-400 text-sm">
                          <CheckCircle className="w-3 h-3 text-purple-400 mr-2 flex-shrink-0" />
                          {service}
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* All Services Grid */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-6xl font-bold mb-6">
                All 50+ Digital Marketing Services
                <span className="bg-gradient-to-r from-purple-400 to-purple-600 bg-clip-text text-transparent"> Available in South Delhi</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-4xl mx-auto">
                Complete range of digital marketing services for every premium business need in South Delhi.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {comprehensiveServices.map((service, index) => (
                <Card key={index} className="bg-slate-900/80 border-purple-500/20 hover:border-purple-500/40 transition-all duration-300">
                  <CardContent className="p-4 text-center">
                    <h3 className="text-white font-semibold text-sm mb-2">{service}</h3>
                    <Button asChild size="sm" variant="outline" className="border-purple-500 text-purple-400 hover:bg-purple-500 hover:text-white text-xs">
                      <Link to="/quote">Get Quote</Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 bg-gradient-to-r from-purple-600 to-purple-500">
          <div className="container mx-auto px-6 text-center">
            <h2 className="text-4xl md:text-6xl font-bold mb-6">Ready to Dominate South Delhi's Premium Market?</h2>
            <p className="text-xl mb-8 max-w-4xl mx-auto">
              Join 800+ South Delhi businesses that trust GOD Digital Marketing for premium digital solutions. All 50+ services available for luxury brands, corporate headquarters, and high-end services.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button asChild size="lg" className="bg-white text-purple-600 hover:bg-slate-100 text-lg px-8 py-4">
                <Link to="/quote">Get Free Premium Digital Marketing Audit</Link>
              </Button>
              <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-purple-600 text-lg px-8 py-4">
                <Link to="/contact">Call South Delhi Office: +91-9999-XXXX-XX</Link>
              </Button>
            </div>
          </div>
        </section>
      </main>
      
      <Footer />
    </div>
  );
};

export default SouthDelhi;
