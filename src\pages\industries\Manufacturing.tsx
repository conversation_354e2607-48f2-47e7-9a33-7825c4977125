import React from 'react';
import { Link } from 'react-router-dom';
import { ArrowRight, Factory, TrendingUp, Users, CheckCircle, Cog, Star } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';

const Manufacturing = () => {
  const services = [
    'Manufacturing SEO',
    'Industrial Lead Generation',
    'B2B Digital Marketing',
    'Manufacturing PPC Management',
    'Industrial Website Development',
    'Manufacturing Content Marketing',
    'Trade Show Digital Marketing',
    'Manufacturing Social Media'
  ];

  const manufacturingTypes = [
    'Automotive Manufacturing',
    'Textile Manufacturing',
    'Chemical Manufacturing',
    'Electronics Manufacturing',
    'Food Processing',
    'Pharmaceutical Manufacturing',
    'Steel & Metal Manufacturing',
    'Machinery Manufacturing'
  ];

  const challenges = [
    {
      challenge: 'B2B Lead Generation',
      solution: 'Advanced B2B lead generation funnels that convert industrial buyers into qualified leads',
      icon: Factory
    },
    {
      challenge: 'Technical Product Marketing',
      solution: 'Specialized content marketing for complex industrial products and services',
      icon: Cog
    },
    {
      challenge: 'Global Market Reach',
      solution: 'International SEO strategies to reach global industrial buyers and distributors',
      icon: Star
    }
  ];

  const stats = [
    {
      metric: '600%',
      description: 'Increase in B2B Leads',
      detail: 'For manufacturing clients'
    },
    {
      metric: '₹5Cr+',
      description: 'Manufacturing Sales Generated',
      detail: 'Through digital marketing'
    },
    {
      metric: '200+',
      description: 'Manufacturing Companies',
      detail: 'Successfully marketed'
    }
  ];

  return (
    <div className="min-h-screen bg-slate-900 relative">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        {/* Hero Section */}
        <section className="pt-32 pb-20">
          <div className="container mx-auto px-6">
            <div className="max-w-4xl mx-auto text-center">
              <div className="inline-flex items-center space-x-2 bg-amber-500/20 border border-amber-500/30 rounded-full px-6 py-2 mb-8">
                <Factory className="w-4 h-4 text-amber-400" />
                <span className="text-amber-400 font-medium">Manufacturing Digital Marketing • B2B Lead Generation</span>
              </div>
              
              <h1 className="text-5xl md:text-6xl font-bold mb-6 leading-tight">
                Manufacturing Digital Marketing That
                <span className="bg-gradient-to-r from-amber-400 to-amber-600 bg-clip-text text-transparent"> Generates Industrial Leads</span>
              </h1>
              
              <p className="text-xl text-slate-300 mb-12 max-w-3xl mx-auto">
                Specialized digital marketing for manufacturing companies, industrial suppliers, and B2B manufacturers. Generate qualified industrial leads, reach global buyers, and grow your manufacturing business with proven digital strategies.
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button asChild size="lg" className="bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700 text-white text-lg px-8 py-4">
                  <Link to="/quote">Get Manufacturing Marketing Strategy</Link>
                </Button>
                <Button asChild size="lg" variant="outline" className="border-amber-500 text-amber-400 hover:bg-amber-500 hover:text-white text-lg px-8 py-4">
                  <Link to="/case-studies">View Manufacturing Results</Link>
                </Button>
              </div>
            </div>
          </div>
        </section>

        {/* Stats Section */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Manufacturing Marketing
                <span className="bg-gradient-to-r from-amber-400 to-amber-600 bg-clip-text text-transparent"> Success Stories</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto mb-12">
                Real results from manufacturing companies across India - from small industrial units to large manufacturing corporations.
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
              {stats.map((stat, index) => (
                <div key={index} className="text-center">
                  <div className="text-5xl font-bold text-amber-400 mb-2">{stat.metric}</div>
                  <div className="text-white font-semibold mb-1">{stat.description}</div>
                  <div className="text-slate-400">{stat.detail}</div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Challenges & Solutions */}
        <section className="py-20">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Manufacturing Marketing
                <span className="bg-gradient-to-r from-amber-400 to-amber-600 bg-clip-text text-transparent"> Challenges We Solve</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto">
                Understanding the unique challenges of manufacturing marketing and providing targeted B2B solutions.
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              {challenges.map((item, index) => (
                <Card key={index} className="bg-slate-900/80 border-amber-500/20 hover:border-amber-500/40 transition-all duration-300">
                  <CardContent className="p-8">
                    <div className="w-12 h-12 bg-gradient-to-r from-amber-500 to-amber-600 rounded-lg flex items-center justify-center mb-6">
                      <item.icon className="w-6 h-6 text-white" />
                    </div>
                    <h3 className="text-xl font-bold text-white mb-4">Challenge: {item.challenge}</h3>
                    <p className="text-slate-300">{item.solution}</p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Services Section */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Manufacturing Digital Marketing
                <span className="bg-gradient-to-r from-amber-400 to-amber-600 bg-clip-text text-transparent"> Services</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto">
                Comprehensive digital marketing solutions designed specifically for manufacturing companies and industrial businesses.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {services.map((service, index) => (
                <Card key={index} className="bg-slate-900/80 border-amber-500/20 hover:border-amber-500/40 transition-all duration-300">
                  <CardContent className="p-6 text-center">
                    <Factory className="w-8 h-8 text-amber-400 mx-auto mb-4" />
                    <h3 className="text-white font-semibold mb-2">{service}</h3>
                    <Button asChild size="sm" variant="outline" className="border-amber-500 text-amber-400 hover:bg-amber-500 hover:text-white">
                      <Link to="/quote">Learn More</Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Manufacturing Types Section */}
        <section className="py-20">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Manufacturing Industries We
                <span className="bg-gradient-to-r from-amber-400 to-amber-600 bg-clip-text text-transparent"> Serve</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto">
                Specialized marketing strategies for different types of manufacturing industries and industrial sectors.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {manufacturingTypes.map((type, index) => (
                <Card key={index} className="bg-slate-900/80 border-amber-500/20 hover:border-amber-500/40 transition-all duration-300">
                  <CardContent className="p-6 text-center">
                    <Star className="w-8 h-8 text-amber-400 mx-auto mb-4" />
                    <h3 className="text-white font-semibold mb-2">{type}</h3>
                    <Button asChild size="sm" variant="outline" className="border-amber-500 text-amber-400 hover:bg-amber-500 hover:text-white">
                      <Link to="/quote">Get Strategy</Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Why Choose Us Section */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Why Manufacturing Companies Choose
                <span className="bg-gradient-to-r from-amber-400 to-amber-600 bg-clip-text text-transparent"> GOD Digital Marketing</span>
              </h2>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              <Card className="bg-slate-900/80 border-amber-500/20">
                <CardContent className="p-8">
                  <TrendingUp className="w-12 h-12 text-amber-400 mb-6" />
                  <h3 className="text-xl font-bold text-white mb-4">Manufacturing Expertise</h3>
                  <p className="text-slate-300">Deep understanding of manufacturing industry dynamics, B2B buyer behavior, and industrial marketing strategies.</p>
                </CardContent>
              </Card>

              <Card className="bg-slate-900/80 border-amber-500/20">
                <CardContent className="p-8">
                  <Users className="w-12 h-12 text-amber-400 mb-6" />
                  <h3 className="text-xl font-bold text-white mb-4">B2B Lead Generation</h3>
                  <p className="text-slate-300">Specialized B2B lead generation systems that convert industrial buyers into qualified manufacturing leads.</p>
                </CardContent>
              </Card>

              <Card className="bg-slate-900/80 border-amber-500/20">
                <CardContent className="p-8">
                  <CheckCircle className="w-12 h-12 text-amber-400 mb-6" />
                  <h3 className="text-xl font-bold text-white mb-4">Global Reach</h3>
                  <p className="text-slate-300">International SEO and marketing strategies to reach global industrial buyers and expand manufacturing markets.</p>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 bg-gradient-to-r from-amber-600 to-amber-500">
          <div className="container mx-auto px-6 text-center">
            <h2 className="text-4xl md:text-5xl font-bold mb-6">Ready to Generate More Industrial Leads?</h2>
            <p className="text-xl mb-8 max-w-3xl mx-auto">
              Join 200+ manufacturing companies who trust GOD Digital Marketing to generate qualified B2B leads and grow their industrial business.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button asChild size="lg" className="bg-white text-amber-600 hover:bg-slate-100 text-lg px-8 py-4">
                <Link to="/quote">Get Manufacturing Marketing Strategy</Link>
              </Button>
              <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-amber-600 text-lg px-8 py-4">
                <Link to="/contact">Book Manufacturing Consultation</Link>
              </Button>
            </div>
          </div>
        </section>
      </main>
      
      <Footer />
    </div>
  );
};

export default Manufacturing;
