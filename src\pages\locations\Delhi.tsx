import React from 'react';
import { Link } from 'react-router-dom';
import { ArrowRight, MapPin, TrendingUp, Users, CheckCircle, Building, Star, Crown, Landmark, Zap, Target } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';

const Delhi = () => {
  const comprehensiveServices = [
    'Search Engine Optimization (SEO) Delhi',
    'Local SEO Services Delhi NCR',
    'International SEO Delhi',
    'E-commerce SEO Delhi',
    'Technical SEO Audit Delhi',
    'Enterprise SEO Solutions Delhi',
    'Google Ads Management Delhi',
    'Pay-Per-Click (PPC) Advertising Delhi',
    'Facebook Advertising Delhi',
    'Instagram Marketing Delhi',
    'LinkedIn Ads Management Delhi',
    'YouTube Marketing Services Delhi',
    'Social Media Marketing Delhi',
    'Social Media Management Delhi',
    'Content Marketing Strategy Delhi',
    'Content Creation Services Delhi',
    'AI Content Generation Delhi',
    'Copywriting Services Delhi',
    'Blog Writing Services Delhi',
    'Social Media Automation Delhi',
    'Marketing Automation Delhi',
    'Business Process Automation Delhi',
    'CRM Automation Delhi',
    'Email Marketing Automation Delhi',
    'Lead Generation Services Delhi',
    'Lead Nurturing Campaigns Delhi',
    'Conversion Rate Optimization Delhi',
    'Website Development Delhi',
    'Web Design Services Delhi',
    'E-commerce Development Delhi',
    'Mobile App Development Delhi',
    'Landing Page Optimization Delhi',
    'Email Marketing Delhi',
    'Newsletter Marketing Delhi',
    'Influencer Marketing Delhi',
    'Video Marketing Services Delhi',
    'Video Production Delhi',
    'Animation Services Delhi',
    'AI Chatbot Development Delhi',
    'Customer Support Automation Delhi',
    'CRM Solutions Delhi',
    'Sales Funnel Automation Delhi',
    'Marketing Analytics Delhi',
    'Digital Strategy Consulting Delhi',
    'Brand Strategy Delhi',
    'Online Reputation Management Delhi',
    'Crisis Management Delhi',
    'Public Relations (PR) Delhi',
    'Media Planning Delhi',
    'Programmatic Advertising Delhi'
  ];

  const delhiDistricts = [
    'Central Delhi Digital Marketing',
    'North Delhi SEO Services',
    'South Delhi Marketing Agency',
    'East Delhi Digital Solutions',
    'West Delhi SEO Company',
    'New Delhi Marketing Services',
    'North East Delhi Digital Marketing',
    'North West Delhi SEO Services',
    'South East Delhi Marketing',
    'South West Delhi Digital Agency',
    'Shahdara Digital Marketing'
  ];

  const majorAreas = [
    'Connaught Place Digital Marketing',
    'Karol Bagh SEO Services',
    'Lajpat Nagar Marketing Agency',
    'Nehru Place IT Solutions',
    'Rajouri Garden Digital Marketing',
    'Khan Market SEO Services',
    'Sarojini Nagar Marketing',
    'Chandni Chowk Digital Solutions',
    'Dwarka Digital Marketing',
    'Aerocity Business Solutions',
    'Okhla Industrial Area Marketing',
    'Cyber Hub Gurgaon Services'
  ];

  const industries = [
    'Government Sector Digital Marketing Delhi',
    'IT Services & Software Companies Delhi',
    'Financial Services & Banking Delhi',
    'Healthcare & Medical Services Delhi',
    'Education & E-learning Delhi',
    'Real Estate & Property Delhi',
    'Retail & E-commerce Delhi',
    'Hospitality & Tourism Delhi',
    'Manufacturing & Industrial Delhi',
    'Consulting Services Delhi',
    'Legal Services Delhi',
    'Media & Entertainment Delhi',
    'Automotive Industry Delhi',
    'Fashion & Lifestyle Delhi',
    'Food & Beverage Delhi',
    'Logistics & Transportation Delhi'
  ];

  const serviceCategories = [
    {
      category: 'Search Engine Optimization (SEO) Delhi',
      services: ['Local SEO Delhi NCR', 'International SEO', 'E-commerce SEO', 'Technical SEO Audit', 'Enterprise SEO Solutions'],
      description: 'Comprehensive SEO services for Delhi businesses to dominate Google search results and drive organic traffic growth',
      icon: TrendingUp
    },
    {
      category: 'Paid Digital Advertising Delhi',
      services: ['Google Ads Management', 'Facebook Advertising', 'LinkedIn Ads', 'YouTube Marketing', 'PPC Campaigns'],
      description: 'Strategic paid advertising campaigns across all platforms to maximize ROI and accelerate business growth in Delhi market',
      icon: Target
    },
    {
      category: 'AI-Powered Marketing Automation Delhi',
      services: ['Marketing Automation', 'AI Content Generation', 'Social Media Automation', 'CRM Automation', 'Lead Nurturing'],
      description: 'Advanced AI and automation solutions to streamline marketing processes and enhance customer engagement',
      icon: Zap
    },
    {
      category: 'Digital Strategy & Consulting Delhi',
      services: ['Digital Strategy Consulting', 'Brand Strategy', 'Content Marketing', 'Social Media Management', 'Analytics'],
      description: 'Comprehensive digital strategy and consulting services to transform your Delhi business into a digital powerhouse',
      icon: Landmark
    }
  ];

  const stats = [
    {
      metric: '2000+',
      description: 'Delhi Businesses Transformed',
      detail: 'Across all sectors and districts'
    },
    {
      metric: '500%',
      description: 'Average Traffic Increase',
      detail: 'For Delhi NCR clients'
    },
    {
      metric: '₹50Cr+',
      description: 'Revenue Generated',
      detail: 'For Delhi businesses'
    },
    {
      metric: '95%',
      description: 'Client Retention Rate',
      detail: 'Long-term partnerships'
    }
  ];

  const achievements = [
    'Top Digital Marketing Agency in Delhi NCR',
    'Google Premier Partner Status',
    'Facebook Marketing Partner',
    'LinkedIn Marketing Partner',
    'HubSpot Certified Agency',
    'Shopify Plus Partner',
    'Microsoft Advertising Partner',
    'ISO 27001 Certified for Data Security'
  ];

  return (
    <div className="min-h-screen bg-slate-900 relative">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        {/* Hero Section */}
        <section className="pt-32 pb-20">
          <div className="container mx-auto px-6">
            <div className="max-w-5xl mx-auto text-center">
              <div className="inline-flex items-center space-x-2 bg-red-500/20 border border-red-500/30 rounded-full px-6 py-2 mb-8">
                <Crown className="w-4 h-4 text-red-400" />
                <span className="text-red-400 font-medium">Delhi Digital Marketing • National Capital Territory Excellence</span>
              </div>

              <h1 className="text-5xl md:text-7xl font-bold mb-6 leading-tight">
                #1 Digital Marketing Company in
                <span className="bg-gradient-to-r from-red-400 to-red-600 bg-clip-text text-transparent"> Delhi NCR</span>
              </h1>

              <p className="text-xl text-slate-300 mb-12 max-w-4xl mx-auto">
                Premier digital marketing agency in Delhi offering comprehensive 50+ services from advanced SEO to AI-powered automation. Serving 2000+ businesses across all Delhi districts - from government enterprises in Central Delhi to tech startups in Gurgaon. Expert solutions for every industry with proven ₹50Cr+ revenue generation track record.
              </p>

              <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
                <Button asChild size="lg" className="bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white text-lg px-8 py-4">
                  <Link to="/quote">Get Free Delhi Digital Marketing Audit</Link>
                </Button>
                <Button asChild size="lg" variant="outline" className="border-red-500 text-red-400 hover:bg-red-500 hover:text-white text-lg px-8 py-4">
                  <Link to="/contact">Call Delhi Office: +91-9999-XXXX-XX</Link>
                </Button>
              </div>

              {/* Achievements */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                {achievements.map((achievement, index) => (
                  <div key={index} className="flex items-center justify-center space-x-2 bg-slate-800/50 rounded-lg p-3">
                    <CheckCircle className="w-4 h-4 text-red-400 flex-shrink-0" />
                    <span className="text-slate-300">{achievement}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* Stats Section */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-6xl font-bold mb-6">
                Delhi Digital Marketing
                <span className="bg-gradient-to-r from-red-400 to-red-600 bg-clip-text text-transparent"> Success Metrics</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-4xl mx-auto mb-12">
                Measurable results from Delhi NCR businesses across all industries - from government sector enterprises to innovative startups in India's capital region.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-4 gap-8 mb-16">
              {stats.map((stat, index) => (
                <div key={index} className="text-center">
                  <div className="text-5xl font-bold text-red-400 mb-2">{stat.metric}</div>
                  <div className="text-white font-semibold mb-1">{stat.description}</div>
                  <div className="text-slate-400">{stat.detail}</div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Service Categories Section */}
        <section className="py-20">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-6xl font-bold mb-6">
                Complete Digital Marketing Services in
                <span className="bg-gradient-to-r from-red-400 to-red-600 bg-clip-text text-transparent"> Delhi NCR</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-4xl mx-auto">
                Comprehensive 50+ digital marketing services covering every aspect of online business growth - from traditional SEO to cutting-edge AI automation solutions.
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-20">
              {serviceCategories.map((category, index) => (
                <Card key={index} className="bg-slate-900/80 border-red-500/20 hover:border-red-500/40 transition-all duration-300">
                  <CardContent className="p-8">
                    <div className="w-12 h-12 bg-gradient-to-r from-red-500 to-red-600 rounded-lg flex items-center justify-center mb-6">
                      <category.icon className="w-6 h-6 text-white" />
                    </div>
                    <h3 className="text-2xl font-bold text-white mb-4">{category.category}</h3>
                    <p className="text-slate-300 mb-6">{category.description}</p>
                    <div className="grid grid-cols-1 gap-2">
                      {category.services.map((service, idx) => (
                        <div key={idx} className="flex items-center text-slate-400 text-sm">
                          <CheckCircle className="w-3 h-3 text-red-400 mr-2 flex-shrink-0" />
                          {service}
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* All Services Grid */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-6xl font-bold mb-6">
                All 50+ Digital Marketing Services
                <span className="bg-gradient-to-r from-red-400 to-red-600 bg-clip-text text-transparent"> Available in Delhi</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-4xl mx-auto">
                Complete range of digital marketing services for every business need in Delhi NCR - from startups to enterprises.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {comprehensiveServices.map((service, index) => (
                <Card key={index} className="bg-slate-900/80 border-red-500/20 hover:border-red-500/40 transition-all duration-300">
                  <CardContent className="p-4 text-center">
                    <h3 className="text-white font-semibold text-sm mb-2">{service}</h3>
                    <Button asChild size="sm" variant="outline" className="border-red-500 text-red-400 hover:bg-red-500 hover:text-white text-xs">
                      <Link to="/quote">Get Quote</Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Industries Section */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-6xl font-bold mb-6">
                Delhi Industries We
                <span className="bg-gradient-to-r from-red-400 to-red-600 bg-clip-text text-transparent"> Serve</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-4xl mx-auto">
                Specialized digital marketing strategies for Delhi's diverse business landscape across all major industries and sectors.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {industries.map((industry, index) => (
                <Card key={index} className="bg-slate-900/80 border-red-500/20 hover:border-red-500/40 transition-all duration-300">
                  <CardContent className="p-6 text-center">
                    <Star className="w-8 h-8 text-red-400 mx-auto mb-4" />
                    <h3 className="text-white font-semibold mb-2 text-sm">{industry}</h3>
                    <Button asChild size="sm" variant="outline" className="border-red-500 text-red-400 hover:bg-red-500 hover:text-white text-xs">
                      <Link to="/quote">Get Strategy</Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Delhi Districts Section */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-6xl font-bold mb-6">
                Delhi Districts We
                <span className="bg-gradient-to-r from-red-400 to-red-600 bg-clip-text text-transparent"> Cover</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-4xl mx-auto">
                Complete digital marketing services across all 11 Delhi districts with local expertise and market understanding.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {delhiDistricts.map((district, index) => (
                <Card key={index} className="bg-slate-900/80 border-red-500/20 hover:border-red-500/40 transition-all duration-300">
                  <CardContent className="p-6 text-center">
                    <MapPin className="w-8 h-8 text-red-400 mx-auto mb-4" />
                    <h3 className="text-white font-semibold mb-2 text-sm">{district}</h3>
                    <Button asChild size="sm" variant="outline" className="border-red-500 text-red-400 hover:bg-red-500 hover:text-white text-xs">
                      <Link to="/contact">Contact Us</Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Major Areas Section */}
        <section className="py-20">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-6xl font-bold mb-6">
                Major Delhi Areas We
                <span className="bg-gradient-to-r from-red-400 to-red-600 bg-clip-text text-transparent"> Serve</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-4xl mx-auto">
                Strategic digital marketing services across Delhi's major commercial hubs, business districts, and IT corridors.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {majorAreas.map((area, index) => (
                <Card key={index} className="bg-slate-900/80 border-red-500/20 hover:border-red-500/40 transition-all duration-300">
                  <CardContent className="p-6 text-center">
                    <Building className="w-8 h-8 text-red-400 mx-auto mb-4" />
                    <h3 className="text-white font-semibold mb-2 text-sm">{area}</h3>
                    <Button asChild size="sm" variant="outline" className="border-red-500 text-red-400 hover:bg-red-500 hover:text-white text-xs">
                      <Link to="/contact">Get Started</Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>



        {/* Why Choose Us Section */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-6xl font-bold mb-6">
                Why Choose GOD Digital Marketing in
                <span className="bg-gradient-to-r from-red-400 to-red-600 bg-clip-text text-transparent"> Delhi NCR?</span>
              </h2>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              <Card className="bg-slate-900/80 border-red-500/20">
                <CardContent className="p-8">
                  <TrendingUp className="w-12 h-12 text-red-400 mb-6" />
                  <h3 className="text-xl font-bold text-white mb-4">Complete Service Portfolio</h3>
                  <p className="text-slate-300">All 50+ digital marketing services available in Delhi - from basic SEO to advanced AI automation for every business need.</p>
                </CardContent>
              </Card>

              <Card className="bg-slate-900/80 border-red-500/20">
                <CardContent className="p-8">
                  <Users className="w-12 h-12 text-red-400 mb-6" />
                  <h3 className="text-xl font-bold text-white mb-4">Proven Delhi Results</h3>
                  <p className="text-slate-300">2000+ successful Delhi campaigns with ₹50Cr+ revenue generated across all industries and districts in the capital region.</p>
                </CardContent>
              </Card>

              <Card className="bg-slate-900/80 border-red-500/20">
                <CardContent className="p-8">
                  <CheckCircle className="w-12 h-12 text-red-400 mb-6" />
                  <h3 className="text-xl font-bold text-white mb-4">Delhi Market Leadership</h3>
                  <p className="text-slate-300">Premier partner status with Google, Facebook, LinkedIn, and HubSpot with deep understanding of Delhi's diverse business ecosystem.</p>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 bg-gradient-to-r from-red-600 to-red-500">
          <div className="container mx-auto px-6 text-center">
            <h2 className="text-4xl md:text-6xl font-bold mb-6">Ready to Dominate Delhi's Digital Market?</h2>
            <p className="text-xl mb-8 max-w-4xl mx-auto">
              Join 2000+ Delhi NCR businesses that trust GOD Digital Marketing for complete digital transformation. All 50+ services available across all Delhi districts - from government sector enterprises to innovative startups.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button asChild size="lg" className="bg-white text-red-600 hover:bg-slate-100 text-lg px-8 py-4">
                <Link to="/quote">Get Free Delhi Digital Marketing Audit</Link>
              </Button>
              <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-red-600 text-lg px-8 py-4">
                <Link to="/contact">Call Delhi Office: +91-9999-XXXX-XX</Link>
              </Button>
            </div>
          </div>
        </section>
      </main>
      
      <Footer />
    </div>
  );
};

export default Delhi;
