import React from 'react';
import { Link } from 'react-router-dom';
import { ArrowRight, Share2, TrendingUp, Users, CheckCircle, Building, Star, Crown, Target, Zap } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';
import Sidebar from '@/components/Sidebar';

const SocialMediaGuwahati = () => {
  const socialMediaServices = [
    'Social Media Marketing Guwahati',
    'Facebook Marketing Guwahati',
    'Instagram Marketing Guwahati',
    'LinkedIn Marketing Guwahati',
    'Twitter Marketing Guwahati',
    'YouTube Marketing Guwahati',
    'Social Media Management Guwahati',
    'Content Creation Guwahati',
    'Influencer Marketing Guwahati',
    'Social Media Advertising Guwahati',
    'Community Management Guwahati',
    'Social Media Strategy Guwahati',
    'Brand Building Guwahati',
    'Social Commerce Guwahati',
    'Video Marketing Guwahati',
    'Tea Industry Social Media Guwahati',
    'Tourism Social Media Guwahati',
    'Cultural Marketing Guwahati',
    'Northeast Business Social Media Guwahati',
    'Regional Language Social Media Guwahati'
  ];

  const industryFocus = [
    {
      industry: 'Tea Industry & Agriculture Social Media',
      description: 'Specialized social media strategies for Assam\'s tea industry and agricultural businesses',
      icon: Building,
      features: ['Tea Garden Marketing', 'Agricultural Product Promotion', 'Tea Export Marketing', 'Organic Tea Social Media']
    },
    {
      industry: 'Tourism & Cultural Social Media',
      description: 'Strategic social media marketing for Northeast tourism and cultural heritage promotion',
      icon: Star,
      features: ['Tourism Destination Marketing', 'Cultural Event Promotion', 'Heritage Site Marketing', 'Adventure Tourism Social Media']
    },
    {
      industry: 'Handloom & Handicraft Social Media',
      description: 'Comprehensive social media for Assam\'s handloom industry and traditional crafts',
      icon: Crown,
      features: ['Handloom Product Marketing', 'Artisan Promotion', 'Traditional Craft Marketing', 'Cultural Product Social Media']
    },
    {
      industry: 'Education & Government Social Media',
      description: 'Targeted social media for educational institutions and government organizations in Northeast',
      icon: Target,
      features: ['Educational Institution Marketing', 'Government Communication', 'Public Awareness Campaigns', 'Student Engagement']
    }
  ];

  const localSocialFeatures = [
    'Guwahati Local Community Building',
    'Assam State Social Media Coverage',
    'Northeast Regional Marketing',
    'Tea Industry Specialization',
    'Tourism Destination Promotion',
    'Cultural Heritage Marketing',
    'Regional Language Content',
    'Local Business Networking',
    'Traditional Craft Promotion',
    'Agricultural Product Marketing'
  ];

  const achievements = [
    '150+ Guwahati Organizations Served',
    '2,400% Average Engagement Growth',
    '₹30Cr+ Revenue Generated for Clients',
    '93% Campaign Success Rate',
    'Top Social Media Agency in Northeast',
    'Tea Industry Social Media Specialists'
  ];

  const platformExpertise = [
    {
      platform: 'Facebook Marketing',
      description: 'Comprehensive Facebook marketing for Guwahati businesses and organizations',
      metrics: ['17.2% Avg Engagement Rate', '2.5M+ Reach Generated', '88% Lead Conversion', '13% CTR Average']
    },
    {
      platform: 'Instagram Marketing',
      description: 'Visual storytelling and Instagram marketing for Northeast brands',
      metrics: ['20.8% Avg Engagement Rate', '2.1M+ Reach Generated', '25% Story Completion', '16% CTR Average']
    },
    {
      platform: 'YouTube Marketing',
      description: 'Video content marketing and YouTube optimization for regional content',
      metrics: ['28% Avg View Rate', '3.2M+ Video Views', '22% Subscriber Growth', '8% CTR Average']
    },
    {
      platform: 'LinkedIn Marketing',
      description: 'Professional networking and B2B marketing for Northeast businesses',
      metrics: ['14.5% Avg Engagement Rate', '950K+ Professional Reach', '32% Lead Quality', '9% CTR Average']
    }
  ];

  return (
    <div className="min-h-screen bg-slate-900 relative">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        <div className="container mx-auto px-6 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            <div className="lg:col-span-3">
              <section className="pt-24 pb-16">
                <div className="max-w-4xl">
                  <div className="inline-flex items-center space-x-2 bg-emerald-500/20 border border-emerald-500/30 rounded-full px-6 py-2 mb-8">
                    <Share2 className="w-4 h-4 text-emerald-400" />
                    <span className="text-emerald-400 font-medium">Social Media Services Guwahati • Northeast's Digital Communication Leaders</span>
                  </div>
                  
                  <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
                    #1 Social Media Company in
                    <span className="bg-gradient-to-r from-emerald-400 to-emerald-600 bg-clip-text text-transparent"> Guwahati</span>
                  </h1>
                  
                  <p className="text-xl text-slate-300 mb-8">
                    Premier social media marketing services in Guwahati offering comprehensive social media management and digital communication solutions for tea industry, tourism businesses, handloom enterprises, and educational institutions. Our Guwahati social media agency provides professional social media services including Facebook marketing, Instagram marketing, YouTube marketing, tea industry social media Guwahati, and tourism social media. Serving 150+ Guwahati organizations with proven ₹30Cr+ revenue generation and 2,400% average engagement growth through strategic social media marketing and digital communication excellence in Assam's largest city and Northeast India's gateway.
                  </p>
                  
                  <div className="flex flex-col sm:flex-row gap-4 mb-8">
                    <Button asChild size="lg" className="bg-gradient-to-r from-emerald-500 to-emerald-600 hover:from-emerald-600 hover:to-emerald-700 text-white text-lg px-8 py-4">
                      <Link to="/quote">Get Free Social Media Audit Guwahati</Link>
                    </Button>
                    <Button asChild size="lg" variant="outline" className="border-emerald-500 text-emerald-400 hover:bg-emerald-500 hover:text-white text-lg px-8 py-4">
                      <Link to="/contact">Call Guwahati Social Media Experts: +91-8708577598</Link>
                    </Button>
                  </div>

                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                    {achievements.map((achievement, index) => (
                      <div key={index} className="flex items-center justify-center space-x-2 bg-slate-800/50 rounded-lg p-3">
                        <CheckCircle className="w-4 h-4 text-emerald-400 flex-shrink-0" />
                        <span className="text-slate-300">{achievement}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </section>

              <section className="mb-16">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Guwahati Industry-Specific
                    <span className="bg-gradient-to-r from-emerald-400 to-emerald-600 bg-clip-text text-transparent"> Social Media Solutions</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Specialized social media strategies tailored for Guwahati's key industries and Northeast business sectors.
                  </p>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  {industryFocus.map((industry, index) => (
                    <Card key={index} className="bg-slate-900/80 border-emerald-500/20 hover:border-emerald-500/40 transition-all duration-300">
                      <CardContent className="p-8">
                        <div className="w-12 h-12 bg-gradient-to-r from-emerald-500 to-emerald-600 rounded-lg flex items-center justify-center mb-6">
                          <industry.icon className="w-6 h-6 text-white" />
                        </div>
                        <h3 className="text-xl font-bold text-white mb-4">{industry.industry}</h3>
                        <p className="text-slate-300 mb-6">{industry.description}</p>
                        <ul className="space-y-2">
                          {industry.features.map((feature, idx) => (
                            <li key={idx} className="flex items-center text-slate-400 text-sm">
                              <CheckCircle className="w-3 h-3 text-emerald-400 mr-2 flex-shrink-0" />
                              {feature}
                            </li>
                          ))}
                        </ul>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>

              <section className="mb-16 bg-slate-800/30 rounded-2xl p-8">
                <h3 className="text-2xl font-bold text-white mb-6 text-center">
                  Comprehensive Social Media Services in Guwahati
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  {socialMediaServices.map((service, index) => (
                    <div key={index} className="bg-slate-900/50 rounded-lg p-4 text-center">
                      <div className="text-emerald-400 font-medium text-sm">{service}</div>
                    </div>
                  ))}
                </div>
              </section>

              <section className="mb-16 bg-slate-800/30 rounded-2xl p-8">
                <h3 className="text-2xl font-bold text-white mb-6 text-center">
                  Platform Performance Metrics
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                  {platformExpertise.map((platform, index) => (
                    <div key={index} className="bg-slate-900/50 rounded-lg p-6">
                      <h4 className="text-emerald-400 font-semibold mb-3">{platform.platform}</h4>
                      <p className="text-slate-300 text-sm mb-4">{platform.description}</p>
                      <div className="space-y-2">
                        {platform.metrics.map((metric, idx) => (
                          <div key={idx} className="text-slate-400 text-xs">{metric}</div>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </section>

              <section className="mb-16 bg-slate-800/30 rounded-2xl p-8">
                <h3 className="text-2xl font-bold text-white mb-6 text-center">
                  Local Social Media Guwahati Specialization
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                  {localSocialFeatures.map((feature, index) => (
                    <div key={index} className="bg-slate-900/50 rounded-lg p-4 text-center">
                      <div className="text-emerald-400 font-medium text-sm">{feature}</div>
                    </div>
                  ))}
                </div>
              </section>

              <section className="mb-16 bg-slate-800/30 rounded-2xl p-8">
                <h3 className="text-2xl font-bold text-white mb-6 text-center">
                  Why Choose Our Guwahati Social Media Services?
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  <div className="text-center">
                    <div className="w-16 h-16 bg-gradient-to-r from-emerald-500 to-emerald-600 rounded-full flex items-center justify-center mx-auto mb-4">
                      <Building className="w-8 h-8 text-white" />
                    </div>
                    <h4 className="text-white font-semibold mb-2">Northeast Market Expertise</h4>
                    <p className="text-slate-400 text-sm">Deep understanding of Guwahati's business landscape, tea industry, and Northeast cultural nuances for targeted social media strategies.</p>
                  </div>
                  <div className="text-center">
                    <div className="w-16 h-16 bg-gradient-to-r from-emerald-500 to-emerald-600 rounded-full flex items-center justify-center mx-auto mb-4">
                      <TrendingUp className="w-8 h-8 text-white" />
                    </div>
                    <h4 className="text-white font-semibold mb-2">Proven Engagement</h4>
                    <p className="text-slate-400 text-sm">150+ successful social media campaigns in Guwahati with 2,400% average engagement growth and ₹30Cr+ revenue generation.</p>
                  </div>
                  <div className="text-center">
                    <div className="w-16 h-16 bg-gradient-to-r from-emerald-500 to-emerald-600 rounded-full flex items-center justify-center mx-auto mb-4">
                      <Users className="w-8 h-8 text-white" />
                    </div>
                    <h4 className="text-white font-semibold mb-2">Regional Industry Experience</h4>
                    <p className="text-slate-400 text-sm">Specialized expertise in tea industry, tourism, handloom, and education sectors prominent in Guwahati and Northeast market.</p>
                  </div>
                </div>
              </section>

              <section className="mb-16 bg-slate-800/30 rounded-2xl p-8">
                <h3 className="text-2xl font-bold text-white mb-6 text-center">
                  Complete Digital Marketing Solutions Guwahati
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  <Link to="/services/seo/guwahati" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-emerald-400 font-semibold mb-2 group-hover:text-emerald-300">SEO Services Guwahati</h4>
                    <p className="text-slate-400 text-sm">Search engine optimization for Guwahati businesses</p>
                  </Link>
                  <Link to="/services/ppc/guwahati" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-emerald-400 font-semibold mb-2 group-hover:text-emerald-300">Google Ads Guwahati</h4>
                    <p className="text-slate-400 text-sm">PPC advertising and Google Ads management for Guwahati</p>
                  </Link>
                  <Link to="/services/content/guwahati" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-emerald-400 font-semibold mb-2 group-hover:text-emerald-300">Content Marketing Guwahati</h4>
                    <p className="text-slate-400 text-sm">Content creation for Guwahati businesses and tourism</p>
                  </Link>
                  <Link to="/services/web-development/guwahati" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-emerald-400 font-semibold mb-2 group-hover:text-emerald-300">Web Development Guwahati</h4>
                    <p className="text-slate-400 text-sm">Professional website development for Guwahati businesses</p>
                  </Link>
                  <Link to="/services/email/guwahati" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-emerald-400 font-semibold mb-2 group-hover:text-emerald-300">Email Marketing Guwahati</h4>
                    <p className="text-slate-400 text-sm">Email marketing campaigns for Guwahati organizations</p>
                  </Link>
                  <Link to="/portfolio" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-emerald-400 font-semibold mb-2 group-hover:text-emerald-300">Guwahati Success Stories</h4>
                    <p className="text-slate-400 text-sm">See our proven results from Guwahati and Northeast clients</p>
                  </Link>
                </div>
              </section>

              <section className="bg-gradient-to-r from-emerald-600 to-emerald-500 rounded-2xl p-8 text-center">
                <h2 className="text-3xl md:text-5xl font-bold mb-6">Ready to Amplify Your Guwahati Presence?</h2>
                <p className="text-xl mb-8">
                  Join 150+ successful Guwahati organizations that trust GOD Digital Marketing for social media excellence. Proven strategies delivering 2,400% engagement growth and ₹30Cr+ revenue generation.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button asChild size="lg" className="bg-white text-emerald-600 hover:bg-slate-100 text-lg px-8 py-4">
                    <Link to="/quote">Get Free Social Media Audit</Link>
                  </Button>
                  <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-emerald-600 text-lg px-8 py-4">
                    <Link to="/contact">Call Guwahati Social Media Experts: +91-8708577598</Link>
                  </Button>
                </div>
              </section>
            </div>

            <div className="lg:col-span-1">
              <Sidebar currentService="social-media" />
            </div>
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
};

export default SocialMediaGuwahati;
