import React from 'react';
import { Link } from 'react-router-dom';

interface InternalLink {
  url: string;
  anchorText: string;
  title?: string;
  isLSI?: boolean;
  isEntity?: boolean;
  relevanceScore?: number;
}

interface AdvancedInternalLinkingProps {
  currentPage: string;
  primaryKeyword: string;
  secondaryKeywords: string[];
  lsiKeywords: string[];
  entities: string[];
  location?: string;
  industry?: string;
  serviceType?: string;
}

const AdvancedInternalLinking: React.FC<AdvancedInternalLinkingProps> = ({
  currentPage,
  primaryKeyword,
  secondaryKeywords,
  lsiKeywords,
  entities,
  location,
  industry,
  serviceType
}) => {

  // Comprehensive internal linking strategy based on big tech giants
  const generateInternalLinks = (): InternalLink[] => {
    const links: InternalLink[] = [];

    // Core Service Links (High Priority)
    const coreServices = [
      { url: '/services/seo', anchor: 'SEO services', lsi: 'search engine optimization' },
      { url: '/services/ppc', anchor: 'Google Ads management', lsi: 'PPC advertising' },
      { url: '/services/social-media', anchor: 'social media marketing', lsi: 'social media management' },
      { url: '/services/content', anchor: 'content marketing', lsi: 'content creation services' },
      { url: '/services/email', anchor: 'email marketing', lsi: 'email campaign management' },
      { url: '/services/web-development', anchor: 'web development', lsi: 'website development services' }
    ];

    // Add core service links with LSI variations
    coreServices.forEach(service => {
      if (!currentPage.includes(service.url)) {
        links.push({
          url: service.url,
          anchorText: service.anchor,
          title: `Professional ${service.anchor} services by GOD Digital Marketing`,
          isLSI: true,
          relevanceScore: 0.9
        });
        
        // Add LSI variation
        links.push({
          url: service.url,
          anchorText: service.lsi,
          title: `Expert ${service.lsi} solutions`,
          isLSI: true,
          relevanceScore: 0.8
        });
      }
    });

    // Location-based Internal Links
    if (location) {
      const locationServices = [
        { url: `/services/seo/${location.toLowerCase()}`, anchor: `SEO services ${location}` },
        { url: `/services/ppc/${location.toLowerCase()}`, anchor: `Google Ads ${location}` },
        { url: `/services/social-media/${location.toLowerCase()}`, anchor: `social media marketing ${location}` },
        { url: `/locations/${location.toLowerCase()}`, anchor: `digital marketing ${location}` }
      ];

      locationServices.forEach(locService => {
        if (!currentPage.includes(locService.url)) {
          links.push({
            url: locService.url,
            anchorText: locService.anchor,
            title: `Professional ${locService.anchor} by local experts`,
            isLSI: true,
            relevanceScore: 0.85
          });
        }
      });
    }

    // Industry-specific Internal Links
    if (industry) {
      const industryLinks = [
        { url: `/services/${industry.toLowerCase()}-seo`, anchor: `${industry} SEO services` },
        { url: `/industries/${industry.toLowerCase()}`, anchor: `${industry} digital marketing` },
        { url: `/services/${industry.toLowerCase()}-ppc`, anchor: `${industry} Google Ads` }
      ];

      industryLinks.forEach(indLink => {
        if (!currentPage.includes(indLink.url)) {
          links.push({
            url: indLink.url,
            anchorText: indLink.anchor,
            title: `Specialized ${indLink.anchor} solutions`,
            isLSI: true,
            relevanceScore: 0.8
          });
        }
      });
    }

    // Entity-based Links (High Authority)
    const entityLinks = [
      { url: '/about', anchor: 'Nitin Tyagi', entity: 'founder' },
      { url: '/about', anchor: 'GOD Digital Marketing', entity: 'company' },
      { url: '/services/ai-automation', anchor: 'AI-powered SEO', entity: 'technology' },
      { url: '/services/business-automation', anchor: 'business automation', entity: 'service' },
      { url: '/portfolio', anchor: 'digital marketing case studies', entity: 'proof' },
      { url: '/testimonials', anchor: 'client testimonials', entity: 'social-proof' }
    ];

    entityLinks.forEach(entityLink => {
      if (!currentPage.includes(entityLink.url)) {
        links.push({
          url: entityLink.url,
          anchorText: entityLink.anchor,
          title: `Learn more about ${entityLink.anchor}`,
          isEntity: true,
          relevanceScore: 0.9
        });
      }
    });

    // LSI Keyword-based Links
    lsiKeywords.forEach(lsiKeyword => {
      const lsiUrl = `/services/${lsiKeyword.toLowerCase().replace(/\s+/g, '-')}`;
      if (!currentPage.includes(lsiUrl)) {
        links.push({
          url: lsiUrl,
          anchorText: lsiKeyword,
          title: `Professional ${lsiKeyword} services`,
          isLSI: true,
          relevanceScore: 0.7
        });
      }
    });

    // Secondary Keyword Links
    secondaryKeywords.forEach(secKeyword => {
      const secUrl = `/services/${secKeyword.toLowerCase().replace(/\s+/g, '-')}`;
      if (!currentPage.includes(secUrl)) {
        links.push({
          url: secUrl,
          anchorText: secKeyword,
          title: `Expert ${secKeyword} solutions`,
          isLSI: true,
          relevanceScore: 0.75
        });
      }
    });

    // Sort by relevance score and return top links
    return links
      .sort((a, b) => (b.relevanceScore || 0) - (a.relevanceScore || 0))
      .slice(0, 15); // Limit to top 15 most relevant links
  };

  const internalLinks = generateInternalLinks();

  // Contextual Link Insertion Component
  const ContextualLinks: React.FC<{ content: string }> = ({ content }) => {
    let processedContent = content;

    // Insert internal links naturally within content
    internalLinks.forEach(link => {
      const regex = new RegExp(`\\b${link.anchorText}\\b`, 'gi');
      const replacement = `<Link to="${link.url}" title="${link.title}" className="text-blue-400 hover:text-blue-300 underline">${link.anchorText}</Link>`;
      
      // Only replace first occurrence to avoid over-optimization
      processedContent = processedContent.replace(regex, replacement);
    });

    return <div dangerouslySetInnerHTML={{ __html: processedContent }} />;
  };

  // Strategic Link Clusters for Sidebar/Footer
  const getLinkClusters = () => {
    return {
      services: internalLinks.filter(link => link.url.includes('/services/')).slice(0, 6),
      locations: internalLinks.filter(link => link.url.includes('/locations/')).slice(0, 4),
      industries: internalLinks.filter(link => link.url.includes('/industries/')).slice(0, 4),
      resources: internalLinks.filter(link => 
        link.url.includes('/portfolio') || 
        link.url.includes('/testimonials') || 
        link.url.includes('/about')
      ).slice(0, 3)
    };
  };

  // Related Services Component
  const RelatedServices: React.FC = () => {
    const clusters = getLinkClusters();
    
    return (
      <div className="bg-slate-800/30 rounded-2xl p-8 mt-16">
        <h3 className="text-2xl font-bold text-white mb-6 text-center">
          Related Digital Marketing Services
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {clusters.services.map((link, index) => (
            <Link
              key={index}
              to={link.url}
              title={link.title}
              className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group"
            >
              <h4 className="text-blue-400 font-semibold mb-2 group-hover:text-blue-300 capitalize">
                {link.anchorText}
              </h4>
              <p className="text-slate-400 text-sm">
                Professional {link.anchorText} solutions by GOD Digital Marketing
              </p>
            </Link>
          ))}
        </div>
      </div>
    );
  };

  // Breadcrumb Navigation with Internal Links
  const BreadcrumbNavigation: React.FC<{ breadcrumbs: Array<{name: string, url: string}> }> = ({ breadcrumbs }) => {
    return (
      <nav className="flex items-center space-x-2 text-sm text-slate-400 mb-8">
        <Link to="/" className="hover:text-white transition-colors">Home</Link>
        {breadcrumbs.map((crumb, index) => (
          <React.Fragment key={index}>
            <span>/</span>
            <Link 
              to={crumb.url} 
              className="hover:text-white transition-colors capitalize"
              title={`Navigate to ${crumb.name}`}
            >
              {crumb.name}
            </Link>
          </React.Fragment>
        ))}
      </nav>
    );
  };

  // In-Content Link Suggestions
  const InContentLinks: React.FC = () => {
    const topLinks = internalLinks.slice(0, 8);
    
    return (
      <div className="bg-slate-800/20 border-l-4 border-blue-500 p-6 my-8">
        <h4 className="text-lg font-semibold text-white mb-4">
          Explore Related Services
        </h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          {topLinks.map((link, index) => (
            <Link
              key={index}
              to={link.url}
              title={link.title}
              className="text-blue-400 hover:text-blue-300 text-sm flex items-center space-x-2 transition-colors"
            >
              <span>→</span>
              <span className="capitalize">{link.anchorText}</span>
            </Link>
          ))}
        </div>
      </div>
    );
  };

  return {
    ContextualLinks,
    RelatedServices,
    BreadcrumbNavigation,
    InContentLinks,
    internalLinks,
    getLinkClusters
  };
};

export default AdvancedInternalLinking;
