import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { ShoppingCart, Heart, Briefcase, Car, Dumbbell, Flower, Gift, Sparkles, Building, Globe, Users, TrendingUp, ArrowRight, CheckCircle } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';

const IndustryShowcase = () => {
  const industries = [
    {
      id: 'ecommerce',
      name: 'E-commerce & Retail',
      icon: ShoppingCart,
      description: 'Dominate online marketplaces with conversion-focused SEO strategies',
      clients: '2,800+',
      avgGrowth: '1,850%',
      color: 'from-blue-500 to-blue-600',
      examples: [
        { name: 'EzOutdoors', growth: '2,400%', industry: 'Outdoor Equipment' },
        { name: 'Camping Gear Store', growth: '1,800%', industry: 'Camping Equipment' },
        { name: 'Fashion Boutique', growth: '1,200%', industry: 'Fashion Retail' }
      ],
      services: [
        'Product Page Optimization',
        'E-commerce Technical SEO',
        'Shopping Ads Management',
        'Conversion Rate Optimization',
        'Marketplace SEO',
        'International E-commerce SEO'
      ],
      caseStudy: {
        client: 'EzOutdoors',
        challenge: 'New e-commerce store with zero organic visibility',
        result: '2,400% revenue growth in 6 months',
        screenshot: '/api/placeholder/400/250'
      }
    },
    {
      id: 'healthcare',
      name: 'Healthcare & Medical',
      icon: Heart,
      description: 'Build medical authority and attract patients with compliant SEO',
      clients: '1,200+',
      avgGrowth: '1,420%',
      color: 'from-red-500 to-red-600',
      examples: [
        { name: 'FH Wellness', growth: '2,100%', industry: 'Corporate Wellness' },
        { name: 'Golden Rose Med Spa', growth: '1,600%', industry: 'Medical Aesthetics' },
        { name: 'Dental Clinic Dubai', growth: '980%', industry: 'Dental Services' }
      ],
      services: [
        'Medical SEO Compliance',
        'Patient Review Management',
        'Medical Content Marketing',
        'Local Medical SEO',
        'Telemedicine SEO',
        'Healthcare PPC Management'
      ],
      caseStudy: {
        client: 'Golden Rose Med Spa',
        challenge: 'Luxury med spa needed high-end client acquisition',
        result: '520% increase in treatment bookings',
        screenshot: '/api/placeholder/400/250'
      }
    },
    {
      id: 'b2b',
      name: 'B2B Services',
      icon: Briefcase,
      description: 'Generate qualified leads with industry-specific B2B strategies',
      clients: '3,500+',
      avgGrowth: '980%',
      color: 'from-purple-500 to-purple-600',
      examples: [
        { name: 'Smart Whip UAE', growth: '1,800%', industry: 'Culinary Equipment' },
        { name: 'Industrial Supplies Co', growth: '1,200%', industry: 'Manufacturing' },
        { name: 'Tech Solutions Ltd', growth: '850%', industry: 'IT Services' }
      ],
      services: [
        'B2B Lead Generation SEO',
        'LinkedIn Marketing',
        'Industry-Specific Content',
        'Account-Based Marketing',
        'B2B PPC Campaigns',
        'Sales Funnel Optimization'
      ],
      caseStudy: {
        client: 'Smart Whip UAE',
        challenge: 'B2B company struggling to reach professional chefs',
        result: '280% increase in qualified B2B leads',
        screenshot: '/api/placeholder/400/250'
      }
    },
    {
      id: 'local',
      name: 'Local Business',
      icon: Building,
      description: 'Dominate local search results and attract nearby customers',
      clients: '4,200+',
      avgGrowth: '1,200%',
      color: 'from-green-500 to-green-600',
      examples: [
        { name: 'VR Dance World', growth: '1,200%', industry: 'Dance Academy' },
        { name: 'Local Restaurant Chain', growth: '980%', industry: 'Food & Beverage' },
        { name: 'Fitness Studio Dubai', growth: '1,500%', industry: 'Fitness & Wellness' }
      ],
      services: [
        'Google My Business Optimization',
        'Local Citation Building',
        'Review Management',
        'Local Content Marketing',
        'Geo-Targeted PPC',
        'Multi-Location SEO'
      ],
      caseStudy: {
        client: 'VR Dance World',
        challenge: 'Local dance academy competing with established studios',
        result: '450% increase in class bookings',
        screenshot: '/api/placeholder/400/250'
      }
    },
    {
      id: 'travel',
      name: 'Travel & Tourism',
      icon: Car,
      description: 'Attract travelers with seasonal and destination-focused SEO',
      clients: '850+',
      avgGrowth: '2,100%',
      color: 'from-cyan-500 to-cyan-600',
      examples: [
        { name: 'Balkland Tours', growth: '3,200%', industry: 'Tour Operator' },
        { name: 'Desert Safari Dubai', growth: '1,800%', industry: 'Adventure Tourism' },
        { name: 'Luxury Hotel Group', growth: '1,400%', industry: 'Hospitality' }
      ],
      services: [
        'Destination SEO',
        'Seasonal Campaign Management',
        'Travel Content Marketing',
        'Booking Platform Optimization',
        'International Tourism SEO',
        'Travel PPC Management'
      ],
      caseStudy: {
        client: 'Balkland Tours',
        challenge: 'Promote Balkan tours to international audience',
        result: '420% increase in advance bookings',
        screenshot: '/api/placeholder/400/250'
      }
    },
    {
      id: 'entertainment',
      name: 'Entertainment & Events',
      icon: Sparkles,
      description: 'Build buzz and drive attendance with entertainment-focused marketing',
      clients: '650+',
      avgGrowth: '1,680%',
      color: 'from-pink-500 to-pink-600',
      examples: [
        { name: 'Event Management Co', growth: '2,200%', industry: 'Event Planning' },
        { name: 'Music Academy Dubai', growth: '1,500%', industry: 'Music Education' },
        { name: 'Entertainment Venue', growth: '1,200%', industry: 'Nightlife' }
      ],
      services: [
        'Event Promotion SEO',
        'Social Media Marketing',
        'Influencer Partnerships',
        'Ticket Sales Optimization',
        'Entertainment Content Creation',
        'Community Building'
      ],
      caseStudy: {
        client: 'Entertainment Venue',
        challenge: 'Increase event attendance and venue bookings',
        result: '300% increase in event bookings',
        screenshot: '/api/placeholder/400/250'
      }
    }
  ];

  return (
    <section className="py-16 bg-slate-900/50">
      <div className="container mx-auto px-6">
        {/* Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center space-x-2 bg-indigo-500/20 border border-indigo-500/30 rounded-full px-6 py-2 mb-8">
            <Globe className="w-4 h-4 text-indigo-400" />
            <span className="text-indigo-400 font-medium">Industry Expertise • Proven Results</span>
          </div>
          
          <h2 className="text-3xl md:text-5xl font-bold mb-6">
            Industries We
            <span className="bg-gradient-to-r from-indigo-400 to-indigo-600 bg-clip-text text-transparent"> Dominate</span>
          </h2>
          
          <p className="text-xl text-slate-300 max-w-3xl mx-auto">
            From e-commerce to healthcare, we've mastered the unique challenges of every industry. 
            See how our specialized strategies deliver exceptional results.
          </p>
        </div>

        {/* Industries Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-8 mb-16">
          {industries.map((industry) => (
            <Card key={industry.id} className="bg-slate-900/80 border-slate-700/50 hover:border-indigo-500/40 transition-all duration-300 group overflow-hidden">
              <CardContent className="p-0">
                {/* Header */}
                <div className={`bg-gradient-to-r ${industry.color} p-6 text-white`}>
                  <div className="flex items-center space-x-3 mb-4">
                    <div className="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center">
                      <industry.icon className="w-6 h-6" />
                    </div>
                    <div>
                      <h3 className="text-xl font-bold">{industry.name}</h3>
                      <p className="text-white/80 text-sm">{industry.clients} clients served</p>
                    </div>
                  </div>
                  <p className="text-white/90">{industry.description}</p>
                </div>

                {/* Content */}
                <div className="p-6">
                  {/* Stats */}
                  <div className="grid grid-cols-2 gap-4 mb-6">
                    <div className="text-center bg-slate-800/50 rounded-lg p-3">
                      <div className="text-2xl font-bold text-indigo-400">{industry.clients}</div>
                      <div className="text-slate-400 text-sm">Clients Served</div>
                    </div>
                    <div className="text-center bg-slate-800/50 rounded-lg p-3">
                      <div className="text-2xl font-bold text-green-400">{industry.avgGrowth}</div>
                      <div className="text-slate-400 text-sm">Avg Growth</div>
                    </div>
                  </div>

                  {/* Case Study Preview */}
                  <div className="bg-slate-800/30 rounded-lg p-4 mb-6">
                    <h4 className="text-white font-semibold mb-2">Featured Success:</h4>
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="text-indigo-400 font-semibold">{industry.caseStudy.client}</div>
                        <div className="text-slate-400 text-sm">{industry.caseStudy.result}</div>
                      </div>
                      <div className="text-2xl font-bold text-green-400">
                        {industry.examples[0].growth}
                      </div>
                    </div>
                  </div>

                  {/* Services */}
                  <div className="mb-6">
                    <h4 className="text-white font-semibold mb-3">Our Services:</h4>
                    <div className="grid grid-cols-1 gap-2">
                      {industry.services.slice(0, 4).map((service, index) => (
                        <div key={index} className="flex items-center text-slate-300 text-sm">
                          <CheckCircle className="w-3 h-3 text-indigo-400 mr-2 flex-shrink-0" />
                          {service}
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* CTA */}
                  <Button asChild className={`w-full bg-gradient-to-r ${industry.color} hover:opacity-90 text-white`}>
                    <Link to="/quote">
                      Get Industry-Specific Strategy
                      <ArrowRight className="w-4 h-4 ml-2" />
                    </Link>
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Bottom CTA */}
        <div className="text-center">
          <h3 className="text-2xl font-bold text-white mb-4">Don't See Your Industry?</h3>
          <p className="text-slate-300 mb-6">
            We've worked with businesses across 50+ industries. Let's discuss your specific needs.
          </p>
          <Button asChild size="lg" className="bg-gradient-to-r from-indigo-500 to-indigo-600 hover:from-indigo-600 hover:to-indigo-700 text-white text-lg px-8 py-4">
            <Link to="/contact">Discuss Your Industry Needs</Link>
          </Button>
        </div>
      </div>
    </section>
  );
};

export default IndustryShowcase;
