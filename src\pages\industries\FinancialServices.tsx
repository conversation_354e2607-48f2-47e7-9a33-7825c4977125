import React from 'react';
import { Link } from 'react-router-dom';
import { ArrowRight, DollarSign, TrendingUp, Users, CheckCircle, Shield, Star, CreditCard } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';

const FinancialServices = () => {
  const services = [
    'Financial Services SEO',
    'Banking Digital Marketing',
    'Insurance Lead Generation',
    'Investment Firm Marketing',
    'Fintech App Promotion',
    'Loan Company Marketing',
    'Financial Advisor SEO',
    'Wealth Management Marketing'
  ];

  const financialTypes = [
    'Banks & Credit Unions',
    'Insurance Companies',
    'Investment Firms',
    'Fintech Startups',
    'Loan & Mortgage Companies',
    'Financial Advisors',
    'Wealth Management',
    'Cryptocurrency Services'
  ];

  const challenges = [
    {
      challenge: 'Regulatory Compliance',
      solution: 'Compliant marketing strategies that adhere to financial regulations and industry standards',
      icon: Shield
    },
    {
      challenge: 'Trust Building',
      solution: 'Build credibility through testimonials, certifications, and transparent communication',
      icon: Star
    },
    {
      challenge: 'Lead Quality',
      solution: 'Generate high-value leads with advanced targeting and qualification processes',
      icon: CreditCard
    }
  ];

  const stats = [
    {
      metric: '500%',
      description: 'Increase in Qualified Leads',
      detail: 'For financial clients'
    },
    {
      metric: '₹10Cr+',
      description: 'Financial Services Revenue',
      detail: 'Generated through marketing'
    },
    {
      metric: '150+',
      description: 'Financial Companies',
      detail: 'Successfully marketed'
    }
  ];

  return (
    <div className="min-h-screen bg-slate-900 relative">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        {/* Hero Section */}
        <section className="pt-32 pb-20">
          <div className="container mx-auto px-6">
            <div className="max-w-4xl mx-auto text-center">
              <div className="inline-flex items-center space-x-2 bg-green-500/20 border border-green-500/30 rounded-full px-6 py-2 mb-8">
                <DollarSign className="w-4 h-4 text-green-400" />
                <span className="text-green-400 font-medium">Financial Services Marketing • Compliant Lead Generation</span>
              </div>
              
              <h1 className="text-5xl md:text-6xl font-bold mb-6 leading-tight">
                Financial Services Marketing That
                <span className="bg-gradient-to-r from-green-400 to-green-600 bg-clip-text text-transparent"> Generates Quality Leads</span>
              </h1>
              
              <p className="text-xl text-slate-300 mb-12 max-w-3xl mx-auto">
                Specialized digital marketing for financial services, banks, insurance companies, and fintech firms. Generate qualified leads, build trust, and grow your financial business with compliant marketing strategies that deliver results.
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button asChild size="lg" className="bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white text-lg px-8 py-4">
                  <Link to="/quote">Get Financial Marketing Strategy</Link>
                </Button>
                <Button asChild size="lg" variant="outline" className="border-green-500 text-green-400 hover:bg-green-500 hover:text-white text-lg px-8 py-4">
                  <Link to="/case-studies">View Financial Results</Link>
                </Button>
              </div>
            </div>
          </div>
        </section>

        {/* Stats Section */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Financial Services Marketing
                <span className="bg-gradient-to-r from-green-400 to-green-600 bg-clip-text text-transparent"> Success Stories</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto mb-12">
                Real results from financial services companies across India - from traditional banks to innovative fintech startups.
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
              {stats.map((stat, index) => (
                <div key={index} className="text-center">
                  <div className="text-5xl font-bold text-green-400 mb-2">{stat.metric}</div>
                  <div className="text-white font-semibold mb-1">{stat.description}</div>
                  <div className="text-slate-400">{stat.detail}</div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Challenges & Solutions */}
        <section className="py-20">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Financial Marketing
                <span className="bg-gradient-to-r from-green-400 to-green-600 bg-clip-text text-transparent"> Challenges We Solve</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto">
                Understanding the unique challenges of financial services marketing and providing compliant solutions.
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              {challenges.map((item, index) => (
                <Card key={index} className="bg-slate-900/80 border-green-500/20 hover:border-green-500/40 transition-all duration-300">
                  <CardContent className="p-8">
                    <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-green-600 rounded-lg flex items-center justify-center mb-6">
                      <item.icon className="w-6 h-6 text-white" />
                    </div>
                    <h3 className="text-xl font-bold text-white mb-4">Challenge: {item.challenge}</h3>
                    <p className="text-slate-300">{item.solution}</p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Services Section */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Financial Services Digital Marketing
                <span className="bg-gradient-to-r from-green-400 to-green-600 bg-clip-text text-transparent"> Services</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto">
                Comprehensive digital marketing solutions designed specifically for financial services companies.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {services.map((service, index) => (
                <Card key={index} className="bg-slate-900/80 border-green-500/20 hover:border-green-500/40 transition-all duration-300">
                  <CardContent className="p-6 text-center">
                    <DollarSign className="w-8 h-8 text-green-400 mx-auto mb-4" />
                    <h3 className="text-white font-semibold mb-2">{service}</h3>
                    <Button asChild size="sm" variant="outline" className="border-green-500 text-green-400 hover:bg-green-500 hover:text-white">
                      <Link to="/quote">Learn More</Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Financial Types Section */}
        <section className="py-20">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Financial Services We
                <span className="bg-gradient-to-r from-green-400 to-green-600 bg-clip-text text-transparent"> Serve</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto">
                Specialized marketing strategies for different types of financial services and institutions.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {financialTypes.map((type, index) => (
                <Card key={index} className="bg-slate-900/80 border-green-500/20 hover:border-green-500/40 transition-all duration-300">
                  <CardContent className="p-6 text-center">
                    <Star className="w-8 h-8 text-green-400 mx-auto mb-4" />
                    <h3 className="text-white font-semibold mb-2">{type}</h3>
                    <Button asChild size="sm" variant="outline" className="border-green-500 text-green-400 hover:bg-green-500 hover:text-white">
                      <Link to="/quote">Get Strategy</Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Why Choose Us Section */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Why Financial Services Choose
                <span className="bg-gradient-to-r from-green-400 to-green-600 bg-clip-text text-transparent"> GOD Digital Marketing</span>
              </h2>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              <Card className="bg-slate-900/80 border-green-500/20">
                <CardContent className="p-8">
                  <TrendingUp className="w-12 h-12 text-green-400 mb-6" />
                  <h3 className="text-xl font-bold text-white mb-4">Financial Industry Expertise</h3>
                  <p className="text-slate-300">Deep understanding of financial services regulations, compliance requirements, and industry best practices.</p>
                </CardContent>
              </Card>

              <Card className="bg-slate-900/80 border-green-500/20">
                <CardContent className="p-8">
                  <Users className="w-12 h-12 text-green-400 mb-6" />
                  <h3 className="text-xl font-bold text-white mb-4">Quality Lead Generation</h3>
                  <p className="text-slate-300">Specialized lead generation systems that attract high-value prospects and qualified financial service leads.</p>
                </CardContent>
              </Card>

              <Card className="bg-slate-900/80 border-green-500/20">
                <CardContent className="p-8">
                  <CheckCircle className="w-12 h-12 text-green-400 mb-6" />
                  <h3 className="text-xl font-bold text-white mb-4">Regulatory Compliance</h3>
                  <p className="text-slate-300">All marketing activities are fully compliant with financial regulations and industry standards.</p>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 bg-gradient-to-r from-green-600 to-green-500">
          <div className="container mx-auto px-6 text-center">
            <h2 className="text-4xl md:text-5xl font-bold mb-6">Ready to Generate More Financial Leads?</h2>
            <p className="text-xl mb-8 max-w-3xl mx-auto">
              Join 150+ financial services companies who trust GOD Digital Marketing to generate qualified leads and grow their business.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button asChild size="lg" className="bg-white text-green-600 hover:bg-slate-100 text-lg px-8 py-4">
                <Link to="/quote">Get Financial Marketing Strategy</Link>
              </Button>
              <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-green-600 text-lg px-8 py-4">
                <Link to="/contact">Book Financial Consultation</Link>
              </Button>
            </div>
          </div>
        </section>
      </main>
      
      <Footer />
    </div>
  );
};

export default FinancialServices;
