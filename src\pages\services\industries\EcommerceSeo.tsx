import React from 'react';
import { Link } from 'react-router-dom';
import { ShoppingCart, Search, TrendingUp, Users, CheckCircle, Building, Star, Crown, Target, Zap, ArrowRight } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';
import Sidebar from '@/components/Sidebar';

const EcommerceSeo = () => {
  const stats = [
    {
      metric: '2,800+',
      description: 'E-commerce Stores Optimized',
      detail: 'Across all platforms'
    },
    {
      metric: '1,850%',
      description: 'Average Revenue Growth',
      detail: 'For e-commerce clients'
    },
    {
      metric: '₹180Cr+',
      description: 'E-commerce Revenue Generated',
      detail: 'Through SEO campaigns'
    },
    {
      metric: '95%',
      description: 'Product Page Rankings',
      detail: 'Top 10 positions achieved'
    }
  ];

  const achievements = [
    'Top E-commerce SEO Company in India',
    'Shopify SEO Specialists',
    'WooCommerce Optimization Experts',
    'Magento SEO Leaders',
    'Amazon SEO Champions',
    'E-commerce Conversion Optimization Pioneers'
  ];

  const ecommerceSpecializations = [
    {
      type: 'Product Page SEO',
      description: 'Optimize individual product pages for maximum visibility and conversions',
      icon: Building,
      features: ['Product Title Optimization', 'Meta Description Enhancement', 'Product Schema Markup', 'Image SEO Optimization', 'User Review Integration', 'Related Product Linking']
    },
    {
      type: 'Category Page Optimization',
      description: 'Structure and optimize category pages for better search rankings',
      icon: Star,
      features: ['Category Hierarchy Optimization', 'Faceted Navigation SEO', 'Category Description Enhancement', 'Internal Linking Strategy', 'Breadcrumb Optimization', 'Filter SEO Implementation']
    },
    {
      type: 'Technical E-commerce SEO',
      description: 'Advanced technical optimization for e-commerce platforms',
      icon: Crown,
      features: ['Site Speed Optimization', 'Mobile-First Indexing', 'Core Web Vitals Enhancement', 'Crawl Budget Optimization', 'Duplicate Content Resolution', 'URL Structure Optimization']
    },
    {
      type: 'E-commerce Content Strategy',
      description: 'Content marketing strategies specifically for online stores',
      icon: Target,
      features: ['Buying Guide Creation', 'Product Comparison Content', 'Blog Content Strategy', 'User-Generated Content', 'Video Content Optimization', 'FAQ Page Optimization']
    }
  ];

  const platforms = [
    { name: 'Shopify', clients: '850+', growth: '2,200%' },
    { name: 'WooCommerce', clients: '720+', growth: '1,900%' },
    { name: 'Magento', clients: '480+', growth: '1,650%' },
    { name: 'BigCommerce', clients: '320+', growth: '1,800%' },
    { name: 'Custom E-commerce', clients: '430+', growth: '2,100%' }
  ];

  const caseStudies = [
    {
      client: 'EzOutdoors',
      industry: 'Outdoor Equipment',
      challenge: 'New e-commerce store with zero organic visibility',
      result: '2,400% revenue growth in 6 months',
      metrics: ['45,000+ monthly visitors', '850+ keywords in top 10', '₹2.8Cr+ revenue generated']
    },
    {
      client: 'Fashion Boutique',
      industry: 'Fashion & Apparel',
      challenge: 'High competition in fashion e-commerce market',
      result: '1,200% organic traffic increase',
      metrics: ['25,000+ monthly visitors', '420+ keywords ranking', '₹1.5Cr+ revenue boost']
    },
    {
      client: 'Electronics Store',
      industry: 'Consumer Electronics',
      challenge: 'Competing with major e-commerce giants',
      result: '1,800% conversion rate improvement',
      metrics: ['35,000+ monthly visitors', '650+ product pages ranking', '₹3.2Cr+ sales increase']
    }
  ];

  return (
    <div className="min-h-screen bg-slate-900 relative">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        <div className="container mx-auto px-6 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            <div className="lg:col-span-3">
              <section className="pt-24 pb-16">
                <div className="max-w-4xl">
                  <div className="inline-flex items-center space-x-2 bg-blue-500/20 border border-blue-500/30 rounded-full px-6 py-2 mb-8">
                    <ShoppingCart className="w-4 h-4 text-blue-400" />
                    <span className="text-blue-400 font-medium">E-commerce SEO • Online Store Optimization Excellence</span>
                  </div>
                  
                  <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
                    #1 E-commerce SEO Company in
                    <span className="bg-gradient-to-r from-blue-400 to-blue-600 bg-clip-text text-transparent"> India</span>
                  </h1>
                  
                  <p className="text-xl text-slate-300 mb-8">
                    Premier e-commerce SEO services offering comprehensive search engine optimization solutions for online stores including Shopify SEO, WooCommerce optimization, Magento SEO, product page optimization, category page SEO, and e-commerce technical SEO. Our e-commerce SEO company provides professional SEO services with product optimization, conversion rate optimization, e-commerce content marketing, and online store growth strategies. Serving 2,800+ e-commerce businesses across all platforms with proven ₹180Cr+ revenue generation and 1,850% average growth for e-commerce clients through strategic search engine optimization and digital marketing excellence.
                  </p>
                  
                  <div className="flex flex-col sm:flex-row gap-4 mb-8">
                    <Button asChild size="lg" className="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white text-lg px-8 py-4">
                      <Link to="/quote">Get Free E-commerce SEO Audit</Link>
                    </Button>
                    <Button asChild size="lg" variant="outline" className="border-blue-500 text-blue-400 hover:bg-blue-500 hover:text-white text-lg px-8 py-4">
                      <Link to="/contact">Call E-commerce SEO Experts: +91-8708577598</Link>
                    </Button>
                  </div>

                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                    {achievements.map((achievement, index) => (
                      <div key={index} className="flex items-center justify-center space-x-2 bg-slate-800/50 rounded-lg p-3">
                        <CheckCircle className="w-4 h-4 text-blue-400 flex-shrink-0" />
                        <span className="text-slate-300">{achievement}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </section>

              <section className="mb-16 bg-slate-800/50 rounded-2xl p-8">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    E-commerce SEO
                    <span className="bg-gradient-to-r from-blue-400 to-blue-600 bg-clip-text text-transparent"> Success Metrics</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Measurable e-commerce SEO results from online stores across all industries and platforms.
                  </p>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
                  {stats.map((stat, index) => (
                    <div key={index} className="text-center">
                      <div className="text-4xl font-bold text-blue-400 mb-2">{stat.metric}</div>
                      <div className="text-white font-semibold mb-1">{stat.description}</div>
                      <div className="text-slate-400">{stat.detail}</div>
                    </div>
                  ))}
                </div>
              </section>

              <section className="mb-16">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    E-commerce SEO Strategies We
                    <span className="bg-gradient-to-r from-blue-400 to-blue-600 bg-clip-text text-transparent"> Master</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Specialized SEO strategies designed for e-commerce success across all platforms and industries.
                  </p>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  {ecommerceSpecializations.map((spec, index) => (
                    <Card key={index} className="bg-slate-900/80 border-blue-500/20 hover:border-blue-500/40 transition-all duration-300">
                      <CardContent className="p-8">
                        <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg flex items-center justify-center mb-6">
                          <spec.icon className="w-6 h-6 text-white" />
                        </div>
                        <h3 className="text-xl font-bold text-white mb-4">{spec.type}</h3>
                        <p className="text-slate-300 mb-6">{spec.description}</p>
                        <ul className="space-y-2">
                          {spec.features.map((feature, idx) => (
                            <li key={idx} className="flex items-center text-slate-400 text-sm">
                              <CheckCircle className="w-3 h-3 text-blue-400 mr-2 flex-shrink-0" />
                              {feature}
                            </li>
                          ))}
                        </ul>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>

              {/* Platform Expertise */}
              <section className="mb-16 bg-slate-800/30 rounded-2xl p-8">
                <h3 className="text-2xl font-bold text-white mb-6 text-center">
                  E-commerce Platform Expertise
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
                  {platforms.map((platform, index) => (
                    <div key={index} className="text-center bg-slate-900/50 rounded-lg p-4">
                      <h4 className="text-blue-400 font-semibold mb-2">{platform.name}</h4>
                      <div className="text-2xl font-bold text-white mb-1">{platform.clients}</div>
                      <div className="text-slate-400 text-sm mb-2">Stores Optimized</div>
                      <div className="text-green-400 font-semibold text-sm">{platform.growth} avg growth</div>
                    </div>
                  ))}
                </div>
              </section>

              {/* Case Studies */}
              <section className="mb-16">
                <h3 className="text-2xl font-bold text-white mb-8 text-center">
                  E-commerce Success Stories
                </h3>
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                  {caseStudies.map((study, index) => (
                    <Card key={index} className="bg-slate-900/60 border-blue-500/20 hover:border-blue-500/40 transition-all duration-300">
                      <CardContent className="p-6">
                        <h4 className="text-blue-400 font-semibold mb-2">{study.client}</h4>
                        <p className="text-slate-400 text-sm mb-3">{study.industry}</p>
                        <p className="text-slate-300 text-sm mb-4">{study.challenge}</p>
                        <div className="bg-slate-800/50 rounded p-3 mb-4">
                          <div className="text-green-400 font-semibold mb-2">{study.result}</div>
                          <ul className="space-y-1">
                            {study.metrics.map((metric, idx) => (
                              <li key={idx} className="text-slate-400 text-xs flex items-center">
                                <CheckCircle className="w-3 h-3 text-blue-400 mr-2 flex-shrink-0" />
                                {metric}
                              </li>
                            ))}
                          </ul>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>

              {/* Internal Linking Section */}
              <section className="mb-16 bg-slate-800/30 rounded-2xl p-8">
                <h3 className="text-2xl font-bold text-white mb-6 text-center">
                  Complete Digital Marketing Solutions for E-commerce
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  <Link to="/services/ppc" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-blue-400 font-semibold mb-2 group-hover:text-blue-300">E-commerce Google Ads</h4>
                    <p className="text-slate-400 text-sm">PPC advertising for online stores and product promotion</p>
                  </Link>
                  <Link to="/services/social-media" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-blue-400 font-semibold mb-2 group-hover:text-blue-300">E-commerce Social Media</h4>
                    <p className="text-slate-400 text-sm">Social media marketing for online store promotion</p>
                  </Link>
                  <Link to="/services/content" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-blue-400 font-semibold mb-2 group-hover:text-blue-300">E-commerce Content Marketing</h4>
                    <p className="text-slate-400 text-sm">Content creation for product promotion and SEO</p>
                  </Link>
                  <Link to="/services/email" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-blue-400 font-semibold mb-2 group-hover:text-blue-300">E-commerce Email Marketing</h4>
                    <p className="text-slate-400 text-sm">Email campaigns for customer retention and sales</p>
                  </Link>
                  <Link to="/services/web-development" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-blue-400 font-semibold mb-2 group-hover:text-blue-300">E-commerce Web Development</h4>
                    <p className="text-slate-400 text-sm">Custom e-commerce website development and optimization</p>
                  </Link>
                  <Link to="/portfolio" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-blue-400 font-semibold mb-2 group-hover:text-blue-300">E-commerce Success Stories</h4>
                    <p className="text-slate-400 text-sm">See proven results from our e-commerce clients</p>
                  </Link>
                </div>
              </section>

              <section className="bg-gradient-to-r from-blue-600 to-blue-500 rounded-2xl p-8 text-center">
                <h2 className="text-3xl md:text-5xl font-bold mb-6">Ready to Dominate E-commerce Search Results?</h2>
                <p className="text-xl mb-8">
                  Join 2,800+ e-commerce stores that trust GOD Digital Marketing for SEO success. Proven strategies that deliver 1,850% revenue growth and ₹180Cr+ sales generation.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button asChild size="lg" className="bg-white text-blue-600 hover:bg-slate-100 text-lg px-8 py-4">
                    <Link to="/quote">Get Free E-commerce SEO Audit</Link>
                  </Button>
                  <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-blue-600 text-lg px-8 py-4">
                    <Link to="/contact">Call E-commerce SEO Experts: +91-8708577598</Link>
                  </Button>
                </div>
              </section>
            </div>

            <div className="lg:col-span-1">
              <Sidebar currentService="seo" />
            </div>
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
};

export default EcommerceSeo;
