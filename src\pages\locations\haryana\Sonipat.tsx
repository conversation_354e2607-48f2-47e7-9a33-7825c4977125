import React from 'react';
import { Link } from 'react-router-dom';
import { ArrowRight, MapPin, TrendingUp, Users, CheckCircle, Building, Star, Factory } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';

const Sonipat = () => {
  const services = [
    'SEO Services Sonipat',
    'Google Ads Management Sonipat',
    'Social Media Marketing Sonipat',
    'Local SEO Sonipat',
    'E-commerce SEO Sonipat',
    'Content Marketing Sonipat',
    'Website Development Sonipat',
    'Digital Marketing Consulting Sonipat'
  ];

  const industries = [
    'Automobile Industry Sonipat',
    'Textile Manufacturing Sonipat',
    'Agriculture Sonipat',
    'Food Processing Sonipat',
    'Real Estate Sonipat',
    'Healthcare Sonipat',
    'Education Sonipat',
    'Retail & E-commerce Sonipat'
  ];

  const areas = [
    'Sector 14 Digital Marketing',
    'Model Town SEO Services',
    'Rai Digital Marketing',
    'Gohana SEO Services',
    'Kharkhoda Digital Marketing',
    'Mundlana SEO Services',
    'Kathura Digital Marketing',
    'Gannaur SEO Services'
  ];

  const stats = [
    {
      metric: '70+',
      description: 'Sonipat Businesses Served',
      detail: 'Across all sectors'
    },
    {
      metric: '350%',
      description: 'Average Traffic Increase',
      detail: 'For Sonipat clients'
    },
    {
      metric: '₹24L+',
      description: 'Revenue Generated',
      detail: 'For Sonipat businesses'
    }
  ];

  return (
    <div className="min-h-screen bg-slate-900 relative">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        {/* Hero Section */}
        <section className="pt-32 pb-20">
          <div className="container mx-auto px-6">
            <div className="max-w-4xl mx-auto text-center">
              <div className="inline-flex items-center space-x-2 bg-red-500/20 border border-red-500/30 rounded-full px-6 py-2 mb-8">
                <Factory className="w-4 h-4 text-red-400" />
                <span className="text-red-400 font-medium">Sonipat Digital Marketing • Industrial Gateway</span>
              </div>
              
              <h1 className="text-5xl md:text-6xl font-bold mb-6 leading-tight">
                #1 Digital Marketing Company in
                <span className="bg-gradient-to-r from-red-400 to-red-600 bg-clip-text text-transparent"> Sonipat</span>
              </h1>
              
              <p className="text-xl text-slate-300 mb-12 max-w-3xl mx-auto">
                Leading digital marketing agency in Sonipat helping businesses dominate online. From automobile manufacturers to textile industries, we've helped 70+ businesses grow with proven SEO, Google Ads, and digital marketing strategies across all Sonipat areas.
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button asChild size="lg" className="bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white text-lg px-8 py-4">
                  <Link to="/quote">Get Free Sonipat SEO Audit</Link>
                </Button>
                <Button asChild size="lg" variant="outline" className="border-red-500 text-red-400 hover:bg-red-500 hover:text-white text-lg px-8 py-4">
                  <Link to="/contact">Call Sonipat Office</Link>
                </Button>
              </div>
            </div>
          </div>
        </section>

        {/* Stats Section */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Sonipat Digital Marketing
                <span className="bg-gradient-to-r from-red-400 to-red-600 bg-clip-text text-transparent"> Success Stories</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto mb-12">
                Real results from Sonipat businesses across industries - from automobile manufacturers to agricultural enterprises in this industrial gateway.
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
              {stats.map((stat, index) => (
                <div key={index} className="text-center">
                  <div className="text-5xl font-bold text-red-400 mb-2">{stat.metric}</div>
                  <div className="text-white font-semibold mb-1">{stat.description}</div>
                  <div className="text-slate-400">{stat.detail}</div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Services Section */}
        <section className="py-20">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Digital Marketing Services in
                <span className="bg-gradient-to-r from-red-400 to-red-600 bg-clip-text text-transparent"> Sonipat</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto">
                Comprehensive digital marketing solutions for Sonipat businesses looking to dominate online in the Industrial Gateway.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {services.map((service, index) => (
                <Card key={index} className="bg-slate-900/80 border-red-500/20 hover:border-red-500/40 transition-all duration-300">
                  <CardContent className="p-6 text-center">
                    <Building className="w-8 h-8 text-red-400 mx-auto mb-4" />
                    <h3 className="text-white font-semibold mb-2">{service}</h3>
                    <Button asChild size="sm" variant="outline" className="border-red-500 text-red-400 hover:bg-red-500 hover:text-white">
                      <Link to="/quote">Learn More</Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Industries Section */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Sonipat Industries We
                <span className="bg-gradient-to-r from-red-400 to-red-600 bg-clip-text text-transparent"> Serve</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto">
                Specialized digital marketing strategies for Sonipat's automobile, textile, and manufacturing industries.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {industries.map((industry, index) => (
                <Card key={index} className="bg-slate-900/80 border-red-500/20 hover:border-red-500/40 transition-all duration-300">
                  <CardContent className="p-6 text-center">
                    <Star className="w-8 h-8 text-red-400 mx-auto mb-4" />
                    <h3 className="text-white font-semibold mb-2">{industry}</h3>
                    <Button asChild size="sm" variant="outline" className="border-red-500 text-red-400 hover:bg-red-500 hover:text-white">
                      <Link to="/quote">Get Strategy</Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Areas Section */}
        <section className="py-20">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Sonipat Areas We
                <span className="bg-gradient-to-r from-red-400 to-red-600 bg-clip-text text-transparent"> Cover</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto">
                Local digital marketing expertise across all major Sonipat areas including industrial and commercial zones.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {areas.map((area, index) => (
                <Card key={index} className="bg-slate-900/80 border-red-500/20 hover:border-red-500/40 transition-all duration-300">
                  <CardContent className="p-6 text-center">
                    <MapPin className="w-8 h-8 text-red-400 mx-auto mb-4" />
                    <h3 className="text-white font-semibold mb-2">{area}</h3>
                    <Button asChild size="sm" variant="outline" className="border-red-500 text-red-400 hover:bg-red-500 hover:text-white">
                      <Link to="/contact">Contact Us</Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Why Choose Us Section */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Why Choose GOD Digital Marketing in
                <span className="bg-gradient-to-r from-red-400 to-red-600 bg-clip-text text-transparent"> Sonipat?</span>
              </h2>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              <Card className="bg-slate-900/80 border-red-500/20">
                <CardContent className="p-8">
                  <TrendingUp className="w-12 h-12 text-red-400 mb-6" />
                  <h3 className="text-xl font-bold text-white mb-4">Industrial Expertise</h3>
                  <p className="text-slate-300">Deep understanding of Sonipat's automobile industry, textile manufacturing, and industrial business dynamics.</p>
                </CardContent>
              </Card>

              <Card className="bg-slate-900/80 border-red-500/20">
                <CardContent className="p-8">
                  <Users className="w-12 h-12 text-red-400 mb-6" />
                  <h3 className="text-xl font-bold text-white mb-4">Proven Sonipat Results</h3>
                  <p className="text-slate-300">70+ successful Sonipat campaigns with measurable ROI improvements and business growth across manufacturing sectors.</p>
                </CardContent>
              </Card>

              <Card className="bg-slate-900/80 border-red-500/20">
                <CardContent className="p-8">
                  <CheckCircle className="w-12 h-12 text-red-400 mb-6" />
                  <h3 className="text-xl font-bold text-white mb-4">Manufacturing Focus</h3>
                  <p className="text-slate-300">Specialized expertise in B2B marketing for manufacturing industries with focus on lead generation and business growth.</p>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 bg-gradient-to-r from-red-600 to-red-500">
          <div className="container mx-auto px-6 text-center">
            <h2 className="text-4xl md:text-5xl font-bold mb-6">Ready to Dominate Sonipat's Digital Market?</h2>
            <p className="text-xl mb-8 max-w-3xl mx-auto">
              Join 70+ Sonipat businesses that trust GOD Digital Marketing for their online growth. From automobile industry to textiles, we deliver results.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button asChild size="lg" className="bg-white text-red-600 hover:bg-slate-100 text-lg px-8 py-4">
                <Link to="/quote">Get Free Sonipat SEO Audit</Link>
              </Button>
              <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-red-600 text-lg px-8 py-4">
                <Link to="/contact">Call Sonipat Office Now</Link>
              </Button>
            </div>
          </div>
        </section>
      </main>
      
      <Footer />
    </div>
  );
};

export default Sonipat;
