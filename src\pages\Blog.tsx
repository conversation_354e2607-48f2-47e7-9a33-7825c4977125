
import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { Clock, User, ArrowRight, Search, Filter } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';

const Blog = () => {
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');

  const categories = ['All', 'SEO', 'PPC', 'Social Media', 'Content Marketing', 'Email Marketing', 'Web Design'];

  const blogPosts = [
    {
      title: '10 SEO Strategies That Will Transform Your Rankings in 2024',
      excerpt: 'Discover the latest SEO techniques that are driving massive organic growth for our clients this year. From technical optimizations to content strategies.',
      author: '<PERSON>',
      date: 'Dec 15, 2023',
      readTime: '5 min read',
      image: 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=400&h=250&fit=crop',
      category: 'SEO',
      featured: true
    },
    {
      title: 'The Ultimate Guide to PPC Campaign Optimization',
      excerpt: 'Learn how to maximize your ad spend ROI with advanced PPC optimization strategies and best practices that actually work.',
      author: 'Sarah Williams',
      date: 'Dec 12, 2023',
      readTime: '8 min read',
      image: 'https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=400&h=250&fit=crop',
      category: 'PPC'
    },
    {
      title: 'Social Media Marketing: Building Brand Authority in 2024',
      excerpt: 'Explore cutting-edge social media strategies that build authentic connections and drive conversions across all platforms.',
      author: 'Mike Chen',
      date: 'Dec 10, 2023',
      readTime: '6 min read',
      image: 'https://images.unsplash.com/photo-1611224923853-80b023f02d71?w=400&h=250&fit=crop',
      category: 'Social Media'
    },
    {
      title: 'Email Marketing Automation: From Setup to Success',
      excerpt: 'Master email automation workflows that nurture leads and drive consistent revenue growth for your business.',
      author: 'Emily Rodriguez',
      date: 'Dec 8, 2023',
      readTime: '7 min read',
      image: 'https://images.unsplash.com/photo-1596526131083-e8c633c948d2?w=400&h=250&fit=crop',
      category: 'Email Marketing'
    },
    {
      title: 'Content Marketing ROI: Measuring What Matters',
      excerpt: 'Learn how to track and optimize your content marketing efforts for maximum business impact and measurable results.',
      author: 'David Kim',
      date: 'Dec 5, 2023',
      readTime: '6 min read',
      image: 'https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d?w=400&h=250&fit=crop',
      category: 'Content Marketing'
    },
    {
      title: 'Web Design Trends That Actually Convert in 2024',
      excerpt: 'Discover the latest web design trends that not only look great but also drive conversions and improve user experience.',
      author: 'Lisa Park',
      date: 'Dec 3, 2023',
      readTime: '5 min read',
      image: 'https://images.unsplash.com/photo-1498050108023-c5249f4df085?w=400&h=250&fit=crop',
      category: 'Web Design'
    }
  ];

  const filteredPosts = blogPosts.filter(post => {
    const matchesCategory = selectedCategory === 'all' || post.category.toLowerCase() === selectedCategory.toLowerCase();
    const matchesSearch = post.title.toLowerCase().includes(searchTerm.toLowerCase()) || 
                         post.excerpt.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesCategory && matchesSearch;
  });

  const featuredPost = blogPosts.find(post => post.featured);
  const regularPosts = filteredPosts.filter(post => !post.featured);

  return (
    <div className="min-h-screen bg-slate-900 text-white relative overflow-hidden">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        {/* Hero Section */}
        <section className="pt-32 pb-20">
          <div className="container mx-auto px-6">
            <div className="max-w-4xl mx-auto text-center">
              <h1 className="text-5xl md:text-6xl font-bold mb-6 leading-tight">
                Digital Marketing
                <span className="bg-gradient-to-r from-amber-400 to-amber-600 bg-clip-text text-transparent"> Insights & Tips</span>
              </h1>
              <p className="text-xl text-slate-300 mb-12 max-w-3xl mx-auto">
                Stay ahead of the curve with our latest digital marketing insights, strategies, and industry trends from our team of experts.
              </p>
              
              {/* Search and Filter */}
              <div className="max-w-2xl mx-auto">
                <div className="flex flex-col sm:flex-row gap-4 mb-8">
                  <div className="relative flex-1">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 w-5 h-5" />
                    <Input
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      placeholder="Search articles..."
                      className="pl-10 bg-slate-800 border-slate-600 text-white"
                    />
                  </div>
                  <div className="flex items-center space-x-2">
                    <Filter className="text-amber-400 w-5 h-5" />
                    <select
                      value={selectedCategory}
                      onChange={(e) => setSelectedCategory(e.target.value)}
                      className="bg-slate-800 border border-slate-600 rounded-md px-4 py-2 text-white"
                    >
                      {categories.map(category => (
                        <option key={category} value={category.toLowerCase()}>
                          {category}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Featured Post */}
        {featuredPost && (
          <section className="pb-20">
            <div className="container mx-auto px-6">
              <div className="max-w-6xl mx-auto">
                <div className="bg-slate-800/50 rounded-lg overflow-hidden">
                  <div className="grid grid-cols-1 lg:grid-cols-2">
                    <div className="relative">
                      <img 
                        src={featuredPost.image} 
                        alt={featuredPost.title}
                        className="w-full h-80 lg:h-full object-cover"
                      />
                      <div className="absolute top-4 left-4">
                        <span className="bg-amber-500 text-white px-3 py-1 rounded-full text-sm font-semibold">
                          Featured
                        </span>
                      </div>
                    </div>
                    <div className="p-8 lg:p-12">
                      <span className="inline-block bg-amber-500/20 text-amber-400 px-3 py-1 rounded-full text-sm font-medium mb-4">
                        {featuredPost.category}
                      </span>
                      <h2 className="text-3xl font-bold text-white mb-4">{featuredPost.title}</h2>
                      <p className="text-slate-300 mb-6">{featuredPost.excerpt}</p>
                      <div className="flex items-center justify-between text-sm text-slate-400 mb-6">
                        <div className="flex items-center">
                          <User className="w-4 h-4 mr-1" />
                          {featuredPost.author}
                        </div>
                        <div className="flex items-center space-x-4">
                          <span>{featuredPost.date}</span>
                          <div className="flex items-center">
                            <Clock className="w-4 h-4 mr-1" />
                            {featuredPost.readTime}
                          </div>
                        </div>
                      </div>
                      <Button className="bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700 text-white">
                        Read Full Article
                        <ArrowRight className="ml-2 w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </section>
        )}

        {/* Blog Posts Grid */}
        <section className="pb-20">
          <div className="container mx-auto px-6">
            <div className="max-w-6xl mx-auto">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {regularPosts.map((post, index) => (
                  <Card key={index} className="bg-slate-900/80 border-amber-500/20 hover:border-amber-500/40 transition-all duration-300 overflow-hidden group">
                    <div className="relative overflow-hidden">
                      <img 
                        src={post.image} 
                        alt={post.title}
                        className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                      />
                      <div className="absolute top-4 left-4">
                        <span className="bg-amber-500 text-white px-3 py-1 rounded-full text-sm font-medium">
                          {post.category}
                        </span>
                      </div>
                    </div>
                    <CardContent className="p-6">
                      <h3 className="text-xl font-bold text-white mb-3 group-hover:text-amber-400 transition-colors line-clamp-2">
                        {post.title}
                      </h3>
                      <p className="text-slate-300 mb-4 line-clamp-3">
                        {post.excerpt}
                      </p>
                      <div className="flex items-center justify-between text-sm text-slate-400 mb-4">
                        <div className="flex items-center">
                          <User className="w-4 h-4 mr-1" />
                          {post.author}
                        </div>
                        <div className="flex items-center">
                          <Clock className="w-4 h-4 mr-1" />
                          {post.readTime}
                        </div>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-slate-400 text-sm">{post.date}</span>
                        <Button variant="ghost" className="text-amber-400 hover:text-white hover:bg-amber-500 p-0">
                          Read More
                          <ArrowRight className="ml-2 w-4 h-4 group-hover:translate-x-1 transition-transform" />
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* Newsletter Section */}
        <section className="py-20 bg-gradient-to-r from-amber-600 to-amber-500">
          <div className="container mx-auto px-6 text-center">
            <h2 className="text-4xl md:text-5xl font-bold mb-6">Stay Updated with Our Latest Insights</h2>
            <p className="text-xl mb-8 max-w-3xl mx-auto">
              Get exclusive digital marketing tips, strategies, and case studies delivered straight to your inbox every week.
            </p>
            <div className="max-w-md mx-auto flex gap-4">
              <Input
                placeholder="Enter your email..."
                className="bg-white/20 border-white/30 text-white placeholder:text-white/70"
              />
              <Button className="bg-white text-amber-600 hover:bg-slate-100">
                Subscribe
              </Button>
            </div>
          </div>
        </section>
      </main>
      
      <Footer />
    </div>
  );
};

export default Blog;
