import React from 'react';
import { Link } from 'react-router-dom';
import { ArrowRight, Target, TrendingUp, Users, CheckCircle, Building, Star, Crown, DollarSign, Zap } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';
import Sidebar from '@/components/Sidebar';

const GoogleAdsJaipur = () => {
  const googleAdsServices = [
    'Google Ads Management Jaipur',
    'PPC Campaign Management Jaipur',
    'Search Ads Jaipur',
    'Display Advertising Jaipur',
    'Shopping Ads Jaipur',
    'YouTube Ads Jaipur',
    'Local Ads Jaipur',
    'Mobile Ads Jaipur',
    'Remarketing Campaigns Jaipur',
    'Lead Generation Ads Jaipur',
    'E-commerce PPC Jaipur',
    'Tourism Industry Ads Jaipur',
    'Heritage Business Ads Jaipur',
    'Handicraft Industry Ads Jaipur',
    'Jewelry Business Ads Jaipur',
    'Hotel & Resort Ads Jaipur',
    'Real Estate Ads Jaipur',
    'Restaurant Ads Jaipur',
    'B2B Lead Generation Jaipur',
    'Conversion Optimization Jaipur'
  ];

  const adSpecializations = [
    {
      type: 'Tourism & Heritage Advertising',
      description: 'Targeted advertising for Jaipur\'s tourism capital and heritage industry businesses',
      icon: Building,
      features: ['Tourism Business Ads', 'Heritage Site Promotion', 'Hotel & Resort Campaigns', 'Travel Package Ads']
    },
    {
      type: 'Handicraft & Jewelry Advertising',
      description: 'Expert advertising for Jaipur\'s famous handicraft and jewelry manufacturing sector',
      icon: Star,
      features: ['Jewelry Business PPC', 'Handicraft Export Ads', 'Artisan Business Campaigns', 'Craft Industry Ads']
    },
    {
      type: 'Textile & Fashion Advertising',
      description: 'Professional advertising for Jaipur\'s textile and fashion industry leaders',
      icon: Crown,
      features: ['Textile Manufacturing Ads', 'Fashion Brand Campaigns', 'Garment Export PPC', 'Fabric Business Ads']
    },
    {
      type: 'Real Estate & Construction Advertising',
      description: 'Comprehensive advertising for Jaipur\'s growing real estate and construction sector',
      icon: DollarSign,
      features: ['Property Developer Ads', 'Construction Company PPC', 'Real Estate Agent Campaigns', 'Architecture Firm Ads']
    }
  ];

  const adPackages = [
    {
      name: 'Google Ads Jaipur Starter',
      price: '₹20,000',
      period: '/month',
      description: 'Perfect for small Jaipur businesses and tourism operators',
      features: [
        'Ad Spend: ₹15,000/month',
        'Search + Display Campaigns',
        'Local Jaipur Targeting',
        'Landing Page Optimization',
        'Monthly Performance Reports',
        'Tourism Industry Focus'
      ]
    },
    {
      name: 'Jaipur Google Ads Professional',
      price: '₹35,000',
      period: '/month',
      description: 'Comprehensive advertising for growing Jaipur businesses',
      features: [
        'Ad Spend: ₹50,000/month',
        'Multi-Campaign Strategy',
        'Advanced Targeting',
        'Conversion Tracking',
        'A/B Testing',
        'Heritage/Handicraft Specialization',
        'Bi-weekly Optimization'
      ],
      popular: true
    },
    {
      name: 'Enterprise Google Ads Jaipur',
      price: '₹65,000',
      period: '/month',
      description: 'Advanced advertising for large Jaipur enterprises and exporters',
      features: [
        'Ad Spend: ₹1,00,000+/month',
        'Full-Funnel Campaigns',
        'Advanced Analytics',
        'Multi-platform Integration',
        'Export Market Targeting',
        'Dedicated Account Manager',
        'Weekly Strategy Calls'
      ]
    }
  ];

  const stats = [
    {
      metric: '1800+',
      description: 'Jaipur Ad Campaigns',
      detail: 'Successfully managed'
    },
    {
      metric: '520%',
      description: 'Average ROAS',
      detail: 'Return on ad spend'
    },
    {
      metric: '₹28Cr+',
      description: 'Revenue Generated',
      detail: 'Through Google Ads'
    },
    {
      metric: '89%',
      description: 'Lead Quality Score',
      detail: 'High-intent prospects'
    }
  ];

  const achievements = [
    'Top Google Ads Agency in Jaipur',
    'Tourism Industry PPC Leaders',
    'Heritage Business Ad Experts',
    'Handicraft Industry Ad Specialists',
    'Jewelry Business PPC Champions',
    'Pink City Digital Advertising Pioneers'
  ];

  return (
    <div className="min-h-screen bg-slate-900 relative">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        <div className="container mx-auto px-6 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            {/* Main Content */}
            <div className="lg:col-span-3">
              {/* Hero Section */}
              <section className="pt-24 pb-16">
                <div className="max-w-4xl">
                  <div className="inline-flex items-center space-x-2 bg-red-500/20 border border-red-500/30 rounded-full px-6 py-2 mb-8">
                    <Target className="w-4 h-4 text-red-400" />
                    <span className="text-red-400 font-medium">Google Ads Jaipur • Pink City PPC Excellence</span>
                  </div>
                  
                  <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
                    #1 Google Ads Company in
                    <span className="bg-gradient-to-r from-red-400 to-red-600 bg-clip-text text-transparent"> Jaipur</span>
                  </h1>
                  
                  <p className="text-xl text-slate-300 mb-8">
                    Premier Google Ads management in Jaipur offering comprehensive PPC advertising solutions including search ads, display campaigns, shopping ads, YouTube advertising, and remarketing campaigns. Our Jaipur Google Ads agency provides professional PPC services with keyword research, ad copywriting, bid management, conversion tracking, and campaign optimization. Serving 1800+ Jaipur businesses across all areas - from tourism operators in Pink City to handicraft exporters in Bagru. Expert Google Ads solutions with proven ₹28Cr+ revenue generation and 520% average ROAS for Jaipur clients in Rajasthan's heritage capital through strategic pay-per-click advertising and digital marketing excellence.
                  </p>
                  
                  <div className="flex flex-col sm:flex-row gap-4 mb-8">
                    <Button asChild size="lg" className="bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white text-lg px-8 py-4">
                      <Link to="/quote">Get Free Jaipur Google Ads Audit</Link>
                    </Button>
                    <Button asChild size="lg" variant="outline" className="border-red-500 text-red-400 hover:bg-red-500 hover:text-white text-lg px-8 py-4">
                      <Link to="/contact">Call Jaipur PPC Experts: +91-8708577598</Link>
                    </Button>
                  </div>

                  {/* Achievements */}
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                    {achievements.map((achievement, index) => (
                      <div key={index} className="flex items-center justify-center space-x-2 bg-slate-800/50 rounded-lg p-3">
                        <CheckCircle className="w-4 h-4 text-red-400 flex-shrink-0" />
                        <span className="text-slate-300">{achievement}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </section>

              {/* Stats Section */}
              <section className="mb-16 bg-slate-800/50 rounded-2xl p-8">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Jaipur Google Ads
                    <span className="bg-gradient-to-r from-red-400 to-red-600 bg-clip-text text-transparent"> Success Metrics</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Measurable Google Ads results from Jaipur businesses across tourism, handicraft, jewelry, and heritage sectors.
                  </p>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
                  {stats.map((stat, index) => (
                    <div key={index} className="text-center">
                      <div className="text-4xl font-bold text-red-400 mb-2">{stat.metric}</div>
                      <div className="text-white font-semibold mb-1">{stat.description}</div>
                      <div className="text-slate-400">{stat.detail}</div>
                    </div>
                  ))}
                </div>
              </section>

              {/* Ad Specializations Section */}
              <section className="mb-16">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Jaipur Google Ads
                    <span className="bg-gradient-to-r from-red-400 to-red-600 bg-clip-text text-transparent"> Specializations</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Specialized advertising strategies designed for Jaipur's unique tourism, handicraft, jewelry, and heritage business landscape.
                  </p>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  {adSpecializations.map((spec, index) => (
                    <Card key={index} className="bg-slate-900/80 border-red-500/20 hover:border-red-500/40 transition-all duration-300">
                      <CardContent className="p-8">
                        <div className="w-12 h-12 bg-gradient-to-r from-red-500 to-red-600 rounded-lg flex items-center justify-center mb-6">
                          <spec.icon className="w-6 h-6 text-white" />
                        </div>
                        <h3 className="text-xl font-bold text-white mb-4">{spec.type}</h3>
                        <p className="text-slate-300 mb-6">{spec.description}</p>
                        <ul className="space-y-2">
                          {spec.features.map((feature, idx) => (
                            <li key={idx} className="flex items-center text-slate-400 text-sm">
                              <CheckCircle className="w-3 h-3 text-red-400 mr-2 flex-shrink-0" />
                              {feature}
                            </li>
                          ))}
                        </ul>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>

              {/* Pricing Section */}
              <section className="mb-16">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Jaipur Google Ads
                    <span className="bg-gradient-to-r from-red-400 to-red-600 bg-clip-text text-transparent"> Pricing</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Competitive Google Ads pricing designed for Jaipur's tourism, handicraft, and heritage business environment.
                  </p>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                  {adPackages.map((pkg, index) => (
                    <Card key={index} className={`bg-slate-900/80 border-red-500/20 hover:border-red-500/40 transition-all duration-300 relative ${pkg.popular ? 'ring-2 ring-red-500' : ''}`}>
                      {pkg.popular && (
                        <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                          <span className="bg-red-500 text-white px-4 py-2 rounded-full text-sm font-semibold">Most Popular</span>
                        </div>
                      )}
                      <CardContent className="p-8">
                        <h3 className="text-2xl font-bold text-white mb-2">{pkg.name}</h3>
                        <p className="text-slate-300 mb-6">{pkg.description}</p>
                        <div className="mb-6">
                          <span className="text-4xl font-bold text-red-400">{pkg.price}</span>
                          <span className="text-slate-400">{pkg.period}</span>
                        </div>
                        <ul className="space-y-3 mb-8">
                          {pkg.features.map((feature, idx) => (
                            <li key={idx} className="flex items-center text-slate-300">
                              <CheckCircle className="w-4 h-4 text-red-400 mr-3 flex-shrink-0" />
                              {feature}
                            </li>
                          ))}
                        </ul>
                        <Button asChild className={`w-full ${pkg.popular ? 'bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700' : 'bg-slate-700 hover:bg-slate-600'} text-white`}>
                          <Link to="/quote">Get Started</Link>
                        </Button>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>

              {/* CTA Section */}
              <section className="bg-gradient-to-r from-red-600 to-red-500 rounded-2xl p-8 text-center">
                <h2 className="text-3xl md:text-5xl font-bold mb-6">Ready to Dominate Jaipur with Google Ads?</h2>
                <p className="text-xl mb-8">
                  Join 1800+ Jaipur businesses that trust GOD Digital Marketing for Google Ads success. Proven strategies that deliver 520% ROAS and ₹28Cr+ revenue generation.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button asChild size="lg" className="bg-white text-red-600 hover:bg-slate-100 text-lg px-8 py-4">
                    <Link to="/quote">Get Free Ads Audit</Link>
                  </Button>
                  <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-red-600 text-lg px-8 py-4">
                    <Link to="/contact">Call PPC Experts: +91-8708577598</Link>
                  </Button>
                </div>
              </section>
            </div>

            {/* Sidebar */}
            <div className="lg:col-span-1">
              <Sidebar currentService="ppc" currentLocation="jaipur" />
            </div>
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
};

export default GoogleAdsJaipur;
