
import React, { useEffect, useState } from 'react';
import { TrendingUp, Users, DollarSign, Globe } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { motion, useAnimation } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import CountUp from 'react-countup';

const ResultsSection = () => {
  const [ref, inView] = useInView({
    threshold: 0.1,
    triggerOnce: true,
  });

  const controls = useAnimation();
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    if (inView) {
      setIsVisible(true);
      controls.start('visible');
    }
  }, [controls, inView]);

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delayChildren: 0.3,
        staggerChildren: 0.2
      }
    }
  };

  const cardVariants = {
    hidden: {
      y: 60,
      opacity: 0,
      scale: 0.8
    },
    visible: {
      y: 0,
      opacity: 1,
      scale: 1,
      transition: {
        duration: 0.8,
        ease: "easeOut" as const
      }
    }
  };

  const results = [
    {
      icon: TrendingUp,
      metric: '10M+',
      description: 'Monthly views generated from zero',
      case: 'Built multiple websites to 5-10 million monthly views'
    },
    {
      icon: Globe,
      metric: '6',
      description: 'Countries with proven SEO success',
      case: 'UK, US, Dubai, India, Kuwait, South Africa dominance'
    },
    {
      icon: Users,
      metric: '1000s',
      description: 'Monthly traffic increases achieved',
      case: 'Consistently scaled websites from zero to thousands monthly'
    },
    {
      icon: DollarSign,
      metric: '₹150K',
      description: 'Premium monthly retainers earned',
      case: 'Premium positioning with ₹60K-150K monthly clients'
    }
  ];

  return (
    <section ref={ref} className="py-20 relative overflow-hidden">
      {/* Background decorative elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <motion.div
          className="absolute top-20 left-20 w-64 h-64 bg-gradient-to-r from-amber-400/5 to-amber-600/5 rounded-full blur-3xl"
          animate={{
            scale: [1, 1.3, 1],
            opacity: [0.2, 0.4, 0.2],
          }}
          transition={{
            duration: 12,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
        <motion.div
          className="absolute bottom-10 right-10 w-48 h-48 bg-gradient-to-r from-blue-400/5 to-blue-600/5 rounded-full blur-3xl"
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.3, 0.5, 0.3],
          }}
          transition={{
            duration: 10,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 3
          }}
        />
      </div>

      <div className="container mx-auto px-6 relative z-10">
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
          transition={{ duration: 0.8, ease: "easeOut" }}
        >
          <motion.h2
            className="text-4xl md:text-5xl font-bold mb-6"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={inView ? { opacity: 1, scale: 1 } : { opacity: 0, scale: 0.9 }}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            International SEO Results
            <motion.span
              className="bg-gradient-to-r from-amber-400 to-amber-600 bg-clip-text text-transparent"
              initial={{ opacity: 0 }}
              animate={inView ? { opacity: 1 } : { opacity: 0 }}
              transition={{ duration: 0.8, delay: 0.5 }}
            >
              {" "}Across 6 Countries
            </motion.span>
          </motion.h2>
          <motion.p
            className="text-xl text-slate-300 max-w-3xl mx-auto"
            initial={{ opacity: 0, y: 20 }}
            animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
            transition={{ duration: 0.8, delay: 0.4 }}
          >
            7 years of proven international SEO expertise. Real results from UK to Dubai, US to South Africa - building digital empires that dominate global markets.
          </motion.p>
        </motion.div>

        <motion.div
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8"
          variants={containerVariants}
          initial="hidden"
          animate={controls}
        >
          {results.map((result, index) => (
            <motion.div
              key={index}
              variants={cardVariants}
              whileHover={{
                y: -15,
                scale: 1.05,
                transition: { duration: 0.3, ease: "easeOut" }
              }}
              whileTap={{ scale: 0.98 }}
            >
              <Card className="bg-gradient-to-br from-slate-800 to-slate-900 border-amber-500/20 text-center group h-full backdrop-blur-sm">
                <CardContent className="p-8 h-full flex flex-col justify-center">
                  <motion.div
                    className="w-16 h-16 bg-gradient-to-r from-amber-500 to-amber-600 rounded-full flex items-center justify-center mx-auto mb-6"
                    initial={{ scale: 0, rotate: -180 }}
                    animate={isVisible ? { scale: 1, rotate: 0 } : { scale: 0, rotate: -180 }}
                    transition={{ duration: 0.8, delay: index * 0.2 + 0.5, ease: "easeOut" }}
                    whileHover={{
                      scale: 1.2,
                      rotate: 360,
                      transition: { duration: 0.6, ease: "easeInOut" }
                    }}
                  >
                    <result.icon className="w-8 h-8 text-white" />
                  </motion.div>

                  <motion.div
                    className="text-4xl font-bold text-amber-400 mb-3"
                    initial={{ opacity: 0, scale: 0.5 }}
                    animate={isVisible ? { opacity: 1, scale: 1 } : { opacity: 0, scale: 0.5 }}
                    transition={{ duration: 0.8, delay: index * 0.2 + 0.7 }}
                  >
                    {result.metric === '10M+' && isVisible && <CountUp end={10} duration={2} suffix="M+" />}
                    {result.metric === '6' && isVisible && <CountUp end={6} duration={2} />}
                    {result.metric === '1000s' && isVisible && <CountUp end={1000} duration={2} suffix="s" />}
                    {result.metric === '₹150K' && isVisible && <CountUp end={150} duration={2} prefix="₹" suffix="K" />}
                    {!['10M+', '6', '1000s', '₹150K'].includes(result.metric) && result.metric}
                  </motion.div>

                  <motion.p
                    className="text-white font-semibold mb-2"
                    initial={{ opacity: 0, y: 20 }}
                    animate={isVisible ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
                    transition={{ duration: 0.6, delay: index * 0.2 + 0.9 }}
                  >
                    {result.description}
                  </motion.p>

                  <motion.p
                    className="text-slate-400 text-sm"
                    initial={{ opacity: 0, y: 20 }}
                    animate={isVisible ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
                    transition={{ duration: 0.6, delay: index * 0.2 + 1.1 }}
                  >
                    {result.case}
                  </motion.p>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </section>
  );
};

export default ResultsSection;
