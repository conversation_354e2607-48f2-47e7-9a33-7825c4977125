
import React, { lazy, Suspense, useState, useEffect } from 'react';
import { <PERSON> } from 'react-router-dom';
<<<<<<< HEAD
import { ArrowRight, Play, Award, Globe, TrendingUp, Users } from 'lucide-react';
=======
import { ArrowRight, Play, Award, Sparkles } from 'lucide-react';
>>>>>>> 19c559cd2c523741131742bcf152fbbdeb09428d
import { Button } from '@/components/ui/button';
import { motion, useAnimation } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import CountUp from 'react-countup';

// Lazy load the 3D logo for better initial page load
const Logo3D = lazy(() => import('@/components/Logo3D'));

// Enhanced fallback component
const LogoFallback = () => (
  <div className="w-full h-48 flex items-center justify-center mb-8">
    <div className="text-center animate-pulse">
      <div className="text-5xl font-bold text-white mb-3 tracking-wider">GOD</div>
      <div className="text-lg text-amber-400 tracking-widest">DIGITAL MARKETING</div>
      <div className="mt-4 flex justify-center space-x-2">
        <div className="w-3 h-3 bg-amber-400 rounded-full animate-bounce"></div>
        <div className="w-3 h-3 bg-blue-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
        <div className="w-3 h-3 bg-green-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
      </div>
    </div>
  </div>
);

const Hero = () => {
  const [ref, inView] = useInView({
    threshold: 0.1,
    triggerOnce: true,
  });

  const controls = useAnimation();
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    if (inView) {
      setIsVisible(true);
      controls.start('visible');
    }
  }, [controls, inView]);

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delayChildren: 0.3,
        staggerChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.6,
        ease: "easeOut" as const
      }
    }
  };

  const statsVariants = {
    hidden: { scale: 0.8, opacity: 0 },
    visible: {
      scale: 1,
      opacity: 1,
      transition: {
        duration: 0.8,
        ease: "easeOut" as const
      }
    }
  };

  const buttonVariants = {
    hidden: { y: 30, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.6,
        ease: "easeOut" as const
      }
    },
    hover: {
      scale: 1.05,
      transition: {
        duration: 0.2,
        ease: "easeInOut" as const
      }
    },
    tap: {
      scale: 0.95
    }
  };

  return (
<<<<<<< HEAD
    <section ref={ref} className="pt-20 pb-32 relative overflow-hidden">
      {/* Animated background elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <motion.div
          className="absolute top-20 left-10 w-32 h-32 bg-gradient-to-r from-amber-400/10 to-amber-600/10 rounded-full blur-xl"
          animate={{
            x: [0, 30, 0],
            y: [0, -20, 0],
            scale: [1, 1.2, 1],
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
        <motion.div
          className="absolute top-40 right-20 w-24 h-24 bg-gradient-to-r from-blue-400/10 to-blue-600/10 rounded-full blur-xl"
          animate={{
            x: [0, -25, 0],
            y: [0, 15, 0],
            scale: [1, 0.8, 1],
          }}
          transition={{
            duration: 6,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 1
          }}
        />
        <motion.div
          className="absolute bottom-20 left-1/4 w-20 h-20 bg-gradient-to-r from-green-400/10 to-green-600/10 rounded-full blur-xl"
          animate={{
            x: [0, 20, 0],
            y: [0, -10, 0],
            scale: [1, 1.1, 1],
          }}
          transition={{
            duration: 7,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 2
          }}
        />
      </div>

      <div className="container mx-auto px-6 relative z-10">
        <motion.div
          className="max-w-4xl mx-auto text-center"
          variants={containerVariants}
          initial="hidden"
          animate={controls}
        >
          {/* Lazy loaded 3D Logo with fallback */}
=======
    <section className="pt-20 pb-32 relative">
      <div className="container mx-auto px-6">
        <div className="max-w-4xl mx-auto text-center">
          {/* Enhanced 3D Logo with fallback */}
>>>>>>> 19c559cd2c523741131742bcf152fbbdeb09428d
          <Suspense fallback={<LogoFallback />}>
            <Logo3D className="mb-8" />
          </Suspense>

<<<<<<< HEAD
          {/* Trust Badge */}
          <motion.div
            className="inline-flex items-center space-x-2 bg-amber-500/20 border border-amber-500/30 rounded-full px-6 py-2 mb-8 backdrop-blur-sm"
            variants={itemVariants}
            whileHover={{
              scale: 1.05,
              backgroundColor: "rgba(245, 158, 11, 0.3)",
              transition: { duration: 0.2 }
            }}
          >
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
            >
              <Award className="w-4 h-4 text-amber-400" />
            </motion.div>
            <span className="text-amber-400 font-medium">7 Years International SEO Experience • 6 Countries</span>
          </motion.div>

          {/* Main Headline */}
          <motion.h1
            className="text-4xl md:text-6xl lg:text-7xl font-bold mb-6 leading-tight"
            variants={itemVariants}
          >
            <motion.span
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.5 }}
            >
              Scale Your Business
            </motion.span>
            <motion.span
              className="bg-gradient-to-r from-amber-400 to-amber-600 bg-clip-text text-transparent"
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.8, delay: 0.8 }}
              whileHover={{
                scale: 1.05,
                transition: { duration: 0.2 }
              }}
            >
              {" "}Globally
            </motion.span>
          </motion.h1>

          {/* Subheadline */}
          <motion.p
            className="text-lg md:text-xl lg:text-2xl text-slate-300 mb-12 max-w-3xl mx-auto leading-relaxed"
            variants={itemVariants}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 1.0 }}
          >
            International SEO consultant who built websites from zero to 10+ million monthly views across UK, US, Dubai, India, Kuwait & South Africa.
            Premium AI-powered strategies for businesses ready to dominate global markets.
          </motion.p>

          {/* Stats - Optimized for mobile */}
          <motion.div
            className="grid grid-cols-1 sm:grid-cols-3 gap-6 md:gap-8 mb-12"
            variants={statsVariants}
            initial="hidden"
            animate={isVisible ? "visible" : "hidden"}
          >
            <motion.div
              className="text-center p-4 bg-slate-800/30 rounded-lg backdrop-blur-sm border border-slate-700/50"
              whileHover={{
                scale: 1.05,
                backgroundColor: "rgba(30, 41, 59, 0.5)",
                transition: { duration: 0.2 }
              }}
            >
              <div className="text-3xl md:text-4xl font-bold text-amber-400 mb-2">
                {isVisible && <CountUp end={10} duration={2} suffix="M+" />}
              </div>
              <div className="text-sm md:text-base text-slate-300">Monthly Views Generated</div>
            </motion.div>
            <motion.div
              className="text-center p-4 bg-slate-800/30 rounded-lg backdrop-blur-sm border border-slate-700/50"
              whileHover={{
                scale: 1.05,
                backgroundColor: "rgba(30, 41, 59, 0.5)",
                transition: { duration: 0.2 }
              }}
            >
              <div className="text-3xl md:text-4xl font-bold text-amber-400 mb-2">
                {isVisible && <CountUp end={6} duration={2} />}
              </div>
              <div className="text-sm md:text-base text-slate-300">Countries Dominated</div>
            </motion.div>
            <motion.div
              className="text-center p-4 bg-slate-800/30 rounded-lg backdrop-blur-sm border border-slate-700/50"
              whileHover={{
                scale: 1.05,
                backgroundColor: "rgba(30, 41, 59, 0.5)",
                transition: { duration: 0.2 }
              }}
            >
              <div className="text-3xl md:text-4xl font-bold text-amber-400 mb-2">
                {isVisible && <CountUp end={7} duration={2} />}
              </div>
              <div className="text-sm md:text-base text-slate-300">Years International Experience</div>
            </motion.div>
          </div>

          {/* CTA Buttons - Mobile optimized */}
          <motion.div
            className="flex flex-col sm:flex-row gap-4 justify-center mb-16"
            variants={containerVariants}
            initial="hidden"
            animate={isVisible ? "visible" : "hidden"}
          >
            <motion.div variants={buttonVariants} whileHover="hover" whileTap="tap">
              <Button asChild size="lg" className="bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700 text-white text-base md:text-lg px-6 md:px-8 py-3 md:py-4 shadow-lg hover:shadow-xl transition-shadow duration-300">
                <Link to="/quote" className="flex items-center justify-center">
                  Get Premium SEO Consultation
                  <motion.div
                    animate={{ x: [0, 5, 0] }}
                    transition={{ duration: 1.5, repeat: Infinity, ease: "easeInOut" }}
                  >
                    <ArrowRight className="ml-2 w-4 h-4 md:w-5 md:h-5" />
                  </motion.div>
                </Link>
              </Button>
            </motion.div>
            <motion.div variants={buttonVariants} whileHover="hover" whileTap="tap">
              <Button asChild size="lg" variant="outline" className="border-amber-500 text-amber-400 hover:bg-amber-500 hover:text-white text-base md:text-lg px-6 md:px-8 py-3 md:py-4 backdrop-blur-sm">
                <Link to="/case-studies" className="flex items-center justify-center">
                  <Play className="mr-2 w-4 h-4 md:w-5 md:h-5" />
                  View International Results
                </Link>
              </Button>
            </motion.div>
          </motion.div>

          {/* Client Logos - Mobile optimized */}
          <motion.div
            className="text-center"
            variants={itemVariants}
            initial="hidden"
            animate={isVisible ? "visible" : "hidden"}
          >
            <motion.p
              className="text-slate-400 mb-6 md:mb-8 text-sm md:text-base"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.8, delay: 1.5 }}
            >
              Proven results across global markets
            </motion.p>
            <motion.div
              className="flex flex-wrap justify-center items-center gap-4 md:gap-8 opacity-60"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 0.6, y: 0 }}
              transition={{ duration: 0.8, delay: 1.8 }}
            >
              {['Easy Outdoor', 'Bulkland', 'Shakespeare Flowers', 'Opus Campers', 'Real Estate Leaders'].map((client, index) => (
                <motion.div
                  key={client}
                  className="bg-white/10 rounded-lg px-4 py-2 md:px-6 md:py-3 text-white font-semibold text-sm md:text-base"
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.5, delay: 2 + index * 0.1 }}
                  whileHover={{
                    scale: 1.05,
                    backgroundColor: "rgba(255, 255, 255, 0.2)",
                    transition: { duration: 0.2 }
                  }}
                >
                  {client}
                </motion.div>
              ))}
            </motion.div>
          </motion.div>
        </motion.div>
=======
          {/* Enhanced Trust Badge */}
          <div className="inline-flex items-center space-x-2 bg-gradient-to-r from-amber-500/20 to-amber-600/20 border border-amber-500/30 rounded-full px-8 py-3 mb-8 backdrop-blur-sm">
            <Award className="w-5 h-5 text-amber-400" />
            <span className="text-amber-400 font-semibold text-sm md:text-base">7 Years International SEO Experience • 6 Countries • Premium Results</span>
            <Sparkles className="w-4 h-4 text-amber-400" />
          </div>

          {/* Main Headline with enhanced styling */}
          <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold mb-6 leading-tight">
            Scale Your Business
            <span className="bg-gradient-to-r from-amber-400 via-amber-500 to-amber-600 bg-clip-text text-transparent block md:inline"> Globally</span>
          </h1>

          {/* Enhanced Subheadline */}
          <p className="text-lg md:text-xl lg:text-2xl text-slate-300 mb-12 max-w-3xl mx-auto leading-relaxed font-medium">
            International SEO consultant who built websites from zero to 10+ million monthly views across UK, US, Dubai, India, Kuwait & South Africa.
            <span className="text-amber-400 font-semibold"> Premium AI-powered strategies</span> for businesses ready to dominate global markets.
          </p>

          {/* Enhanced Stats with better mobile layout */}
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-6 md:gap-8 mb-12">
            <div className="text-center group">
              <div className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-amber-400 to-amber-600 bg-clip-text text-transparent mb-2 group-hover:scale-110 transition-transform duration-300">10M+</div>
              <div className="text-sm md:text-base text-slate-300 font-medium">Monthly Views Generated</div>
            </div>
            <div className="text-center group">
              <div className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-blue-400 to-blue-600 bg-clip-text text-transparent mb-2 group-hover:scale-110 transition-transform duration-300">6</div>
              <div className="text-sm md:text-base text-slate-300 font-medium">Countries Dominated</div>
            </div>
            <div className="text-center group">
              <div className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-green-400 to-green-600 bg-clip-text text-transparent mb-2 group-hover:scale-110 transition-transform duration-300">7</div>
              <div className="text-sm md:text-base text-slate-300 font-medium">Years International Experience</div>
            </div>
          </div>

          {/* Enhanced CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center mb-16">
            <Button asChild size="lg" className="bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700 text-white text-base md:text-lg px-8 md:px-10 py-4 md:py-5 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105">
              <Link to="/quote" className="flex items-center justify-center">
                Get Premium SEO Consultation
                <ArrowRight className="ml-2 w-5 h-5 md:w-6 md:h-6" />
              </Link>
            </Button>
            <Button asChild size="lg" variant="outline" className="border-2 border-amber-500 text-amber-400 hover:bg-amber-500 hover:text-white text-base md:text-lg px-8 md:px-10 py-4 md:py-5 backdrop-blur-sm bg-slate-900/50 hover:scale-105 transition-all duration-300">
              <Link to="/case-studies" className="flex items-center justify-center">
                <Play className="mr-2 w-5 h-5 md:w-6 md:h-6" />
                View International Results
              </Link>
            </Button>
          </div>

          {/* Enhanced Client Logos */}
          <div className="text-center">
            <p className="text-slate-400 mb-6 md:mb-8 text-sm md:text-base font-medium">Proven results across global markets</p>
            <div className="flex flex-wrap justify-center items-center gap-4 md:gap-8 opacity-70 hover:opacity-90 transition-opacity duration-300">
              {['Easy Outdoor', 'Bulkland', 'Shakespeare Flowers', 'Opus Campers', 'Real Estate Leaders'].map((client, index) => (
                <div key={index} className="bg-gradient-to-r from-white/10 to-white/5 backdrop-blur-sm rounded-lg px-4 py-3 md:px-6 md:py-4 text-white font-semibold text-sm md:text-base border border-white/10 hover:border-white/20 transition-all duration-300 hover:scale-105">
                  {client}
                </div>
              ))}
            </div>
          </div>
        </div>
>>>>>>> 19c559cd2c523741131742bcf152fbbdeb09428d
      </div>
    </section>
  );
};

export default Hero;
